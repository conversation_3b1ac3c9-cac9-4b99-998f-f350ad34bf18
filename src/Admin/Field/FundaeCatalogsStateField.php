<?php

namespace App\Admin\Field;

use App\Form\Type\Admin\FundaeCatalogsStateType;
use EasyCorp\Bundle\EasyAdminBundle\Contracts\Field\FieldInterface;
use EasyCorp\Bundle\EasyAdminBundle\Field\FieldTrait;

final class FundaeCatalogsStateField implements FieldInterface
{
    use FieldTrait;

    public static function new(string $propertyName, ?string $label = null)
    {
        return (new self())
            ->setProperty($propertyName)
            ->setLabel($label)
            ->setFormType(FundaeCatalogsStateType::class)
            ->setFormTypeOptions([
                'attr' => [
                    'required' => true
                ]
            ])
            ;
    }
}
