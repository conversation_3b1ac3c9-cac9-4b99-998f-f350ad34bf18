<?php

declare(strict_types=1);

namespace App\Admin\Traits;

trait BrowserAndDeviceLoginTrait
{
    protected function detectBrowser()
    {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $resultBrowser = '';

        $basicBrowser = [
            'Trident\/7.0' => 'Internet Explorer 11',
            'Beamrise' => 'Beamrise',
            'Opera' => 'Opera',
            'OPR' => 'Opera',
            'Shiira' => '<PERSON><PERSON>',
            'Chimera' => 'Chimera',
            'Phoenix' => 'Phoenix',
            'Firebird' => 'Firebird',
            'Camino' => 'Camino',
            'Netscape' => 'Netscape',
            'OmniWeb' => 'OmniWeb',
            'Konqueror' => 'Konqueror',
            'icab' => 'iCab',
            'Lynx' => 'Lynx',
            'Links' => 'Links',
            'hotjava' => 'HotJava',
            'amaya' => 'Amaya',
            'IBrowse' => 'IBrowse',
            'iTunes' => 'iTunes',
            '<PERSON>' => '<PERSON>',
            'Dillo' => '<PERSON><PERSON>',
            'Maxthon' => 'Maxthon',
            'A<PERSON>ra' => '<PERSON><PERSON>ra',
            'Galeon' => '<PERSON>on',
            'Iceape' => 'Iceape',
            'Iceweasel' => 'Iceweasel',
            'Midori' => 'Midori',
            'QupZilla' => 'QupZilla',
            'Namoroka' => 'Namoroka',
            'NetSurf' => 'NetSurf',
            'BOLT' => 'BOLT',
            'EudoraWeb' => 'EudoraWeb',
            'shadowfox' => 'ShadowFox',
            'Swiftfox' => 'Swiftfox',
            'Uzbl' => 'Uzbl',
            'UCBrowser' => 'UCBrowser',
            'Kindle' => 'Kindle',
            'wOSBrowser' => 'wOSBrowser',
            'Epiphany' => 'Epiphany',
            'SeaMonkey' => 'SeaMonkey',
            'Avant Browser' => 'Avant Browser',
            'Firefox' => 'Firefox',
            'Chrome' => 'Google Chrome',
            'MSIE' => 'Internet Explorer',
            'Internet Explorer' => 'Internet Explorer',
            'Safari' => 'Safari',
            'Mozilla' => 'Firefox'
        ];

        foreach ($basicBrowser as $key => $browser) {
            if (false !== stripos($user_agent, $key)) {
                $resultBrowser = $browser;
                break;
            }
        }

        return $resultBrowser;
    }

    protected function detectPlatform()
    {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $resultPlatform = '';
        $basicPlatform = [
            'windows' => 'Windows',
            'iPad' => 'iPad',
            'iPod' => 'iPod',
            'iPhone' => 'iPhone',
            'mac' => 'Apple',
            'Android' => 'Android',
            'linux' => 'Linux',
            'Nokia' => 'Nokia',
            'BlackBerry' => 'BlackBerry',
            'FreeBSD' => 'FreeBSD',
            'OpenBSD' => 'OpenBSD',
            'NetBSD' => 'NetBSD',
            'UNIX' => 'UNIX',
            'DragonFly' => 'DragonFlyBSD',
            'OpenSolaris' => 'OpenSolaris',
            'SunOS' => 'SunOS',
            'OS\/2' => 'OS/2',
            'BeOS' => 'BeOS',
            'win' => 'Windows',
            'Dillo' => 'Linux',
            'PalmOS' => 'PalmOS',
            'RebelMouse' => 'RebelMouse'
        ];

        foreach ($basicPlatform as $key => $platform) {
            if (false !== stripos($user_agent, $key)) {
                $resultPlatform = $platform;
                break;
            }
        }

        return $resultPlatform;
    }

    protected function detectDevice()
    {
        $detect = new \Mobile_Detect();

        if ($detect->isMobile() && $detect->isiOS()) {
            $device = 'mobile';
        } elseif ($detect->isMobile() && $detect->isAndroidOS()) {
            $device = 'mobile';
        } elseif ($detect->isTablet()) {
            $device = 'tablet';
        } elseif ($detect->isIpad()) {
            $device = 'tablet';
        } else {
            $device = 'computer';
        }

        return $device;
    }
}
