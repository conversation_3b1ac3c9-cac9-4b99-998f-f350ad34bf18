<?php

namespace App\Admin\Traits;


use App\Entity\Course;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;

trait EntityAccessTrait
{

    use ResponseTrait;

    public abstract function checkEntityAccess ();


    public function detail (AdminContext $context)
    {
        if (!$this->checkEntityAccess())
        {
            return $this->redirectToIndex();
        }

        return parent::detail($context);
    }


    public function edit (AdminContext $context)
    {
        if (!$this->checkEntityAccess())
        {
            return $this->redirectToIndex();
        }

        return parent::edit($context);
    }


    public function delete (AdminContext $context)
    {
        if (!$this->checkEntityAccess())
        {
            return $this->redirectToIndex();
        }

        return parent::delete($context);
    }
}