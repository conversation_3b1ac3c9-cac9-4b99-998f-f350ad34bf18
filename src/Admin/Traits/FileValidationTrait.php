<?php

namespace App\Admin\Traits;

use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Add other validations based on requirements
 */
trait FileValidationTrait
{
    public function isPdfFile(UploadedFile $file): bool
    {
        if ('application/pdf' !== $file->getMimeType()) return false;
        $originalName = explode('.', $file->getClientOriginalName());
        if (count($originalName) < 2) return false;
        if ($originalName[count($originalName) - 1] !== 'pdf') return false;
        return true;
    }

    public function isImage(UploadedFile $file): bool {
        $mimeType = $file->getMimeType();
        if (strpos($mimeType, 'image') === FALSE) return false;
        return true;
    }
}
