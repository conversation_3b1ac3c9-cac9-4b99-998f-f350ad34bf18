<?php

namespace App\Service\Annoucement\Email;

use App\Service\SettingsService;
use PhpParser\Builder\Param;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;



class BaseEmailService
{
	protected $mailer;
	protected $settings;
	

	public function __construct(MailerInterface $mailer,  SettingsService $settings)
	{
		$this->mailer = $mailer;
		$this->settings = $settings;
		
	}

	public function sendEmail($to, $subject, $template, $context = [], $files = [])
	{
		$from = [$this->settings->get('app.fromEmail'), $this->settings->get('app.fromName')];

		$email = (new TemplatedEmail())
			->from(new Address(...$from))
			->to(new Address($to))
			->subject($subject)
			->htmlTemplate($template)
			->context($context);

		if (count($files) > 0) {
			foreach ($files as $file) {
				$email->embedFromPath($file['path'], $file['name']);
			}
		}

		$this->mailer->send($email);
	}
}
