<?php

namespace App\Service\Annoucement\Email;

use App\Entity\RecoveryCode;
use App\Entity\User;
use App\Service\Annoucement\Email\BaseEmailService as EmailBaseEmailService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Mailer\MailerInterface;


class AnnouncementUserEmailService extends EmailBaseEmailService
{    
    private $em;
    private $translator;   

    public function __construct(
        MailerInterface $mailer,
        SettingsService $settings,
        ParameterBagInterface $params,        
        EntityManagerInterface $em,
        TranslatorInterface $translator        
    ) {
        parent::__construct($mailer, $settings, $params);      
        $this->em = $em;
        $this->translator = $translator;       
    }

    public function sendNotificationForRecoverPassword(User $user)
    {
        $recoveryData = $this->generateRecoveryCode($user);
    
        $subject = $this->translator->trans('email.template_email.recover_password', [], 'email', $recoveryData['locale']);
        $htmTemplate = 'template_email/recover-password-user.html.twig';
    
        $content = [
            'expiration_date' => new \DateTime('+7 days'),
            'user' => $recoveryData['user'],
            'codigo' => $recoveryData['codigo'],
            'locale' => $recoveryData['locale'],
        ];
    
        $this->sendEmail(
            $recoveryData['email'],
            $subject,
            $htmTemplate,
            $content
        );
    }
    
    private function generateRecoveryCode(User $user)
    {
        $code = date("His");
        $email = $user->getEmail();
        $locale = $user->getLocale() ?? $this->settings->get('app.defaultLanguage');
    
        $recovery = $this->createRecoveryCodeEntity($user, $code, $email);
    
        try {
            $this->em->persist($recovery);
            $this->em->flush();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    
        return [
            'codigo' => md5($code),
            'email' => $email,
            'locale' => $locale,
            'user' => $user,
        ];
    }
    
    private function createRecoveryCodeEntity(User $user, string $code, string $email)
    {
        $recovery = new RecoveryCode();
        $recovery->setUser($user);
        $recovery->setEmail($email);
        $recovery->setDateRecovery(new \DateTime());
        $recovery->setCodeActivation(md5($code));
        $recovery->setState(0);
    
        return $recovery;
    }
    
}
