<?php

namespace App\Service\Annoucement\CronJob;

use App\Repository\ClassroomvirtualRepository;
use App\Service\VirtualClass\PlugNmeetService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class  ActiveVirtualClassCronService
{
    private  $em;
    private $logger;
    private $classroomvirtualRepository;
    private $plugNmeetService;

    public function __construct(
        EntityManagerInterface $em,
        LoggerInterface $logger,
        ClassroomvirtualRepository $classroomvirtualRepository,
        PlugNmeetService $plugNmeetService
    ) {
        $this->em = $em;
        $this->logger = $logger;
        $this->classroomvirtualRepository = $classroomvirtualRepository;
        $this->plugNmeetService = $plugNmeetService;
    }

    public function execute()
    {
        try {
            $result= $this->setActiveVirtualClass();
            return $result;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }


    private function setActiveVirtualClass()
    {//activa las reuniones de PlugNmeet a realizarse dentro de [0, 20] minutos
        try {
            $result = new \stdClass();

            $dateFinish = new \DateTime('now');
            date_add($dateFinish,date_interval_create_from_date_string("20 minutes"));
            $dateInitial = new \DateTime('now');

            $classroomVirtuals = $this->classroomvirtualRepository->getClassroomToActiveRangeDate($dateInitial, $dateFinish);

            foreach ($classroomVirtuals as $classroomVirtual) {
                $parameters = ['idClassroomvirtual' => $classroomVirtual->getId()];

                $result->classroomVirtual_id = $classroomVirtual->getId();
                $result->result = $this->plugNmeetService->activeRoom($parameters);
            }
//date_format($dateInitial, "Y-m-d H:i");//            
            return   json_encode($result);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
