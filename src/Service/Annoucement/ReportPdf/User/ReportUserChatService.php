<?php

namespace App\Service\Annoucement\ReportPdf\User;

use App\Entity\Announcement;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\ChatChannel;
use App\Entity\ChatMessage;
use App\Entity\User;

class ReportUserChatService extends ReportBaseService
{
    public function generatePdf(
        ChatChannel $directChannel,
        User $tutor,
        User $user,
        AnnouncementUser $announcementUser,
        $fullPath = null
    ) {


        $dataReport = array_merge(
            $this->headReportUser($announcementUser),
            $this->getUserChat($announcementUser)
        );

        $mpdf = $this->generate(
            'fundae/report_pdf_by_user/chat.html.twig',
            $dataReport
        );

        if (!empty($fullPath)) $mpdf->Output($fullPath, 'F');

        return $mpdf;
    }

    public function getUserChat(AnnouncementUser $announcementUser ) {
        $announcement = $announcementUser->getAnnouncement();
        $directChannel = $this->em->getRepository(Announcement::class)->getDirectChatChannel($announcement);

        $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(['announcement' => $announcement, 'announcementGroup' => $announcementUser->getAnnouncementGroup()]);
     
        if(!$announcementTutor) {
            return [
                'user' => $announcementUser->getUser(),
                'tutor' => null,
                'messages' => []
            ];
        }
        
        $channel = $this->em->getRepository(ChatChannel::class)->getDirectChatChannel(
            $directChannel,
            $announcementUser->getUser(),
            $announcementTutor->getTutor(),
            false
        );

        $messages = $this->em->getRepository(ChatMessage::class)->createQueryBuilder('cm')
            ->select('cm.id', 'cm.message', 'cm.seen', 'cm.createdAt')
            ->addSelect('u.id as userId', 'u.firstName')
            ->join('cm.user', 'u')
            ->where('cm.channel =:channel')
            ->setParameter('channel', $channel)
            ->getQuery()
            ->getResult();

        return [
            'user' => $announcementUser->getUser(),
            'tutor' => $announcementTutor->getTutor(),
            'messages' => $messages
        ];
    }
}
