<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Report\Generators;

use App\Service\Annoucement\Report\AnnouncementContainer;
use App\Service\Annoucement\Report\AnnouncementReportConstants;
use App\Service\Annoucement\Report\AnnouncementReportDataService;
use App\Utils\SpreadsheetUtil;
use Psr\Log\LoggerInterface;

class GroupInfoReportGenerator
{
    private AnnouncementReportDataService $announcementReportDataService;
    private AnnouncementReportConstants $announcementReportConstants;
    private LoggerInterface $logger;

    public function __construct(
        AnnouncementReportDataService $announcementReportDataService,
        AnnouncementReportConstants $announcementReportConstants,
        LoggerInterface $logger
    ) {
        $this->announcementReportDataService = $announcementReportDataService;
        $this->announcementReportConstants = $announcementReportConstants;
        $this->logger = $logger;
    }

    public function generate(
        AnnouncementContainer $announcementContainer,
        string $announcementDir,
        callable $initSheetCallback,
        bool $isOnlineMode
    ): void {
        $headersInfo = $isOnlineMode ?
            AnnouncementReportConstants::ONLINE_GROUP_INFO_HEADERS['info'] :
            AnnouncementReportConstants::ON_SITE_GROUP_INFO_HEADERS['info'];

        $headersUsers = $isOnlineMode ?
            AnnouncementReportConstants::ONLINE_GROUP_INFO_HEADERS['users'] :
            AnnouncementReportConstants::ON_SITE_GROUP_INFO_HEADERS['users'];

        $headersSessions = $isOnlineMode ?
            [] :
            AnnouncementReportConstants::ON_SITE_GROUP_INFO_HEADERS['sessions'];

        $report = new SpreadsheetUtil('Reporte del grupo', 'Info');

        $initSheetCallback($report, 'Info', $headersInfo);
        $dataInfo = $this->announcementReportDataService->groupInfoData(
            $announcementContainer->announcement,
            $announcementContainer->announcementGroups,
            !$isOnlineMode,
            !$isOnlineMode
        );
        $report->fromArray($dataInfo, '--', 'A2')->alignAllLeft();

        $initSheetCallback($report, 'Personas', $headersUsers);
        $dataUsers = $this->announcementReportDataService->groupInfoUsersData(
            $announcementContainer->announcement,
            $announcementContainer->announcementGroups,
            !$isOnlineMode,
            !$isOnlineMode
        );
        $report->fromArray($dataUsers, '--', 'A2')->alignAllLeft();

        if (!$isOnlineMode && !empty($headersSessions)) {
            $initSheetCallback($report, 'Sesiones', $headersSessions);
            $dataSessions = $this->announcementReportDataService->groupInfoSessionsDataRaw(
                $announcementContainer->announcement,
                $announcementContainer->announcementGroups
            );

            $filteredSessions = array_map(function ($row) {
                $copy = $row;
                unset($copy['_studentAssistanceRaw']);
                unset($copy['_tutorAssistanceRaw']);
    
                return $copy;
            }, $dataSessions);
            $report->fromArray($filteredSessions, '--', 'A2')->alignAllLeft();
        }

        $report->saveReport($announcementDir);
    }
}
