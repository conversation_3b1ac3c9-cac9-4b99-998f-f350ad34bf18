<?php

namespace App\Service\Traits\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementTemporalization;


trait AnnouncementTemporalizationTrait
{

    private function hasMinimunTimeChapter(AnnouncementTemporalization $announcementTemporalization = null,  $hasTemporalization)
    {

        if (!$announcementTemporalization) return false;
        if (!$hasTemporalization) return false;

        return $announcementTemporalization->getMinimumTime() > 0;
    }

    private function getStartDateTemporalization(Announcement $announcement, AnnouncementTemporalization $announcementTemporalization = null)
    {

        if (!$announcementTemporalization) return $announcement->getStartAt();

        if ($announcementTemporalization->getStartedAt() >= $announcement->getStartAt() && $announcementTemporalization->getStartedAt() < $announcement->getFinishAt()) {
            return $announcementTemporalization->getStartedAt();
        }

        return $announcement->getStartAt();
    }

    private function getFinishDateTemporalization(Announcement $announcement, AnnouncementTemporalization $announcementTemporalization = null)
    {
        if (!$announcementTemporalization) return $announcement->getFinishAt();

        if ($announcementTemporalization->getFinishedAt() > $announcement->getStartAt() && $announcementTemporalization->getFinishedAt() <= $announcement->getFinishAt()) {
            return $announcementTemporalization->getFinishedAt();
        }

        return $announcement->getFinishAt();
    }
}
