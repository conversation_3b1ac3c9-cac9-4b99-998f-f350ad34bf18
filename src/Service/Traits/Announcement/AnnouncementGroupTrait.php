<?php

declare(strict_types=1);

namespace App\Service\Traits\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Service\Annoucement\ReportPdf\User\ReportBaseService;

trait AnnouncementGroupTrait
{
    use TutorTrait;
    use AnnouncementUserTrait;
    use UserTrait;

    public function getInformationGroup(Announcement $announcement, $idGroup, string $source = ReportBaseService::SOURCE_REQUEST)
    {
        $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->findOneBy(['announcement' => $announcement, 'id' => $idGroup]);
        $typeMoney = $announcementGroup ? $announcementGroup->getTypeMoney() : null;

        return [
            'id' => $announcementGroup ? $announcementGroup->getId() : 1,
            'companyProfile' => $announcementGroup ? $announcementGroup->getCompanyProfile() : '--',
            'code' => $announcementGroup ? $announcementGroup->getCode() : '--',
            'companyCif' => $announcementGroup ? $announcementGroup->getCompanyCif() : '--',
            'denomination' => $announcementGroup ? $announcementGroup->getDenomination() : '--',
            'numExpendient' => $announcementGroup ? $announcementGroup->getFileNumber() : '--',
            'numSessions' => $announcementGroup ? $announcementGroup->getNumSessions() : '0',
            'place' => $announcementGroup ? $announcementGroup->getPlace() : '--',
            'tutor' => $this->getInfomationTutor($announcement, $idGroup, $source),
            'report' => $this->em->getRepository(AnnouncementGroup::class)->tutorZipReportStatus($idGroup),
            'cost' => $announcementGroup ? $announcementGroup->getCost() : 0,
            'typeCost' => $typeMoney ? [
                'id' => $typeMoney->getId(),
                'name' => $typeMoney->getName(),
                'symbol' => $typeMoney->getSymbol(),
            ] : [],
        ];
    }

    private function getUsersByGroup(Announcement $announcement, bool $includeTutorInfo = true): array
    {
        $announcementsUser = $this->getUsersForAnnouncement($announcement);

        $directChatChannel = $this->em->getRepository(Announcement::class)->getDirectChatChannel($announcement);

        $usersByGroup = [];
        foreach ($announcementsUser as $user) {
            $group = $user->getAnnouncementGroup() ? $user->getAnnouncementGroup()->getId() : 1;
            $userInfo = $this->getInformationUser($user->getUser());
            $userInfo['groupId'] = $group;

            if ($includeTutorInfo) {
                $extraInfo = $this->getInformationAnnouncementUser($user, $directChatChannel);
                $userCombinedInfo = array_merge($userInfo, $extraInfo);
            } else {
                $userCombinedInfo = $userInfo;
            }

            $usersByGroup[$group][] = $userCombinedInfo;
        }

        return $usersByGroup;
    }

    private function getUsersForAnnouncement(Announcement $announcement)
    {
        $user = $this->security->getUser();

        if ($this->shouldFilterByTutor($user)) {
            return $this->getUsersForTutor($announcement, $user);
        }

        return $this->getAllUsersForAnnouncement($announcement);
    }

    private function shouldFilterByTutor($user)
    {
        return $user->isTutor() && !($user->isAdmin() || $user->isManager());
    }

    private function getUsersForTutor(Announcement $announcement, $user)
    {
        $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)
            ->findOneBy(['announcement' => $announcement, 'tutor' => $user]);

        if ($announcementTutor && $announcementTutor->getAnnouncementGroup()) {
            $announcementGroup = $announcementTutor->getAnnouncementGroup();

            return $this->getUsersByAnnouncementAndGroup($announcement);
        }

        return $this->getUsersByAnnouncement($announcement);
    }

    private function getAllUsersForAnnouncement(Announcement $announcement)
    {
        return $this->getUsersByAnnouncement($announcement);
    }

    private function getUsersByAnnouncement(Announcement $announcement)
    {
        return $this->em->getRepository(AnnouncementUser::class)
            ->getUsersByAnnouncement($announcement);
    }

    private function getUsersByAnnouncementAndGroup(Announcement $announcement)
    {
        $user = $this->security->getUser();

        $announcementGroups = $this->shouldFilterByTutor($user) ?
            $this->announcementGroupRepository->getGroupsByTutor($announcement, $user) :
            $this->em->getRepository(AnnouncementGroup::class)->findBy(['announcement' => $announcement]);

        $users = []; // Almacenar usuarios de todos los grupos

        foreach ($announcementGroups as $group) {
            $groupUsers = $this->em->getRepository(AnnouncementUser::class)
                ->findBy(['announcement' => $announcement, 'announcementGroup' => $group]);

            // Agregar usuarios al array principal
            $users = array_merge($users, $groupUsers);
        }

        // Si no se encontraron usuarios específicos para el grupo, obtener todos los usuarios del grupo dado
        if (empty($users)) {
            $groupUsers = $this->em->getRepository(AnnouncementUser::class)
                ->findBy(['announcement' => $announcement]);

            $users = array_merge($users, $groupUsers);
        }

        return $users;
    }
}
