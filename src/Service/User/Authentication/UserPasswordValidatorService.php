<?php

declare(strict_types=1);

namespace App\Service\User\Authentication;

use App\Service\SettingsService;
use Symfony\Contracts\Translation\TranslatorInterface;

class UserPasswordValidatorService
{
    private SettingsService $settings;
    private TranslatorInterface $translator;

    public function __construct(SettingsService $settings, TranslatorInterface $translator)
    {
        $this->settings = $settings;
        $this->translator = $translator;
    }

    public function hasMinimumSize(string $password): bool
    {
        return \strlen($password) >= $this->settings->get('password.minimum');
    }

    public function hasUppercase(string $password): bool
    {
        if (!$this->settings->get('password.uppercase')) {
            return true;
        }

        return (bool) preg_match('@[A-Z]@', $password);
    }

    public function hasLowercase(string $password): bool
    {
        if (!$this->settings->get('password.lowercase')) {
            return true;
        }

        return (bool) preg_match('@[a-z]@', $password);
    }

    public function hasNumber(string $password): bool
    {
        if (!$this->settings->get('password.number')) {
            return true;
        }

        return (bool) preg_match('@[0-9]@', $password);
    }

    public function hasSpecialCharacters(string $password): bool
    {
        if (!$this->settings->get('password.special_characters')) {
            return true;
        }

        return (bool) preg_match('@\W@', $password);
    }

    public function noConsecutiveChars(string $password): bool
    {
        if (!$this->settings->get('password.disable_3_consecutive_chars')) {
            return true;
        }
        $size = \strlen($password);
        if ($size < 1) {
            return false;
        }
        $char = $password[0];
        $number = 1;
        for ($i = 1; $i < $size; ++$i) {
            if ($char === $password[$i]) {
                ++$number;
            } else {
                $char = $password[$i];
                $number = 1;
            }
            if ($number >= 3) {
                return false;
            }
        }

        return true;
    }

    /**
     * @return array|true
     */
    public function isPasswordValid(string $password, ?string $pLocale = null) {
        if (!$this->settings->get('password.policy.enabled')) return true;// No validation required

        $locale = empty($pLocale) ? $this->settings->get('app.defaultLanguage') : $pLocale;
        $failed = false;
        $data = [];
        if (!self::hasMinimumSize($password)) {
            $failed = true;
            $data[] = \sprintf($this->translator->trans('password.minimum', [], 'messages', $locale), $this->settings->get('password.minimum'));
        }

        if (!self::hasUppercase($password)) {
            $failed = true;
            $data[] = $this->translator->trans('password.uppercase', [], 'messages', $locale);
        }

        if (!self::hasLowercase($password)) {
            $failed = true;
            $data[] = $this->translator->trans('password.lowercase', [], 'messages', $locale);
        }

        if (!self::hasNumber($password)) {
            $failed = true;
            $data[] = $this->translator->trans('password.number', [], 'messages', $locale);
        }

        if (!self::hasSpecialCharacters($password)) {
            $failed = true;
            $data[] = $this->translator->trans('password.special_characters', [], 'messages', $locale);
        }

        if (!self::noConsecutiveChars($password)) {
            $failed = true;
            $data[] = $this->translator->trans('password.disable_3_consecutive_chars', [], 'messages', $locale);
        }

        if ($failed) {
            return $data;
        }

        return true;
    }
}
