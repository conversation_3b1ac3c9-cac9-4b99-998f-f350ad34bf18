<?php

declare(strict_types=1);

namespace App\Service\User\Token;

use App\Entity\UserToken;
use App\Service\User\Authentication\MyDigiCoopSSO;
use App\Service\User\Authentication\StarTeam;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Security\Core\Exception\AuthenticationException;

class TokenLoginService
{
    private $starTeam;
    private $myDigiCoop;
    private $logger;
    private $parameters;
    private $em;

    /**
     * TokenLoginService constructor.
     */
    public function __construct(StarTeam $starTeam, MyDigiCoopSSO $myDigiCoop, LoggerInterface $logger, ParameterBagInterface $parameters, EntityManagerInterface $em)
    {
        $this->starTeam = $starTeam;
        $this->myDigiCoop = $myDigiCoop;
        $this->logger = $logger;
        $this->parameters = $parameters;
        $this->em = $em;
    }

    public function checkToken(string $token)
    {
        $userToken = $this->em->getRepository(UserToken::class)->findOneBy(['token' => $token]);

        if ($userToken) {
            if (!$userToken->getIsValid()) {
                throw new AuthenticationException('Invalid Token');
            }// return new JsonResponse(['error' => true, 'data' => 'Invalid Token'], 401);

            $user = $userToken->getUser();

            $userToken->setUsed(true)->setUsedAt(new \DateTimeImmutable());
            switch ($userToken->getType()) {
                case UserToken::TYPE_OTP:
                    $this->em->remove($userToken);
                    break;
                case UserToken::TYPE_INSPECTOR_IMPERSONATE_USER:
                    // If an inspector is impersonating a user, return an array with the extra info
                    $user = $userToken->getUser();
                    $data = $userToken->getExtra();
                    $this->em->flush();

                    return [
                        'user' => $user,
                        'payload' => $data
                    ];
                case UserToken::TYPE_ANNOUNCEMENT_INSPECTOR:
                    throw new AuthenticationException('Token not allowed');
            }

            $this->em->flush($userToken);

            return $user;
        }

        $provider = ($this->parameters->get('sso.provider')) ? $this->parameters->get('sso.provider') : null;

        $this->logger->debug($provider);

        switch ($provider) {
            case 'MyDigiCoop':
                $user = $this->myDigiCoop->checkToken($token);
                break;
            case 'StarTeam':
                $user = $this->starTeam->checkToken($token);
                break;
            default:
                $user = null;
        }

        return $user;
    }
}
