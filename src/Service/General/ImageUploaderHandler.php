<?php

namespace App\Service\General;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpKernel\KernelInterface;

class ImageUploaderHandler
{
    private string $projectDir;
    public function __construct(
        KernelInterface $kernel
    )
    {
        $this->projectDir = $kernel->getProjectDir();
    }


    /**
     * @param UploadedFile $uploadedFile
     * @param string $pathRelativeToPublic e.g /uploads/images/
     * @param string|null $filename filename without extension, if not specified, a random name will be generated
     * @param int $maxWidth
     * @param int $maxHeight
     * @return string $pathRelativeToPublic / $filename.jpeg
     * @throws \Exception
     */
    public function handleImage(
        UploadedFile $uploadedFile,
        string $pathRelativeToPublic,
        string $filename = null,
        int $maxWidth = 1440,
        int $maxHeight = 1440
    ): string
    {
        $directory = $this->projectDir . '/public';

        if (substr($pathRelativeToPublic, 0, 1) !== '/') $directory .= '/' . $pathRelativeToPublic;
        if (substr($pathRelativeToPublic, -1) !== '/') $pathRelativeToPublic .= '/';

        if (empty($filename)) {
            $filename = bin2hex(random_bytes(10)) . (new \DateTime())->getTimestamp();
        }
        $filePath = $pathRelativeToPublic . $filename . '.jpeg';
        $directory .= $filePath;

        if(!is_dir(dirname($directory))) mkdir(dirname($directory), 0777, true);

        list($srcWidth, $srcHeight) = getimagesize($uploadedFile->getPathname());
        $src = imagecreatefromstring($uploadedFile->getContent());
        $newWidth = min($srcWidth, $maxWidth);

        $newHeight = ($srcHeight / $srcWidth) * $newWidth;// New Height based on aspect ratio

        $dst = imagecreatetruecolor($newWidth, $newHeight);
        imagecopyresampled($dst, $src, 0, 0, 0, 0, $newWidth, $newHeight, $srcWidth, $srcHeight);

        imagedestroy($src); // Free memory
        imagejpeg($dst, $directory, 90);
        imagedestroy($dst); // Free memory

        return $filename . '.jpeg';
    }
}
