<?php

declare(strict_types=1);

namespace App\Service\Api;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseSection;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\ItineraryRepository;
use App\Repository\NpsRepository;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\Nps\NpsService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class ApiCourseService extends AbstractBaseService
{
    private $announcementUserService;
    private $itineraryRepository;
    private $courseRepository;
    private $apiAnnouncementService;
    private $announcementConfigurationsService;
    private $announcementRepository;
    private $npsRepository;
    private $npsService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        AnnouncementUserService $announcementUserService,
        ItineraryRepository $itineraryRepository,
        CourseRepository $courseRepository,
        ApiAnnouncementService $apiAnnouncementService,
        AnnouncementConfigurationsService $announcementConfigurationsService,
        AnnouncementRepository $announcementRepository,
        NpsRepository $npsRepository,
        NpsService $npsService
    ) {
        parent::__construct($em, $settings);
        $this->announcementUserService = $announcementUserService;
        $this->itineraryRepository = $itineraryRepository;
        $this->courseRepository = $courseRepository;
        $this->apiAnnouncementService = $apiAnnouncementService;
        $this->announcementConfigurationsService = $announcementConfigurationsService;
        $this->announcementRepository = $announcementRepository;
        $this->npsRepository = $npsRepository;
        $this->npsService = $npsService;
    }

    public function courseToSend(User $user, Course $course)
    {
        if ($course->hasACompleteChapter()) {
            $locale = $user->getLocaleCampus() ?? $this->settings->get('app.defaultLanguage');
            $courseToSend = $this->getTranslatedCourse($course, $locale);

            $dataCommonCourse = $this->getDataCommonCourse($user, $courseToSend);
            $dataCourse = $this->getDataCourse($user, $courseToSend);

            return array_merge($dataCommonCourse, $dataCourse);
        }

        return [];
    }

    public function courseToSendAnnouncement(
        User $user,
        Announcement $announcement,
        ?bool $isDiplomaReport = false
    ): array {
        $course = $announcement->getCourse();

        $dataCommonCourse = $this->getDataCommonCourse($user, $course);
        $dataAnnouncement = $this->getDataAnnouncement($user, $announcement, $isDiplomaReport);

        return array_merge($dataCommonCourse, $dataAnnouncement);
    }

    private function getDataCommonCourse(User $user, Course $course)
    {
        $hasItineraries = $this->checkIfUserHasItineraries($user, $course);

        return [
            'id' => $course->getId(),
            'code' => $course->getCode(),
            'name' => $course->getName(),
            'description' => $course->getDescription(),
            'image' => $course->getImage(),
            'thumbnail' => $course->getThumbnailUrl(),
            'thumbnails' => $course->getThumbnailUrls(),
            'tags' => $course->getTags(),
            'segments' => $course->getCourseSegments(),
            'locale' => $course->getLocale(),
            'new' => $course->getIsNew(),
            'itinerary' => $hasItineraries ?? false,
            'stars' => $this->npsService->statCourse($course),
        ];
    }

    private function getDataAnnouncement(User $user, Announcement $announcement, ?bool $isDiplomaReport = false)
    {
        $course = $announcement->getCourse();
        $idAnnouncement = $announcement->getId();
        $userCourse = $this->getUserCourse($user, $course, $idAnnouncement);

        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'user' => $user,
            'announcement' => $announcement,
        ]);

        $dataUserCourse = $this->getDataCommonUserCourse($userCourse);

        $dataUserCourse['finished'] = $announcementUser ? $this->isCompletedCourse($announcementUser) : false;
        $dataUserCourse['finishAt'] = $announcementUser ? $announcementUser->getDateApproved() : null;
        $dataUserCourse['state'] = (\is_null($announcementUser) || \is_null($announcementUser->getDateApproved())) ? false : true; // Muestra alerta de encuesta
        $dataUserCourse['valued'] = $announcementUser ? $announcementUser->getValuedCourseAt() : false;

        $dataAnnouncementUser = [
            'active' => $announcement ? $announcement->isStarted() : $course->getActive(),
            'progress' => $announcementUser ? $this->announcementUserService->getProgressTotal($announcementUser) : 0,
            'isAnnouncement' => true,
            'extraAnnouncement' => $this->apiAnnouncementService->getDataExtraAnnouncement($announcement, $user, $isDiplomaReport),
            'hasSurvey' => $this->announcementConfigurationsService->hasSurvey($announcement),
        ];

        if (TypeCourse::TYPE_PRESENCIAL == $announcement->getCourse()->getTypeCourse()->getId() || TypeCourse::TYPE_AULA_VIRTUAL == $announcement->getCourse()->getTypeCourse()->getId()) {
            if ($this->announcementUserService->getProgressTotalAssistance($announcementUser) > 0) {
                $dataUserCourse['startAt'] = $announcement->getStartAt();
            }
        }

        $response = array_merge($dataAnnouncementUser, $dataUserCourse);

        return $response;
    }

    private function isCompletedCourse(AnnouncementUser $announcementUser)
    {
        $announcement = $announcementUser->getAnnouncement();
        $hasSurvey = $this->announcementConfigurationsService->hasSurvey($announcement);

        if (!$hasSurvey && $announcementUser->getDateApproved()) {
            return true;
        }

        return $hasSurvey && $announcementUser->getDateApproved() && $announcementUser->getValuedCourseAt();
    }

    private function getDataCourse(User $user, Course $course)
    {
        $userCourse = $this->getUserCourse($user, $course, null);

        $dataUserCourse = $this->getDataCommonUserCourse($userCourse);
        $dataCourse = [
            'active' => $course->getActive(),
            'progress' => !\is_null($userCourse) ? $userCourse->getProgress() : 0,
        ];

        return array_merge($dataCourse, $dataUserCourse);
    }

    private function getDataCommonUserCourse(?UserCourse $userCourse = null)
    {
        return [
            'finished' => !\is_null($userCourse) ? $userCourse->getUserCourseIfIsFinished() : false,
            'valued' => (\is_null($userCourse) || \is_null($userCourse->getValuedAt())) ? false : true,
            'state' => (\is_null($userCourse) || \is_null($userCourse->getFinishedAt())) ? false : true,
            'startAt' => !\is_null($userCourse) ? $userCourse->getStartedAt() : null,
            'finishAt' => !\is_null($userCourse) ? $userCourse->getFinishedAt() : null,
            'courseCurrentlyViewed' => !\is_null($userCourse) ? $userCourse->getUserCourseIfIsCurrentlyViewed() : false,
        ];
    }

    private function getTranslatedCourse(Course $course, $locale)
    {
        if ($this->settings->get('app.multilingual') && $locale != $course->getLocale()) {
            $translation = $course->getTranslationByLocale($locale);

            if ($translation && $translation->getActive()) {
                return $translation;
            }
        }

        return $course;
    }

    private function getUserCourse(User $user, Course $course, $idAnnouncement)
    {
        return $this->em->getRepository(UserCourse::class)->findOneBy([
            'user' => $user,
            'course' => $course,
            'announcement' => $idAnnouncement,
        ]);
    }

    private function checkIfUserHasItineraries(User $user, Course $course)
    {
        if ($this->settings->get('app.use_itinerary')) {
            $itineraries = $this->itineraryRepository->findByUserAndCourse($user, $course);
            if (!$itineraries) {
                foreach ($course->getItineraryCourses() as $itineraryCourse) {
                    if ($user->hasItineraryByFilters($itineraryCourse->getItinerary())) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public function getCoursesUserFilterAccess(User $user)
    {
        $categories = $this->em->getRepository(CourseCategory::class)->findBy([], ['sort' => 'ASC']);

        $coursesFilter = [];

        foreach ($categories as $category) {
            $courses = $this->searchCoursesByUserAndCategory($user, $category);

            if (!$courses) {
                continue;
            }

            $coursesFilter[] = $this->getCoursesInCategory($user, $courses);
        }

        return array_merge(...$coursesFilter);
    }

    private function getCoursesInCategory(User $user, array $courses)
    {
        $coursesToSend = [];

        foreach ($courses as $course) {
            $coursesToSend[] = $this->courseToSend($user, $course);
        }

        return $coursesToSend;
    }

    /**
     * @return Course[]
     */
    public function searchCoursesByUserAndCategory(User $user, CourseCategory $category, $open = true)
    {
        $openCampus = $open && $user->getOpen();

        if ($this->settings->get('app.user.useFilters')) {
            $preCourses = $this->courseRepository->findByFilters($user->getFilter(), $category, true, $openCampus);

            $courses = [];
            foreach ($preCourses as $course) {
                if ($course->checkUserFilterAccess($user)) {
                    if (!\in_array($course, $courses)) {
                        array_push($courses, $course);
                    }
                }
            }

            return $courses;
        } elseif ($user->getExtra()) {
            return $this->courseRepository->findByProfessionalCategoryAndCategory($user->getExtra()->getCategory(), $category, $this->settings->get('app.showDeactivatedCourses'));
        } else {
            return $this->courseRepository->findOtherProfessionalCategoryAndCategory($category, [], $this->settings->get('app.showDeactivatedCourses'));
        }
    }

    /**
     * @return Course[]
     */
    public function getSectionCourses(User $user, CourseCategory $category, CourseSection $courseSection): array
    {
        $coursesByCampus = $this->getCoursesByCampus($user, $category, $courseSection);
        $coursesByFilter = $this->getCoursesByFilter($user, $category, $courseSection);

        return array_merge($coursesByFilter, $coursesByCampus);
    }

    private function getCoursesByCampus(User $user, CourseCategory $category, CourseSection $courseSection): array
    {
        $coursesByCampus = [];
        $showDeactivatedCourses = $this->settings->get('app.showDeactivatedCourses');

        if ($user->getOpen() && $courseSection->isOpenCampus()) {
            $coursesOpenCampus = $this->courseRepository->courseSectionOpenCampus($category, $showDeactivatedCourses);
            foreach ($coursesOpenCampus as $course) {
                if ($this->shouldIncludeCourse($user, $course) && $course->hasACompleteChapter()) {
                    $coursesByCampus[] = $course;
                }
            }
        }

        return $coursesByCampus;
    }

    private function getCoursesByFilter(User $user, CourseCategory $category, CourseSection $courseSection): array
    {
        $coursesByFilter = [];
        $showDeactivatedCourses = $this->settings->get('app.showDeactivatedCourses');

        if ($this->settings->get('app.user.useFilters') && $courseSection->isCourseByFilter()) {
            $courses = $this->courseRepository->findByFilters($user->getFilter(), $category, $showDeactivatedCourses);
            foreach ($courses as $course) {
                if ($course->checkUserFilterAccess($user) && $this->shouldIncludeCourse($user, $course) && $course->hasACompleteChapter()) {
                    $coursesByFilter[] = $course;
                }
            }
        }

        return $coursesByFilter;
    }

    private function shouldIncludeCourse(User $user, Course $course): bool
    {
        $announcement = $this->announcementRepository->findAnnouncementUserNotified($course, $user);
        $status = $announcement ? $announcement->getStatus() : null;

        return Announcement::STATUS_ACTIVE !== $status;
    }

    public function getCoursesUsersOpenVisibles(User $user, CourseCategory $category, string $locale): array
    {
        $categoryData = [
            'id' => $category->getId(),
            'name' => $this->getCategoryName($category, $locale),
            'featured' => false,
            'courses' => [],
        ];

        $courses = $this->courseRepository->courseSectionOpenCampus($category, true);

        foreach ($courses as $course) {
            $courseToSend = $this->courseToSend($user, $course);

            if (!empty($courseToSend) && $courseToSend['locale'] == $locale) {
                $categoryData['courses'][] = $courseToSend;
            }
        }

        return $categoryData;
    }

    public function getCategoryName(CourseCategory $category, string $locale): string
    {
        $categoryTranslation = $category->getTranslations()->get($locale);

        return $categoryTranslation ? $categoryTranslation->getName() : $category->getName();
    }
}
