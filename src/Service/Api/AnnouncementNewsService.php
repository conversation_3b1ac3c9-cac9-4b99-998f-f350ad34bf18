<?php

declare(strict_types=1);

namespace App\Service\Api;

use App\Entity\User;
use App\Repository\AnnouncementRepository;
use App\Service\Geolocation\GeolocationService;
use App\Service\SettingsService;
use App\Utils\DateCalculationUtils;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Translation\TranslatorInterface;

class AnnouncementNewsService extends AbstractBaseService
{
    private $announcementRepository;
    private $requestStack;
    private $translator;
    private $geolocationService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        AnnouncementRepository $announcementRepository,
        RequestStack $requestStack,
        TranslatorInterface $translator,
        GeolocationService $geolocationService
    ) {
        parent::__construct($em, $settings);
        $this->announcementRepository = $announcementRepository;
        $this->requestStack = $requestStack;
        $this->translator = $translator;
        $this->geolocationService = $geolocationService;
    }

    public function getNews(User $user)
    {
        $announcements = $this->announcementRepository->findAnnouncementByUser($user, false);
        $timezoneUser = $this->geolocationService->getTimeZoneConnection() ?? $this->settings->get('app.default_timezone');
        $now = new \DateTime('now', new \DateTimeZone($timezoneUser));
        $host = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();

        $news = [];
        $timeZoneActual = $this->geolocationService->getTimeZoneConnection() ?? $this->settings->get('app.default_timezone');

        foreach ($announcements as $announcement) {
            $timezoneAnnouncement = $announcement->getTimezone() ?? $this->settings->get('app.default_timezone');

            $startAtTimezone = $this->geolocationService->convertTimeZone($announcement->getStartAt(), $timezoneAnnouncement, $timeZoneActual);
            $finishAtTimezone = $this->geolocationService->convertTimeZone($announcement->getFinishAt(), $timezoneAnnouncement, $timeZoneActual);

            $startAt = new \DateTime($startAtTimezone);
            $finishAt = new \DateTime($finishAtTimezone);

            $textStart = $this->translator->trans('announcements.news.start_announcement', ['%course%' => $announcement->getCourse()->getName()], 'messages', $user->getLocaleCampus());
            $textFinish = $this->translator->trans('announcements.news.finish_announcement', ['%course%' => $announcement->getCourse()->getName()], 'messages', $user->getLocaleCampus());

            $isEndingCourse = DateCalculationUtils::isEndingSoon($startAt, $finishAt, 20);

            if ($startAt > $now) {
                $type = 'START';
            } elseif ($isEndingCourse && $finishAt > $now) {
                $type = 'END';
            } else {
                continue; // Skip if neither condition is met
            }

            $textMessages = [
                'days' => $this->translator->trans('stats.chapter_day', [], 'messages', $user->getLocaleCampus()),
                'hours' => $this->translator->trans('stats.chaper_hours', [], 'messages', $user->getLocaleCampus()),
                'minutes' => $this->translator->trans('stats.chapter_minutes', [], 'messages', $user->getLocaleCampus()),
            ];

            $news[] = [
                'title' => $announcement->getCourse()->getName(),
                'text' => 'START' === $type ? $textStart : $textFinish,
                'imageUrl' => $announcement->getCourse()->getImage() ? $host . '/uploads/images/course/' . $announcement->getCourse()->getImage() : null,
                'datetime' => 'START' === $type ? $startAt : $finishAt,
                'time' => DateCalculationUtils::calculateDateDifference($now, 'START' === $type ? $startAt : $finishAt, $textMessages),
                'type' => $type,
                'start' => $startAt,
                'now' => $now,
                'timezone' => $timezoneUser,
            ];
        }

        return $news;
    }
}
