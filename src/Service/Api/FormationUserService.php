<?php

namespace App\Service\Api;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\SectionDefaultFront;
use App\Entity\User;
use App\Entity\TypeCourse;
use App\Enum\AnnouncementState;
use App\Enum\SectionDefaultFront as EnumSectionDefaultFront;
use App\Repository\AnnouncementRepository;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Lexik\Bundle\JWTAuthenticationBundle\TokenExtractor\TokenExtractorInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;




class FormationUserService extends AbstractBaseService
{
    private $announcementRepository;
    private $apiCourseService;
    private $announcementConfigurationsService;
    private $announcementUserService;
    private $security;
    private $tokenExtractor;
    private $jwtManager;
    private $requestStack;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        AnnouncementRepository $announcementRepository,
        ApiCourseService $apiCourseService,
        AnnouncementConfigurationsService $announcementConfigurationsService,
        AnnouncementUserService $announcementUserService,
        Security $security,
        TokenExtractorInterface $tokenExtractor,
        JWTTokenManagerInterface $jwtManager,
        RequestStack $requestStack


    ) {
        parent::__construct($em, $settings);
        $this->announcementRepository = $announcementRepository;
        $this->apiCourseService = $apiCourseService;
        $this->announcementConfigurationsService = $announcementConfigurationsService;
        $this->announcementUserService = $announcementUserService;
        $this->security = $security;
        $this->tokenExtractor = $tokenExtractor;
        $this->jwtManager = $jwtManager;
        $this->requestStack = $requestStack;
    }

    public function getCoursesTeleformation(User $user)
    {
        $coursesByAccessFilter = $this->apiCourseService->getCoursesUserFilterAccess($user);
        return $this->getCoursesByType($user, TypeCourse::TYPE_TELEFORMACION, $coursesByAccessFilter);
    }

    public function getCoursesPresential(User $user)
    {
        return $this->getCoursesByType($user, TypeCourse::TYPE_PRESENCIAL);
    }

    public function getCoursesMixed(User $user)
    {
        return $this->getCoursesByType($user, TypeCourse::TYPE_MIXTO);
    }

    public function getCoursesVirtualClass(User $user)
    {
        return $this->getCoursesByType($user, TypeCourse::TYPE_AULA_VIRTUAL);
    }

    public function getCoursesByType(User $user, $type, $extra = [])
    {
        $announcements = $this->announcementRepository->findAnnouncementByUser($user);
        $typeCourse = $this->em->getRepository(TypeCourse::class)->find(['id' => $type]);


        $translated = $typeCourse->translate($user->getLocaleCampus(), false);
        $name = $translated->getName();
        $description = $translated->getDescription();

        return [
            "id" => $typeCourse->getId(),
            "name" => $name ?? $typeCourse->getName(),
            "type" => "ANNOUNCEMENT",
            "description" => $description ?? $typeCourse->getDescription(),
            "featured" => true,
            "courses" => array_merge(($this->processAnnouncementCourses($announcements, $user, $type) ?? []), ($extra ?? [])),

        ];
    }

    private function processAnnouncementCourses(array $announcements, User $user, $idTypeCourse)
    {
        $typeCourse = $this->em->getRepository(TypeCourse::class)->findOneBy(['id' => $idTypeCourse, 'active' => true]);
        $groupedCourses = [];

        foreach ($announcements as $announcement) {
            $typeCourseId = $this->getTypeCourseIdFromAnnouncement($announcement);

            if (!$this->hasAccessContentAfterFinish($announcement, $user)) {
                continue;
            }

            if ($typeCourse && $typeCourse->getId() == $typeCourseId) {
                $announcementToSend = $this->apiCourseService->courseToSendAnnouncement($user, $announcement);
                $groupedCourses[] = $announcementToSend;
            }
        }

        return $groupedCourses;
    }


    private function hasAccessContentAfterFinish(Announcement $announcement, $user)
    {
        $hasAccessContentAfterFinish = $this->announcementConfigurationsService->hasAccessContentAfterFinish($announcement);
        $stateAnnouncement = $this->stateAnnouncement($announcement);
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['announcement' => $announcement, 'user' => $user]);
        $hasSurvey = $announcementUser->isAproved() && !$this->announcementUserService->hasSurveyCompleted($announcementUser);
        return $hasAccessContentAfterFinish || $stateAnnouncement != AnnouncementState::STATE_FINISHED || $hasSurvey;
    }

    private function stateAnnouncement(Announcement $announcement)
    {
        $now = new \DateTime();
        $startAt = $announcement->getStartAt();
        $finishAt = $announcement->getFinishAt();

        if ($startAt > $now) {
            return AnnouncementState::STATE_NOT_STARTED;
        } elseif ($finishAt < $now) {
            return AnnouncementState::STATE_FINISHED;
        } else {
            return AnnouncementState::STATE_IN_PROGRESS;
        }
    }

    private function getTypeCourseIdFromAnnouncement($announcement)
    {
        $course = $announcement->getCourse();
        return $course ? $course->getTypeCourse()->getId() : null;
    }


    public function updateDidacticGuideStatus(AnnouncementUser $announcementUser, bool $isDownload)
    {
        if ($isDownload) {
            $this->updateStatus($announcementUser, 'Download');
        } else {
            $this->updateStatus($announcementUser, 'Read');
        }
    }

    private function updateStatus(AnnouncementUser $announcementUser, string $statusType)
    {
        $statusMethod = "is{$statusType}DidacticGuide";
        $setterMethod = "set{$statusType}DidacticGuide";

        if (!$announcementUser->$statusMethod()) {
            $announcementUser->$setterMethod(true);
            $dateMethod = "setDate{$statusType}DidacticGuide";
            $announcementUser->$dateMethod(new \DateTimeImmutable());
        }

        $this->em->persist($announcementUser);
        $this->em->flush();
    }

    public function showMenuItineraryInTheFront()
    {
        $request = $this->requestStack->getCurrentRequest();
        $token = $this->tokenExtractor->extract($request);
        $payload = $this->jwtManager->parse($token);

        if ($payload && isset($payload['announcement'])) {
            return true;
        }

        $stateMenu = $this->getStateSectionDefaultFront(EnumSectionDefaultFront::SECTION_ITINERAY);

        if (!$stateMenu) {
            return false;
        }

        $user = $this->security->getUser();

        if ($this->shouldShowItineraryMenu($user)) {
            return true;
        }

        return false;
    }

    private function shouldShowItineraryMenu($user)
    {
        $userHasItineraries = $user->hasItineraries();
        $useItinerarySettings = $this->settings->get('app.use_itinerary');
        $useFiltersSettings = $this->settings->get('app.user.useFilters');
        $announcements = $this->announcementRepository->findAnnouncementByUser($user) !== null;

        return $userHasItineraries && ($useItinerarySettings || $useFiltersSettings || $announcements);
    }

    private function getStateSectionDefaultFront($section)
    {
        return $this->em->getRepository(SectionDefaultFront::class)->getStateSectionDefaultFront($section);
    }
}
