<?php

declare(strict_types=1);

namespace App\Service\Course\Report\General;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\UserCourseChapter;
use App\Service\Course\Common\Stats\StatsGeneralService;
use App\Service\Course\Common\UserCourseService;
use App\Service\Course\DT0\ChapterQueryParams;
use App\Service\Course\GlobalFilter;
use App\Service\StatsUser\ResultGameService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;

abstract class BaseStatsService
{
    protected EntityManagerInterface $em;
    protected GlobalFilter $globalFilter;
    protected RequestStack $requestStack;
    protected UserCourseService $userCourseService;
    protected ResultGameService $resultGameService;

    protected StatsGeneralService $statsGeneralService;

    protected $params = [];

    public function __construct(
        EntityManagerInterface $em,
        UserCourseService $userCourseService,
        RequestStack $requestStack,
        GlobalFilter $globalFilter,
        ResultGameService $resultGameService,
        StatsGeneralService $statsGeneralService
    ) {
        $this->em = $em;
        $this->userCourseService = $userCourseService;
        $this->requestStack = $requestStack;
        $this->globalFilter = $globalFilter;
        $this->resultGameService = $resultGameService;
        $this->statsGeneralService = $statsGeneralService;
    }

    abstract public function getStats(Course $course, ?Announcement $announcement = null): array;

    abstract public function getStatsUserChaperCourse(Course $course): array;

    protected function getStructureStats(Course $course): array
    {
        return $this->getChaptersCourse($course);
    }

    protected function getStructureStatsUserChapterCourse(Course $course): array
    {
        return $this->getChapterUserCourse($course);
    }

    private function getChaptersCourse(Course $course): array
    {
        $chapters = [];

        foreach ($course->getChapters() as $chapter) {
            $chapterQueryParams = $this->getChapterQueryParameters($chapter);

            $progressMetrics = $this->statsGeneralService->calculateChapterProgressMetrics(
                $chapterQueryParams
            );

            $chapterData = $this->statsGeneralService->chaptersStatsGetChapterData($chapterQueryParams);

            $chapterTypeName = $chapter->getType()->getNormalized();

            if ('FillGaps' === $chapterTypeName) {
                // method responsible for replacing the 'gaps' in the questions when the chapter is of type FillGaps
                $chapterData = $this->handleFillGapsData($chapterData);
            }

            $chapters[] = array_merge(
                [
                    'id' => $chapter->getId(),
                    'name' => $chapter->getTitle(),
                    'image' => $chapter->getImage(),
                    'type' => $chapter->getType()->getName(),
                    'class' => $chapter->getType()->getType(),
                    'icon' => $chapter->getType()->getIcon()
                ],
                $progressMetrics,
                $chapterData
            );
        }

        return $chapters;
    }

    protected function getAnnouncement(): ?Announcement
    {
        return $this->em->getRepository(Announcement::class)->find($this->params['announcementId'] ?? 0);
    }

    protected function getUsers(Course $course): array
    {
        $announcement = $this->getAnnouncement();
        if ($announcement) {
            return $this->getUsersByAnnouncement($announcement);
        }

        return $this->userCourseService->getAllUsersIds($course, $this->params);
    }

    protected function getUsersByAnnouncement(Announcement $announcement): array
    {
        $announcementUsers = $this->em->getRepository(AnnouncementUser::class)
            ->getUsersByAnnouncement($announcement);

        $users = [];

        foreach ($announcementUsers as $announcementUser) {
            $users[] = $announcementUser->getUser()->getId();
        }

        return $users;
    }

    private function getChapterQueryParameters(Chapter $chapter): ChapterQueryParams
    {
        $course = $chapter->getCourse();
        $users = $this->getUsers($course);

        $courseParams = $this->getParamsCourse($course);

        $queryParamsChapter = ChapterQueryParams::create([
            'chapter' => $chapter,
            'findUsers' => false,
            'users' => $users,
            'dateFrom' => $courseParams['dateFrom'],
            'dateTo' => $courseParams['dateTo'],
            'courseStartedOnTime' => $courseParams['courseStartedIntime'],
            'announcementId' => $courseParams['announcementId'],
            'courseFinishedOnTime' => $courseParams['courseFinishedIntime'],
        ]);

        return $queryParamsChapter;
    }

    protected function getParamsCourse(Course $course): array
    {
        $chapters = $course->getChapters();

        $content = $this->params;
        $this->globalFilter->cleanFilters($content);
        $courseStartedIntime = filter_var($content['courseStartedIntime'] ?? false, FILTER_VALIDATE_BOOLEAN);
        $courseFinishedIntime = filter_var($content['courseFinishedIntime'] ?? false, FILTER_VALIDATE_BOOLEAN);
        $dateFrom = null;
        $dateTo = null;

        if (!empty($content['dateFrom'])) {
            $dateFrom = \DateTimeImmutable::createFromFormat('Y-m-d', $content['dateFrom']);
            $courseStartedIntime = true;
        }

        if (!empty($content['dateTo'])) {
            $dateTo = \DateTimeImmutable::createFromFormat('Y-m-d', $content['dateTo']);
            $courseFinishedIntime = true;
        }

        $findUsers = false;
        $this->globalFilter->findUsersFilter($content, $findUsers);

        $usersIds = $this->getUsers($course);

        return [
            'chapters' => $chapters,
            'findUsers' => $findUsers,
            'usersIds' => $usersIds,
            'courseFinishedIntime' => $courseFinishedIntime,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'courseStartedIntime' => $courseStartedIntime,
            'announcementId' => $content['announcementId'] ?? 0,
        ];
    }

    private function getChapterUserCourse(Course $course): array
    {
        $UsersChapter = [];

        foreach ($course->getChapters() as $chapter) {
            $chapterQueryParams = $this->getChapterQueryParameters($chapter);

            $baseQb = $this->em->getRepository(UserCourseChapter::class)
                ->chapterGetUserCourseChaptersQuery($chapterQueryParams);

            if (null === $baseQb) {
                continue;
            }

            $offset = 0;
            $pageSize = 100;
            $chapterType = $chapter->getType();
            $chapterTypeName = $chapterType->getNormalized();

            do {
                if (!$baseQb) {
                    break;
                }
                $lastRow = null;
                $dataPaginated = $this->em->getRepository(UserCourseChapter::class)
                    ->getUserChapterPaginated($baseQb, $offset, $pageSize);

                $userChapterInfo = [];
                foreach ($dataPaginated as $userCourseChapter) {
                    if (!$userCourseChapter) {
                        $lastRow = $userCourseChapter;
                        break;
                    }

                    $user = $userCourseChapter->getUserCourse()->getUser();
                    $gameResult = $this->resultGameService
                        ->getResultGameAttemptsWithParams($userCourseChapter, $chapterType);

                    $resultInfoUser = $this->handleGameResultUserChapterCourse($gameResult, $user);

                    if ('FillGaps' === $chapterTypeName) {
                        // method responsible for replacing the 'gaps' in the questions when the chapter is of type FillGaps
                        $resultInfoUser = $this->handleFillGapsData($resultInfoUser);
                    }

                    if (\count($resultInfoUser) > 0) {
                        $userChapterInfo = array_merge($userChapterInfo, $resultInfoUser);
                    }

                    $lastRow = $userCourseChapter;
                }

                ++$offset;
                if (\count($userChapterInfo)) {
                    $UsersChapter[$chapter->getId()][] = $userChapterInfo;
                }
            } while (null !== $lastRow);
        }

        return $UsersChapter;
    }

    /**
     * Auxiliary method to replace [idX-...] with '______' in the data,
     * for FillGaps-type chapters. It checks two possible structures:
     *  1) 'questions' => [ [ 'question' => '...' ], ... ]
     *  2) [ [ 'QUESTION' => '...' ], [ 'QUESTION' => '...' ] ]  (user-chapter results).
     */
    private function handleFillGapsData(array $data): array
    {
        if (isset($data['questions']) && \is_array($data['questions'])) {
            foreach ($data['questions'] as $index => $question) {
                if (isset($question['question'])) {
                    $data['questions'][$index]['question'] = preg_replace(
                        '/\[id\d+\-[a-zA-Z]+\]/',
                        '______',
                        $question['question']
                    );
                }
            }

            return $data;
        }

        foreach ($data as $key => $singleResult) {
            if (isset($singleResult['QUESTION']) && \is_string($singleResult['QUESTION'])) {
                $data[$key]['QUESTION'] = preg_replace(
                    '/\[id\d+\-[a-zA-Z]+\]/',
                    '______',
                    $singleResult['QUESTION']
                );
            }
        }

        return $data;
    }

    private function handleGameResultUserChapterCourse(array $gameResult, $user): array
    {
        $userResultEvaluation = [];
        foreach ($gameResult as $result) {
            $questions = $result['questions'];
            if ($questions) {
                foreach ($questions as $question) {
                    $questionUserEvaluation = $this->getUserTemplate($user);
                    $questionUserEvaluation['ATTEMPT'] = $result['attempt'] ?? 1;
                    $questionUserEvaluation['QUESTION_ID'] = $question['id'];
                    $questionUserEvaluation['QUESTION'] = $question['question'] ?? '';
                    $questionUserEvaluation['RESULT'] = $this->answerQuestions($question);
                    $userResultEvaluation[] = $questionUserEvaluation;
                }
            }
        }

        return $userResultEvaluation;
    }

    private function getUserTemplate($user): array
    {
        return [
            'USER_ID' => $user->getId(),
            'USER_CODE' => $user->getCode(),
            'FIRST_NAME' => $user->getFirstName(),
            'LAST_NAME' => $user->getLastName(),
            'EMAIL' => $user->getEmail(),
            'ATTEMPT' => 1,
            'QUESTION_ID' => -1,
            'QUESTION' => '',
            'RESULT' => 'OK/KO',
        ];
    }

    private function answerQuestions($question)
    {
        if (\is_null($question['answers'])) {
            return false;
        }

        $answers = $question['answers'];
        $result = false;
        foreach ($answers as $answer) {
            if ($answer['correct'] ?? false) {
                $result = true;
            }
        }

        return $result;
    }
}
