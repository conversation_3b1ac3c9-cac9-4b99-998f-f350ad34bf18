<?php

declare(strict_types=1);

namespace App\Service\Course\Report\Persons;

use App\Entity\Course;
use App\Entity\Itinerary;
use App\Service\Course\Report\General\BaseReport;
use App\Service\Course\Report\Persons\PersonStatsReport;
use App\Service\Course\Report\General\CourseReportService;
use Psr\Log\LoggerInterface;
use Doctrine\ORM\EntityManagerInterface;

class ItineraryReport extends BaseReport
{
    private PersonStatsReport $personStatsReport;
    private CourseReportService $courseReportService;
    private LoggerInterface $logger;
    private EntityManagerInterface $em;

    public function __construct(PersonStatsReport $personStatsReport, CourseReportService $courseReportService, LoggerInterface $logger, EntityManagerInterface $em)
    {
        $this->personStatsReport = $personStatsReport;
        $this->courseReportService = $courseReportService;
        $this->logger = $logger;
        $this->em = $em;
    }

    public function generate(?Course $course = null, array $params = []): string
    {

        $itineraryId = $params['itineraryId'] ?? null;
        $userIds = [$params['userId']] ?? [];
        if (null === $itineraryId) {
            throw new \RuntimeException('Itinerary not found');
        }

        $itinerary = $this->em->getRepository(Itinerary::class)->find($itineraryId);
        
        //Adding identifier to know if is itinerary or not
        $itineraryFiles = 'itinerary-report';
        foreach ($itinerary->getItineraryCourses() as $course) {
            $this->courseReportService->getDataInfoCourse($course->getCourse()->getId());
            $this->personStatsReport->setDefaultData($course->getCourse(), $params);
            $this->personStatsReport->personReport($course->getCourse(), $params, $userIds);
            //Concat strings of courses adding separator '&& ' to separate in explode later 
            $itineraryFiles .= '&& ' . $this->courseReportService->getUserSaveFileDirectoryName($course->getCourse(), $params);
        }

        $this->logger->info('Personal_Itinerary_Generate_Report[Started]');
        return $itineraryFiles;
    }
}
