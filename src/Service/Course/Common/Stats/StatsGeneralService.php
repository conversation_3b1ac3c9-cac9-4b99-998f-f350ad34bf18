<?php

declare(strict_types=1);

namespace App\Service\Course\Common\Stats;

use App\Entity\UserCourseChapter;
use App\Service\Course\DT0\ChapterQueryParams;
use App\Service\StatsUser\ResultGameService;
use Doctrine\ORM\EntityManagerInterface;

class StatsGeneralService
{
    private EntityManagerInterface $em;
    private ResultGameService $resultGameService;

    public function __construct(
        EntityManagerInterface $em,
        ResultGameService $resultGameService
    ) {
        $this->em = $em;
        $this->resultGameService = $resultGameService;
    }

    public function calculateChapterProgressMetrics(ChapterQueryParams $chapterQueryParams, &$allUsersInChapters = true): array
    {
        $userCount = \count($chapterQueryParams->users);

        if (0 === $userCount) {
            return [
                'time' => 0,
                'inProgress' => 0,
                'finished' => 0,
                'allUsersInChapters' => false
            ];
        }

        $timeByChapter = $this->em->getRepository(UserCourseChapter::class)->getTimeByChapter($chapterQueryParams);
        $totalUserFinished = $this->em->getRepository(UserCourseChapter::class)->getTotalUserFinishedChapters($chapterQueryParams);
        $totalUserInProgress = $this->em->getRepository(UserCourseChapter::class)->chapterGetTotalUsersStarted($chapterQueryParams);
        $allUsersInChapters = $this->em->getRepository(UserCourseChapter::class)->findUsersInChapterAndFinishedCourse($chapterQueryParams);

        $percentageFinished = $this->calculatePercentage(\intval($totalUserFinished['finished']) ?? 0, $userCount);
        $percentageProgress = $this->calculatePercentage(\intval($totalUserInProgress) ?? 0, $userCount);

        return [
            'time' => \intval($timeByChapter['totalTime'] ?? 0),
            'inProgress' => $percentageProgress,
            'finished' => $percentageFinished,
            'allUsersInChapters' => $allUsersInChapters
        ];
    }

    private function calculatePercentage(int $value, int $total): float
    {
        if (0 === $total) {
            return 0.0;
        }

        $percentage = ($value / $total) * 100;

        if ($percentage > 0 && $percentage < 1) {
            return round($percentage, 2);
        }

        return round($percentage);
    }

    public function chaptersStatsGetChapterData(ChapterQueryParams $chapterQueryParams): array
    {
        $nAttempts = 0;
        $nUserAttempts = 0;
        $questionsIndexed = [];
        $baseQb = $this->em->getRepository(UserCourseChapter::class)
            ->chapterGetUserCourseChaptersQuery(
                $chapterQueryParams
            );

        if (null === $baseQb) {
            return [
                'totalAttempts' => 0,
                'totalUserAttempts' => 0,
                'questions' => []
            ];
        }

        $offset = 0;
        $pageSize = 100;
        $chapterType = $chapterQueryParams->chapter->getType();

        do {
            $lastRow = null;
            $dataPaginated = $this->em->getRepository(UserCourseChapter::class)->getUserChapterPaginated($baseQb, $offset, $pageSize);

            foreach ($dataPaginated as $userCourseChapter) {
                if (!$userCourseChapter) {
                    $lastRow = $userCourseChapter;
                    break;
                }

                ++$nUserAttempts;
                $gameResult = $this->resultGameService
                    ->getResultGameAttemptsWithParams($userCourseChapter, $chapterType);
                $this->handleGameResult($gameResult, $questionsIndexed, $nAttempts);
                $lastRow = $userCourseChapter;
            }
            ++$offset;
        } while (null !== $lastRow);

        return [
            'totalAttempts' => $nAttempts,
            'totalUserAttempts' => $nUserAttempts,
            'questions' => array_values($questionsIndexed)
        ];
    }

    private function handleGameResult(array $gameResult, array &$questionsIndexed, &$nAttempts = 0): void
    {
        foreach ($gameResult as $result) {
            ++$nAttempts;
            foreach ($result['questions'] as $question) {
                if (!\array_key_exists('question-' . $question['id'], $questionsIndexed)) {
                    $questionsIndexed['question-' . $question['id']] = [
                        'id' => $question['id'],
                        'question' => $question['question'],
                        'totalSuccess' => 0,
                        'totalErrors' => 0,
                        'correctAnswer' => '',
                        'answers' => $question['answers'],
                    ];
                }

                foreach ($question['answers'] as $answer) {
                    $correct = false;
                    if (isset($answer['correct'])) {
                        $correct = $answer['correct'];
                    }
                    if ($correct) {
                        $questionsIndexed['question-' . $question['id']]['correctAnswer'] = $answer['answer'];
                        ++$questionsIndexed['question-' . $question['id']]['totalSuccess'];
                    } else {
                        ++$questionsIndexed['question-' . $question['id']]['totalErrors'];
                    }
                }
            }
        }
    }
}
