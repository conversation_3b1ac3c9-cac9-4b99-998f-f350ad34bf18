<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\OrdenarMenormayor as EntityGuessword;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use App\Service\StatsUser\Games\Traits\ScoreAttempGameTrait;
use Doctrine\ORM\EntityManagerInterface;

class GuessWord implements GamesStragegyResultInterface
{
    use ScoreAttempGameTrait;

    private $em;
    private $settings;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $guessWordRepository = $this->em->getRepository(EntityGuessword::class);

        if (!empty($userCourseChapter->getData()['attempts'])) {
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $questions = [];
                $timeTotalAttempt = 0;
                $lastDateInQuestions = null;

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $at) {
                    $questionFind = $guessWordRepository->find($at['questionId']);
                    $questionsGuessWord = $this->em->getRepository(EntityGuessword::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);

                    foreach ($at['attempts'] as $question) {
                        if (!$questionFind) {
                            continue;
                        }
                        $timeInQuestion = $question['time'] ? ceil($question['time']) : 0;
                        $lastDateInQuestions = $question['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                        $questions[] = [
                            'id' => $questionFind->getId(),
                            'question' => $questionFind->getTitle(),
                            'correct' => $question['correct'] ?? false,
                            'answers' => $this->getAnswers($questionFind, $question ?? null),
                        ];

                        $timeTotalAttempt += $timeInQuestion ?? 0;

                        $attempForCalculateScore = [
                            'answers' => $attempt,
                            'timeTotal' => $questionFind->getTime() ?? 0,
                            'totalQuestions' => count($questionsGuessWord) ?? 0,
                        ];
                    }
                }
                if (!$questions) {
                    continue;
                }

                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),
                    'date' => $lastDateInQuestions,
                    'state' => $this->getStateGameAttemp($userCourseChapter, $this->userCourseService, $attempForCalculateScore),
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswers(EntityGuessword $guessWord, $answerUser)
    {
        if (!$guessWord) {
            return [];
        }

        $answer = [];
        $words = $guessWord->getWordsArray();

        $answer[] = [
            // 'id' => $guessWord->getId(),
            'userAnswer' => $answerUser['word'] ?? null,
            'answer' => $words,
            'correct' => $answerUser['correct'] ?? false,
            'incorrect' => !$answerUser['correct'] ?? false,
        ];

        return $answer;
    }
}
