<?php

declare(strict_types=1);

namespace App\Service\FilesManager;

use App\Entity\AnnouncementTutor;
use App\Entity\FilesManager;
use App\Entity\User;
use App\Enum\UploadedFilesSource;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\String\Slugger\AsciiSlugger;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class FilesManagerService
{
    private const DIRECTORY_NAME = 'FilesManager';
    private const PATH_RELATIVE_TO_PROJECT = 'files' . DIRECTORY_SEPARATOR . self::DIRECTORY_NAME;

    public const ALLOWED_MIME_TYPES = [
        'image/*',
        'video/*',
        'text/plain',
        'application/pdf',
        'application/msword',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];

    private EntityManagerInterface $em;
    private ParameterBagInterface $params;
    private Security $security;
    private ValidatorInterface $validator;
    private LoggerInterface $logger;
    private RequestStack $requestStack;

    private string $baseDir; // [app.file_manager.baseDir]
    private string $projectDir;

    public function __construct(
        EntityManagerInterface $em,
        ParameterBagInterface $params,
        Security $security,
        ValidatorInterface $validator,
        LoggerInterface $logger,
        RequestStack $requestStack,
        KernelInterface $kernel
    ) {
        $this->em = $em;
        $this->params = $params;
        $this->security = $security;
        $this->baseDir = $this->params->get('app.file_manager.base_dir');
        $this->validator = $validator;
        $this->logger = $logger;
        $this->requestStack = $requestStack;
        $this->projectDir = $kernel->getProjectDir();
    }

    public function getBaseDir()
    {
        return $this->baseDir;
    }

    public function getUrl(FilesManager $filesManager): string
    {
        $request = $this->requestStack->getCurrentRequest();

        return $request->getBaseUrl() . '/files/' . $filesManager->getFilename();
    }

    /**
     * @param bool $fullPath Decide whether to return full path or path relative to [app.file_manager.base_dir]
     *
     * @throws \Exception
     */
    private function getFilesManagerDirectory(bool $fullPath = true): string
    {
        if (!file_exists($this->baseDir)) {
            if (!mkdir($this->baseDir)) {
                throw new \Exception('Please check FilesManager directory permissions');
            }
        }

        $path = '';
        if ($fullPath) {
            $path = $this->baseDir . DIRECTORY_SEPARATOR;
        }
        $path .= self::DIRECTORY_NAME;

        return $path;
    }

    public function validateFile(UploadedFile $file, array $allowedMimeTypes = []): bool
    {
        $violations = $this->validator->validate(
            $file,
            new File([
                'mimeTypes' => $allowedMimeTypes,
            ])
        );

        return 0 === $violations->count();
    }

    /**
     * @param string|null $directory
     *
     * @throws \Exception
     */
    public function upload(UploadedFile $uploadedFile, string $directory = '', string $requiredRole = User::ROLE_USER): FilesManager
    {
        $violations = $this->validator->validate(
            $uploadedFile,
            new File([
                'mimeTypes' => self::ALLOWED_MIME_TYPES,
            ])
        );

        $mimeType = $uploadedFile->getMimeType();

        if (\count($violations)) {
            $this->logger->error('Validación fallida para el archivo con MIME type: ' . $mimeType);
            throw new \RuntimeException('Please check allowed file types: ' . implode(',', self::ALLOWED_MIME_TYPES));
        }

        $uploadedFile->guessExtension();
        $path = $this->getFilesManagerDirectory() . (empty($directory) ? '' : DIRECTORY_SEPARATOR . $directory);
        if (!file_exists($path)) {
            if (!mkdir($path, 0777, true)) {
                throw new \Exception('Failed to create directory. Check file permissions');
            }
        }

        $extension = $uploadedFile->guessExtension();
        if (empty($extension)) {
            $tmp = explode('.', $uploadedFile->getClientOriginalName());
            $extension = end($tmp);
        }
        $filename = (new \DateTime())->getTimestamp() . '-' . bin2hex(random_bytes(10)) . '.' . $extension;

        $filesManager = new FilesManager();
        $filesManager->setFileSize(filesize((string) $uploadedFile))
            ->setOriginalName($uploadedFile->getClientOriginalName())
            ->setFilename($filename)
            ->setRequiredRole($requiredRole)
            ->setMimeType($uploadedFile->getMimeType())
            ->setFilePath($this->getFilesManagerDirectory(false) . (empty($directory) ? '' : DIRECTORY_SEPARATOR . $directory))
        ;

        $srcStream = fopen($uploadedFile->getPathname(), 'r');
        $result = file_put_contents($path . DIRECTORY_SEPARATOR . $filename, $srcStream);
        if (false === $result) {
            throw new \Exception('Could not write uploaded file');
        }

        if (\is_resource($srcStream)) {
            fclose($srcStream);
        }

        $this->em->persist($filesManager);

        return $filesManager;
    }

    /**
     * @param int|FilesManager $file
     * @param string|null      $location Define location relative to [app.file_manager.base_dir]/FilesManager
     *
     * @throws \Exception
     */
    public function clone($file, ?string $location = null): FilesManager
    {
        if (\is_int($file)) {
            $filesManager = $this->em->getRepository(FilesManager::class)->find($file);
            if (!$filesManager) {
                throw new \Exception('FilesManager not found');
            }
        } else {
            $filesManager = $file;
        }

        /** @var User $user */
        $user = $this->security->getUser();
        $createdBy = $filesManager->getCreatedBy();
        if ($user->getId() !== $createdBy->getId() && !$this->security->isGranted($filesManager->getRequiredRole())) {
            throw new \Exception('Not authorized to clone/modify/view the file', Response::HTTP_UNAUTHORIZED);
        }

        $srcFile = $this->baseDir . DIRECTORY_SEPARATOR . $filesManager->getFullPath();

        if (!file_exists($srcFile)) {
            throw new \Exception('File does not exits');
        }

        /**
         * Define new name.
         */
        $extension = explode('.', $filesManager->getOriginalName());
        $filename = (new \DateTime())->getTimestamp() . '-' . bin2hex(random_bytes(10)) . '.' . end($extension);
        if (!empty($location)) {
            $newPath = $this->getFilesManagerDirectory() . DIRECTORY_SEPARATOR . $location;
            if (!file_exists($newPath)) {
                if (!mkdir($newPath, 0777, true)) {
                    throw new \Exception('Failed to create directory. Check permissions');
                }
            }
            $dstFile = $newPath . DIRECTORY_SEPARATOR . $filename;
        } else {
            $dstFile = $this->baseDir . DIRECTORY_SEPARATOR . $filesManager->getFilePath() . DIRECTORY_SEPARATOR . $filename;
        }

        $clone = new FilesManager();
        $clone->setFileSize($filesManager->getFileSize())
            ->setOriginalName($filesManager->getOriginalName())
            ->setFilename($filename)
            ->setRequiredRole($filesManager->getRequiredRole())
            ->setMimeType($filesManager->getMimeType())
            ->setFilePath(empty($location) ? $filesManager->getFilePath() : ($this->getFilesManagerDirectory(false) . DIRECTORY_SEPARATOR . $location))
        ;

        if (!copy($srcFile, $dstFile)) {
            throw new \Exception('Failed to copy file');
        }

        $this->em->persist($clone);

        return $clone;
    }

    /**
     * Delete the file, not the instance.
     *
     * @return void
     */
    public function delete(FilesManager $filesManager)
    {
        $path = $this->baseDir . DIRECTORY_SEPARATOR . $filesManager->getFullPath();
        if (!file_exists($path)) {
            return;
        }
        if (!unlink($path)) {
            $this->logger->error('Failed to delete file: ' . $path);

            return;
        }
        $this->logger->info('File: ' . $path . ' [deleted]');
    }

    /**
     * @param string|int $fileValue Pass filename or FilesManager id
     */
    public function getFileResponse($fileValue): Response
    {
        /** @var User|null $user */
        $user = $this->security->getUser();
        if (!$user) {
            return new JsonResponse('Unauthorized', Response::HTTP_UNAUTHORIZED);
        }

        if (\is_int($fileValue)) {
            $filesManager = $this->em->getRepository(FilesManager::class)->find($fileValue);
        } else {
            $filesManager = $this->em->getRepository(FilesManager::class)->findOneBy(['filename' => $fileValue]);
        }
        if (!$filesManager) {
            return new Response('File not found', Response::HTTP_NOT_FOUND);
        }

        $createdBy = $filesManager->getCreatedBy();
        if ($user->getId() !== $createdBy->getId() && !$this->security->isGranted($filesManager->getRequiredRole())) {
            return new Response('Not allowed to view/modify the file', Response::HTTP_UNAUTHORIZED);
        }

        $path = $this->baseDir . DIRECTORY_SEPARATOR . $filesManager->getFullPath();
        if (!file_exists($path)) {
            return new Response('File not found', Response::HTTP_NOT_FOUND);
        }

        $response = new StreamedResponse(function () use ($path): void {
            $outputStream = fopen('php://output', 'wb');
            $fileStream = fopen($path, 'rb');
            stream_copy_to_stream($fileStream, $outputStream);
        });

        $slugger = new AsciiSlugger();
        $extension = pathinfo($filesManager->getOriginalName(), PATHINFO_EXTENSION);
        $filenameBase = pathinfo($filesManager->getOriginalName(), PATHINFO_FILENAME);
        $filenameBase = $slugger->slug($filenameBase)->toString();
        $filename = $filenameBase . '.' . $extension;

        $response->headers->set('Content-Type', $filesManager->getMimeType());
        $disposition = HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_INLINE,
            $filename
        );
        $response->headers->set('Content-Disposition', $disposition);

        return $response;
    }

    /**
     * @param int|string $identifier
     */
    public function getFilesManager($identifier): ?FilesManager
    {
        $repository = $this->em->getRepository(FilesManager::class);
        if (\is_int($identifier)) {
            return $repository->find($identifier);
        } else {
            return $repository->findOneBy(['filename' => $identifier]);
        }
    }

    /**
     * @return false|FilesManager Returns new FilesManager| false otherwise
     */
    public function generateFilesManagerFromSource(array $reference, string $directory = '')
    {
        $id = $reference['id'] ?? null;
        if (empty($id)) {
            return false;
        }
        $srcFile = null;
        $source = $reference['source'] ?? null;
        $originalName = '';
        switch ($source) {
            case UploadedFilesSource::SOURCE_FILES_MANAGER:
                $filesManager = $this->getFilesManager($id);

                return $this->clone($filesManager);
            case UploadedFilesSource::SOURCE_ANNOUNCEMENT_TUTOR:
                $path = $this->projectDir . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . $this->params->get('app.pdf_cv_file_uploads_path');
                $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->find($id);
                if (!$announcementTutor) {
                    return false;
                }
                $srcFile = $path . DIRECTORY_SEPARATOR . $announcementTutor->getFilename();

                $originalName = $announcementTutor->getOriginalName();
                $mimeType = $announcementTutor->getMimeType();
                break;
            default:
                return false;
        }

        if (!file_exists($srcFile)) {
            return false;
        }

        try {
            $dstFile = $this->getFilesManagerDirectory() . (!empty($directory) ? DIRECTORY_SEPARATOR . $directory : '');
            $extension = explode('.', $originalName);
            $filename = (new \DateTime())->getTimestamp() . '-' . bin2hex(random_bytes(10)) . '.' . end($extension);
            if (!file_exists($dstFile)) {
                if (!mkdir($dstFile)) {
                    return false;
                }
            }
            $dstFile .= DIRECTORY_SEPARATOR . $filename;
        } catch (\Exception $e) {
            return false;
        }

        $filesManager = new FilesManager();
        $filesManager->setFileSize(filesize($srcFile))
            ->setOriginalName($originalName)
            ->setFilename($filename)
            ->setRequiredRole(User::ROLE_USER)
            ->setMimeType($mimeType)
            ->setFilePath(!empty($directory) ? $this->getFilesManagerDirectory(false) . DIRECTORY_SEPARATOR . $directory : '')
        ;

        if (!copy($srcFile, $dstFile)) {
            return false;
        }

        $this->em->persist($filesManager);

        return $filesManager;
    }
}
