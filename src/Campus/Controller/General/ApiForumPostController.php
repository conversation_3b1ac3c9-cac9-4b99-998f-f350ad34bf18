<?php

namespace App\Campus\Controller\General;


use App\Entity\Course;
use App\Entity\ForumPost;
use App\Entity\ForumLikes;
use App\Entity\ForumReport;
use Swagger\Annotations as SWG;
use App\Entity\AnnouncementUser;
use App\Entity\EmailNotification;
use App\Repository\CourseRepository;
use App\Repository\ForumPostRepository;
use App\Repository\ForumLikesRepository;
use App\Repository\UserCourseRepository;
use App\Repository\ForumReportRepository;
use Doctrine\ORM\EntityNotFoundException;
use App\Repository\AnnouncementRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpKernel\KernelInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Component\Security\Core\Encoder\UserPasswordEncoderInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;

/**
 * Class ApiController
 * @package App\Controller
 *
 * @Route("/api")
 */
class ApiForumPostController extends ApiBaseController
{

    /**
     *
     * @SWG\Get(
     *     path="/api/forums",
     *     summary="Get forums list",
     * )
     *
     * @SWG\Response(
     *     response=200,
     *     description="Forums list"
     * )
     *
     * @SWG\Tag(name="Forum")
     * @return Response
     */
    public function getForums (ForumPostRepository $forumPostRepository, UserCourseRepository $userCourseRepository, AnnouncementRepository $announcementRepository): Response
    {
        $user = $this->getUser();

        $courses = [];

        $hasOpenthread = false;
        $userCourses = $userCourseRepository->getUserCourses($this->getUser());

        foreach ($userCourses as $userCourse)
        {
            try
            {
                if($this->checkCourseAccess($userCourse->getCourse())){
                    $courses[$userCourse->getCourse()->getId()] = [
                        'id'        => $userCourse->getCourse()->getId(),
                        'name'      => $userCourse->getCourse()->getName(),
                        'started'   => $userCourse->getStartedAt()->getTimestamp(),
                        'finished'  => $userCourse->getFinishedAt() ? $userCourse->getFinishedAt()->getTimestamp() : '',
                    ];
                }

                $openThread = $forumPostRepository->getOpenThreads();
                if(count($openThread) > 0){
                    $hasOpenthread = true;
                }
            }
            catch (EntityNotFoundException $notFoundException)
            {
            }
        }

        $announcements = [];
        $userAnnouncements  = $announcementRepository->getUserAnnouncements($user);
        foreach ($userAnnouncements as $announcement){
            if(!isset($courses[$announcement->getAnnouncement()->getCourse()->getId()])){
                $announcements[] = $announcement->getAnnouncement()->getCourse();
            }
        }

        $threads = [
            'courses' => array_values($courses),
            'hasOpenThread' => $hasOpenthread,
            'announcement' => $announcements,
        ];

        $status   = Response::HTTP_OK;
        $error    = false;
        $response = [
            'status' => $status,
            'error'  => $error,
            'data'   => $threads,
        ];

        return $this->sendResponse($response, ['groups' => ['forumChanelList']]);
    }

    /**
     *
     * @SWG\Get(
     *     path="/api/courses/{id}/forum",
     *     summary="Get course forum posts",
     * )
     *
     * @SWG\Response(
     *     response=200,
     *     description="Course forum posts"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="Course not found"
     * )
     *
     * @SWG\Tag(name="Courses")
     * @return JsonResponse|Response
     */
    public function getCourseThread (Course $course, ForumPostRepository $forumPostRepository,AnnouncementRepository $announcementRepository)
    {
        $user = $this->getUser();
        $announcementThread = [];
        $courseOnlyThread = [];

        if (!$course)
        {
            return $this->respondNotFound();
        }

        if (!$this->checkCourseAccess($course))
        {
            $status = Response::HTTP_NOT_FOUND;
            $error  = true;
            $dataError   = 'Access not allowed';
        }
        else if (!$course->getActive() or $course->getDeletedAt())
        {
            $status = Response::HTTP_NOT_FOUND;
            $error  = true;
            $dataError   = 'Course deleted or inactive. Access not allowed';
        }
        else
        {
            $status = Response::HTTP_OK;
            $error  = false;
            $courseOnlyThread = $forumPostRepository->getCouseOnlyThreads($course);
        }

        $userAnnouncements  = $announcementRepository->getUserAnnouncements($user);
            foreach ($userAnnouncements as $userAnnouncement){
                if($userAnnouncement->getAnnouncement()->getCourse() === $course){
                    $announcementThread = $forumPostRepository->findBy(['announcement'=> $userAnnouncement,'parent' => null]);
                }
            }
            $status = Response::HTTP_OK;
            $error  = false;


        if($error){
            $data = $dataError;
        }else{
            $allThreads = array_merge($courseOnlyThread, $announcementThread);
            $data   =  $allThreads;
        }

        $response = [
            'status' => $status,
            'error'  => $error,
            'data'   => $data,
        ];

        return $this->sendResponse($response, ['groups' => ['forum']]);
    }

    /**
     *
     * @SWG\Get(
     *     path="/api/forum/openthread",
     *     summary="Get course forum posts",
     * )
     *
     * @SWG\Response(
     *     response=200,
     *     description="Course forum posts"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="Course not found"
     * )
     *
     * @SWG\Tag(name="forum_openthread")
     * @return JsonResponse|Response
     */
    public function getOpenThread (ForumPostRepository $forumPostRepository)
    {
        $openThread = $forumPostRepository->getOpenThreads();

        $status = Response::HTTP_OK;
        $error  = false;

        $response = [
            'status' => $status,
            'error'  => $error,
            'data'   => $openThread,
        ];

        return $this->sendResponse($response, ['groups' => ['forum']]);
    }

    /**
     *
     * @SWG\Get(
     *     path="/api/forum/thread/{id}",
     *     summary="Get thread posts",
     * )
     *
     * @SWG\Response(
     *     response=200,
     *     description="Thread posts"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="Thread|Course not found"
     * )
     *
     * @SWG\Tag(name="Forum")
     * @param ForumPost $forumPost
     * @param ForumPostRepository $forumPostRepository
     * @return JsonResponse|Response
     */
    public function thread (ForumPost $forumPost, ForumPostRepository $forumPostRepository)
    {
        if (!$forumPost or $forumPost->getParent())
        {
            return $this->respondNotFound();
        }

        $course = $forumPost->getCourse();

        if($course){
            if (!$this->checkCourseAccess($course))
            {
                $status = Response::HTTP_NOT_FOUND;
                $error  = true;
                $data   = 'Not enrollment registered. Access not allowed';
            }
            else if (!$course->getActive() or $course->getDeletedAt())
            {
                $status = Response::HTTP_NOT_FOUND;
                $error  = true;
                $data   = 'Course deleted or inactive. Access not allowed';
            }
            else
            {
                $status = Response::HTTP_OK;
                $error  = false;
                $data   = $forumPost;
            }
        }else{
            $status = Response::HTTP_OK;
            $error  = false;
            $data   = $forumPost;
        }

        $response = [
            'status' => $status,
            'error'  => $error,
            'data'   => $data,
        ];

        return $this->sendResponse($response, [
            'groups' => ['forum', 'thread'],
            ObjectNormalizer::ENABLE_MAX_DEPTH => true,
            AbstractNormalizer::CIRCULAR_REFERENCE_LIMIT  => 2,
        ]);
    }

    /**
     *
     * @SWG\Get(
     *     path="/api/forum/post",
     *     summary="Get thread posts",
     * )
     *
     * @SWG\Post(
     *      @SWG\Parameter(
     *         name="title",
     *         description="The title",
     *         in="body",
     *         @SWG\Schema(type="string"),
     *     ),
     *          *
     *      @SWG\Parameter(
     *         name="message",
     *         description="The message",
     *         in="body",
     *         @SWG\Schema(type="string"),
     *     ),
     *
     *     @SWG\Parameter(
     *         name="forum_post_id",
     *         description="The forum post id",
     *         in="body",
     *         @SWG\Schema(type="integer"),
     *     ),
     *     @SWG\Parameter(
     *         name="course_id",
     *         description="The course id",
     *         in="body",
     *         @SWG\Schema(type="integer"),
     *     ),
     * )
     * @SWG\Response(
     *     response=200,
     *     description="The post sent"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="ForumPost|Course not found"
     * )
     *
     * @SWG\Tag(name="Forum")
     * @param Request $request
     * @param ForumPostRepository $forumPostRepository
     * @param CourseRepository $courseRepository
     * @return JsonResponse|Response
     */
    public function post (Request $request, ForumPostRepository $forumPostRepository, CourseRepository $courseRepository)
    {
        $em      = $this->getDoctrine()->getManager();
        $request = $this->transformJsonBody($request);


        $course = $courseRepository->find($request->get('course_id'));

        if (!$course)
        {
            return $this->respondNotFound();
        }

        if (!$this->checkCourseAccess($course))
        {
            $status = Response::HTTP_NOT_FOUND;
            $error  = true;
            $data   = 'Not enrollment registered. Access not allowed';
        }
        else if (!$course->getActive() or $course->getDeletedAt())
        {
            $status = Response::HTTP_NOT_FOUND;
            $error  = true;
            $data   = 'Course deleted or inactive. Access not allowed';
        }
        else
        {
            $parent = null;
            if ($request->get('forum_post_id'))
            {
                $parent = $forumPostRepository->find($request->get('forum_post_id'));
            }

            $forumPost = new ForumPost();
            $forumPost
                ->setUser($this->getUser())
                ->setCourse($course)
                ->setTitle($request->get('title'))
                ->setMessage($request->get('message'))
                ->setCreatedAt(new \DateTime());

            if ($parent)
            {
                $forumPost->setParent($parent);
                $parent->setLastResponseAt($forumPost->getCreatedAt());
                $em->persist($parent);
            }

            if ($request->get('response_id')){
                $responseId = $forumPostRepository->find($request->get('response_id'));
                $forumPost->setResponse($responseId);
            }

            //Registro para envio de notificacion - email
            $emailNotification = new EmailNotification();
            $emailNotification->setUser($this->getUser());
            $emailNotification->setForumPost($forumPost);
            $emailNotification->setSent(0);
            $emailNotification->setType('forum');

            $em->persist($emailNotification);

            $em->persist($forumPost);
            $em->flush();


            $status = Response::HTTP_OK;
            $error  = false;
            $data   = $forumPost;
        }

        $response = [
            'status' => $status,
            'error'  => $error,
            'data'   => $data,
        ];

        return $this->sendResponse($response, ['groups' => ['thread','forum']]);
    }

    /**
     *
     * @SWG\Post(
     *     path="/api/forum/like",
     *     summary="Send like to comment",
     *      @SWG\Parameter(
     *         name="title",
     *         description="The title",
     *         in="body",
     *         @SWG\Schema(type="string"),
     *     ),
     *
     *      @SWG\Parameter(
     *         name="message",
     *         description="The message",
     *         in="body",
     *         @SWG\Schema(type="string"),
     *     ),
     *
     *     @SWG\Parameter(
     *         name="forum_post_id",
     *         description="The forum post id",
     *         in="body",
     *         @SWG\Schema(type="integer"),
     *     ),
     *     @SWG\Parameter(
     *         name="course_id",
     *         description="The course id",
     *         in="body",
     *         @SWG\Schema(type="integer"),
     *     ),
     * )
     * @SWG\Response(
     *     response=200,
     *     description="The post sent"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="ForumPost|Course not found"
     * )
     *
     * @SWG\Tag(name="Forumlike")
     * @param Request $request
     * @param ForumPostRepository $forumPostRepository
     * @return JsonResponse|Response
     */
    public function like (Request $request, ForumPostRepository $forumPostRepository, ForumLikesRepository $forumLikeRepository)
    {
        $em = $this->getDoctrine()->getManager();
        $request = $this->transformJsonBody($request);

        try {
            $code = Response::HTTP_OK;
            $error = false;

            $postInfo = $forumPostRepository->find($request->get('forum_post_id'));
            $isPostLiked = $forumLikeRepository->findOneBy(['forumPost'=>$postInfo]);

            if(!is_null($isPostLiked)){
                $em->remove($isPostLiked);
            }else{
                $isPostLiked = new ForumLikes();
                $isPostLiked->setForumPost($postInfo)
                    ->setUser($this->getUser())
                    ->setCreatedAt(new \DateTimeImmutable());
                $em->persist($isPostLiked);
            }

            $em->flush();

            $response = [
                'status' => $code,
                'error' => $error,
                'data' => $postInfo,
            ];

        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to like the comment - Error: {' . $e->getMessage() . '}'
            ];
        }

        return $this->sendResponse($response, array('groups' => array('likes','forum', 'thread')));

    }

    /**
     *
     * @SWG\Post(
     *     path="/api/forum/like",
     *     summary="Send report to comment",
     *      @SWG\Parameter(
     *         name="title",
     *         description="The title",
     *         in="body",
     *         @SWG\Schema(type="string"),
     *     ),
     *
     *      @SWG\Parameter(
     *         name="message",
     *         description="The message",
     *         in="body",
     *         @SWG\Schema(type="string"),
     *     ),
     *
     *     @SWG\Parameter(
     *         name="forum_post_id",
     *         description="The forum post id",
     *         in="body",
     *         @SWG\Schema(type="integer"),
     *     ),
     *     @SWG\Parameter(
     *         name="course_id",
     *         description="The course id",
     *         in="body",
     *         @SWG\Schema(type="integer"),
     *     ),
     * )
     * @SWG\Response(
     *     response=200,
     *     description="The post sent"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="ForumPost|Course not found"
     * )
     *
     * @SWG\Tag(name="Forumlike")
     * @param Request $request
     * @param ForumPostRepository $forumPostRepository
     * @return JsonResponse|Response
     */
    public function report (Request $request, ForumPostRepository $forumPostRepository, ForumReportRepository $forumReportRepository)
    {
        $em = $this->getDoctrine()->getManager();
        $request = $this->transformJsonBody($request);

        try {
            $code = Response::HTTP_OK;
            $error = false;

            $postInfo = $forumPostRepository->find($request->get('forum_post_id'));

            $reportedPost = new ForumReport();
            $reportedPost->setForumPost($postInfo)
                ->setUser($this->getUser())
                ->setCreatedAt(new \DateTimeImmutable());
            $em->persist($reportedPost);

            $em->flush();

            $response = [
                'status' => $code,
                'error' => $error,
                'data' => 'ok',
            ];

        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to like the comment - Error: {' . $e->getMessage() . '}'
            ];
        }

        return $this->sendResponse($response, array('groups' => array('likes','forum', 'thread')));

    }

    protected function transformJsonBody (Request $request)
    {
        $data = json_decode($request->getContent(), true);

        if ($data === null)
        {
            return $request;
        }

        $request->request->replace($data);

        return $request;
    }

}
