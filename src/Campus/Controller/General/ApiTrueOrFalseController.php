<?php


namespace App\Campus\Controller\General;

use App\Entity\ChapterType;
use App\Entity\Puzzle;
use App\Entity\UserCourseChapter;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;



/**
 * Class ApiController
 * @package App\Controller
 *
 * @Route("/api")
 */
class ApiTrueOrFalseController extends ApiBaseController
{
    protected $em;

    /**
     * @Rest\Get("/chapters/trueorfalse/{id}/questions", name="api_trueorfalse_questions")
     *
     * @return Response
     */
    public function getTrueOrFalse(UserCourseChapter $userCourseChapter)
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;

            $times = [];

            $response = [
                'status' => $code,
                'error' => $error,
                'data' =>  [
                    'questions' => $this->getQuestiosTrueOrFalse($userCourseChapter),
                    "time" => 2000,
                    'times' => $times,
                ]
            ];
            return $this->sendResponse($response);
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the user - Error: {' . $e->getMessage() . '}'
            ];
        }
    }


    private function getQuestiosTrueOrFalse($userCourseChapter)
    {
        $questions = [];
        $answers = [];
        $path =  '/public/' . $this->getParameter('app.gameTrueOrFalse_uploads_path');
        $time = 0;

        if (isset($userCourseChapter->getData()['answers'])) {
            $time -= count($userCourseChapter->getData()['answers']) * $this->getParameter('app.trueorfalse.time');
        }

        $trueOrFalses = $userCourseChapter->getChapter()->getTrueOrFalses();

        $answer = new \stdClass;
        $answer->id = 1;
        $answer->answer = "true";
        $answer->image = $path + 'check.svg';
        array_push($answers, $answer);

        $answer = new \stdClass;
        $answer->id = 2;
        $answer->answer = "false";
        $answer->image =  $path + 'cross.svg';;
        array_push($answers, $answer);

        foreach ($trueOrFalses as $trueOrFalse) {
            $question = new \stdClass;
            $question->id = $trueOrFalse->getId();
            $question->question = $trueOrFalse->getText();
            $question->imageUrl = $path . $trueOrFalse->getRoute();
            $question->answers = $answers;
            $question->time = $time;

            array_push($questions, $question);
        }

        return $questions;
    }
}
