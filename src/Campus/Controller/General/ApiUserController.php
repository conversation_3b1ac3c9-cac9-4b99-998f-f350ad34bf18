<?php

declare(strict_types=1);

namespace App\Campus\Controller\General;

use App\Entity\Announcement;
use App\Entity\User;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\UserRepository;
use App\Repository\UserTokenRepository;
use App\Service\Notification\EmailNotificationService;
use App\Service\SettingsService;
use App\Service\User\General\UserTimeService;
use App\V2\Domain\Security\TokenInterface as V2TokenInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Swagger\Annotations as SWG;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController.
 *
 * @Rest\Route("/api")
 */
class ApiUserController extends ApiBaseController
{
    public function __construct(
        LoggerInterface $logger,
        UserRepository $userRepository,
        CourseRepository $courseRepository,
        AnnouncementRepository $announcementRepository,
        TranslatorInterface $translator,
        SettingsService $settingsService,
        private readonly V2TokenInterface $v2TokenInterface,
    ) {
        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settingsService);
    }

    /**
     * @Rest\Post("/login", name="api_user_manual_login")
     */
    public function apiUserLogin(
        Security $security,
        RequestStack $requestStack,
        TokenStorageInterface $tokenStorage
    ): Response {
        /** @var User|null $user */
        $user = $this->getUser();
        if (!$user) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'User failed to login',
            ]);
        }

        $token = $security->getToken();
        $jwtToken = $token->getAttribute('token');
        $refreshToken = $token->getAttribute('refresh_token');

        $usernamePasswordToken = new UsernamePasswordToken($user, null, 'main', $user->getRoles());
        $tokenStorage->setToken($usernamePasswordToken);
        $requestStack->getSession()->set('_security_main', serialize($usernamePasswordToken));

        $response = $this->sendResponse(
            [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'user' => $this->getUser(),
                    'token' => $jwtToken,
                    'level' => $this->settings->get('ranking.useLevel'),
                    'refreshToken' => $refreshToken->getRefreshToken(),
                ],
            ],
            [
                'groups' => ['user_area'],
            ]
        );

        $this->v2TokenInterface->setTokensCookie($response, $token->getUser());

        return $response;
    }

    /**
     * @Rest\Get("/logout", name="api_logout")
     *
     * @SWG\Response(
     *     response=200,
     *     description="User successfully logout"
     * )
     * @SWG\Response(
     *     response=401,
     *     description="User was not alloweb to access"
     * )
     * @SWG\Response(
     *     response=500,
     *     description="User was not logged in successfully"
     * )
     *
     * @SWG\Tag(name="User")
     */
    public function logout(
        Request $request,
        TokenStorageInterface $tokenStorage,
        UserTokenRepository $userTokenRepository
    ): Response {
        return parent::logout($request, $tokenStorage, $userTokenRepository);
    }

    /**
     * @Rest\Post("/user/update-time")
     */
    public function userTimeOnElements(Request $request, UserTimeService $userTimeService, EmailNotificationService $emailNotificationService): Response
    {
        $content = json_decode($request->getContent(), true);
        $userTimeService->saveUserTimer($request, $this->getUser());
        $this->updateUserTime($request, $userTimeService);

        $notification = $content['notification'] ?? 0;
        if (0 == $notification) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [],
            ]);
        }

        $notifications = $emailNotificationService->listUserEmailNotifications($this->getUser(), $request);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $notifications,
        ]);
    }

    /**
     * @Rest\Post("/user/update-usertime-announcement/{id}")
     */
    public function updateUserTimeAnnouncement(Announcement $announcement, UserTimeService $userTimeService): Response
    {
        $result = $userTimeService->existRegisterConexion($this->getUser(), $announcement);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $result,
        ]);
    }

    private function updateUserTime(Request $request, UserTimeService $userTimeService)
    {
        $content = json_decode($request->getContent(), true);
        $params = $content['params'] ?? [];
        $announcementUpdate = null;
        $announcementId = \array_key_exists('announcementId', $params) ? $params['announcementId'] : null;
        if ($announcementId) {
            $announcementUpdate = $this->announcementRepository->find($announcementId);
        }
        if ($announcementUpdate) {
            $result = $userTimeService->existRegisterConexion($this->getUser(), $announcementUpdate);
        }
    }
}
