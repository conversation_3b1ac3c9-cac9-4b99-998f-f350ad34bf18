<?php

namespace App\Campus\Controller\General;

use App\Admin\Traits\LanguagesTrait;
use App\Entity\Announcement;
use App\Entity\AnnouncementTutor;
use App\Entity\CommentTask;
use App\Entity\FilesHistoryTask;
use App\Entity\FilesTask;
use App\Entity\HistoryDeliveryTask;
use App\Entity\HistorySeenMaterial;
use App\Entity\MaterialCourse;
use App\Entity\MaterialDownloadHistory;
use App\Entity\Message;
use App\Entity\TaskCourse;
use App\Entity\TaskUser;
use App\Entity\User;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Mailer\MailerInterface;
use Vich\UploaderBundle\Handler\DownloadHandler;

/**
 * Class ApiController
 * @package App\Controller
 *
 * @Route("/api")
 */
class   ApiTaskUserController extends ApiBaseController
{
	use LanguagesTrait;

	private $em;
	private $mailer;

	/**
	 * ApiController constructor.
	 *
	 * @param $logger
	 */
	public function __construct(LoggerInterface $logger, UserRepository $userRepository, CourseRepository $courseRepository, AnnouncementRepository $announcementRepository, EntityManagerInterface $em, TranslatorInterface $translator, MailerInterface $mailer, SettingsService $settings)
	{

		parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settings);
		$this->em = $em;
		$this->mailer = $mailer;
	}


	public function getInfoUser()
	{
		if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
			$user = $this->em->getRepository(User::class)->findOneBy(array('email' => $this->getUser()->getEmail()));
		} else {
			$user = null;
		}

		return $user;
	}

	/**
	 * @Rest\Get("/file-download-course/{id}", name="api_file_course_download")
	 *
	 * @return Response
	 */
	public function downloadMaterialCourse(MaterialCourse $materialCourse, DownloadHandler $downloadHandler)
	{
		$status = Response::HTTP_NOT_FOUND;
		$error = true;
		$data = 'Attachment not allowed';

		if ($materialCourse) {
			$materialDownloadHistory = new MaterialDownloadHistory();
			$materialDownloadHistory->setCourse($materialCourse->getCourse());
			$materialDownloadHistory->setUser($this->getUser());
			$materialDownloadHistory->setMaterial($materialCourse);

			$this->em->persist($materialDownloadHistory);
			$this->em->flush();
			return $downloadHandler->downloadObject($materialCourse, 'filenameFile', null, $materialCourse->getName());
		}

		$response = [
			'status'  => $status,
			'error'   => $error,
			'message' => $data,
		];

		return $this->sendResponse($response);
	}

	/**
	 * @Rest\Get("/history-seen-material/{id}", name="api_history_seen_material")
	 *
	 * @return Response
	 */
	public function saveHistorySeenMaterial(MaterialCourse $materialCourse)
	{
		$status = Response::HTTP_OK;
		$error = true;
		$data = 'Saved correct';

		if ($materialCourse) {
			$historySeenMaterial = new HistorySeenMaterial();
			$historySeenMaterial->setCourse($materialCourse->getCourse());
			$historySeenMaterial->setUser($this->getUser());
			$historySeenMaterial->setMaterialCourse($materialCourse);

			$this->em->persist($historySeenMaterial);
			$this->em->flush();
		}

		$response = [
			'status'  => $status,
			'error'   => $error,
			'message' => $data,
		];

		return $this->sendResponse($response);
	}

	/**
	 * @Rest\Get("/file-task-download/{id}", name="api_file_task")
	 *
	 * @return Response
	 */
	public function downloadFileTask(FilesTask $fileTask, DownloadHandler $downloadHandler)
	{
		$status = Response::HTTP_NOT_FOUND;
		$error = true;
		$data = 'Attachment not allowed';

		if ($fileTask) {
			return $downloadHandler->downloadObject($fileTask, 'filenameFile', null, $fileTask->getFilename());
		}

		$response = [
			'status'  => $status,
			'error'   => $error,
			'message' => $data,
		];

		return $this->sendResponse($response);
	}

	/**
	 * @Rest\Post("/fileTaskUser/new", name="api-file-task-user")
	 *
	 * @return Response
	 */
	public function saveFileTaskUser(Request $request)
	{
		try {
			$code = Response::HTTP_OK;
			$error = false;
			$message = $this->translator->trans('taskCourse.configureFields.senTask');
			$taskId = $request->get('taskId');
			$count_files = $request->get('count-files');

			$taskCourse = $this->em->getRepository(TaskCourse::class)->find($taskId);
			$user = $this->getInfoUser();
			$taskUser = $this->em->getRepository(TaskUser::class)->findOneBy(['task' => $taskCourse, 'user' => $user], ['id' => 'desc']);

			if ($taskUser) {
				$historyTask = $taskUser->getHistoryDeliveryTasks();
				$historyDeliveryTask = $this->em->getRepository(HistoryDeliveryTask::class)->find($historyTask[0]->getId());
				$this->historyDeliverTask($historyDeliveryTask, $count_files, $request);
			} else {
				$taskUser = new TaskUser();
				$taskUser->setUser($user);
				$taskUser->setTask($taskCourse);
				$this->em->persist($taskUser);

				if ($taskUser) {
					$historyDeliveryTask = new HistoryDeliveryTask();
					$historyDeliveryTask->setTaskUser($taskUser);
					$historyDeliveryTask->setState(0);
					$this->em->persist($historyDeliveryTask);

					if ($historyDeliveryTask) {
						$this->historyDeliverTask($historyDeliveryTask, $count_files, $request);
					}
				}
			}

			$this->em->flush();
		} catch (\Exception $e) {
			$code = Response::HTTP_INTERNAL_SERVER_ERROR;
			$error = true;
			$message = "An error has occurred trying to save task user: {$e->getMessage()}";
		}

		$taskUser = $this->em->getRepository(TaskUser::class)->findOneBy(['task' => $taskCourse, 'user' => $user], ['id' => 'desc']);

		$response = [
			'status' => $code,
			'error'  => $error,
			'data'   => [
				'message'     => $message,
				'lastHistory' => $taskUser ? $taskUser->getHistoryDeliveryTasks() : ''
			],
		];

		return $this->sendResponse($response, array('groups' => array('detail', 'fileable', 'messages')));
	}

	/**
	 * @param $historyDeliveryTask
	 * @param $count_files
	 * @param Request $request
	 * @return void
	 */
	private function historyDeliverTask($historyDeliveryTask, $count_files, Request $request): void
	{
		if ($historyDeliveryTask) {
			$this->filesHistoryTask($count_files, $historyDeliveryTask, $request);
		}
	}

	/**
	 * @param $count_files
	 * @param $historyDeliveryTask
	 * @param Request $request
	 * @return void
	 */
	private function filesHistoryTask($count_files, $historyDeliveryTask, Request $request): void
	{
		for ($i = 0; $i < $count_files; $i++) {
			$filesHistoryTask = new FilesHistoryTask();
			$filesHistoryTask->setHistoryDeliveryTask($historyDeliveryTask);
			$file = $request->files->get('file' . $i);
			$filesHistoryTask->setFilename($file->getClientOriginalName());
			$filesHistoryTask->setFilenameFile($file);
			$this->em->persist($filesHistoryTask);
		}
	}

	/**
	 * @Rest\Post("/taskUser/new", name="api-task-user")
	 *
	 * @return Response
	 */
	public function saveTaskUser(Request $request)
	{
		try {
			$code = Response::HTTP_OK;
			$error = false;
			$message = $this->translator->trans('taskCourse.configureFields.senTask');

			$taskId = $request->get('taskId');
			$comment = $request->get('comment');

			$taskCourse = $this->em->getRepository(TaskCourse::class)->find($taskId);
			$user = $this->getInfoUser();
			$taskUser = $this->em->getRepository(TaskUser::class)->findOneBy(['task' => $taskCourse, 'user' => $user], ['id' => 'desc']);

			if ($taskUser) {
				$historyTask = $taskUser->getHistoryDeliveryTasks();
				$historyDeliveryTask = $this->em->getRepository(HistoryDeliveryTask::class)->find($historyTask[0]->getId());
				$historyDeliveryTask->setState(1);
				$this->em->persist($historyDeliveryTask);

				if ($historyDeliveryTask) {
					if ($comment != '') {
						$commentTask = new CommentTask();
						$commentTask->setHistoryDeliveryTask($historyDeliveryTask);
						$commentTask->setComment($comment);
						$this->em->persist($commentTask);
					}
				}
			}
			$this->em->flush();
		} catch (\Exception $e) {
			$code = Response::HTTP_INTERNAL_SERVER_ERROR;
			$error = true;
			$message = "An error has occurred trying to save task user: {$e->getMessage()}";
		}

		$response = [
			'status' => $code,
			'error'  => $error,
			'data'   => [
				'message'     => $message,
				'lastHistory' => $taskUser ? $taskUser->getHistoryDeliveryTasks() : ''
			],
		];

		return $this->sendResponse($response, array('groups' => array('detail', 'fileable', 'messages')));
	}

	/**
	 * @Rest\Get("/task-user/{id}", name="api_information_task_user")
	 *
	 * @return Response
	 */
	public function taskInformationUser(TaskCourse $task)
	{
		try {
			$user = $this->getInfoUser();
			$allHistoryTask = $this->em->getRepository(TaskUser::class)->findBy(['task' => $task, 'user' => $user], ['id' => 'asc']);
			$taskUser = $this->em->getRepository(TaskUser::class)->findOneBy(['task' => $task, 'user' => $user], ['id' => 'desc']);

			$response = [
				'status' => Response::HTTP_OK,
				'error'  => false,
				'data'   => [
					'history'       => $allHistoryTask,
					'lastHistory'   => $taskUser ? $taskUser->getHistoryDeliveryTasks() : '',
					'idHistoryTask' => $taskUser ? $taskUser->getHistoryDeliveryTasks()[0]->getId() : ''
				],
			];
		} catch (\Exception $e) {
			$response = [
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => 'An error has occurred trying to get information task user - Error: {' . $e->getMessage() . '}'
			];
		}


		return $this->sendResponse($response, array('groups' => array('detail', 'fileable', 'messages')));
	}

	/**
	 * @Rest\Post("/file-task-user/delete", name="api-delete-file-task")
	 *
	 * @return Response
	 */
	public function deleteFileTaskUser(Request $request)
	{
		try {
			$requestData = \json_decode($request->getContent(), true);
			$idFileTask = $requestData['idFileTask'];
			$fileHistoryTask = $this->em->getRepository(FilesHistoryTask::class)->find($idFileTask);

			$taskId = $requestData['taskId'];
			$user = $this->getInfoUser();
			$taskCourse = $this->em->getRepository(TaskCourse::class)->find($taskId);
			$taskUser = $this->em->getRepository(TaskUser::class)->findOneBy(['task' => $taskCourse, 'user' => $user], ['id' => 'desc']);

			if ($fileHistoryTask) {
				$this->deleteFileTask($fileHistoryTask->getFilename());
				$this->em->remove($fileHistoryTask);
				$this->em->flush();
			}

			$response = [
				'status' => Response::HTTP_OK,
				'error'  => false,
				'data'   => [
					'message'     => 'Archivo eliminado',
					'lastHistory' => $taskUser ? $taskUser->getHistoryDeliveryTasks() : ''
				]
			];
		} catch (\Exception $e) {
			$response = [
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => 'An error has occurred to delete file task user - Error: {' . $e->getMessage() . '}'
			];
		}

		return $this->sendResponse($response, array('groups' => array('detail', 'valoration', 'user_classroom', 'material', 'fileable')));
	}

	private function deleteFileTask($file)
	{
		$filename = $this->settings->get('app.task_user_path') . $file;

		if (file_exists($filename)) {
			$success = unlink($filename);

			if (!$success) {
				throw $this->createNotFoundException("Cannot delete $filename");
			}
		}
	}

	/**
	 * @Rest\Get("/file-taskuser-download/{id}", name="api_file_taskuser")
	 *
	 * @return Response
	 */
	public function downloadFileTaskUser(FilesHistoryTask $fileTask, DownloadHandler $downloadHandler)
	{
		$status = Response::HTTP_NOT_FOUND;
		$error = true;
		$data = 'Attachment not allowed';

		if ($fileTask) {
			return $downloadHandler->downloadObject($fileTask, 'filenameFile', null, $fileTask->getFilename());
		}

		$response = [
			'status'  => $status,
			'error'   => $error,
			'message' => $data,
		];

		return $this->sendResponse($response);
	}

	/**
	 * @Rest\Post("/chat/sent/student", name="api_chat_sent_student")
	 *
	 * @return Response
	 */
	public function sentMessage(Request $request)
	{
		$status = Response::HTTP_BAD_REQUEST;
		$error  = true;

		$body      = $request->get('form-body');
		$userId = $request->get('form-user-id');
		$idAnnouncement = $request->get('form-id-announcement');

		$announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
		$announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(['announcement' => $announcement]);
		$sender = $this->em->getRepository(User::class)->find($userId);

		$subject   = $announcement->getCourse()->getName();
		$recipient = $announcementTutor ? $announcementTutor->getTutor() : $announcement->getCreatedBy();

		if ($subject and $body) {
			$message = new Message();
			$message
				->setSender($sender)
				->setRecipient($recipient)
				->setSentAt(new \DateTime())
				->setSubject($subject)
				->setBody($body);

			$this->em->persist($message);

			$this->em->flush();

			$status = Response::HTTP_OK;
			$error  = false;
		}

		$messagesStudent = $this->em->getRepository(Message::class)->findBy(['sender' => $recipient->getId(), 'recipient' => $sender->getId()]);
		$messagesTutor = $this->em->getRepository(Message::class)->findBy(['sender' =>  $sender->getId(), 'recipient' => $recipient->getId()]);
		$messages = array_merge($messagesStudent, $messagesTutor);

		$data = [];
		foreach ($messages as $message) {
			$data[] = [
				'id' => $message->getId(),
				'body' => $message->getBody(),
				'sender' => $message->getSender(),
				'recipient' => $message->getRecipient(),
				'sentAt' => $message->getSentAt(),
				'openAt' => $message->getOpenAt()
			];
		}
		usort($data, function (array $elem1, $elem2) {
			return $elem1['id'] <=> $elem2['id'];
		});

		$response = [
			'status' => $status,
			'error'  => $error,
			'data' => $data
		];

		return $this->sendResponse($response, ['groups' => ['list', 'messages', 'user_area']]);
	}
}
