<?php

declare(strict_types=1);

namespace App\Campus\Service\TrainingUser;

use App\Entity\CourseSection;
use App\Service\Api\ApiCourseService;
use App\Service\SettingsService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Security;

class ConcreteItineraryTraining extends AbstractTrainingStrategy
{
    public function __construct(EntityManagerInterface $em, SettingsService $settings, ApiCourseService $apiCourseService, Security $security)
    {
        $this->em = $em;
        $this->settings = $settings;
        $this->apiCourseService = $apiCourseService;
        $this->security = $security;
    }

    public function getTraining(CourseSection $courseSection): array
    {
        $user = $this->getUser();
        $itineraries = $this->getUserItineraries($user);

        $data = [];
        foreach ($itineraries as $itineraryUser) {
            if (!$itineraryUser->isActive()) {
                continue;
            }

            /** @var ItineraryTranslation $translation */
            $translation = $itineraryUser->translate($user->getLocaleCampus());
            $name = $translation->getName();

            $itinerary = [
                'id' => $itineraryUser->getId(),
                'name' => $name ?? $itineraryUser->getName(),
                'type' => 'ITINERARY',
                'courses' => [],
            ];

            foreach ($itineraryUser->getItineraryCourses() as $course) {
                if (!$course->getCourse()->getActive()) {
                    continue;
                }

                $courseToSend = $this->apiCourseService->courseToSend($user, $course->getCourse());

                if (empty($courseToSend)) {
                    continue;
                }

                $courseToSend['typeItinerary'] = [
                    'id' => $itineraryUser->getId(),
                    'name' => $itineraryUser->getName(),
                ];

                array_push($itinerary['courses'], $courseToSend);
            }

            usort($itinerary['courses'], function ($a, $b) {
                return $a['new'] < $b['new'];
            });

            if (\count($itinerary['courses']) > 0) {
                array_push($data, $itinerary);
            }
        }

        return $data;
    }

    private function getUserItineraries($user)
    {
        $itineraries = new ArrayCollection();

        $itineraries = $this->addUserItineraries($user, $itineraries);
        $itineraries = $this->addItinerariesThroughFilters($user, $itineraries);
        $itineraries = $itineraries->toArray();

        usort($itineraries, function ($a, $b) {
            return $a->getSort() > $b->getSort();
        });

        return $itineraries;
    }

    private function addUserItineraries($user, ArrayCollection $itineraries)
    {
        foreach ($user->getItineraryUsers() as $itineraryUser) {
            $itinerary = $itineraryUser->getItinerary();
            if (!$itinerary->getId()) {
                continue;
            }
            $itineraries->add($itinerary);
        }

        return $itineraries;
    }

    private function addItinerariesThroughFilters($user, ArrayCollection $itineraries)
    {
        foreach ($user->getFilter() as $filter) {
            foreach ($filter->getItineraries() as $itinerary) {
                if (!$itinerary->getId()) {
                    continue;
                }

                if (!$itineraries->contains($itinerary)) {
                    $differentCategories = $this->getDifferentCategories($itinerary);
                    $countFilters = $this->countMatchingFilters($user, $itinerary);

                    if (\count($differentCategories) <= $countFilters) {
                        $itineraries->add($itinerary);
                    }
                }
            }
        }

        return $itineraries;
    }

    private function getDifferentCategories($itinerary)
    {
        $differentCategories = [];
        foreach ($itinerary->getFilters() as $itineraryFilter) {
            if (!\in_array($itineraryFilter->getFilterCategory(), $differentCategories)) {
                array_push($differentCategories, $itineraryFilter->getFilterCategory());
            }
        }

        return $differentCategories;
    }

    private function countMatchingFilters($user, $itinerary)
    {
        $countFilters = 0;
        foreach ($user->getFilter() as $userFilter) {
            if ($itinerary->getFilters()->contains($userFilter)) {
                ++$countFilters;
            }
        }

        return $countFilters;
    }
}
