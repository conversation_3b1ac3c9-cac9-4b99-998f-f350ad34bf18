<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Chapter;
use App\Entity\TrueOrFalse as TrueOrFalseEntity;
use App\Enum\Games as EnumGameFormula;
use App\Enum\Games as GamesEnum;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class TrueOrFalse extends Game
{
    protected EntityManagerInterface $em;
    protected SettingsService $settings;

    public function __construct(EntityManagerInterface $em, SettingsService $settings)
    {
        $this->em = $em;
        $this->settings = $settings;
        parent::__construct($em);
    }

    private const UPLOADS_PATH = 'app.gameTrueOrFalse_uploads_path';

    public function getQuestions($userCourseChapter): array
    {
        $repository = $this->em->getRepository(TrueOrFalseEntity::class);
        $data = $repository->findBy([
            'chapter' => $userCourseChapter->getChapter()->getId(),
        ]);

        $questions = $this->getFormattedQuestions($data);
        $time = $this->getTotalTime($questions);

        return [
            'questions' => $questions,
            'time' => $time,
        ];
    }

    private function getFormattedQuestions($data): array
    {
        $path = $this->settings->get(GamesEnum::TRUE_OR_FALSE_UPLOADS_PATH) . '/';
        $questions = [];

        foreach ($data as $question) {
            $questions[] = [
                'id' => $question->getId(),
                'text' => $question->getText(),
                'categorized' => false,
                'imageUrl' => $path . $question->getRoute(),
                'time' => $question->getTime(),
                'answers' => [
                    ['id' => 1],
                    ['id' => 2],
                ],
            ];
        }

        return $questions;
    }

    private function getTotalTime($questions)
    {
        $time = 0;
        foreach ($questions as $question) {
            $time += $question['time'];
        }

        return $time;
    }

    public function check($userCourseChapter, $answers): array
    {
        $questionId = isset($answers->questionId) ? $answers->questionId : null;
        $answer = isset($answers->id) && 1 == $answers->id ? 1 : 0;

        if (null == $questionId) {
            return [
                'correct' => false,
            ];
        }

        $repository = $this->em->getRepository(TrueOrFalseEntity::class);
        $trueOrFalse = $repository->find($questionId);

        if (isset($answers->id)) {
            $correct = ($trueOrFalse->getResult() == $answer);
        } else {
            $correct = false;
        }

        return [
            'correct' => $correct,
        ];
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (!isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal']) || !isset($data['totalQuestions'])) {
            return 0;
        }

        $answers = $data['answers'];
        $nAnswers = \count($answers);
        $chapterType = $args->getType();
        $percentageForComplete = $chapterType->getPercentageCompleted();
        $nQuestions = $data['totalQuestions'];
        $nRequiredQuestions = round($nQuestions * $percentageForComplete, 1);
        $rightAnswers = 0;
        $time = 0;
        $maxTime = $data['timeTotal'];
        $nAttempts = \array_key_exists('attempts', $data) && \is_array($data['attempts'])
            ? \count($data['attempts']) : 1;

        if($nAnswers > 0){
            foreach ($answers as $answer) {
                if (isset($answer['correct']) && $answer['correct']) {
                    ++$rightAnswers;
                }
                $time += $answer['time'];
            }
        }

        $completionPercentage = $nQuestions > 0 ? ($rightAnswers / $nQuestions) : 0;
        if ($completionPercentage < $percentageForComplete) {
            return 0;
        }
        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $numerator = $rightAnswers - $nRequiredQuestions;
        $denominator = $nQuestions - $nRequiredQuestions;
        $basePercentage = EnumGameFormula::BASE_HALF + (EnumGameFormula::BASE_QUARTER * ($numerator / $denominator)) + (EnumGameFormula::BASE_QUARTER * ($remainingTime / $maxTime));
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return $attemptsCorrectionPercentage;
    }
}
