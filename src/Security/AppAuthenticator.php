<?php

declare(strict_types=1);

namespace App\Security;

use App\Entity\User;
use App\Repository\UserRepository;
use App\Security\Integrations\Clients\LdapClient;
use App\Security\Integrations\Exceptions\IntegrationLdapException;
use App\V2\Domain\Security\TokenInterface as V2TokenInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Security\Http\Authenticator\AbstractLoginFormAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\CsrfTokenBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\PasswordUpgradeBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\RememberMeBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;
use Symfony\Component\Security\Http\Util\TargetPathTrait;

class AppAuthenticator extends AbstractLoginFormAuthenticator
{
    use TargetPathTrait;
    use AuthenticationLogsTrait;

    public const string LOGIN_ROUTE = 'app_login';
    public const string LDAP_LOGIN_ROUTE = 'app_login_ldap';

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly UrlGeneratorInterface $urlGenerator,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly UserPasswordHasherInterface $passwordEncoder,
        private readonly ParameterBagInterface $params,
        private readonly LdapClient $ldapClient,
        private readonly UserRepository $userRepository,
        private readonly V2TokenInterface $v2TokenInterface,
    ) {
    }

    public function supports(Request $request): bool
    {
        $supported = self::LOGIN_ROUTE === $request->attributes->get('_route')
            || self::LDAP_LOGIN_ROUTE === $request->attributes->get('_route');

        return $supported && $request->isMethod('POST');
    }

    public function authenticate(Request $request): Passport
    {
        $email = $request->request->get('email');
        $password = $request->request->get('password');
        $csrfToken = $request->request->get('_csrf_token');
        $rememberMe = $request->request->get('_remember_me');
        $ref = $request->request->get('ref');

        if (!empty($ref) && ApiLoginAuthenticator::REF_LDAP === $ref) {
            $passport = $this->ldapLogin($email, $password, $csrfToken);
        } else {
            $passport = $this->normalLogin($email, $password, $csrfToken, $rememberMe);
        }

        if ($rememberMe) {
            $passport->addBadge(new RememberMeBadge());
        }

        return $passport;
    }

    private function normalLogin(string $email, string $password, $csrfToken, $rememberMe): Passport
    {
        $passport = new Passport(
            new UserBadge($email),
            new PasswordCredentials($password),
            [new CsrfTokenBadge('authenticate', $csrfToken)],
        );
        $passport->addBadge(
            new PasswordUpgradeBadge($password, $this->entityManager->getRepository(User::class))
        );

        return $passport;
    }

    private function ldapLogin(string $uid, string $password, $csrfToken): Passport
    {
        try {
            if (!$this->ldapClient->isEnabled()) {
                throw new AuthenticationException(
                    'LDAP services not enabled'
                );
            }
            $user = $this->ldapClient->login($uid, $password);
        } catch (IntegrationLdapException $e) {
            throw new AuthenticationException($e->getMessage(), $e->getCode(), $e);
        }

        return new SelfValidatingPassport(
            new UserBadge(
                $user->getEmail(),
                function ($userIdentifier) {
                    // if is email find by email
                    $u = $this->userRepository->findOneBy(['email' => $userIdentifier]);
                    if ($u) {
                        return $u;
                    }

                    // Supports user code
                    $u = $this->userRepository->findOneBy(['code' => $userIdentifier]);
                    if (!$u) {
                        throw new UserNotFoundException();
                    }

                    return $u;
                }
            ),
            [new CsrfTokenBadge('authenticate', $csrfToken)],
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        $email = $request->request->get('email') ?? null;
        if (!empty($email)) {
            $this->generateLog($request, $email, 'SUCCESS', $this->params->get('app.authentication_logs'));
        }

        if ($targetPath = $this->getTargetPath($request->getSession(), $firewallName)) {
            $response = new RedirectResponse($targetPath);
        } else {
            $response = new RedirectResponse($this->urlGenerator->generate('admin'));
        }

        $this->v2TokenInterface->setTokensCookie($response, $token->getUser());

        return $response;
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): Response
    {
        $email = $request->request->get('email') ?? null;
        if (!empty($email)) {
            $this->generateLog($request, $email, 'DENIED', $this->params->get('app.authentication_logs'));
        }

        if ($request->hasSession()) {
            $request->getSession()->set(Security::AUTHENTICATION_ERROR, $exception);
        }

        $url = $this->getLoginUrl($request);

        return new RedirectResponse($url);
    }

    protected function getLoginUrl(Request $request): string
    {
        $isLdap = self::LDAP_LOGIN_ROUTE === $request->attributes->get('_route');

        return $this->urlGenerator->generate($isLdap ? self::LDAP_LOGIN_ROUTE : self::LOGIN_ROUTE);
    }
}
