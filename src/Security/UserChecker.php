<?php

namespace App\Security;

use Symfony\Component\Security\Core\Exception\CustomUserMessageAccountStatusException;
use Symfony\Component\Security\Core\User\UserCheckerInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class User<PERSON>hecker implements UserCheckerInterface
{
    public function checkPreAuth(UserInterface $user)
    {
        if (!$user->getIsActive() || !$user->getValidated()) {
            throw new CustomUserMessageAccountStatusException("User is not active");
        }
    }

    public function checkPostAuth(UserInterface $user)
    {}
}
