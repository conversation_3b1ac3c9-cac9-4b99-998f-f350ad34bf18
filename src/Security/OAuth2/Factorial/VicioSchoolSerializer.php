<?php

namespace App\Security\OAuth2\Factorial;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\User;
use App\Security\OAuth2\BaseClientSerializer;
use Doctrine\ORM\EntityManagerInterface;
use League\OAuth2\Client\Provider\AbstractProvider;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use League\OAuth2\Client\Token\AccessToken;

class VicioSchoolSerializer extends BaseClientSerializer
{
    const FILTER_CATEGORY_PUESTO = "Puesto";
    const FILTER_CATEGORY_WORKPLACE = "Lugar de trabajo";
    const FILTER_CATEGORY_SISTEMA_RETICULAR = "Sistema Reticular";

    const CUSTOM_FIELDS_CATEGORIES = [self::FILTER_CATEGORY_SISTEMA_RETICULAR];

    /**
     * @throws \Exception
     */
    public function serialize(AccessToken $token, User $user)
    {
        $this->writeContentToLogFile($user, "\n-----------------------------------");
        $this->writeContentToLogFile($user, "\nSerialize: " . (new \DateTime())->format('c'));
        $this->writeContentToLogFile($user, "\nUser OAuth2 Token: " . $token->getToken());
        $rf1 = $this->handleContracts($token, $user);
        $rf2 = $this->getFilters($token, $user);
        $remoteFilters = array_merge($rf1, $rf2);

        $localFilters = $user->getLocalFilters();
        $user->setFilters(array_merge($localFilters, $remoteFilters));

        $this->em->persist($user);
        $this->em->flush();
        $this->writeContentToLogFile($user, "\nSerializer completed-----------");
    }

    private function handleContracts(AccessToken $token, User $user): array
    {
        if (!($this->provider instanceof FactorialProvider)) throw new \Exception("Provider must be instance of FactorialProvider");
        $this->logger->info('Handle contracts');
        $this->writeContentToLogFile($user, "\nHandle contracts");
        $user->setRemoteRoles([]);// Clean remote roles
        $remoteFilters = [];
        try {
            $contracts = $this->provider->getReferenceContracts(
                $token,
                [$user->getCode()]
            );

            $this->logger->info('Contracts', $contracts);

            $this->writeContentToLogFile($user, "\nContracts " . json_encode($contracts));

            foreach ($contracts as $contract) {
                // Read all contracts info
                if (!empty($contract['role'])) {// Apply user role
                    $this->applyUserRole($user, $contract['role']);
                }

                $jobTitle = $contract['role'] ?? '';
                if (empty($jobTitle)) {
                    $this->logger->error('Handle contracts: No job_title value provided');
                    continue;
                };
                $filterCategory = $this->em->getRepository(FilterCategory::class)->findOneBy([
                    'name' => self::FILTER_CATEGORY_PUESTO
                ]);
                if (!$filterCategory) {
                    $filterCategory = new FilterCategory();
                    $filterCategory->setName(self::FILTER_CATEGORY_PUESTO);
                    $this->em->persist($filterCategory);
                }
                $filter = $this->em->getRepository(Filter::class)->findOneBy([
                    'name' => $jobTitle,
                    'filterCategory' => $filterCategory
                ]);
                if (!$filter) {
                    $filter = new Filter();
                    $filter->setName($jobTitle)
                        ->setCode($jobTitle)
                        ->setSource(Filter::SOURCE_REMOTE)
                        ->setFilterCategory($filterCategory)
                    ;
                    $this->em->persist($filter);
                }
                $remoteFilters[] = $filter;
            }

            // Flush contracts information (Filter and roles)
            $this->em->flush();
            return $remoteFilters;
        } catch (\Exception $e) {
            $this->logger->error('Failed to get contracts: ' . $e->getMessage(), $e->getTrace());
            $this->writeContentToLogFile($user, "\nFailed to get contracts: " . $e->getMessage());
            return [];
        }
    }

    /**
     * @param AccessToken $token
     * @param $id
     * @return array
     * @throws \Exception
     */
    private function generateCustomCategories(AccessToken $token, $id): array
    {
        if (empty(self::CUSTOM_FIELDS_CATEGORIES)) return [];
        if (!($this->provider instanceof FactorialProvider)) throw new \Exception("Provider must be instance of FactorialProvider");

        try {
            $responseCustomFields = $this->provider->getCustomFields($token, 'employees-questions');
            $this->logger->info('Custom fields', $responseCustomFields);
        } catch (\Exception $e) {
            $this->logger->error('Failed to get custom fields', $e->getTrace());
            return [];
        }

        $customFields = [];
        foreach ($responseCustomFields as $r)
        {
            if (!in_array($r['label'], self::CUSTOM_FIELDS_CATEGORIES)) continue;

            $category = $this->findOrCreateFilterCategory($r['label']);
            if ($r['field_type'] === 'single_choice')
            {
                // Insert posible values as filters
                foreach ($r['choice_options'] as $option)
                {
                    $this->findOrCreateFilter($category, $option['label']);
                }
            }
            $customFields[] = $r;
        }
        $this->em->flush();
        return $customFields;
    }

    private function workplaceFilters(AccessToken $token, User $user): array
    {
        if (!($this->provider instanceof FactorialProvider)) throw new \Exception("Provider must be instance of FactorialProvider");
        try {
            $response = $this->provider->getAllWorkplaces($token, $user->getCode());
        } catch (IdentityProviderException $e) {
            $this->logger->warning('Workplace response: ' . $e->getMessage(), $e->getTrace());
            return [];
        }

        $this->logger->info('Workplaces', $response);
        $this->writeContentToLogFile($user, "\nWorkplaces: " . json_encode($response));

        $category = $this->findOrCreateFilterCategory(self::FILTER_CATEGORY_WORKPLACE);
        $remoteFilters = [];
        foreach ($response as $r)
        {
            $remoteFilters[] = $this->findOrCreateFilter(
                $category,
                $r['name'],
            );
        }
        return $remoteFilters;
    }

    /**
     * @param AccessToken $token
     * @param User $user
     * @return array
     * @throws \Exception
     */
    public function getFilters(AccessToken $token, User $user): array
    {
        $this->writeContentToLogFile($user, "\nHandle filters");
        if (!($this->provider instanceof FactorialProvider)) throw new \Exception("Provider must be instance of FactorialProvider");

        $customFieldsResponse = $this->generateCustomCategories($token, $user->getCode());
        $this->writeContentToLogFile($user, "\nCustom fields: " . json_encode($customFieldsResponse));
        $remoteFilters = [];
        foreach ($customFieldsResponse as $customField)
        {
            /**
             * Request custom field values passing the field id and the user id
             */
            try {
                $resultResponse = $this->provider->getCustomFieldValues($token, $customField['id'], $user->getCode());
                $this->logger->info('Request a custom field', $resultResponse);
            } catch (\Exception $e) {
                $this->logger->error("getCustomFiltersValues: " . $e->getMessage(), $e->getTrace());
                $this->writeContentToLogFile($user, "\nFailed to get custom field values: " . $e->getMessage());
                continue;
            }

            if (empty($resultResponse))
            {
                $this->logger->warning('Empty result for custom field: ' . $customField['label']);
                continue;
            }

            $this->writeContentToLogFile($user, "\nCustom field values: " . json_encode($resultResponse));

            $category = $this->findOrCreateFilterCategory($customField['label']);

            $firstKey = array_key_first($resultResponse);
            $isMulti = is_array($resultResponse[$firstKey]);
            if ($isMulti)
            {
                foreach ($resultResponse as $r)
                {
                    $filterValue = $r['value'];

                    $remoteFilters[] = $this->findOrCreateFilter(
                        $category,
                        $filterValue
                    );
                }
            } else {
                $filterValue = $resultResponse['value'];
                $remoteFilters[] = $this->findOrCreateFilter(
                    $category,
                    $filterValue
                );
            }
        }
        $this->em->flush();

        $workplaceFilters = $this->workplaceFilters($token, $user);
        return array_merge($remoteFilters, $workplaceFilters);
    }
}
