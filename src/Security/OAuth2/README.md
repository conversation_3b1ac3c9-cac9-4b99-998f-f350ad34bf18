# OAuth2 Configuration

## Enable OAuth2
Configure `config/services/oauth2.yaml`
1. Set the variable `OAUTH2_ENABLED` to `true` in the `.env` file.
2. Select the provider OAuth2 is working: `OAUTH2_ACTIVE_PROVIDER` from the list of supported providers
3. Define a client serializer, sometimes fields names between clients is different, so a serializer
    per client is required. The serializer must extend the class `App\Security\OAuth2\BaseClientSerializer`
    to handle client filters.
4. Set the relation between `label|field|value` in `oauth2.filters`
    ```yaml
   oauth2.filters:
      field1orLabelOrValue:
        - 1 # Category ID 1
        - 2 # Category ID 2
        - 3 # Category ID 3
    ```
   The serializer to use must understand how the configuration works
5. Configure the role relation
    ```yaml
     # Define the roles
    oauth2.role.field: role
    # Read the value from oauth2.role.field and compare against oauth2.role.roles
    oauth2.role.roles:
    ROLE_USER:
      - Value 1 # If the user has the value "Value 1" must assign the ROLE_USER into remote_roles
    ROLE_ADMIN:
      - "TI Admin" # If the user has the value "TI Admin" must assign the ROLE_ADMIN into remote_roles
      ```
   Role configuration must be defined per client
6. An authenticator is required: The authenticator must extend the class: `App\Security\OAuth2\OAuth2BaseAuthenticator`
    and configure the authenticator in: `config/packages/security.yaml`
    ```yaml
   security:
    firewalls:
      main:
        custom_authenticators:
          - NewAuthenticator
    ```

When the client is configuring OAuth2 support for EasyLearning application, the redirection url is:
`https://<hostname>/oauth2/authenticate`

## Supported provider
1. Factorial: ```App\Security\OAuth2\Factorial\FactorialProvider```
    
    Requires the following env variables, both provided by the client:
    1. `OAUTH2_FACTORIAL_CLIENT_ID`
    2. `OAUTH2_FACTORIAL_CLIENT_SECRET`
    
    Required the configuration of an authenticator, in Factorial case, the authenticator is located at:
    `App\Security\OAuth2\Factorial\FactorialAuthenticator`

### New Providers
For new providers, follow documentation in: https://github.com/knpuniversity/oauth2-client-bundle#configuration

