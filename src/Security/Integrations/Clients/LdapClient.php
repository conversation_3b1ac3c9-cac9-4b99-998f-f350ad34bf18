<?php

declare(strict_types=1);

namespace App\Security\Integrations\Clients;

use App\Entity\User;
use App\Security\Integrations\Exceptions\IntegrationException;
use App\Security\Integrations\Exceptions\IntegrationLdapException;
use App\Security\Integrations\Exceptions\IntegrationMappingException;
use App\Security\Integrations\IntegrationLdapService;
use App\Security\Integrations\IntegrationMappingResult;
use App\Security\Integrations\IntegrationMappingService;
use App\Security\Integrations\IntegrationService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class LdapClient extends BaseClient implements MappingResultModifierInterface
{
    public const NAME = 'LdapClient';
    private IntegrationLdapService $integrationLdapService;

    private int $integrationGroupId = 0;

    public function __construct(HttpClientInterface $httpClient, IntegrationMappingService $integrationMappingService, IntegrationService $integrationService, EntityManagerInterface $em, SettingsService $settingsService, LoggerInterface $logger, IntegrationLdapService $integrationLdapService)
    {
        parent::__construct($httpClient, $integrationMappingService, $integrationService, $em, $settingsService, $logger);
        $this->integrationLdapService = $integrationLdapService;
        $this->findIntegrationGroupId();
    }

    /**
     * @throws IntegrationLdapException
     */
    public function isEnabled(): bool
    {
        return $this->integrationLdapService->isEnabled();
    }

    public function getClientName(): string
    {
        return self::NAME;
    }

    public function getHeaders(): array
    {
        return [];
    }

    public function checkResponse(ResponseInterface $response, $data): void
    {
    }

    private function findIntegrationGroupId(): void
    {
        try {
            $configuration = $this->integrationLdapService->getLdapConfiguration();
            $this->integrationGroupId = empty($configuration['mappingGroupId']) ? 0 : $configuration['mappingGroupId'];
        } catch (IntegrationLdapException $e) {
        }
    }

    public function getIntegrationGroupId(): int
    {
        return $this->integrationGroupId;
    }

    /**
     * @throws IntegrationLdapException
     */
    private function setMappingGroupId(array $params = [])
    {
        if (!empty($params) && !empty($params['mappingGroupId'])) {
            $this->integrationGroupId = $params['mappingGroupId'];
        } else {
            $this->integrationGroupId = $this->integrationLdapService->getLdapConfiguration()['mappingGroupId'] ?? 1;
        }
    }

    /**
     * @throws IntegrationLdapException
     * @throws IntegrationException
     * @throws IntegrationMappingException
     */
    public function getUsers(array $params = []): array
    {
        $this->setMappingGroupId($params);
        $result = $this->integrationLdapService->getUsers($params);
        $mapped = $this->mapResults($result, true, $this);
        return [];
    }

    /**
     * @throws IntegrationLdapException
     * @throws IntegrationException
     * @throws IntegrationMappingException
     */
    public function getUser(array $params)
    {
        $this->setMappingGroupId($params);
        $result = $this->integrationLdapService->getUser($params['username'] ?? '', $params['password'] ?? '', $params);
        $mapped = $this->mapResults($result, true, $this);
    }

    public function isUserActive(array $data): bool
    {
        return true;
    }

    public function handleMappingResult(IntegrationMappingResult &$mappingResult, array $data): void
    {
        $user = $mappingResult->getUser();
        $user->setIsActive(true);
        $mappingResult->setUser($user);
    }

    /**
     * @throws IntegrationLdapException
     */
    public function login(string $username, string $password): User
    {
        $result = $this->integrationLdapService->getUser($username, $password);
        try {
            $mappingResult = $this->mapResults($result, true, $this);
        } catch (IntegrationException|IntegrationMappingException $e) {
            throw IntegrationLdapException::userMappingError($e);
        }

        if (0 === \count($mappingResult)) {
            throw IntegrationLdapException::noUsersFound();
        }

        $user = $mappingResult[0]->getUser();
        if (!$user) {
            throw IntegrationLdapException::userMappingError();
        }

        return $user;
    }

    public function __toString(): string
    {
        return self::NAME;
    }
}
