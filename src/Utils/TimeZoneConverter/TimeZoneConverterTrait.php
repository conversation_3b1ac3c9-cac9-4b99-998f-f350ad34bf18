<?php

declare(strict_types=1);

namespace App\Utils\TimeZoneConverter;

trait TimeZoneConverterTrait
{
    public function toUtc(array $args = [])
    {
        $availableVariables = get_object_vars($this);
        if (!\array_key_exists('timezone', $availableVariables)) {
            return null;
        }// No timezone defined, saved using the server timezone, avoid conversion

        $utcTimeZone = new \DateTimeZone('UTC');
        foreach ($availableVariables as $key => $v) {
            $variable = $this->{$key};
            if ($variable instanceof \DateTime || $variable instanceof \DateTimeImmutable) {
                if (0 === $variable->getOffset()) {
                    continue;
                }// Already in UTC 0, continue
                /*
                 * Not in UTC0 change values
                 * e.g. If the value is created as new DateTime(); // Is using the timezone from the server
                 * e.g. If the value is created as new DateTime($value); // Is using the timezone from the server
                 * e.g. If the value is created as new DateTime(<'now'|$value>, new DateTimeZone('value')) A timezone has been defined
                 * Is required to convert to UTC
                 */
                $this->{$key} = $variable->setTimezone($utcTimeZone);
            }
        }
    }

    /**
     * Convert all DateTime objects from UTC timezone to timezone defined in the object.
     *
     * @return void
     *
     * @throws \Exception
     */
    public function fromUtc(array $args = [])
    {
        $availableVariables = get_object_vars($this);
        if (!\array_key_exists('timezone', $availableVariables)) {
            return null;
        }// No timezone defined, saved using the server timezone, avoid conversion
        if (empty($this->timezone)) {
            return null;
        }
        $utcTimeZone = new \DateTimeZone('UTC');
        $currentTimezone = new \DateTimeZone($this->timezone);
        foreach ($availableVariables as $key => $v) {
            $variable = $this->{$key};
            if ($variable instanceof \DateTime || $variable instanceof \DateTimeImmutable) {
                $variable = new \DateTimeImmutable($variable->format('Y-m-d H:i:s'), $utcTimeZone);
                $this->{$key} = $variable->setTimezone($currentTimezone);
            }
        }
    }
}
