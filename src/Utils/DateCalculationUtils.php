<?php

declare(strict_types=1);

namespace App\Utils;

class DateCalculationUtils
{
    public static function calculateDaysDifference(\DateTime $startDate, \DateTime $finishDate): int
    {
        $interval = $finishDate->diff($startDate);

        return $interval->days;
    }

    public static function calculatePercentageProgress(\DateTimeInterface $startDate, \DateTimeInterface $finishDate): int
    {
        $now = new \DateTime();

        if ($now > $finishDate) {
            return 100;
        }

        $totalSeconds = ($finishDate->getTimestamp() - $startDate->getTimestamp()) + 24 * 60 * 60;
        $passedSeconds = ($now->getTimestamp() - $startDate->getTimestamp());

        if ($passedSeconds < 0) {
            $passedSeconds = 0;
        } elseif ($passedSeconds > $totalSeconds) {
            $passedSeconds = $totalSeconds;
        }

        $percentage = ($passedSeconds / $totalSeconds) * 100;

        return intval(round($percentage));
    }

    public static function isEndingSoon(\DateTimeInterface $startDate, \DateTimeInterface $finishDate, int $desiredPercentage = 15): bool
    {
        $now = new \DateTime();
        $totalSeconds = ($finishDate->getTimestamp() - $startDate->getTimestamp()) + 24 * 60 * 60;
        $remainingSeconds = ($finishDate->getTimestamp() - $now->getTimestamp());

        if ($remainingSeconds <= 0) {
            return true;
        }

        $percentageRemaining = ($remainingSeconds / $totalSeconds) * 100;

        return $percentageRemaining <= $desiredPercentage;
    }

    public static function getStateOfDateRange($startAt, $endAt)
    {
        $now = new \DateTime();

        if ($startAt instanceof \DateTimeImmutable) {
            $startAt = \DateTime::createFromImmutable($startAt);
            $startAt->format('Y-m-d H:i:s');
            // $startAt->modify('-2 hours');
        }
        if ($endAt instanceof \DateTimeImmutable) {
            $endAt = \DateTime::createFromImmutable($endAt);
        }

        if ($now < $startAt) {
            return 'NOT_STARTED';
        } elseif ($now >= $startAt && $now <= $endAt) {
            return 'IN_PROCESS';
        } else {
            return 'FINISHED';
        }
    }

    public static function calculateDateDifference(\DateTime $startDate, \DateTime $endDate, $textMessage = [])
    {
        $difference = $startDate->diff($endDate);
        $textMessage = $textMessage ? $textMessage : [
            'days' => 'días',
            'hours' => 'horas',
            'minutes' => 'minutos'
        ];

        $message = '';

        if ($difference->days > 0) {
            $message .= $difference->format('%a ' . $textMessage['days'] . ' ');
        }

        if ($difference->h > 0) {
            $message .= $difference->format('%h ' . $textMessage['hours'] . ' ');
        }

        if ($difference->i > 0) {
            $message .= $difference->format('%i ' . $textMessage['minutes'] . '');
        }

        $message = trim($message);

        if (empty($message)) {
            $message .= ' 0 ' . $textMessage['days'];
            $message .= ' 0 ' . $textMessage['hours'];
            $message .= ' 0 ' . $textMessage['minutes'];

            $message = trim($message);

            return $message;
        }

        return $message;
    }
}
