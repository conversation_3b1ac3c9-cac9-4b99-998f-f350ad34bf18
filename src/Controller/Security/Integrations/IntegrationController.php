<?php

declare(strict_types=1);

namespace App\Controller\Security\Integrations;

use App\Admin\Traits\VueAppDefaultConfiguration;
use App\Entity\FilterCategory;
use App\Entity\User;
use App\Security\Integrations\Clients\IntegrationClients;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/admin/api/v1/integrations")
 */
class IntegrationController extends AbstractController
{
    use VueAppDefaultConfiguration;

    private ParameterBagInterface $params;
    private AdminContextProvider $context;
    private SettingsService $settings;
    private EntityManagerInterface $em;
    private JWTManager $JWTManager;
    private IntegrationClients $integrationClients;

    public function __construct(
        ParameterBagInterface $params,
        AdminContextProvider $context,
        EntityManagerInterface $em,
        JWTManager $JWTManager,
        SettingsService $settings,
        IntegrationClients $integrationClients
    ) {
        $this->params = $params;
        $this->context = $context;
        $this->em = $em;
        $this->JWTManager = $JWTManager;
        $this->settings = $settings;
        $this->integrationClients = $integrationClients;
    }

    /**
     * @Route("/", methods={"GET"}, name="integrations-app-index")
     */
    public function index(Request $request): Response
    {
        $entities = $this->params->get('integrations.entity.allowed');
        $allowedEntities = [];
        foreach ($entities as $entity => $namespace) {
            $allowedEntities[] = $entity;
        }

        $enabledClients = $this->integrationClients->getEnabledClients();
        $tags = [];
        foreach ($enabledClients as $client) {
            $filterTags = $client['filter_tags'] ?? [];
            $tags = array_merge($tags, $filterTags);
        }

        $categories = $this->em->getRepository(FilterCategory::class)->findAll();
        $keyValueStore = KeyValueStore::new([]);
        $keyValueStore = $this->configureAppResponseParameters(
            $keyValueStore,
            $this->settings,
            $this->context,
            $this->JWTManager,
            [],
            [
                'baseUrl' => $request->getSchemeAndHttpHost(),
                'userRoles' => $this->getUserRoles(),
                'categories' => array_map(function ($category) {
                    return ['id' => $category->getId(), 'name' => $category->getName()];
                }, $categories),
                'entities' => $allowedEntities,
                'fields' => $this->params->get('integrations.entity.fields'),
                'identifiers' => $this->params->get('integrations.entity.identifiers'),
                'filterTags' => $tags,
            ]
        );

        return $this->render('security/integrations.html.twig', $keyValueStore->all());
    }

    private function getUserRoles(): array
    {
        $userRoles = User::ROLES;
        unset($userRoles[User::ROLE_SUPER_ADMIN]);
        unset($userRoles[User::ROLE_USER]);

        return $userRoles;
    }
}
