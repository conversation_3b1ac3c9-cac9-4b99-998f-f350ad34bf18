<?php

namespace App\Controller\Admin;

use App\Admin\Field\VichImageField;
use App\Admin\Traits\SerializerTrait;
use App\Entity\Challenge;
use App\Entity\ChallengeQuestions;
use App\Entity\ChallengeUser;
use App\Entity\ProfessionalCategory;
use App\Entity\User;
use App\Repository\ChallengeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\AST\Join;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ImageField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\LocaleField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\CrudUrlGenerator;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\ParamConverter;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Routing\Annotation\Route;
use App\Entity\AnnouncementUser;
use App\Service\SettingsService;
use Doctrine\ORM\Query\Expr;

class ChallengeUserCrudController extends AbstractCrudController
{
    use SerializerTrait;

    private $settings;
    private $em;
    private $context;
    private $requestStack;

    public function __construct(SettingsService $settings,RequestStack $requestStack, EntityManagerInterface $em, AdminContextProvider $context, LoggerInterface $logger)
    {
        $this->settings       = $settings;
        $this->em           = $em;
        $this->context      = $context;
        $this->requestStack = $requestStack;
    }

    public static function getEntityFqcn(): string
    {
        return ChallengeUser::class;
    }

    public function configureFields(string $pageName): iterable
    {
        $panel1 = FormField::addPanel('Basic information');
        $challenge =  AssociationField::new('challenge');
        $user =  AssociationField::new('user');


//        if($this->settings->get('app.multilingual') == true){
//            $locale = LocaleField::new('locale');
//        }

        if($this->settings->get('app.setCoursePoints') == true){
            $points = IntegerField::new('points');
        }

        if (Crud::PAGE_INDEX === $pageName) {
            $fields = [$challenge, $user];

//            if(isset($locale)){
//                $translation = TextField::new('translation');
//                array_splice($fields, 4, 0, [$locale]);
//                array_splice($fields, 9, 0, [$translation]);
//            }

            return $fields;
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            return  [$challenge, $user];
        } elseif (Crud::PAGE_NEW === $pageName || Crud::PAGE_EDIT) {

            $pointsPosition = 4;

            $fields = [$panel1, $challenge, $user];

//            if(isset($locale)) {
//                if(Crud::PAGE_NEW === $pageName) $locale->setFormTypeOptionIfNotSet('data', $this->settings->get('app.defaultLanguage'));
//                array_splice($fields, 2, 0, [$locale]);
//                $pointsPosition++;
//            }

            if(isset($points)) array_splice($fields, $pointsPosition, 0, [$points]);

            return $fields;
        }
    }

   /* public function configureActions(Actions $actions): Actions
    {
        $summonUsers = Action::new('summonUsers','summonUsers')
            ->linkToCrudAction('admin_challenge_summonusers');

        return $actions
            ->add(Crud::PAGE_DETAIL, $summonUsers);
    }*/

    public function createEntity (string $entityFqc)
    {
        $challengeUser   = new ChallengeUser();
        $challengeId = $this->requestStack->getCurrentRequest()->get('challenge_id');

        if ($challengeId)
        {
            $challengeRepository = $this->em->getRepository(Challenge::class);
            $challenge           = $challengeRepository->find($challengeId);
            $challengeUser->setChallenge($challenge);
        }

        return $challengeUser;
    }

    /**
     * @Route ("/admin/challengeuser/users", name="admin_challenge_users")
     * @return Response
     */
    public function getUsersNotInChallenge(Request $request)
    {
        $params = json_decode($request->getContent(), true);
        $challengeRepository = $this->em->getRepository(Challenge::class);
        $challenge = $challengeRepository->find($params['value']);

        $userRepository = $this->em->getRepository(User::class);
        $users = $userRepository->findAllUsersNotInChallenge($challenge);

        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => [
                'users' => $users,
            ],
        ];

        return $this->sendResponse($response, ['groups' => ['challenge_users']]);
    }

    /**
     * @Route ("/admin/challengeuser/summonusers", name="admin_challenge_summonusers")
     * @return Response
     */
    public function summonUsers (Request $request)
    {
        $challengeId = $request->get('challenge_id');
        $challengeRepository = $this->em->getRepository(Challenge::class);

        $challenge = $challengeRepository->find($challengeId);
        return $this->render('admin/challenge/users/summonUsers.html.twig',['challenge' => $challenge]);
    }

    /**
     * @Route ("/admin/challengeuser/{id}/called", name="admin_challenge_calleduser")
     * @param Challenge $challenge
     * @return Response
     */
    public function calledUsers (Challenge $challenge)
    {
        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'called' => $challenge->getChallengeUsers()
            ]
        ];

        return $this->sendResponse($response, array('groups' => array('challenge_userscalled')));
    }

    /**
     * @Route ("/admin/challengeusers/{id}/search", name="admin_challenge_searchusers")
     * @param Challenge $challenge
     * @param Request $request
     * @return Response
     */
    public function search(Challenge $challenge, Request $request)
    {
        $content = json_decode($request->getContent());

        $qb = $this->em->createQueryBuilder();
        $query = $qb->select('u')
            ->from(User::class, 'u');

        if(isset($content->q)) {
            $query = $query
                ->where(
                    $qb->expr()->like('u.email', ':q')
                )
                ->setParameter('q', '%'. $content->q .'%');
        }

        $challengeAnnouncement = $challenge->getAnnouncement();

        if($challengeAnnouncement) {
           $query = $query->leftjoin(AnnouncementUser::class, 'au', Expr\Join::WITH,'au.user = u.id')
                ->andWhere('au.announcement=:announcement')
                ->setParameter('announcement', $challengeAnnouncement);
        }

        $results = $query
            ->getQuery()
            ->getResult();

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'results' => $results
            ]
        ];

        return $this->sendResponse($response, array('groups' => array('announcement')));
    }

    /**
     * @Route ("/admin/challengeusers/{challenge}/call/{user}", name="admin_challenge_calluser")
     * @param Challenge $challenge
     * @return Response
     */
    public function call(Challenge $challenge, User $user)
    {
        $called = $this->em->getRepository(ChallengeUser::class)->findOneBy(
            [
                'challenge'     => $challenge,
                'user'          => $user
            ]
        );

        if($called){
            $response = [
                'status' => Response::HTTP_CONFLICT,
                'error' => true,
                'message' => 'Error: User already called!',
            ];
        }
        else{
            $called = new ChallengeUser();
            $called->setChallenge($challenge);
            $called->setUser($user);

            $challenge->addChallengeUser($called);
            $this->em->flush();

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/challengeusers/{challenge}/uncall/{user}", name="admin_challenge_uncalluser")
     * @return Response
     */
    public function unCall(Challenge $challenge, ChallengeUser $user)
    {

        if($challenge->getChallengeUsers()->contains($user)){
            $this->em->remove($user);
            $this->em->flush();

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false
            ];
        }
        else{
            $response = [
                'status' => Response::HTTP_CONFLICT,
                'error' => true,
                'message' => 'Error: User hasn\'t be called to this announcement!',
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/announcements/{challenge}/notify/{user}", name="admin_challenge_notifyuser")
     * @param Challenge $challenge
     * @param ChallengeUser $user
     * @param MailerInterface $mailer
     * @return Response
     * @throws Exception
     */
    public function notify(Challenge $challenge, ChallengeUser $user, MailerInterface $mailer)
    {
        if($challenge->getChallengeUsers()->contains($user)){

            if(is_null($user->getNotified())){

                try {
                    $email = (new Email())
                        ->from('<EMAIL>')
                        ->to('<EMAIL>')
                        //->cc('<EMAIL>')
                        //->bcc('<EMAIL>')
                        //->replyTo('<EMAIL>')
                        //->priority(Email::PRIORITY_HIGH)
                        ->subject('Time for Symfony Mailer!')
                        ->html('<p>See Twig integration for better HTML integration!</p>');

                    $mailer->send($email);

                    $user->setNotified(new \DateTime());
                    $this->em->flush();

                    $response = [
                        'status' => Response::HTTP_OK,
                        'error' => false
                    ];
                } catch (TransportExceptionInterface $e) {
                    $response = [
                        'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                        'error' => true,
                        'message' => 'Error: Notification could not be sent!',
                    ];
                }

            }
            else {
                $response = [
                    'status' => Response::HTTP_CONFLICT,
                    'error' => true,
                    'message' => 'Error: The user has already been notified!',
                ];
            }

        }
        else{
            $response = [
                'status' => Response::HTTP_CONFLICT,
                'error' => true,
                'message' => 'Error: User hasn\'t be called to this announcement!',
            ];
        }

        return $this->sendResponse($response);
    }
}
