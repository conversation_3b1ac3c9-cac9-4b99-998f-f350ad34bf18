<?php

namespace App\Controller\Admin;

use App\Admin\Traits\FilterValuesTrait;
use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\VueAppDefaultConfiguration;
use App\Entity\Survey;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class SettingCatalogCrudController extends AbstractCrudController
{
    use VueAppDefaultConfiguration;
    use SerializerTrait;
    use FilterValuesTrait;

    private EntityManagerInterface $em;
    protected TranslatorInterface $translator;
    private SettingsService $settings;
    private AdminContextProvider $context;
    private JWTManager $JWTManager;

    public function __construct(
        EntityManagerInterface $em,
        TranslatorInterface    $translator,
        SettingsService  $settings,
        AdminContextProvider   $context,
        JWTManager             $JWTManager
    )
    {
        $this->em = $em;
        $this->translator = $translator;
        $this->settings = $settings;
        $this->context = $context;
        $this->JWTManager = $JWTManager;
    }

    public static function getEntityFqcn(): string
    {
        return Survey::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud->overrideTemplate('crud/index', 'admin/settings/catalog/app.html.twig');
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_INDEX ===  $responseParameters->get('pageName')) {
            $this->configureAppResponseParameters(
                $responseParameters,
                $this->settings,
                $this->context,
                $this->JWTManager,
                [],
            );
        }
        return $responseParameters;
    }
}
