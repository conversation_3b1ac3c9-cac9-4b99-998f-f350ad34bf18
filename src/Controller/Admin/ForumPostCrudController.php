<?php

namespace App\Controller\Admin;

use App\Admin\Field\VichImageField;
use App\Admin\Traits\SerializerTrait;
use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\ForumPost;
use App\Entity\User;
use App\Repository\AnnouncementRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FieldCollection;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FilterCollection;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\SearchDto;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\Field;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ImageField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\LocaleField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextareaField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Filter\ChoiceFilter;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Contracts\Translation\TranslatorInterface;


class ForumPostCrudController extends AbstractCrudController
{
    use SerializerTrait;

    private $settings;
    private $logger;
    private $requestStack;
    private $em;
    private $context;
    private $translator;


    public function __construct (SettingsService $settings, LoggerInterface $logger, RequestStack $requestStack, EntityManagerInterface $em, AdminContextProvider $context, TranslatorInterface $translator)
    {
        $this->settings       = $settings;
        $this->logger       = $logger;
        $this->requestStack = $requestStack;
        $this->em           = $em;
        $this->context      = $context;
        $this->translator   = $translator;
    }

    public function configureCrud (Crud $crud): Crud
    {
        return $crud
            ->overrideTemplate('crud/detail', 'admin/forum/detail.html.twig');
    }

    public static function getEntityFqcn(): string
    {
        return ForumPost::class;
    }

    /*
    public function configureFields(string $pageName): iterable
    {
        return [
            IdField::new('id'),
            TextField::new('title'),
            TextEditorField::new('description'),
        ];
    }
    */

    /**
     * @param SearchDto $searchDto
     * @param EntityDto $entityDto
     * @param FieldCollection $fields
     * @param FilterCollection $filters
     * @return QueryBuilder
     */
    public function createIndexQueryBuilder (SearchDto $searchDto, EntityDto $entityDto, FieldCollection $fields, FilterCollection $filters): QueryBuilder
    {
        $response = parent::createIndexQueryBuilder($searchDto, $entityDto, $fields, $filters);
        $response->andWhere("entity.parent IS NULL");

        return $response;
    }

    public function configureFields(string $pageName): iterable
    {
        $user                 = AssociationField::new('user');
        $course               = AssociationField::new('course');
        $announcement         = AssociationField::new('announcement');
        $parent               = AssociationField::new('parent')->setLabel('Hilo');
        $title                = TextField::new('title');
        $message              = TextareaField::new('message');
        $createdAt            = DateTimeField::new('createdAt');

        $thread = TextField::new('thread')->setLabel('Hilo');

        if (Crud::PAGE_INDEX === $pageName)
        {
            return [$user, $course,$announcement, $thread, $message, $createdAt];
        }
        else if (Crud::PAGE_DETAIL === $pageName)
        {
            return [$user, $course,$announcement, $thread, $message, $createdAt];
        }
        else if (Crud::PAGE_NEW === $pageName)
        {
            return [$course, $announcement, $title, $message];
        }
        else if (Crud::PAGE_EDIT === $pageName)
        {
            return [$title, $message];
        }
    }

    public function configureActions (Actions $actions): Actions
    {
        return $actions
            //->remove(Crud::PAGE_INDEX, Action::EDIT)
            //->remove(Crud::PAGE_INDEX, Action::NEW)
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

    public function persistEntity (EntityManagerInterface $entityManager, $entityInstance): void
    {
        if(!$entityInstance->getUser())
        {
            $entityInstance->setUser($this->getUser());
        }

        parent::persistEntity($entityManager, $entityInstance);
    }

    public function configureFilters(Filters $filters): Filters
    {
        $filters
            ->add('course');

        return $filters;
    }

    public function configureResponseParameters (KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName'))
        {
            $threadRepository = $this->em->getRepository(ForumPost::class);

            $entity          = $this->context->getContext()->getEntity();
            $thread          = $threadRepository->find($entity->getPrimaryKeyValue());

            $responseParameters->set('thread', $thread);
        }
        return $responseParameters;
    }

    /**
     * @Route("/admin/forum/postcomment", name="admin_forum_postcomment")
     * @param Request $request
     * @return Response
     */
    public function postComment (Request $request): Response
    {
        $request = json_decode($request->getContent());

        $courseRepository = $this->em->getRepository(Course::class);
        $forumPostRepository = $this->em->getRepository(ForumPost::class);

        $course = $courseRepository->find($request->course_id);

        if (!$course)
        {
            return $this->respondNotFound();
        }

        if (!$course->getActive() || $course->getDeletedAt())
        {
            $status = Response::HTTP_NOT_FOUND;
            $error  = true;
            $data   = 'Course deleted or inactive. Access not allowed';
        }
        else
        {
            $parent = null;
            if ($request->forum_post_id)
            {
                $parent = $forumPostRepository->find($request->forum_post_id);
            }

            $forumPost = new ForumPost();
            $forumPost
                ->setUser($this->getUser())
                ->setCourse($course)
                ->setTitle($request->title)
                ->setMessage($request->message)
                ->setCreatedAt(new \DateTime());

            if ($parent)
            {
                $forumPost->setParent($parent);
                $parent->setLastResponseAt($forumPost->getCreatedAt());
                $this->em->persist($parent);
            }

            if (isset($request->response_id)){
                $responseId = $forumPostRepository->find($request->response_id);
                $forumPost->setResponse($responseId);
            }

            $this->em->persist($forumPost);
            $this->em->flush();

            $status = Response::HTTP_OK;
            $error  = false;
            $data   = $forumPost;
        }

        $response = [
            'status'    => $status,
            'error'     => $error,
            'payload'   => $data,
        ];

        return $this->sendResponse($response, ['groups' => ['thread', 'forum']]);
    }

    /**
     * @Route ("/admin/forum/{id}", name="admin_forum_getPosts", methods={"GET"})
     * @param ForumPost $forumPost
     * @return Response
     */
    public function getForumPost(ForumPost $forumPost)
    {
        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => $forumPost,
        ];
        return $this->sendResponse($response, [
            'groups' => ['forum', 'thread'],
            ObjectNormalizer::ENABLE_MAX_DEPTH => true,
            AbstractNormalizer::CIRCULAR_REFERENCE_LIMIT  => 2,
        ]);
    }

    	/**
	 * @Route("/admin/new-forum/{announcement}", name="new_forum", methods={"GET","POST"})
	 */
	public function newForum(Request $request, Announcement $announcement, AnnouncementRepository $announcementRepository)
	{
		try {
			$title = $request->get('title');
			$message = $request->get('message');

			$forumPost = new ForumPost();
			$forumPost->setAnnouncement($announcement);
			$forumPost->setUser($this->getUser());
			$forumPost->setCourse($announcement->getCourse());
			$forumPost->setTitle($title);
			$forumPost->setMessage($message);

			$this->em->persist($forumPost);
			$this->em->flush();

			$this->addFlash('success', $this->translator->trans('save_success'));

		} catch (Exception $e) {
			$this->addFlash('danger',  $this->translator->trans('error_success') . $e);
		}

		return $this->redirect(
			$this->get(AdminUrlGenerator::class)
				->setController(AnnouncementCrudController::class)
				->set('tab', 'foro')
				->setEntityId($announcement->getId())
				->setAction(Action::DETAIL)->generateUrl()
		);
	}

	/**
	 * @Route("/admin/edit-forum/announcement/{announcement}", name="edit_forum", methods={"GET","POST"})
	 */
	public function editForum(Request $request, Announcement  $announcement)
	{
		try {
			$title = $request->get('title');
			$message = $request->get('message');
			$idForum = $request->get('idForum');

			$forumPost = $this->em->getRepository(ForumPost::class)->find($idForum);

			$forumPost->setTitle($title);
			$forumPost->setMessage($message);

			$this->em->persist($forumPost);
			$this->em->flush();

			$this->addFlash('success', $this->translator->trans('save_success'));

		} catch (Exception $e) {
			$this->addFlash('danger',  $this->translator->trans('error_success') . $e);
		}

		return $this->redirect(
			$this->get(AdminUrlGenerator::class)
				->setController(AnnouncementCrudController::class)
				->set('tab', 'foro')
				->setEntityId($announcement->getId())
				->setAction(Action::DETAIL)->generateUrl()
		);
	}

    /**
     * @Rest\Delete("/admin/forum/{id}", requirements={"id"="\d+"})
     * @param ForumPost $forumPost
     * @return Response
     */
    public function restDeleteForum(ForumPost $forumPost): Response {
        $this->em->remove($forumPost);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'Deleted'
        ]);
    }

    /**
     * @Rest\Put("/admin/forum/{id}", requirements={"id"="\d+"})
     * @param Request $request
     * @param ForumPost $forumPost
     * @return Response
     */
    public function restUpdateForum(Request $request, ForumPost $forumPost): Response {
        $content = json_decode($request->getContent(), true);
        if (!empty($content['title'])) {
            $forumPost->setTitle($content['title']);
        }
        if (!empty($content['message'])) {
            $forumPost->setMessage($content['message']);
        }

        $this->em->flush($forumPost);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'id' => $forumPost->getId(),
                'title' => $forumPost->getTitle(),
                'message' => $forumPost->getMessage()
            ]
        ]);
    }

    /**
     * @Rest\Post("/admin/forum/{announcement}", requirements={"announcement"="\d+"})
     * @param Request $request
     * @param Announcement $announcement
     * @return Response
     */
    public function restNewForum(Request $request, Announcement $announcement): Response {
        try {
            $title = $request->get('title');
            $message = $request->get('message');

            if (empty($title)) return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Title is required'
            ]);

            if (empty($message)) return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Message is required'
            ]);

            $forum = new ForumPost();
            $forum->setAnnouncement($announcement)
                ->setCourse($announcement->getCourse())
                ->setTitle($title)
                ->setMessage($message)
                ->setUser($this->getUser())
            ;

            $this->em->persist($forum);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false,
                'data' => $this->em->getRepository(ForumPost::class)->createQueryBuilder('fp')
                    ->select('fp.id', 'fp.title', 'fp.message', 'fp.lastResponseAt', 'fp.createdAt')
                    ->addSelect('u.id as user_id', 'u.firstName', 'u.lastName', 'u.avatar')
                    ->join('fp.user', 'u')
                    ->andWhere('fp =:forum')
                    ->setParameter('forum', $forum)
                    ->getQuery()
                    ->getSingleResult()
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage()
            ]);
        }
    }

    /**
     * @Rest\Get("/admin/forum/announcement/{id}", requirements={"id"="\d+"})
     * @param Announcement $announcement
     * @return Response
     */
    public function getAnnouncementForums(Announcement $announcement): Response {
	    $qb = $this->em->getRepository(ForumPost::class)->createQueryBuilder('fp')
            ->select('fp.id', 'fp.title', 'fp.message', 'fp.lastResponseAt', 'fp.createdAt')
            ->addSelect('u.id as user_id', 'u.firstName', 'u.lastName', 'u.avatar')
            ->join('fp.user', 'u');

	    $forums = (clone $qb)->where('fp.announcement =:announcement')
		    ->setParameter('announcement', $announcement)
		    ->getQuery()->getResult();

	    $data = [];
		foreach ($forums as $index => $forum) {
			$param = "responseID$index";
			$forum['replies'] = (clone $qb)->andWhere("fp.response =:$param")
					->setParameter($param, $forum['id'])
					->getQuery()->getResult() ?? [];
			$data[] = $forum;
		}

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * Infinite loading
     * @Rest\Get("/admin/forum-post/{id}/{page}", requirements={"id"="\d+", "page"="\d+"})
     * @param ForumPost $forumPost
     * @param int $page
     * @return Response
     */
    public function getAnnouncementForumInfo(ForumPost $forumPost, int $page = 1): Response {
        $qb = $this->em->getRepository(ForumPost::class)
            ->createQueryBuilder('fp')
            ->select('fp.id', 'fp.title', 'fp.message', 'fp.lastResponseAt', 'fp.createdAt', 'response.id AS parent_id')
            ->addSelect('u.id as user_id', 'u.firstName', 'u.lastName', 'u.avatar')
            ->leftJoin('fp.response', 'response')
            ->join('fp.user', 'u')
            ->where('fp.parent =:parent')
            ->setParameter('parent', $forumPost)
            ->orderBy('fp.createdAt', 'ASC')
        ;
        $countQuery = (clone $qb)->select('COUNT(fp.id) as total')->getQuery()->getSingleScalarResult();
        $messages = (clone $qb)->andWhere('fp.response IS NULL')->getQuery()->getResult();

		$data = [];
		foreach ($messages as $index => $message) {
			$param = "responseID$index";
			$message['replies'] = (clone $qb)->andWhere("fp.response =:$param")
				->setParameter($param, $message['id'])
				->getQuery()->getResult() ?? [];
			$data[] = $message;
		}

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'items' => $data,
                'total-items' => (int)$countQuery
            ]
        ]);
    }

    /**
     * @Rest\Post("/admin/forum/{forum}/save-message", requirements={"forum"="\d+"})
     * @param Request $request
     * @param ForumPost $forum
     * @return Response
     */
    public function saveNewForumReply(Request $request, ForumPost $forum): Response {
        try {
            $forumPostRepository = $this->em->getRepository(ForumPost::class);
            $content = json_decode($request->getContent(), true);

            if (empty($content['message'])) return $this->sendResponse(['status' => Response::HTTP_ACCEPTED, 'error' => true, 'data' => 'Message is required']);

            $forumPost = new ForumPost();
            $forumPost->setUser($this->getUser())
                ->setParent($forum)
                ->setCourse($forum->getCourse())
                ->setMessage($content['message']);

            $replyToId = $request->get('replyTo', null);
            if (!empty($content['replyTo'])) {
                $replyTo = $forumPostRepository->find($content['replyTo']);
                $forumPost->setResponse($replyTo);
            }

            $this->em->persist($forumPost);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false,
                'data' => $forumPostRepository->createQueryBuilder('fp')
                    ->select('fp.id', 'fp.title', 'fp.message', 'fp.lastResponseAt', 'fp.createdAt', 'response.id AS parent_id')
                    ->addSelect('u.id as user_id', 'u.firstName', 'u.lastName', 'u.avatar')
                    ->join('fp.user', 'u')
                    ->leftJoin('fp.response', 'response')
                    ->where('fp.id =:id')
                    ->setParameter('id', $forumPost->getId())
                    ->getQuery()
                    ->getSingleResult()
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage()
            ]);
        }
    }
}
