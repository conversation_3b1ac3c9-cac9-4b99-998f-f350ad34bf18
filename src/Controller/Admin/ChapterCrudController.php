<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Field\ChapterTypeField;
use App\Admin\Field\FosCkeditorField;
use App\Admin\Field\VichImageField;
use App\Admin\Traits\ResponseTrait;
use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\VueAppDefaultConfiguration;
use App\Entity\AdivinaImagen;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Entity\Content;
use App\Entity\Course;
use App\Entity\Fillgaps;
use App\Entity\Guessword;
use App\Entity\LtiChapter;
use App\Entity\LtiTool;
use App\Entity\OrdenarMenormayor;
use App\Entity\Parejas;
use App\Entity\ParejasImagen;
use App\Entity\Pdf;
use App\Entity\Ppt;
use App\Entity\Question;
use App\Entity\RouletteWord;
use App\Entity\Scorm;
use App\Entity\Season;
use App\Entity\TrueOrFalse;
use App\Entity\User;
use App\Entity\UserCourseChapter;
use App\Entity\Video;
use App\Entity\Videopreguntas;
use App\Entity\Videoquiz;
use App\Enum\ChapterContent;
use App\Enum\ChapterContent as EnumChapterType;
use App\Enum\Games;
use App\Form\Type\Admin\Chapter\ContentFormType;
use App\Repository\AdivinaImagenRepository;
use App\Repository\ChapterRepository;
use App\Repository\FillgapsRepository;
use App\Repository\GameswordRepository;
use App\Repository\GuesswordRepository;
use App\Repository\OrdenarMenormayorRepository;
use App\Repository\ParejasRepository;
use App\Repository\SeasonRepository;
use App\Repository\TrueOrFalseRepository;
use App\Repository\VideopreguntasRepository;
use App\Repository\VideoquizRepository;
use App\Service\SettingsService;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\Field;
use EasyCorp\Bundle\EasyAdminBundle\Field\ImageField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Filter\EntityFilter;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use FOS\RestBundle\Controller\Annotations as Rest;
use KMS\FroalaEditorBundle\Form\Type\FroalaEditorType;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class ChapterCrudController extends AbstractCrudController
{
    use ResponseTrait;
    use SerializerTrait;

    use VueAppDefaultConfiguration;

    private $logger;
    private $requestStack;
    private $em;
    private $context;
    private $adminUrlGenerator;
    protected $translator;
    private JWTManager $JWTManager;
    private $settings;

    public function __construct(
        LoggerInterface $logger,
        RequestStack $requestStack,
        EntityManagerInterface $em,
        AdminContextProvider $context,
        AdminUrlGenerator $adminUrlGenerator,
        TranslatorInterface $translator,
        JWTManager $JWTManager,
        SettingsService $settings
    ) {
        $this->logger = $logger;
        $this->requestStack = $requestStack;
        $this->em = $em;
        $this->context = $context;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->translator = $translator;
        $this->JWTManager = $JWTManager;
        $this->settings = $settings;
    }

    public static function getEntityFqcn(): string
    {
        return Chapter::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular($this->translator->trans('chapter.label_in_singular', [], 'chapters', $this->getUser()->getLocale()))
            ->setEntityLabelInPlural($this->translator->trans('chapter.label_in_plural', [], 'chapters', $this->getUser()->getLocale()))
            ->addFormTheme('@KMSFroalaEditor/Form/froala_widget.html.twig')
            ->addFormTheme('admin/crud/form_theme.html.twig')
            ->setSearchFields(['id', 'title', 'description', 'image'])
            ->overrideTemplate('crud/detail', 'admin/chapter/detail.html.twig')
            ->overrideTemplate('crud/edit', 'admin/chapter/edit.html.twig')
            ->overrideTemplate('crud/new', 'admin/chapter/new.html.twig');
    }

    public function configureFilters(Filters $filters): Filters
    {
        return $filters
            ->add(EntityFilter::new('course'));
    }

    public function configureActions(Actions $actions): Actions
    {
        $actions->remove(Crud::PAGE_DETAIL, Action::INDEX);
        $actions->remove(Crud::PAGE_NEW, Action::SAVE_AND_ADD_ANOTHER);
        $actions->remove(Crud::PAGE_EDIT, Action::SAVE_AND_CONTINUE);

        return $actions;
    }

    public function configureFields(string $pageName): iterable
    {
        $title = TextField::new('title', $this->translator->trans('chapter.configureFields.title', [], 'chapters', $this->getUser()->getLocale()))->setColumns('col-md-6');

        $course = AssociationField::new('course', $this->translator->trans('chapter.configureFields.course', [], 'chapters', $this->getUser()->getLocale()))->setColumns('col-md-6');

        $type = ChapterTypeField::new('type', $this->translator->trans('chapter.configureFields.type', [], 'chapters', $this->getUser()->getLocale()))
            ->setFormTypeOptions([
                'attr' => [
                    'required' => true,
                ],
            ]);

        $courseEntity = null;
        if ($this->context->getContext()->getEntity()) {
            $chapterEntity = $this->context->getContext()->getEntity()->getInstance();
            $courseEntity = $chapterEntity->getCourse();
        } elseif ($this->requestStack->getCurrentRequest()->get('courseId')) {
            $courseEntity = $this->em->getRepository(Course::class)->find($this->requestStack->getCurrentRequest()->get('courseId'));
        }

        $seasonsDefault = $this->em->getRepository(Season::class)->findOneBy([
            'course' => $courseEntity,
        ]);

        if ($this->context->getContext()->getEntity()->getPrimaryKeyValue()) {
            $seasonsDefault = $this->context->getContext()->getEntity()->getInstance()->getSeason();
        }

        $season = AssociationField::new('season', $this->translator->trans('chapter.configureFields.season', [], 'messages', $this->getUser()->getLocale()))
            ->setColumns('col-md-6')
            ->setFormTypeOptions([
                'choices' => $courseEntity->getSeasons(),
                'attr' => [
                    'required' => true,
                ],
                'data' => $seasonsDefault,
            ]);
        //		$descrisption = FosCkeditorField::new('description', $this->translator->trans('chapter.configureFields.description', [], 'messages', $this->getUser()->getLocale()))->setFormTypeOptions([
        //			'config_name' => 'basic',
        //		])->setColumns('col-md-6');
        $description = Field::new('description', $this->translator->trans('chapter.configureFields.description', [], 'chapters', $this->getUser()->getLocale()))
            ->setFormType(FroalaEditorType::class)
            ->setFormTypeOption('froala_height', '150')
            ->setFormTypeOptions([
                'froala_height' => 270,
                'froala_toolbarButtons' => ['bold', 'italic', 'underline'],
                'froala_pastePlain' => true,
                'froala_charCounterMax' => 350,
                'froala_language' => $this->getUser()->getLocale() ?? 'es',
                'froala_toolbarSticky' => true,
                'froala_toolbarStickyOffset' => 50,
                'froala_codeMirror' => false,
                'froala_charCounterCount' => true,
            ])
            ->setColumns('col-md-6');

        $imageFile = VichImageField::new('imageFile', $this->translator->trans('chapter.configureFields.image_file', [], 'messages', $this->getUser()->getLocale()))
            ->setFormTypeOptions(['attr' => ['accept' => 'image/*']])
            ->addCssClass('chapter-image-selector')
            ->setColumns('col-md-6')
            ->setTextAlign('center');
        $id = IntegerField::new('id', 'ID');
        $image = ImageField::new('image')->setBasePath($this->settings->get('app.chapter_uploads_path'))->setColumns('col-md-4');
        $createdAt = DateTimeField::new('createdAt');
        $updatedAt = DateTimeField::new('updatedAt');
        $deletedAt = DateTimeField::new('deletedAt');
        $stateRedirect = BooleanField::new('stateRedirect')->addCssClass('d-none');

        if ($courseEntity) {
            $season->setFormTypeOption('query_builder', function (SeasonRepository $seasonRepository) use ($courseEntity) {
                return $seasonRepository->createQueryBuilder('s')
                    ->andWhere('s.course = :course')
                    ->setParameter('course', $courseEntity);
            });
        }

        if (Crud::PAGE_INDEX === $pageName) {
            return [$id, $title, $course, $type, $image, $createdAt];
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            return [$id, $title, $description, $image, $createdAt, $updatedAt, $deletedAt, $course, $type];
        } elseif (Crud::PAGE_NEW === $pageName) {
            return [$title, $season, $description, $imageFile, $type, $stateRedirect];
        } elseif (Crud::PAGE_EDIT === $pageName) {
            return [$title, $season, $description, $imageFile, $type, $stateRedirect];
        }
    }

    public function createEntity(string $entityFqc)
    {
        $chapter = new Chapter();

        $courseRepository = $this->em->getRepository(Course::class);
        $course = $courseRepository->find($this->requestStack->getCurrentRequest()->get('courseId'));

        $chapter->setCourse($course);
        $chapter->setIsActive(true);

        return $chapter;
    }

    public function deleteEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        $userCourseChapter = $this->em->getRepository(UserCourseChapter::class)->findBy(['chapter' => $entityInstance]);
        if (\in_array('ROLE_SUPER_ADMIN', $this->getUser()->getRoles())
            || \in_array('ROLE_ADMIN', $this->getUser()->getRoles())
        ) {
            $user = $this->em->getRepository(User::class)->find($this->getUser());
            $userCourseChapter = $this->em->getRepository(UserCourseChapter::class)->getUserCourseChapterDiffAdminSuper($entityInstance, $user);
        }

        if (!$userCourseChapter) {
            $this->logger->debug($entityInstance->getTitle());
            parent::deleteEntity($entityManager, $entityInstance); // TODO: Change the autogenerated stub
        } else {
            $this->addFlash('danger', $this->translator->trans('chapter_type.validation_course'));
        }
    }

    public function activeChapterAction(AdminContext $context)
    {
        $entity = $context->getEntity();
        $chapterRepository = $this->em->getRepository(Chapter::class);
        $chapter = $chapterRepository->find($entity->getPrimaryKeyValue());

        $active = !$chapter->isIsActive();
        $chapter->setIsActive($active);
        $this->em->persist($chapter);
        $this->em->flush();

        return $this->redirect(
            $this->adminUrlGenerator
          ->unsetAll()
          ->setController(CourseCrudController::class)
          ->setAction('detail')
          ->setEntityId($chapter->getCourse()->getId())
          ->generateUrl()
        );
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        $table_template = null;

        $chaptersType = $this->em->getRepository(ChapterType::class)->createQueryBuilder('ct')
            ->select('ct')->where('ct.active = 1')
            ->orderBy('ct.id', 'asc')
            ->getQuery()
            ->getResult();

        if ($this->requestStack->getCurrentRequest()->get('courseId')) {
            $responseParameters->set('course', $this->em->getRepository(Course::class)->find($this->requestStack->getCurrentRequest()->get('courseId')));
        }

        if (Crud::PAGE_NEW === $responseParameters->get('pageName')) {
            $responseParameters->set('chaptersType', $chaptersType);
        }

        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName') || Crud::PAGE_EDIT === $responseParameters->get('pageName')) {
            $chapterRepository = $this->em->getRepository(Chapter::class);
            $entity = $this->context->getContext()->getEntity();
            $chapter = $chapterRepository->find($entity->getPrimaryKeyValue());

            $responseParameters->set('upload_subdomain', $this->settings->get('app.uploadSubdomain'));

            switch ($chapter->getType()->getCode()) {
                case EnumChapterType::SCORM_CODE:
                    $scormRepository = $this->em->getRepository(Scorm::class);
                    $scorm = $scormRepository->findOneBy([
                        'chapter' => $chapter,
                    ]);

                    if ($scorm && null == $scorm->getMenu()) {
                        $scorm->unzipScorm();
                    }

                    $this->em->flush();

                    if ($scorm) {
                        $this->deleteZipScorm($scorm->getFolder() . '.zip');
                    }
                    $responseParameters->set('scorm', $scorm);
                    $table_template = ChapterContent::SCORM_DETAIL_TEMPLATE;
                    break;
                case EnumChapterType::LTI_CODE:
                    $lti = $this->em->getRepository(LtiChapter::class)->findOneBy([
                        'chapter' => $chapter
                    ]);

                    $ltiTool = $this->em->getRepository(LtiTool::class)->findAll();

                    $this->em->flush();

                    $responseParameters->set('toolId', $ltiTool);

                    $responseParameters->set('lti', $lti);
                    $table_template = EnumChapterType::LTI_DETAIL_TEMPLATE;
                    break;
                case EnumChapterType::CONTENT_CODE:
                    $contentRepository = $this->em->getRepository(Content::class);
                    $contents = $contentRepository->findBy([
                        'chapter' => $chapter,
                    ], ['position' => 'ASC']);

                    $contentsForms = [];
                    foreach ($contents as $cont) {
                        $content = $this->em->getRepository(Content::class)->find($cont->getId());
                        $form = $this->createForm(ContentFormType::class, $content);
                        $contentsForms[] = [
                            'content' => $cont,
                            'form' => $form->createView(),
                        ];
                    }

                    $responseParameters->set('contents', $contentsForms);

                    $content = new Content();
                    $form = $this->createForm(ContentFormType::class, $content);
                    $responseParameters->set('formContent', $form);
                    $table_template = ChapterContent::CONTENT_LIST_TEMPLATE;
                    $this->configureAppResponseParameters(
                        $responseParameters,
                        $this->settings,
                        $this->context,
                        $this->JWTManager,
                        [
                            'id' => $chapter->getId(),
                        ],
                        [],
                        'ChapterTypeContent'
                    );
                    break;
                case Games::WHEEL_CODE:
                case Games::DOUBLE_OR_NOTHING_CODE:
                case Games::QUIZ_CODE:
                case Games::PUZZLE_CODE:
                case Games::SECRET_WORD_CODE:
                    $questionRepository = $this->em->getRepository(Question::class);
                    $questions = $questionRepository->findBy([
                        'chapter' => $chapter,
                    ]);
                    $responseParameters->set('questions', $questions);
                    $table_template = ChapterContent::QUESTIONS_LIST_TEMPLATE;
                    break;
                case EnumChapterType::PDF_CODE:
                    $pdfRepository = $this->em->getRepository(Pdf::class);
                    $pdf = $pdfRepository->findOneBy([
                        'chapter' => $chapter,
                    ]);

                    $this->em->flush();

                    $responseParameters->set('pdf', $pdf);
                    $table_template = ChapterContent::PDF_DETAIL_TEMPLATE;
                    break;

                case EnumChapterType::PPT_CODE:
                    $pptRepository = $this->em->getRepository(Ppt::class);
                    $ppt = $pptRepository->findOneBy([
                        'chapter' => $chapter,
                    ]);

                    $this->em->flush();

                    $responseParameters->set('ppt', $ppt);
                    $table_template = EnumChapterType::PPT_DETAIL_TEMPLATE;
                    break;

                case EnumChapterType::VIDEO_CODE:
                    $videoRepository = $this->em->getRepository(Video::class);
                    $video = $videoRepository->findOneBy([
                        'chapter' => $chapter,
                    ]);

                    $this->em->flush();

                    $responseParameters->set('video', $video);
                    $table_template = ChapterContent::VIDEO_DETAIL_TEMPLATE;
                    break;
                case Games::TRUE_OR_FALSE_CODE:
                    $trueOrFalseRepository = $this->em->getRepository(TrueOrFalse::class);

                    $blocks = $trueOrFalseRepository->findBy([
                        'chapter' => $chapter,
                    ]);
                    $responseParameters->set('chapterId', $chapter->getId());

                    $responseParameters->set('blocks', $this->serialize($blocks, [
                        'groups' => ['trueOrFalse'],
                        'circular_reference_handler' => function ($object) {
                            return $object->getId();
                        },
                    ]));
                    $table_template = Games::TRUE_OR_FALSE_TEMPLATE;
                    break;
                case Games::SORT_LETTERS_CODE:
                    $guesswordRepository = $this->em->getRepository(OrdenarMenormayor::class);

                    $blocks = $guesswordRepository->findBy([
                        'chapter' => $chapter,
                        'gametype' => 'guessword',
                    ]);

                    $guessword = $this->getGamesWord($blocks);
                    $responseParameters->set('chapterId', $chapter->getId());
                    $responseParameters->set('blocks', $guessword);

                    $table_template = Games::SORT_LETTERS_TEMPLATE;
                    break;
                case Games::WHERE_DOES_IT_FIT_CODE:
                    $trueOrFalseRepository = $this->em->getRepository(TrueOrFalse::class);

                    $blocks = $trueOrFalseRepository->findBy([
                        'chapter' => $chapter,
                    ]);
                    $responseParameters->set('chapterId', $chapter->getId());

                    $responseParameters->set('blocks', $this->serialize($blocks, [
                        'groups' => ['trueOrFalse'],
                        'circular_reference_handler' => function ($object) {
                            return $object->getId();
                        },
                    ]));
                    $table_template = Games::WHERE_DOES_IT_FIT_TEMPLATE;
                    break;

                case Games::HIGHER_OR_LOWER_CODE:
                    $ordenarMenorMayorRepository = $this->em->getRepository(OrdenarMenormayor::class);

                    $blocks = $ordenarMenorMayorRepository->findBy([
                        'chapter' => $chapter,
                    ]);

                    $responseParameters->set('chapterId', $chapter->getId());

                    $responseParameters->set('blocks', $this->serialize($blocks, [
                        'groups' => ['ordenarMenorMayor'],
                        'circular_reference_handler' => function ($object) {
                            return $object->getId();
                        },
                    ]));
                    $table_template = Games::HIGHER_OR_LOWER_TEMPLATE;
                    break;
                case Games::ENIGMA_CODE:
                    $guesswordRepository = $this->em->getRepository(OrdenarMenormayor::class);

                    $blocks = $guesswordRepository->findBy([
                        'chapter' => $chapter,
                        'gametype' => 'wordle',
                    ]);

                    $wordle = $this->getGamesWord($blocks);
                    $responseParameters->set('chapterId', $chapter->getId());
                    $responseParameters->set('blocks', $wordle);

                    $table_template = Games::ENIGMA_TEMPLATE;
                    break;
                case Games::RIDDLE_CODE:
                    $adivinaImagenRepository = $this->em->getRepository(AdivinaImagen::class);

                    $blocks = $adivinaImagenRepository->findBy([
                        'chapter' => $chapter,
                    ]);

                    $responseParameters->set('chapterId', $chapter->getId());

                    $responseParameters->set('blocks', $this->serialize($blocks, [
                        'groups' => ['adivinaImagen'],
                        'circular_reference_handler' => function ($object) {
                            return $object->getId();
                        },
                    ]));
                    $table_template = Games::RIDDLE_TEMPLATE;
                    break;
                case Games::MATCH_CODE:
                    $parejasRepository = $this->em->getRepository(Parejas::class);

                    $blocks = $parejasRepository->findBy([
                        'chapter' => $chapter,
                    ]);

                    if ($blocks) {
                        $parejaImagens = $blocks[0]->getParejasImagens();
                    } else {
                        $parejaImagens = [];
                    }

                    $responseParameters->set('chapterId', $chapter->getId());

                    $responseParameters->set('blocks', $this->serialize($blocks, [
                        'groups' => ['parejas'],
                        'circular_reference_handler' => function ($object) {
                            return $object->getId();
                        },
                    ]));

                    $responseParameters->set('parejaImagenes', $this->serialize($parejaImagens, [
                        'groups' => ['parejas'],
                        'circular_reference_handler' => function ($object) {
                            return $object->getId();
                        },
                    ]));
                    $table_template = Games::MATCH_TEMPLATE;
                    break;
                case Games::WORD_SEARCH_CODE:
                    $guesswordRepository = $this->em->getRepository(OrdenarMenormayor::class);

                    $blocks = $guesswordRepository->findBy([
                        'chapter' => $chapter,
                        'gametype' => 'lettersoup',
                    ]);

                    $lettersoup = $this->getGamesWord($blocks);
                    $responseParameters->set('chapterId', $chapter->getId());
                    $responseParameters->set('blocks', $lettersoup);

                    $table_template = Games::WORD_SEARCH_TEMPLATE;
                    break;
                case Games::FILL_IN_THE_BLANKS_CODE:
                    $fillgapsRepository = $this->em->getRepository(Fillgaps::class);
                    $blocks = $fillgapsRepository->findBy([
                        'chapter' => $chapter,
                    ]);

                    $responseParameters->set('chapterId', $chapter->getId());

                    $responseParameters->set('blocks', $this->serialize($blocks, [
                        'groups' => ['fillgaps'],
                        'circular_reference_handler' => function ($object) {
                            return $object->getId();
                        },
                    ]));
                    $table_template = Games::FILL_IN_THE_BLANKS_TEMPLATE;
                    break;
                case Games::VIDEO_QUIZ_CODE:
                    $videoquizRepository = $this->em->getRepository(Videoquiz::class);

                    $blocks = $videoquizRepository->findBy([
                        'chapter' => $chapter,
                    ]);

                    $responseParameters->set('chapterId', $chapter->getId());

                    $responseParameters->set('blocks', $this->serialize($blocks, [
                        'groups' => ['videoquiz'],
                        'circular_reference_handler' => function ($object) {
                            return $object->getId();
                        },
                    ]));
                    $table_template = Games::VIDEO_QUIZ_TEMPLATE;
                    break;

                case Games::LETTERS_WHEEL_CODE:
                    $rouletteLetter = $this->em->getRepository(RouletteWord::class);

                    $letters = $rouletteLetter->findBy([
                        'chapter' => $chapter,
                    ]);

                    $responseParameters->set('chapterId', $chapter->getId()); // getId
                    // $responseParameters->set('letters', $letters);
                    $responseParameters->set('letters', $this->serialize($letters, [
                        'groups' => ['letter', 'roulette'],
                        'circular_reference_handler' => function ($object) {
                            return $object->getId();
                        },
                    ]));
                    $table_template = Games::LETTERS_WHEEL_TEMPLATE;
                    break;
            }

            $referrerChapter = $this->adminUrlGenerator
                ->setController(ChapterCrudController::class)
                ->setAction('edit')
                ->setEntityId($chapter->getId())  // getId
                ->generateUrl();

            $referrer = $this->adminUrlGenerator
                ->setController(CourseCrudController::class)
                ->setAction('edit')
                ->setEntityId($chapter->getId()) // getId
                ->generateUrl();

            $responseParameters->set('referrer', $referrer);
            $responseParameters->set('chapter', $chapter);
            $responseParameters->set('referrerChapter', $referrerChapter);
            $responseParameters->set('table_template', $table_template);
            $responseParameters->set('chaptersType', $chaptersType);
        }

        return $responseParameters;
    }

    public function getGamesWord($blocks)
    {
        $gamesword = [];
        foreach ($blocks as $block) {
            $elemento = new \stdClass();
            $elemento->id = $block->getId();
            $elemento->question = $block->getTitle();
            $elemento->word = $block->getWordsArray();
            $elemento->time = $block->getTime();
            $elemento->gametype = $block->getGametype();

            array_push($gamesword, $elemento);
        }
        $gamesword = json_encode($gamesword);

        return $gamesword;
    }

    public function edit(AdminContext $context)
    {
        if (!$this->checkEntityAccess()) {
            return $this->redirectToIndex();
        }

        $responseParameters = parent::edit($context);

        if (method_exists($responseParameters, 'set')) {
            $responseParameters->set('chapter', $this->em->getRepository(Chapter::class)->find($context->getEntity()->getPrimaryKeyValue()));
            $responseParameters->set('ltiTools', $this->em->getRepository(LtiTool::class)->findAll());
        }

        return $responseParameters;
    }

    /**
     * @Route("/admin/chapter/order", name="chapter-order")
     */
    public function updateOrders(Request $request)
    {
        try {
            $ordered_items = $request->get('orden');
            $positions = [];

            if (empty($ordered_items) || !\is_array($ordered_items)) {
                throw new \InvalidArgumentException("Invalid 'orden' parameter");
            }

            $chapterRepository = $this->em->getRepository(Chapter::class);

            foreach ($ordered_items as $index => $item) {
                $chapter = $chapterRepository->find($item);

                if (!$chapter) {
                    throw new \Exception("Item with ID {$item} not found");
                }

                $chapter->setPosition($index + 1);

                $positions[] = [
                    'id' => $chapter->getId(),
                    'name' => $chapter->getTitle(),
                    'position' => $index + 1,
                    'newPosition' => $chapter->getPosition(),
                ];
            }

            $this->em->flush();

            return $this->sendResponse([
                'status' => 200,
                'error' => false,
                'data' => 'Actualizado correctamente',
                'order' => $positions,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => 500,
                'error' => true,
                'data' => 'Error: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * @param Chapter $entityInstance
     */
    public function persistEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        $criteria = Criteria::create()
            ->where(Criteria::expr()->eq('season', $entityInstance->getSeason()));

        $chaptersCount = $entityInstance->getCourse()->getChapters()->matching($criteria)->count();
        $entityInstance->setPosition($chaptersCount + 1);
        parent::persistEntity($entityManager, $entityInstance);
    }

    protected function checkEntityAccess()
    {
        if (!\in_array('ROLE_ADMIN', $this->getUser()->getRoles())) {
            /**
             * @var $course Course
             */
            $course = $this->context->getContext()->getEntity()->getInstance()->getCourse();

            if ($course->getCreatedBy() != $this->getUser() && !$course->isManager($this->getUser())) {
                $this->addFlash('danger', 'You have not access to this course');

                return false;
            }
        }

        return true;
    }

    public function deleteZipScorm($file)
    {
        $filename = $this->settings->get('app.scorm_uploads_path') . $file;

        if (file_exists($filename)) {
            $success = unlink($filename);

            if (!$success) {
                throw $this->createNotFoundException("Cannot delete $filename");
            }
        }
    }

    /**
     * @Route("/admin/chapter/scorm/update-times", name="scorm-update-times")
     */
    public function updateScormTime()
    {
        $type = $this->em->getRepository(ChapterContent::class)->findOneBy([
            'id' => ChapterContent::SCORM_TYPE,
        ]);

        /**
         * @var Chapter[] $chapters
         */
        $chapters = $this->em->getRepository(Chapter::class)->findBy([
            'type' => $type,
        ]);

        $titles = '';

        foreach ($chapters as $chapter) {
            $titles .= $chapter->getTitle() . ' ';

            /**
             * @var UserCourseChapter[] $userChapters
             */
            $userChapters = $this->em->getRepository(UserCourseChapter::class)->findBy([
                'chapter' => $chapter,
            ]);

            foreach ($userChapters as $userChapter) {
                $data = $userChapter->getData();

                if (isset($data['scorm']['cmi.core.session_time'])) {
                    $titles .= '(' . $data['scorm']['cmi.core.session_time'] . ') ';

                    $time = explode(':', $data['scorm']['cmi.core.session_time']);

                    $time = $time[0] * 60 * 60 + $time[1] * 60 + $time[2];

                    $titles .= ' (' . $time . ') - ';

                    $userChapter->setTimeSpent($time);
                    $this->em->flush();
                } else {
                    $titles .= '<br>' . serialize($data) . '</br>';
                }
            }
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => $titles,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/roulette-words/set-letter", name="set_letter_rouleteewords", methods={"POST"})
     *
     * @return Response
     */
    public function setLetter(Request $request)
    {
        try {
            $requestData = json_decode($request->getContent(), true);
            $chapterId = $requestData['chapter'];
            $chapter = $this->em->getRepository(Chapter::class)->find($chapterId);
            if (!isset($chapter)) {
                return $this->sendResponse([
                    'status' => 400,
                    'error' => true,
                    'data' => 'Chapter not found',
                ]);
            }

            $existingLetter = $this->em->getRepository(RouletteWord::class)->findBy([
                'chapter' => $chapterId,
                'letter' => $requestData['letter'],
            ]);

            $rouletteLetter = isset($existingLetter[0]) ? $existingLetter[0] : new RouletteWord();
            $rouletteLetter->setType($requestData['type']);
            $rouletteLetter->setChapter($chapter);
            $rouletteLetter->setWord($requestData['word']);
            $rouletteLetter->setLetter($requestData['letter']);
            $rouletteLetter->setQuestion($requestData['question']);

            $this->em->persist($rouletteLetter);
            $this->em->flush();

            return $this->sendResponse([
                'status' => 200,
                'error' => false,
                'data' => [
                    'letter' => [
                        'id' => $rouletteLetter->getId(),
                        'type' => $rouletteLetter->getType(),
                        'letter' => $rouletteLetter->getLetter(),
                        'question' => $rouletteLetter->getQuestion(),
                        'word' => $rouletteLetter->getWord(),
                    ],
                    'message' => $this->translator->trans('rouletteWord.response.update_letter', [], 'messages', $this->getUser()->getLocale()),
                ],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => 500,
                'error' => true,
                'data' => $this->translator->trans('global.error', [], 'messages', $this->getUser()->getLocale()),
            ]);
        }
    }

    /**
     * @Route("/admin/chapter/roulette-words/remove-letter", name="remove_letter_rouleteewords", methods={"POST"})
     *
     * @return Response
     */
    public function removeLetter(Request $request)
    {
        try {
            $requestData = json_decode($request->getContent(), true);
            $chapterId = $requestData['chapterId'];
            $chapter = $this->em->getRepository(Chapter::class)->find($chapterId);

            if (!isset($chapter)) {
                throw new \Exception('Chapter not found', Response::HTTP_BAD_REQUEST);
            }

            $letterId = $requestData['letterId'];
            $existingLetter = $this->em->getRepository(RouletteWord::class)->find($letterId);
            if (!isset($existingLetter)) {
                throw new \Exception('No letter found', Response::HTTP_NOT_FOUND);
            }

            $this->em->remove($existingLetter);
            $this->em->flush();

            return $this->sendResponse([
                'status' => 200,
                'error' => false,
                'data' => $this->translator->trans('rouletteWord.response.delete_letter', [], 'messages', $this->getUser()->getLocale()),
            ]);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());

            return $this->sendResponse([
                'status' => $e->getCode(),
                'error' => true,
                'data' => $this->translator->trans('global.error', [], 'messages', $this->getUser()->getLocale()),
                'stack' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route("/admin/chapter/trueorfalse/set-block", name="set_block_trueorfalse", methods={"POST"})
     *
     * @return Response
     */
    public function setBlock(Request $request, TrueOrFalseRepository $trueOrFalseRepository, ChapterRepository $chapterRepository)
    {
        $chapterId = $request->get('chapterId');
        $trueorfalse_id = $request->get('trueorfalse_id');
        $text = $request->get('text');
        $time = $request->get('time');
        $categorized = 'true' === $request->get('categorized') ? true : false;
        $trueorfalse_id = $request->get('trueorfalse_id');
        $select = 'true' === $request->get('select') ? true : false;

        $chapter = $chapterRepository->find($chapterId);

        $trueOrFalseBlock = new TrueOrFalse();
        $trueOrFalseBlock
            ->setChapter($chapter)
            ->setText($text)
            ->setCategorized($categorized)
            ->setResult($select);

        if (0 !== $trueorfalse_id) {
            $trueorfalse_id = \intval($trueorfalse_id);
            $trueOrFalseBlock->setTrueorfalseId($trueorfalse_id);
        }

        if ('' !== $time) {
            $time = \intval($time);
            $trueOrFalseBlock->setTime($time);
        }
        if (\is_object($request->files->get('route'))) {
            $file = $request->files->get('route');
            $filename = $file->getClientOriginalName();
            $trueOrFalseBlock->setRoute($filename)->setImageFile($file);
        }

        if ('' !== $trueorfalse_id) {
            $trueOrFalseBlock->setTrueorfalseId($trueorfalse_id);
        }

        $this->em->persist($trueOrFalseBlock);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'route' => $this->urlChapter($chapterId),
        ];

        return $this->sendResponse($response);
    }

    private function urlChapter($chapterId)
    {
        return $this->adminUrlGenerator
            ->unsetAll()
            ->setController(ChapterCrudController::class)
            ->setAction('edit')
            ->setEntityId($chapterId)
            ->generateUrl();
    }

    /**
     * @Route("/admin/chapter/trueorfalse/edit-line", name="edit_line_trueorfalse", methods={"POST"})
     */
    public function editLine(Request $request, TrueOrFalseRepository $trueOrFalseRepository)
    {
        $id = $request->get('id');
        $text = $request->get('text');
        $time = $request->get('time');
        $trueorfalse_id = $request->get('trueorfalse_id');
        $select = 'true' === $request->get('select') ? true : false;

        $trueOrFalseBlock = $trueOrFalseRepository->find($id);
        $trueOrFalseBlock->setResult($select)->setText($text);

        if ('' !== $time) {
            $time = \intval($time);
            $trueOrFalseBlock->setTime($time);
        }
        if (\is_object($request->files->get('route'))) {
            $file = $request->files->get('route');
            $filename = $file->getClientOriginalName();
            $trueOrFalseBlock->setRoute($filename)->setImageFile($file);
        }

        if ('' !== $trueorfalse_id) {
            $trueOrFalseBlock->setTrueorfalseId($trueorfalse_id);
        }

        $this->em->persist($trueOrFalseBlock);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/trueorfalse/reload-block", name="reload_block_trueorfalse", methods={"POST"})
     */
    public function reloadBlock(Request $request, TrueOrFalseRepository $trueOrFalseRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $chapterId = $requestData['chapterId'];

        $blocks = $trueOrFalseRepository->findBy([
            'chapter' => $chapterId,
        ]);

        $response = [
            'status' => 200,
            'error' => false,
            'data' => $blocks,
        ];

        return $this->sendResponse($response, [
            'groups' => ['trueOrFalse'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]);
    }

    /**
     * @Route("/admin/chapter/trueorfalse/delete-line", name="delete_line_trueorfalse", methods={"POST"})
     */
    public function deleteLine(Request $request, TrueOrFalseRepository $trueOrFalseRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $id = $requestData['id'];

        // adicionado para categorize... borra cada una de las respuestas adjuntas a una pregunta
        $respuestas = $trueOrFalseRepository->findBy(['trueorfalse_id' => $id]);
        foreach ($respuestas as $respuesta) {
            $this->em->remove($respuesta);
        }

        $blocks = $trueOrFalseRepository->find($id);
        $this->em->remove($blocks);

        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/adivinaimagen/set-block", name="set_block_adivinaimagen", methods={"POST"})
     *
     * @return Response
     */
    public function adivinaimagenSetBlock(Request $request, AdivinaImagenRepository $adivinaimagenRepository, ChapterRepository $chapterRepository)
    {
        $id = $request->get('id');
        $words = $request->get('words');
        $time = $request->get('time');
        $title = $request->get('title');
        $clue = $request->get('clue');
        $file = $request->files->get('image');

        $chapter = $chapterRepository->find($id);
        $filename = $file->getClientOriginalName();

        $adivinaimagenBlock = new AdivinaImagen();
        $adivinaimagenBlock
            ->setChapter($chapter)
            ->setWords($words)
            ->setTime($time)
            ->setTitle($title)
            ->setClue($clue)
            ->setImage($filename)
            ->setImageFile($file);

        $this->em->persist($adivinaimagenBlock);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/adivinaimagen/reload-block", name="reload_block_adivinaimagen", methods={"POST"})
     */
    public function adivinaimagenReloadBlock(Request $request, AdivinaImagenRepository $adivinaImagenRepository, ChapterRepository $chapterRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $chapterId = $requestData['chapterId'];
        $chapter = $chapterRepository->find($chapterId);

        $blocks = $adivinaImagenRepository->findBy([
            'chapter' => $chapter,
        ]);

        $response = [
            'status' => 200,
            'error' => false,
            'data' => $blocks,
        ];

        return $this->sendResponse($response, [
            'groups' => ['adivinaImagen'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]);
    }

    /**
     * @Route("/admin/chapter/adivinaimagen/edit-line", name="edit_line_adivinaimagen", methods={"POST"})
     */
    public function adivinaimagenEditLine(Request $request, AdivinaImagenRepository $adivinaimagenRepository)
    {
        $id = $request->get('id');
        $words = $request->get('words');
        $time = $request->get('time');
        $title = $request->get('title');
        $clue = $request->get('clue');
        $path = $this->settings->get('app.gameAdivinaImagen_uploads_path');

        $block = $adivinaimagenRepository->find($id);
        $block
            ->setWords($words)
            ->setTime($time)
            ->setTitle($title)
            ->setClue($clue);

        if (\is_object($request->files->get('image'))) {
            $ruta = $path . $block->getImage();
            $success = unlink($ruta);
            $file = $request->files->get('image');
            $filename = $file->getClientOriginalName();
            $block
                ->setImage($filename)
                ->setImageFile($file);
        }

        $this->em->persist($block);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/adivinaimagen/delete-line", name="delete_line_adivinaimagen", methods={"POST"})
     */
    public function adivinaimagenDeleteLine(Request $request, AdivinaImagenRepository $adivinaimagenRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $id = $requestData['id'];

        $blocks = $adivinaimagenRepository->find($id);

        $this->em->remove($blocks);

        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/ordenarmenormayor/set-block", name="set_block_ordenarmenormayor", methods={"POST"})
     *
     * @return Response
     */
    public function ordenarMenorMayorSetBlock(Request $request, OrdenarMenormayorRepository $ordenarMenormayorRepository, ChapterRepository $chapterRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $chapterId = $requestData['chapterId'];
        $words_array = $requestData['words_array'];
        $time = $requestData['time'];
        $title = $requestData['title'];
        if (isset($requestData['gametype'])) {
            $gametype = $requestData['gametype'];
        } else {
            $gametype = 'HigherLower';
        }

        $chapter = $chapterRepository->find($chapterId);

        $ordenarMenorMayorBlock = new OrdenarMenormayor();
        $ordenarMenorMayorBlock
            ->setChapter($chapter)
            ->setWordsArray($words_array)
            ->setTime($time)
            ->setGametype($gametype)
            ->setTitle($title);

        $this->em->persist($ordenarMenorMayorBlock);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
            'route' => $this->urlChapter($chapterId),
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/ordenarmenormayor/edit-line", name="edit_line_ordenarmenormayor", methods={"POST"})
     */
    public function ordenarMenorMayorEditLine(Request $request, OrdenarMenormayorRepository $ordenarMenormayorRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $id = $requestData['id'];
        $words_array = $requestData['words_array'];
        $time = $requestData['time'];
        $title = $requestData['title'];

        $block = $ordenarMenormayorRepository->find($id);
        $block
            ->setWordsArray($words_array)
            ->setTime($time)
            ->setTitle($title);

        $this->em->persist($block);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/ordenarmenormayor/reload-block", name="reload_block_ordenarmenormayor", methods={"POST"})
     */
    public function ordenarMenorMayorReloadBlock(Request $request, OrdenarMenormayorRepository $ordenarMenorMayorRepository, ChapterRepository $chapterRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        if (isset($requestData['gametype'])) {
            $gametype = $requestData['gametype'];
        } else {
            $gametype = 'HigherLower';
        }

        $chapterId = $requestData['chapterId'];
        $chapter = $chapterRepository->find($chapterId);

        $blocks = $ordenarMenorMayorRepository->findBy([
            'chapter' => $chapter,
            'gametype' => $gametype,
        ]);

        if ('HigherLower' != $gametype) {
            $gamesword = [];
            foreach ($blocks as $block) {
                $elemento = new \stdClass();
                $elemento->id = $block->getId();
                $elemento->question = $block->getTitle();
                $elemento->word = $block->getWordsArray();
                $elemento->time = $block->getTime();
                $elemento->gametype = $block->getGametype();

                array_push($gamesword, $elemento);
            }
        } else {
            $gamesword = [];
            foreach ($blocks as $block) {
                $elemento = new \stdClass();
                $elemento->id = $block->getId();
                $elemento->title = $block->getTitle();
                $elemento->words_array = $block->getWordsArray();
                $elemento->time = $block->getTime();
                $elemento->gametype = $block->getGametype();

                array_push($gamesword, $elemento);
            }
        }
        $blocks = json_encode($gamesword);

        $response = [
            'status' => 200,
            'error' => false,
            'data' => $gamesword,
        ];

        return $this->sendResponse($response);
        // else {
        //     $response = [
        //         'status' => 200,
        //         'error' => false,
        //         'data' => $blocks,
        //     ];
        //     return $this->sendResponse($response, [
        //         'groups'                     => ['ordenarmenormayor'],
        //         'circular_reference_handler' => function ($object) {
        //             return $object->getId();
        //         }
        //     ]);
        // }
    }

    /**
     * @Route("/admin/chapter/ordenarmenormayor/delete-line", name="delete_line_ordenarmenormayor", methods={"POST"})
     */
    public function ordenarmenormayorDeleteLine(Request $request, OrdenarMenormayorRepository $ordenarMenorMayorRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $id = $requestData['id'];

        $blocks = $ordenarMenorMayorRepository->find($id);

        $this->em->remove($blocks);

        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/parejas/reload-block", name="reload_block_parejas", methods={"POST"})
     */
    public function parejasReloadBlock(Request $request, ParejasRepository $parejasRepository, ChapterRepository $chapterRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $chapterId = $requestData['chapterId'];
        $chapter = $chapterRepository->find($chapterId);

        $blocks = $parejasRepository->findBy([
            'chapter' => $chapter,
        ]);

        if ($blocks) {
            $parejaImagens = $blocks[0]->getParejasImagens();
        } else {
            $parejaImagens = [];
        }

        $response = [
            'status' => 200,
            'error' => false,
            'data' => $blocks,
            'parejaImagenes' => $parejaImagens,
        ];

        return $this->sendResponse($response, [
            'groups' => ['parejas'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]);
    }

    /**
     * @Route("/admin/chapter/parejas/add-pareja", name="add_pareja_parejas", methods={"POST"})
     *
     * @return Response
     */
    public function parejasAddPareja(Request $request, ParejasRepository $parejasRepository, ChapterRepository $chapterRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $chapterId = $requestData['chapterId'];
        $tipo = $requestData['tipo'];
        $tiempo = $requestData['tiempo'];

        $chapter = $chapterRepository->find($chapterId);

        $parejasBlock = new Parejas();
        $parejasBlock
            ->setChapter($chapter)
            ->setTipo($tipo)
            ->setTiempo($tiempo);

        $this->em->persist($parejasBlock);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/parejas/edit-pareja", name="edit_pareja_parejas", methods={"POST"})
     */
    public function parejasEditParejas(Request $request, ParejasRepository $parejasRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $id = $requestData['id'];
        $tiempo = $requestData['tiempo'];

        $block = $parejasRepository->find($id);
        $block->setTiempo($tiempo);

        $this->em->persist($block);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/parejas/delete-pareja", name="delete_line_parejas", methods={"POST"})
     */
    public function parejasDeleteLine(Request $request, ParejasRepository $parejasRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $id = $requestData['id'];

        $blocks = $parejasRepository->find($id);

        $this->em->remove($blocks);

        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/parejas/add-parejasimagen", name="parejas_add_parejasimagen", methods={"POST"})
     *
     * @return Response
     */
    public function addParejaImagen(Request $request, ParejasRepository $parejasRepository)
    {
        $parejaId = $request->get('parejaId');
        $tipo = $request->get('tipo');
        $texto = $request->get('texto');
        $pareja = $parejasRepository->find($parejaId);

        $parejaImagen = new ParejasImagen();
        $parejaImagen
            ->setParejas($pareja)
            ->setTexto($texto);

        if (1 !== $tipo) {
            $file = $request->files->get('imagen');
            $filename = $file->getClientOriginalName();
            $parejaImagen->setImagen($filename)->setImageFile($file);
        }

        $this->em->persist($parejaImagen);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/parejas/edit-parejasimagen", name="parejas_edit_parejasimagen", methods={"POST"})
     *
     * @return Response
     */
    public function editParejaImagen(Request $request, ParejasRepository $parejasRepository)
    {
        $id = $request->get('id');
        $tipo = $request->get('tipo');
        $texto = $request->get('texto');
        $path = $this->settings->get('app.gameParejasImagen_uploads_path');

        $parejaImagen = $this->em->getRepository(ParejasImagen::class)->find($id);
        $parejaImagen->setTexto($texto);

        if (1 !== $tipo) {
            if (\is_object($request->files->get('imagen'))) {
                $ruta = $path . $parejaImagen->getImagen();
                $success = unlink($ruta);
                $file = $request->files->get('imagen');
                $filename = $file->getClientOriginalName();
                $parejaImagen->setImagen($filename)->setImageFile($file);
            }
        }

        $this->em->persist($parejaImagen);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/parejas/delete-parejasimagen", name="parejas_delete_parejasimagen", methods={"POST"})
     */
    public function deleteParejaImagen(Request $request, ParejasRepository $parejasRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $id = $requestData['id'];
        $parejaImagen = $this->em->getRepository(ParejasImagen::class)->find($id);

        $this->em->remove($parejaImagen);
        $this->em->flush();
        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/fillgaps/set-block", name="fillgaps_set_block", methods={"POST"})
     *
     * @return Response
     */
    public function addFillgaps(Request $request, FillgapsRepository $fillgapsRepository, ChapterRepository $chapterRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $chapterId = $requestData['chapterId'];
        $id = $requestData['id'];
        $text = $requestData['text'];
        $answers = json_encode($requestData['answers']);
        $label = $requestData['label'];
        $time = $requestData['time'];

        $chapter = $chapterRepository->find($chapterId);
        $fillgaps = new Fillgaps();
        $fillgaps
            ->setChapter($chapter)
            ->setText($text)
            ->setTime($time)
            ->setAnswers($answers)
            ->setLabel($label);

        $this->em->persist($fillgaps);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/fillgaps/edit-block", name="fillgaps_edit_block", methods={"POST"})
     *
     * @return Response
     */
    public function editFillgaps(Request $request, FillgapsRepository $fillgapsRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $chapterId = $requestData['chapterId'];
        $id = $requestData['id'];
        $text = $requestData['text'];
        $answers = json_encode($requestData['answers']);
        $label = $requestData['label'];
        $time = $requestData['time'];

        $fillgaps = $fillgapsRepository->find($id);
        $fillgaps
            ->setText($text)
            ->setTime($time)
            ->setAnswers($answers)
            ->setLabel($label);

        $this->em->persist($fillgaps);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/fillgaps/delete-block", name="fillgaps_delete_block", methods={"POST"})
     *
     * @return Response
     */
    public function deleteFillgaps(Request $request, FillgapsRepository $fillgapsRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $id = $requestData['id'];
        $fillgaps = $fillgapsRepository->find($id);

        $this->em->remove($fillgaps);
        $this->em->flush();
        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/fillgaps/reload-block", name="fillgaps_reload-block", methods={"POST"})
     *
     * @return Response
     */
    public function reloadFillgaps(Request $request, FillgapsRepository $fillgapsRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $chapterId = $requestData['chapterId'];

        $blocks = $fillgapsRepository->findBy([
            'chapter' => $chapterId,
        ]);

        $response = [
            'status' => 200,
            'error' => false,
            'data' => $blocks,
        ];

        return $this->sendResponse($response, [
            'groups' => ['fillgaps'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]);
    }

    /**
     * @Route("/admin/chapter/guessword/set-block", name="guessword_set_block", methods={"POST"})
     *
     * @return Response
     */
    public function addGuessword(Request $request, GuesswordRepository $guesswordRepository, ChapterRepository $chapterRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $chapterId = $requestData['chapterId'];
        $question = $requestData['question'];
        $word = $requestData['word'];
        $time = $requestData['time'];

        $chapter = $chapterRepository->find($chapterId);
        $guessword = new Guessword();
        $guessword
            ->setChapter($chapter)
            ->setQuestion($question)
            ->setTime($time)
            ->setWord($word);

        $this->em->persist($guessword);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/guessword/edit-block", name="guessword_edit_block", methods={"POST"})
     *
     * @return Response
     */
    public function editGuessword(Request $request, GuesswordRepository $guesswordRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $id = $requestData['id'];
        $question = $requestData['question'];
        $word = $requestData['word'];
        $time = $requestData['time'];

        $guessword = $guesswordRepository->find($id);
        $guessword
            ->setQuestion($question)
            ->setTime($time)
            ->setWord($word);

        $this->em->persist($guessword);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/guessword/delete-block", name="guessword_delete_block", methods={"POST"})
     *
     * @return Response
     */
    public function deleteGuessword(Request $request, GuesswordRepository $guesswordRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $id = $requestData['id'];
        $guessword = $guesswordRepository->find($id);

        $this->em->remove($guessword);
        $this->em->flush();
        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/guessword/reload-block", name="guessword_reload-block", methods={"POST"})
     *
     * @return Response
     */
    public function reloadGuessword(Request $request, GuesswordRepository $guesswordRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $chapterId = $requestData['chapterId'];

        $blocks = $guesswordRepository->findBy([
            'chapter' => $chapterId,
        ]);

        $response = [
            'status' => 200,
            'error' => false,
            'data' => $blocks,
        ];

        return $this->sendResponse($response, [
            'groups' => ['guessword'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]);
    }

    /**
     * @Route("/admin/chapter/gamesword/set-block", name="gamesword_set_block", methods={"POST"})
     *
     * @return Response
     */
    public function addWordle(Request $request, GameswordRepository $gamesWordRepository, ChapterRepository $chapterRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $chapterId = $requestData['chapterId'];
        $title = $requestData['question'];
        $words_array = $requestData['word'];
        $time = $requestData['time'];
        $gametype = $requestData['gametype'];

        $chapter = $chapterRepository->find($chapterId);
        $gamesword = new OrdenarMenormayor();
        $gamesword
            ->setChapter($chapter)
            ->setTitle($title)
            ->setTitle($time)
            ->setWordsArray($words_array)
            ->setGametype($gametype);

        $this->em->persist($gamesword);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/gamesword/edit-block", name="gamesword_edit_block", methods={"POST"})
     *
     * @return Response
     */
    public function editGamesword(Request $request, GameswordRepository $gamesWordRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $id = $requestData['id'];
        $title = $requestData['question'];
        $words_array = $requestData['word'];
        $time = $requestData['time'];

        $gamesword = $gamesWordRepository->find($id);
        $gamesword
            ->setQuestion($title)
            ->setTime($time)
            ->setWord($words_array);

        $this->em->persist($gamesword);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/gamesword/delete-block", name="gamesword_delete_block", methods={"POST"})
     *
     * @return Response
     */
    public function deleteGamesword(Request $request, GameswordRepository $gamesWordRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $id = $requestData['id'];
        $gamesword = $gamesWordRepository->find($id);

        $this->em->remove($gamesword);
        $this->em->flush();
        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/gamesword/reload-block", name="gamesword_reload-block", methods={"POST"})
     *
     * @return Response
     */
    public function reloadGamesword(Request $request, GameswordRepository $gamesWordRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $chapterId = $requestData['chapterId'];
        $gametype = $requestData['gametype'];

        $blocks = $gamesWordRepository->findBy([
            'chapter' => $chapterId,
            'gametype' => $gametype,
        ]);

        $response = [
            'status' => 200,
            'error' => false,
            'data' => $blocks,
        ];

        return $this->sendResponse($response, [
            'groups' => ['gamesword'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]);
    }

    /**
     * @Route("/admin/chapter/videoquiz/set-block", name="set_block_videoquiz", methods={"POST"})
     *
     * @return Response
     */
    public function videoquizSetBlock(Request $request, VideoquizRepository $videoquizRepository, ChapterRepository $chapterRepository)
    {
        $chapterId = $request->get('chapterId');
        $title = $request->get('title');
        $duration = $request->get('duration');
        $file = $request->files->get('url');

        $chapter = $chapterRepository->find($chapterId);
        $filename = $file->getClientOriginalName();

        $videoquiz = new Videoquiz();
        $videoquiz
            ->setChapter($chapter)
            ->setTitle($title)
            ->setVideoDuration($duration)
            ->setUrl($filename)
            ->urlVideo($file);

        $this->em->persist($videoquiz);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/videoquiz/edit-line", name="edit_line_videoquiz", methods={"POST"})
     */
    public function videoquizEditLine(Request $request, VideoquizRepository $videoquizRepository)
    {
        $id = $request->get('id');
        $title = $request->get('title');
        $duration = $request->get('duration');
        $path = $this->settings->get('app.gameVideoquiz_uploads_path');

        $block = $videoquizRepository->find($id);
        $block->setTitle($title);

        if (\is_object($request->files->get('url'))) {
            $ruta = $path . $block->getUrl();
            $success = unlink($ruta);
            $file = $request->files->get('url');
            $filename = $file->getClientOriginalName();
            $block
                ->setVideoDuration($duration)
                ->setUrl($filename)
                ->urlVideo($file);
        }

        $this->em->persist($block);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/videoquiz/delete-line", name="delete_line_videoquiz", methods={"POST"})
     */
    public function videoquizDeleteLine(Request $request, VideoquizRepository $videoquizRepository, VideopreguntasRepository $videopreguntasRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $id = $requestData['id'];

        $blocks = $videoquizRepository->find($id);

        $videopreguntas = $blocks->getVideopreguntas();
        foreach ($videopreguntas as $videopregunta) {
            $pregunta = $videopreguntasRepository->find($videopregunta->getId());
            $this->em->remove($pregunta);
        }

        $this->em->remove($blocks);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/videoquiz/reload-block", name="reload_block_videoquiz", methods={"POST"})
     */
    public function videoquizReloadBlock(Request $request, VideoquizRepository $videoquizRepository, ChapterRepository $chapterRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $chapterId = $requestData['chapterId'];
        $chapter = $chapterRepository->find($chapterId);

        $blocks = $videoquizRepository->findBy([
            'chapter' => $chapter,
        ]);

        $response = [
            'status' => 200,
            'error' => false,
            'data' => $blocks,
        ];

        return $this->sendResponse($response, [
            'groups' => ['videoquiz'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]);
    }

    /**
     * @Route("/admin/chapter/videoquiz/set-block-preguntas", name="set_block_videopreguntas", methods={"POST"})
     *
     * @return Response
     */
    public function videoquizSetBlockPreguntas(Request $request, VideopreguntasRepository $videopreguntasRepository, VideoquizRepository $videoquizRepository)
    {
        $videoquizId = $request->get('videoquizId');
        $currenttime = $request->get('currenttime');
        $texto = $request->get('texto');
        $respuestas = $request->get('respuestas');

        $videoquiz = $videoquizRepository->find($videoquizId);

        $videopreguntas = new Videopreguntas();
        $videopreguntas
            ->setVideoquiz($videoquiz)
            ->setCurrenttime($currenttime)
            ->setTexto($texto)
            ->setRespuestas($respuestas);

        if (\is_object($request->files->get('image'))) {
            $file = $request->files->get('image');
            $filename = $file->getClientOriginalName();
            $videopreguntas->setImage($filename)->setImageFile($file);
        }
        $this->em->persist($videopreguntas);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/videoquiz/edit-line-preguntas", name="edit_line_videopreguntas", methods={"POST"})
     *
     * @return Response
     */
    public function videoquizEditBlockPreguntas(Request $request, VideopreguntasRepository $videopreguntasRepository)
    {
        $id = $request->get('id');
        $currenttime = $request->get('currenttime');
        $texto = $request->get('texto');
        $respuestas = $request->get('respuestas');
        $path = $this->settings->get('app.gameVideoquiz_uploads_path');

        $videopreguntas = $videopreguntasRepository->find($id);

        $videopreguntas
            ->setCurrenttime($currenttime)
            ->setTexto($texto)
            ->setRespuestas($respuestas);

        if (\is_object($request->files->get('image'))) {
            $ruta = $path . $videopreguntas->getImage();
            $success = unlink($ruta);
            $file = $request->files->get('image');
            $filename = $file->getClientOriginalName();
            $videopreguntas->setImage($filename)->setImageFile($file);
        }
        $this->em->persist($videopreguntas);
        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/videoquiz/delete-line-preguntas", name="delete_line_videopreguntas", methods={"POST"})
     */
    public function videopreguntasDeleteLine(Request $request, VideopreguntasRepository $videopreguntasRepository)
    {
        $requestData = json_decode($request->getContent(), true);
        $id = $requestData['id'];

        $blocks = $videopreguntasRepository->find($id);

        $this->em->remove($blocks);

        $this->em->flush();

        $response = [
            'status' => 200,
            'error' => false,
            'data' => 'ok',
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/chapter/videoquiz/reload-block-preguntas", name="reload_block_videopreguntas", methods={"POST"})
     */
    public function videoquizReloadBlockPreguntas(Request $request, VideoquizRepository $videoquizRepository, ChapterRepository $chapterRepository)
    {
        $requestData = json_decode($request->getContent(), true);

        $videoquiz_id = $requestData['videoquiz_id'];

        $blocks = $videoquizRepository->find($videoquiz_id);

        $response = [
            'status' => 200,
            'error' => false,
            'data' => $blocks->getVideopreguntas(),
        ];

        return $this->sendResponse($response, [
            'groups' => ['videoquiz'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]);
    }

    /**
     * @Route("/admin/chapter/{id}/redirect-to-course", name="chapter_redirect_to_course", methods={"GET"})
     */
    public function redirectToCourse(Request $request, Chapter $chapter): RedirectResponse
    {
        $referrerBase = $request->get('referrer', $request->headers->get('referer'));
        if (!empty($referrerBase) && str_contains($referrerBase, 'courseUrlRedirectNew')) {
            $urlFromCreate = explode('courseUrlRedirectNew', $referrerBase);
            $referrer = $urlFromCreate[1];
        } else {
            $referrer = $this->generateUrl('admin_dashboard');
        }

        return $this->redirect($referrer);
    }

    /**
     * @Route("/admin/chapter/{id}/redirect-to-chapter", name="chapter_redirect_to_chapter", methods={"GET"})
     */
    public function redirectToCourseChapter(Chapter $chapter): RedirectResponse
    {
        $request = $this->requestStack->getCurrentRequest();
        $referrer = $request->get('referrer', $request->headers->get('referer'));
        if (!$referrer) {
            $referrer = $this->generateUrl('admin_dashboard');
        }

        return $this->redirect($referrer);
    }

    /**
     * @Rest\Get("/admin/course/chapter/{chapter}/contents", requirements={"chapter"="\d+"})
     */
    public function getChapterContents(Chapter $chapter): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->em->getRepository(Content::class)
                ->createQueryBuilder('c')
                ->select('c.id', 'c.title', 'c.content', 'c.position')
                ->where('c.chapter =:chapter')
                ->setParameter('chapter', $chapter)
                ->orderBy('c.position', 'ASC')
                ->getQuery()->getResult(),
        ]);
    }

    /**
     * @Rest\Post("/admin/course/chapter/{chapter}/content/{content}/update")
     */
    public function updateChapterContent(Request $request, Chapter $chapter, Content $content): Response
    {
        $contentData = json_decode($request->getContent(), true);
        $title = $contentData['title'];
        if (empty($title)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NO_TITLE',
            ]);
        }

        $content->setTitle($title)
            ->setContent($contentData['content'])
            ->setChapter($chapter);

        $this->em->persist($content);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
            'data' => [
                'id' => $content->getId(),
                'title' => $content->getTitle(),
                'content' => $content->getContent(),
                'position' => $content->getPosition(),
            ],
        ]);
    }

    /**
     * @Rest\Post("/admin/course/chapter/{chapter}/contents/new")
     */
    public function createChapterContent(Request $request, Chapter $chapter): Response
    {
        $title = $request->get('title');
        $content = $request->get('content');
        if (empty($title)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NO_TITLE',
            ]);
        }

        /** @var Content|null $maxPosition */
        $maxPosition = $this->em->getRepository(Content::class)->createQueryBuilder('c')
            ->select('c')
            ->where('c.chapter =:chapter')
            ->setParameter('chapter', $chapter)
            ->orderBy('c.position', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        $chapterContent = new Content();
        $chapterContent->setTitle($title)
            ->setContent($content)
            ->setChapter($chapter)
            ->setPosition($maxPosition ? $maxPosition->getPosition() + 1 : 1);

        $this->em->persist($chapterContent);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
            'data' => [
                'id' => $chapterContent->getId(),
                'title' => $chapterContent->getTitle(),
                'content' => $chapterContent->getContent(),
                'position' => $chapterContent->getPosition(),
            ],
        ]);
    }

    /**
     * @Rest\Get("/admin/chapter/chaptertypes")
     */
    public function getChapterChapterTypes(): Response
    {
        $chapterTypes = [];
        $chaptersTypes = $this->em->getRepository(ChapterType::class)->createQueryBuilder('ct')
            ->select('ct')->where('ct.active = 1')
            ->orderBy('ct.id', 'asc')
            ->getQuery()
            ->getResult();
        foreach ($chaptersTypes as $chapterType) {
            $chapterTypes[] = $chapterType->getCode();
        }

        // dd($chaptersTypes);
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $chapterTypes,
        ]);
    }

    /**
     * @Rest\Delete("/admin/course/chapter/{chapter}/contents/{content}")
     */
    public function deleteChapterContent(Chapter $chapter, Content $content): Response
    {
        $this->em->remove($content);
        $this->em->flush();

        $contents = $this->em->getRepository(Content::class)->findBy(['chapter' => $chapter], ['position' => 'ASC']);
        $position = 1;
        foreach ($contents as $content) {
            $content->setPosition($position);
            ++$position;
        }
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => '',
        ]);
    }
}
