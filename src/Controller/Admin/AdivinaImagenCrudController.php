<?php

namespace App\Controller\Admin;

use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\DeleteFilePathTrait;
use App\Entity\AdivinaImagen;
use App\Entity\Chapter;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class AdivinaImagenCrudController extends AbstractCrudController
{
	use DeleteFilePathTrait, SerializerTrait;

	private   $em;
	private   $requestStack;
	private   $logger;
	private   $context;
	protected $translator;
	protected $adminUrlGenerator;

	public function __construct(EntityManagerInterface $em, RequestStack $requestStack, LoggerInterface $logger, AdminContextProvider $context, TranslatorInterface $translator, AdminUrlGenerator $adminUrlGenerator)
	{
		$this->em = $em;
		$this->requestStack = $requestStack;
		$this->logger = $logger;
		$this->context = $context;
		$this->translator = $translator;
		$this->adminUrlGenerator = $adminUrlGenerator;
	}

	public static function getEntityFqcn(): string
	{
		return AdivinaImagen::class;
	}

	/**
	 * @Route("/admin/new/adivina-image", name="admin_adivina_new", methods={"POST"})
	 */
	public function newAdivinaImagen(Request $request)
	{
		try {
			$title = $request->get('title');
			$time = $request->get('time');
			$image = $request->files->get('image');
			$words = $request->get('words');
			$help = $request->get('help');
			$idChapter = $request->get('idChapter');
			$chapter = $this->em->getRepository(Chapter::class)->find($idChapter);

			$adivinaImagen = new AdivinaImagen();
			$adivinaImagen->setTime($time);
			$adivinaImagen->setChapter($chapter);
			$adivinaImagen->setWords($words);
			$adivinaImagen->setTitle($title);
			$adivinaImagen->setClue($help);

			if ($image != null) {
				$adivinaImagen->setImage('');
				$adivinaImagen->setImageFile($image);
			}

			$this->em->persist($adivinaImagen);
			$this->em->flush();

			$response = [
				'status'  => 200,
				'message' => 'adivina created successfully',
				'route'   => $this->urlChapter($idChapter)
			];
		} catch (\Exception $e) {
			$response = [
				'status'  => 500,
				'message' => $e->getMessage(),
				'route'   => $this->urlChapter($idChapter)
			];
		}

		return $this->sendResponse($response);
	}

	/**
	 * @Route("/admin/adivina-image/{id}", name="admin_adivina_fetch", methods={"GET"})
	 */
	public function fetchAdivinaimagen(Chapter $chapter)
	{
		try {
			$adivinaImagen = $this->em->getRepository(AdivinaImagen::class)->findBy(['chapter' => $chapter->getId()]);

			$response = [
				'status'  => 200,
				'message' => 'adivina fetched successfully',
				'route'   => $this->urlChapter($chapter->getId()),
				'data'    => $adivinaImagen
			];
		} catch (\Exception $e) {
			$response = [
				'status'  => 500,
				'message' => $e->getMessage(),
				'route'   => $this->urlChapter($chapter->getId())
			];
		}

		return $this->sendResponse($response, array('groups' => array('adivinaImagen', 'detail')));
	}

	/**
	 * @Route("/admin/edit/adivina-image/", name="admin_adivina_edit", methods={"POST"})
	 */
	public function editAdivinaImagen(Request  $request)
	{
		try {
			$id = $request->get('id');
			$title = $request->get('title');
			$time = $request->get('time');
			$image = $request->files->get('image');
			$words = $request->get('words');
			$help = $request->get('help');
			$idChapter = $request->get('idChapter');
			$chapter = $this->em->getRepository(Chapter::class)->find($idChapter);

			$adivinaImagen = $this->em->getRepository(AdivinaImagen::class)->find($id);

			$adivinaImagen->setTime($time);
			$adivinaImagen->setChapter($chapter);
			$adivinaImagen->setWords($words);
			$adivinaImagen->setTitle($title);
			$adivinaImagen->setClue($help);

			if ($image != null) {
				if (!is_null($adivinaImagen->getImage())) $this->deleteFile($this->getParameter('app.gameAdivinaImagen_uploads_path'), $adivinaImagen->getImage());
				$adivinaImagen->setImage('');
				$adivinaImagen->setImageFile($image);
			}

			$this->em->persist($adivinaImagen);
			$this->em->flush();

			$response = [
				'status'  => 200,
				'message' => 'adivina created successfully',
				'route'   => $this->urlChapter($idChapter)
			];
		} catch (\Exception $e) {
			$response = [
				'status'  => 500,
				'message' => $e->getMessage(),
				'route'   => $this->urlChapter($idChapter)
			];
		}

		return $this->sendResponse($response);
	}

	/**
	 * @Route("/admin/delete/adivina-image", name="admin_adivina_delete", methods={"POST"})
	 */
	public function deleteAdivinaImagen(Request  $request)
	{
		try {
			$id = $request->get('id');
			$adivinaImagen = $this->em->getRepository(AdivinaImagen::class)->find($id);
			$idChapter = $adivinaImagen->getChapter()->getId();
			if (!is_null($adivinaImagen->getImage())) $this->deleteFile($this->getParameter('app.gameAdivinaImagen_uploads_path'), $adivinaImagen->getImage());
			$this->em->remove($adivinaImagen);
			$this->em->flush();
			$response = [
				'status'  => 200,
				'message' => 'adivina deleted successfully',
				'route'   => $this->urlChapter($idChapter)
			];
		} catch (\Exception $e) {
			$response = [
				'status'  => 500,
				'message' => $e->getMessage(),
				'route'   => $this->urlChapter($idChapter)
			];
		}

		return $this->sendResponse($response);
	}

	private function urlChapter($chapterId)
	{
		return $this->adminUrlGenerator
			->unsetAll()
			->setController(ChapterCrudController::class)
			->setAction('edit')
			->setEntityId($chapterId)
			->generateUrl();
	}
}
