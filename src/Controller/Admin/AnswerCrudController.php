<?php

namespace App\Controller\Admin;

use App\Entity\Answer;
use App\Entity\Chapter;
use App\Entity\Question;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;

class AnswerCrudController extends AbstractCrudController
{
    private $em;
    private $requestStack;
    private $logger;

    public function __construct(EntityManagerInterface $em, RequestStack $requestStack, LoggerInterface $logger)
    {
        $this->em = $em;
        $this->requestStack = $requestStack;
        $this->logger = $logger;
    }

    public static function getEntityFqcn(): string
    {
        return Answer::class;
    }

    public function configureFields(string $pageName): iterable
    {
        $answer = TextField::new('answer');
        $correct = BooleanField::new('correct');

        return [
            $answer, $correct
        ];
    }


    public function createEntity(string $entityFqc)
    {
        $answer = new Answer();

        $questionRepository = $this->em->getRepository(Question::class);
        $question = $questionRepository->find($this->requestStack->getCurrentRequest()->get('questionId'));

        $answer->setQuestion($question);

        return $answer;
    }
}
