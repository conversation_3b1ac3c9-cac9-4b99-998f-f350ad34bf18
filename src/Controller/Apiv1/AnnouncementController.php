<?php

declare(strict_types=1);

namespace App\Controller\Apiv1;

use App\Entity\Announcement;
use App\Exception\ExcededApiRequestsException;
use App\Exception\InvalidApiKeyException;
use App\Service\Announcement\AnnouncementExtraService;
use App\Service\Apiv1\AnnouncementService;
use App\Service\General\FilterService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/api/v1")
 */
class AnnouncementController extends ApiBaseController
{
    private $announcementExtraService;

    public function __construct(
        AnnouncementExtraService $announcementExtraService
    ) {
        $this->announcementExtraService = $announcementExtraService;
    }

    /**
     * @Route("/announcements/{announcementId}", methods={"GET"})
     *
     * @throws \Exception
     */
    public function __invoke(int $announcementId, FilterService $filterService, AnnouncementService $announcementService): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try {
            $this->checkAccess($apiKey);
        } catch (InvalidApiKeyException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data' => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        } catch (ExcededApiRequestsException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data' => ['error' => 'Too many requests.'],
            ]);
        }

        $this->saveRequest($apiKey, 'user');

        $announcementRepository = $this->entityManager->getRepository(Announcement::class);
        $announcement = $announcementRepository->find($announcementId);

        if (!$announcement) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'data' => ['error' => 'Announcement not found.'],
            ]);
        }

        $data = $announcementService->getAnnouncementData($announcement);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $data,
        ]);
    }
}
