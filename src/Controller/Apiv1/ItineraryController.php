<?php

declare(strict_types=1);

namespace App\Controller\Apiv1;

use App\Entity\Itinerary;
use App\Exception\ExcededApiRequestsException;
use App\Exception\InvalidApiKeyException;
use App\Service\General\FilterService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/api/v1")
 */
class ItineraryController extends ApiBaseController
{
    /**
     * @Route("/itineraries/{itineraryId}", methods={"GET"})
     */
    public function __invoke(int $itineraryId, FilterService $filterService): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try {
            $this->checkAccess($apiKey);
        } catch (InvalidApiKeyException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data' => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        } catch (ExcededApiRequestsException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data' => ['error' => 'Too many requests.'],
            ]);
        }

        $this->saveRequest($apiKey, 'itinerary');

        $itineraryRepository = $this->entityManager->getRepository(Itinerary::class);
        $itinerary = $itineraryRepository->find($itineraryId);

        if (!$itinerary) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'data' => ['error' => 'Itinerary not found.'],
            ]);
        }

        $data = [
            'id' => $itinerary->getId(),
            'name' => $itinerary->getName(),
            'description' => $itinerary->getDescription(),
            'users' => $itineraryRepository->getUsersIdsByFiltersAndManual($itineraryId),
            'courses' => $this->getCourses($itinerary, true),
            'active' => $itinerary->isActive(),
            'filters' => $filterService->groupFiltersByCategory($itinerary->getFilters(), true),
        ];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $data,
        ]);
    }

    private function getCourses(Itinerary $itinerary, $onlyIds = false): array
    {
        $courses = [];

        if ($onlyIds) {
            foreach ($itinerary->getItineraryCourses() as $itineraryCourse) {
                $course = $itineraryCourse->getCourse();
                $courses[] = $course->getId();
            }
        } else {
            foreach ($itinerary->getItineraryCourses() as $itineraryCourse) {
                $course = $itineraryCourse->getCourse();
                $courses[] = [
                    'id' => $course->getId(),
                    'name' => $course->getName(),
                    'type' => $course->getTypeCourse()->getName(),
                    'categoryId' => $course->getCategory()->getId(),
                    'categoryName' => $course->getCategory()->getName(),
                ];
            }
        }

        return $courses;
    }
}
