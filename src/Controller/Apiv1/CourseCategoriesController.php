<?php


namespace App\Controller\Apiv1;

use App\Entity\CourseCategory;
use App\Exception\ExcededApiRequestsException;
use App\Exception\InvalidApiKeyException;
use App\Service\General\FilterService;
use Exception;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;


/**
 * @Route("/api/v1")
 */
class CourseCategoriesController extends ApiBaseController
{

    /**
     * @Route("/course-categories", methods={"GET"})
     * @return Response
     * @throws Exception
     */
    public function __invoke (FilterService $filterService): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try
        {
            $this->checkAccess($apiKey);
        }
        catch (InvalidApiKeyException $e)
        {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data'   => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        }
        catch (ExcededApiRequestsException $e)
        {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data'   => ['error' => 'Too many requests.'],
            ]);
        }


        $this->saveRequest($apiKey, 'course-categories');

        $courseCategoryRepository = $this->entityManager->getRepository(CourseCategory::class);
        $categories           = $courseCategoryRepository->findBy([], ['sort' => 'ASC', 'name' => 'ASC']);

        $data = [];
        foreach ($categories as $category)
        {
            /** @var CourseCategory $category */
            $data[] = [
                'id'          => $category->getId(),
                'name'        => $category->getName(),
                'sort'        => $category->getSort(),
            ];
        }


        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data'   => $data,
        ]);
    }
}
