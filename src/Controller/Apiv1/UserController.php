<?php

declare(strict_types=1);

namespace App\Controller\Apiv1;

use App\Entity\User;
use App\Exception\ExcededApiRequestsException;
use App\Exception\InvalidApiKeyException;
use App\Service\General\FilterService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/api/v1")
 */
class UserController extends ApiBaseController
{
    /**
     * @Route("/users/{userId}", methods={"GET"})
     *
     * @throws \Exception
     */
    public function __invoke(int $userId, FilterService $filterService): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try {
            $this->checkAccess($apiKey);
        } catch (InvalidApiKeyException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data' => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        } catch (ExcededApiRequestsException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data' => ['error' => 'Too many requests.'],
            ]);
        }

        $this->saveRequest($apiKey, 'user');

        $userRepository = $this->entityManager->getRepository(User::class);
        $user = $userRepository->find($userId);

        if (!$user) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'data' => ['error' => 'User not found.'],
            ]);
        }

        $data = [
            'id' => $user->getId(),
            'email' => $user->getEmail(),
            'roles' => $user->getRoles(),
            'firstName' => $user->getFirstName(),
            'lastName' => $user->getLastName(),
            'active' => $user->getIsActive(),
            'code' => $user->getCode(),
            'HRP' => $user->getMetaValueByLabel('HRP'),
            'locale' => $user->getLocale(),
            'open' => $user->getOpen(),
            'timezone' => $user->getTimezone(),
            'filters' => $filterService->groupFiltersByCategory($user->getFilter()),
            'itineraries' => $userRepository->getItinerariesIdsByUser($user),
        ];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $data,
        ]);
    }
}
