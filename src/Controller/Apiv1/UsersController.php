<?php

declare(strict_types=1);

namespace App\Controller\Apiv1;

use App\Entity\User;
use App\Exception\ExcededApiRequestsException;
use App\Exception\InvalidApiKeyException;
use App\Exception\InvalidDateFormatException;
use App\Exception\InvalidDateRangeException;
use App\Repository\UserCompanyRepository;
use App\Service\General\FilterService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/api/v1")
 */
class UsersController extends ApiBaseController
{
    /**
     * @Route("/users", methods={"POST"})
     *
     * @throws \Exception
     */
    public function __invoke(FilterService $filterService): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try {
            $this->checkAccess($apiKey);
        } catch (InvalidApiKeyException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data' => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        } catch (ExcededApiRequestsException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data' => ['error' => 'Too many requests.'],
            ]);
        }

        $requestContent = json_decode($this->requestStack->getCurrentRequest()->getContent(), true);

        $from = $requestContent['date_from'] ?? '';
        $to = $requestContent['date_to'] ?? '';

        try {
            $this->checkDates($from, $to);
        } catch (InvalidDateFormatException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'data' => ['error' => 'The date format is not valid.'],
            ]);
        } catch (InvalidDateRangeException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_REQUESTED_RANGE_NOT_SATISFIABLE,
                'data' => ['error' => 'The date range is not valid.'],
            ]);
        }

        $this->saveRequest($apiKey, 'users');

        $userRepository = $this->entityManager->getRepository(User::class);
        $users = $userRepository->createQueryBuilder('user')
            ->addOrderBy('user.firstName', 'ASC')
            ->addOrderBy('user.lastName', 'ASC')
            ->andWhere('user.updatedAt BETWEEN :from AND :to')
            ->setParameter('from', $from)
            ->setParameter('to', $to)
            ->getQuery()
            ->getResult();

        $data = [];
        foreach ($users as $user) {
            /* @var User $user */
            $data[] = [
                'id' => $user->getId(),
                'email' => $user->getEmail(),
                'roles' => $user->getRoles(),
                'firstName' => $user->getFirstName(),
                'lastName' => $user->getLastName(),
                'active' => $user->getIsActive(),
                'code' => $user->getCode(),
                'HRP' => $user->getMetaValueByLabel('HRP'),
                'locale' => $user->getLocale(),
                'open' => $user->getOpen(),
                'timezone' => $user->getTimezone(),
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $data,
        ]);
    }

    /**
     * @Route("/user_companies", methods={"GET"})
     */
    public function getUserCompanies(UserCompanyRepository $userCompanyRepository): Response
    {
        $userCompanies = $userCompanyRepository->findAll();
        $data = [];
        foreach ($userCompanies as $company) {
            $data[] = [
                'id' => $company->getId(),
                'name' => $company->getName(),
                'description' => $company->getDescription(),
                'profile' => $company->getProfile(),
                'identityId' => $company->getCif(),
                'code' => $company->getCode(),
                'state' => $company->getState(),
                'external' => true === $company->getExternal() ? 1 : 0,
            ];
        }

        if (empty($data)) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'data' => ['error' => 'No User Companies found.'],
            ]);
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $data,
        ]);
    }
}
