<?php

namespace App\Controller\SuperAdmin;

use App\Entity\AnnouncementModality;
use App\Entity\Announcement;
use App\Entity\ClassroomvirtualType;
use App\Entity\Catalog;
use App\Entity\TypeDiploma;
use App\Entity\ExtraData;
use App\Entity\TypeIdentification;
use App\Entity\TypeMoney;
use App\Entity\SettingGroup;
use App\Entity\Setting;
use App\Entity\UserProfessionalCategory;
use App\Entity\UserWorkCenter;
use App\Entity\UserWorkDepartment;
use App\Entity\UserStudyLevel;
use App\Entity\ExtraDataTranslation;
use App\Entity\AnnouncementModalityTranslation;
use App\Entity\TypeMoneyTranslation;
use App\Entity\TypeIdentificationTranslation;
use App\Admin\Traits\SerializerTrait;
use App\Service\Application\CatalogFixturesServices\ComandFixturesServices;
use App\Service\Catalog\CatalogService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use FOS\RestBundle\Controller\Annotations as Rest;
use Doctrine\ORM\EntityManagerInterface;

use Psr\Log\LoggerInterface;

use App\Service\SettingsService;


/**
 * @Route("/admin/")
 */

class CatalogController extends AbstractController
{
    use SerializerTrait;

    private EntityManagerInterface $em;
    private CatalogService $catalogService;
    private LoggerInterface $logger;
    protected SettingsService $settings;

    
    public function __construct(SettingsService $settings,EntityManagerInterface $em, CatalogService $catalogService, LoggerInterface $logger)
    {
        $this->em = $em;
        $this->catalogService = $catalogService;
        $this->logger = $logger;
        $this->settings = $settings;
    }


    private function handleException(\Exception $e): Response
    {
        $errorMessage = sprintf('Error on line %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());
        return $this->sendResponse(['error' => true, 'status' => 500, 'message' => $errorMessage]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("catalog/all")
     * @return Response
     */
    public function getAllCatalog(): Response
    {
        $data = [];
        $catalogs = $this->em->getRepository(catalog::class)->findAll();
        foreach ($catalogs as $catalog) {
            $data[] = [
                'id' => $catalog->getId(),
                'name' => $catalog->getName(),
                'description' => $catalog->getDescription(),
                'component' => $catalog->getComponent(),
                'routeName' => $catalog->getRoute(),
                'relation' => $catalog->getRelation(),
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("type-money/all")
     * @return Response
     */
    public function getAllTypeMoney(): Response
    {
        $data = [];
        $translations = [];
        $typeMoney = $this->em->getRepository(TypeMoney::class)->findAll();
        foreach ($typeMoney as $type) {
            /** @var TypeMoneyTranslation $translation */
            foreach ($type->getTranslations() as $translation) {
                $name = $translation->getName();
                $country = $translation->getCountry();
                $translations[] = [
                    'locale' => $translation->getLocale(),
                    'name' => $name,
                    'country' => $country
                ];
            }

            $data[] = [
                'id' => $type->getId(),
                'name' => $type->getName(),
                'symbol' => $type->getSymbol(),
                'country' => $type->getCountry(),
                'fractionalUnit' => $type->getFractionalUnit(),
                'codeIso' => $type->getCodeIso(),
                'state' => $type->isState(),
                'translations' => $translations
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle type money information
     * @Rest\Post("type-money/{id}/state")
     * @param Request $request
     * @return Response
     */
    public function changeTypeMoneyState(Request $request, TypeMoney $typeMoney): Response
    {
        return $this->updateStateEntity($request, $typeMoney);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle type money information
     * @Rest\Post("type-money/update")
     * @param Request $request
     * @return Response
     */
    public function updateTypeMoney(Request $request): Response
    {
        try {
            if (!$this->catalogService->existEntity($request, TypeMoney::class))
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'NOT_FOUND'
                ]);
            if (($result = $this->saveTypeMoney($request)) instanceof Response) return $result;
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    public function saveTypeMoney(Request $request) {
        $parameters = [
            'data' => json_decode($request->getContent(), true),
            'parameters'=>[
                'fieldsToTranslate' => ['name', 'country'],
                'setFieldsToTranslations' => ['name' => 'setName', 'country' => 'setCountry']
            ],
            'setFields' => $this->catalogService->getPropertiesEntity(TypeMoney::class),
            'baseEntity' => TypeMoney::class,
            'validarCampos' => ['name',],
            'translationEntity' => TypeMoneyTranslation::class,
        ];

        try {
            $parameters = [
                'data' => json_decode($request->getContent(), true),
                'parameters' => [
                    'fieldsToTranslate' => ['name', 'country'],
                    'setFieldsToTranslations' => ['name' => 'setName', 'country' => 'setCountry']
                ],
                'setFields' => $this->catalogService->getPropertiesEntity(TypeMoney::class),
                'baseEntity' => TypeMoney::class,
                'validarCampos' => ['name',],
                'translationEntity' => TypeMoneyTranslation::class,
            ];

            $this->catalogService->setDefaultData($parameters);

            if (($result = $this->catalogService->saveToEntity()) instanceof Response) return $result;

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("type-money/create")
     * @param Request $request
     * @return Response
     */
    public function createTypeMoney(Request $request): Response
    {
        if (($result = $this->saveTypeMoney($request)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

        
    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("extra-data/all")
     * @return Response
     */
    public function getAllExtraData(): Response
    {
        $data = [];
        $extraDataEntities = $this->em->getRepository(ExtraData::class)->findAll();

        foreach ($extraDataEntities as $extraData) {
            /** @var ExtraDataTranslation $translation */
            $translations = [];
            foreach ($extraData->getTranslations() as $translation) {
                $translations[] = [
                    'locale' => $translation->getLocale(),
                    'name' => $translation->getName(),
                    'description' => $translation->getDescription(),
                ];
            }

            $data[] = [
                'id' => $extraData->getId(),
                'name' => $extraData->getName(),
                'description' => $extraData->getDescription(),
                'active' => $extraData->isActive(),
                'translations' => $translations,
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }


    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle extra data information
     * @Rest\Post("extra-data/{id}/state")
     * @param Request $request
     * @return Response
     */
    public function changeExtraDataState(Request $request, ExtraData $extraData): Response
    {
        return $this->updateStateEntity($request, $extraData);
    }


    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("extra-data/create")
     * @param Request $request
     * @return Response
     */
    public function createExtraData(Request $request): Response
    {
        if (($result = $this->saveExtraData($request)) instanceof Response) {
            return $result;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }


    
    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle extra data information
     * @Rest\Post("extra-data/update")
     * @param Request $request
     * @return Response
     */
    public function updateExtraData(Request $request): Response
    {
        try {
            if (!$this->catalogService->existEntity($request, ExtraData::class))
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'NOT_FOUND'
                ]);
            if (($result = $this->saveExtraData($request)) instanceof Response) return $result;
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }


    public function saveExtraData(Request $request)
    {
        $parameters = [
            'data' => json_decode($request->getContent(), true),
            'parameters' => [
                'fieldsToTranslate' => ['name', 'description'],
                'setFieldsToTranslations' => [
                    'name' => 'setName',
                    'description' => 'setDescription',
                ]
            ],
            'setFields' => $this->catalogService->getPropertiesEntity(ExtraData::class),
            'baseEntity' => ExtraData::class,
            'validarCampos' => ['name'],
            'translationEntity' => ExtraDataTranslation::class,
        ];

        try {
            $data = json_decode($request->getContent(), true);

            if (!isset($data['name'])) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'message' => 'Missing required fields: name or description.'
                ]);
            }

            $parameters['data'] = $data;

            $this->catalogService->setDefaultData($parameters);

            if (($result = $this->catalogService->saveToEntity()) instanceof Response) {
                return $result;
            }

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }




    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("setting-group/all")
     * @return Response
     */
    public function getAllSettingGroup(): Response
    {
        $data = [];
        $settingGroup = $this->em->getRepository(SettingGroup::class)->findAll();
        foreach ($settingGroup as $group) {
            $data[] = [
                'id' => $group->getId(),
                'title' => $group->getTitle(),
                'sort' => $group->getSort(),
                'canDelete' => $this->getCanDeleteGroup($group),
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle setting group information
     * @Rest\Post("setting-group/update")
     * @param Request $request
     * @return Response
     */
    public function updateSettingGroup(Request $request): Response
    {
        if (!$this->catalogService->existEntity($request, SettingGroup::class))
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND'
            ]);
        if (($result = $this->saveSettingGroup($request)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    public function saveSettingGroup(Request $request)
    {

        $parameters = [
            'data' => json_decode($request->getContent(), true),
            'setFields' => $this->catalogService->getPropertiesEntity(SettingGroup::class),
            'validarCampos' => ['title'],
            'baseEntity' => SettingGroup::class,
        ];

        $this->catalogService->setDefaultData($parameters);

        if (($result = $this->catalogService->saveToEntity()) instanceof Response) return $result;

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("setting-group/create")
     * @param Request $request
     * @return Response
     */
    public function createSettingGroup(Request $request): Response
    {
        if (($result = $this->saveSettingGroup($request)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("setting-group/delete")
     * @param Request $request
     * @return Response
     */
    public function deleteSettingGroup(Request $request): Response
    {
        return $this->removeEntity($request, SettingGroup::class);
    }

    public function getCanDeleteGroup(SettingGroup $settingGroup)
    {
        $settings = $this->em->getRepository(Setting::class)->findBy(["settingGroup" => $settingGroup]);
        $canDelete = $settings ? false : true;

        return $canDelete;
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("setting/all")
     * @return Response
     */
    public function getAllSetting(): Response
    {
        $data = [];
        $settings = $this->em->getRepository(Setting::class)->findAllOrderBySettingGroup();

        foreach ($settings as $setting) {
            $defaultValue = new \stdClass();
            $defaultValue->id = $setting->getSettingGroup()->getId();
            $defaultValue->title = $setting->getSettingGroup()->getTitle();
            $groups = $this->getGroups();

            $data[] = [
                'id' => $setting->getId(),
                'code' => $setting->getCode(),
                'value' => $setting->getValue(),
                'name' => $setting->getName(),
                'description' => $setting->getDescription(),
                'settingGroupID' => $setting->getSettingGroup()->getId(),
                'settingGroupTitle' => $setting->getSettingGroup()->getTitle(),
                'sort' => $setting->getSort(),
                'options' => $setting->getOptions(),
                'type' => $setting->getType(),
                'groups' => $groups,
                'defaultValue' => $defaultValue,
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle setting information
     * @Rest\Post("setting/update")
     * @param Request $request
     * @return Response
     */
    public function updateSetting(Request $request): Response
    {
        if (!$this->catalogService->existEntity($request, Setting::class))
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND'
            ]);
        if (($result = $this->saveSetting($request)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    public function saveSetting(Request $request)
    {

        $content = json_decode($request->getContent(), true);

        $data = [
            'id' => $content['id'] ?? -1,
            'code' => $content['code'] ?? null,
            'value' => $content['value'] ?? null,
            'name' => $content['name'] ?? null,
            'description' => $content['description'] ?? null,
            'settingGroup' => $content['settingGroup'] ?? null,
            'sort' => $content['sort'] ?? null,
            'type' => $content['type'] ?? null,
            'options' => $content['options'] ? explode(",", $content['options']) : [],
        ];

        $settingGroup = $this->em->getRepository(SettingGroup::class)->find($data['settingGroup']);
        $data['settingGroup'] = $settingGroup;

        $parameters = [
            'data' => $data,
            'setFields' => $this->catalogService->getPropertiesEntity(Setting::class),
            'validarCampos' => ['name'],
            'baseEntity' => Setting::class,
        ];

        $this->catalogService->setDefaultData($parameters);

        if (($result = $this->catalogService->saveToEntity()) instanceof Response) return $result;

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("setting/create")
     * @param Request $request
     * @return Response
     */
    public function createSetting(Request $request): Response
    {
        if (($result = $this->saveSetting($request)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("setting/delete")
     * @param Request $request
     * @return Response
     */
    public function deleteSetting(Request $request): Response
    {
        return $this->removeEntity($request, Setting::class);
    }

    public function getGroups()
    {
        $groups = [];
        $settingGroups = $this->em->getRepository(SettingGroup::class)->findAll();

        foreach ($settingGroups as $settingGroup) {
            $elemento = new \stdClass();
            $elemento->id = $settingGroup->getId();
            $elemento->title = $settingGroup->getTitle();
            array_push($groups, $elemento);
        }

        return $groups;
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("professionalCategory/all")
     * @return Response
     */
    public function getAllProfessionalCategory(): Response
    {
        $data = [];
        $professionalCategorys = $this->em->getRepository(UserProfessionalCategory::class)->findAllOrderByName();

        foreach ($professionalCategorys as $professionalCategory) {
            $defaultValue = new \stdClass();

            $data[] = [
                'id' => $professionalCategory->getId(),
                'name' => $professionalCategory->getName(),
                'description' => $professionalCategory->getDescription(),
                'state' => $professionalCategory->getState(),
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle professionalCategory information
     * @Rest\Post("professionalCategory/{id}/state")
     */
    public function changeProfessionalCategoryStateStatus(
        Request $request,
        UserProfessionalCategory $userProfessionalCategory
    ): Response {
        return $this->updateStateEntity($request, $userProfessionalCategory);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("professionalCategory/create")
     * @param Request $request
     * @return Response
     */
    public function createProfessionalCategory(Request $request): Response
    {
        if (($result = $this->saveToEntity($request, UserProfessionalCategory::class)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle userProfessionalCategory information
     * @Rest\Post("professionalCategory/update")
     * @param Request $request
     * @return Response
     */
    public function updateProfessionalCategory(Request $request): Response
    {

        if (!$this->catalogService->existEntity($request, UserProfessionalCategory::class))
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND'
            ]);
        if (($result = $this->saveToEntity($request, UserProfessionalCategory::class)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("userWorkCenter/all")
     * @return Response
     */
    public function getAllUserWorkCenter(): Response
    {
        $data = [];
        $userWorkCenters = $this->em->getRepository(UserWorkCenter::class)->findAllOrderByName();

        foreach ($userWorkCenters as $userWorkCenter) {
            $data[] = [
                'id' => $userWorkCenter->getId(),
                'name' => $userWorkCenter->getName(),
                'description' => $userWorkCenter->getDescription(),
                'state' => $userWorkCenter->getState(),
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle userWorkCenter information
     * @Rest\Post("userWorkCenter/{id}/state")
     */
    public function changeUserWorkCenterStateStatus(
        Request $request,
        UserWorkCenter $userWorkCenter
    ): Response {
        return $this->updateStateEntity($request, $userWorkCenter);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("userWorkCenter/create")
     * @param Request $request
     * @return Response
     */
    public function createUserWorkCenter(Request $request): Response
    {
        if (($result = $this->saveToEntity($request, UserWorkCenter::class)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle userWorkCenter information
     * @Rest\Post("userWorkCenter/update")
     * @param Request $request
     * @return Response
     */
    public function updateUserWorkCenter(Request $request): Response
    {
        if (!$this->catalogService->existEntity($request, UserWorkCenter::class))
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND'
            ]);

        if (($result = $this->saveToEntity($request, UserWorkCenter::class)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("userWorkDepartment/all")
     * @return Response
     */
    public function getAllUserWorkDepartment(): Response
    {
        $data = [];
        $userWorkDepartments = $this->em->getRepository(UserWorkDepartment::class)->findAllOrderByName();

        foreach ($userWorkDepartments as $userWorkDepartment) {
            $data[] = [
                'id' => $userWorkDepartment->getId(),
                'name' => $userWorkDepartment->getName(),
                'description' => $userWorkDepartment->getDescription(),
                'state' => $userWorkDepartment->getState(),
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle user Work Department information
     * @Rest\Post("userWorkDepartment/{id}/state")
     */
    public function changeUserWorkDepartmentStateStatus(
        Request $request,
        UserWorkDepartment $userWorkDepartment
    ): Response {
        return $this->updateStateEntity($request, $userWorkDepartment);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("userWorkDepartment/create")
     * @param Request $request
     * @return Response
     */
    public function createUserWorkDepartment(Request $request): Response
    {
        if (($result = $this->saveToEntity($request, UserWorkDepartment::class)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle userWorkCenter information
     * @Rest\Post("userWorkDepartment/update")
     * @param Request $request
     * @return Response
     */
    public function updateUserWorkDepartment(Request $request): Response
    {
        if (!$this->catalogService->existEntity($request, UserWorkDepartment::class))
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND'
            ]);
        if (($result = $this->saveToEntity($request, UserWorkDepartment::class)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("userStudyLevel/all")
     * @return Response
     */
    public function getAllUserStudyLevel(): Response
    {
        $data = [];
        $userStudyLevels = $this->em->getRepository(UserStudyLevel::class)->findAllOrderByName();

        foreach ($userStudyLevels as $userStudyLevel) {
            $data[] = [
                'id' => $userStudyLevel->getId(),
                'name' => $userStudyLevel->getName(),
                'description' => $userStudyLevel->getDescription(),
                'state' => $userStudyLevel->getState(),
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle user Study Level information
     * @Rest\Post("userStudyLevel/{id}/state")
     */
    public function changeUserStudyLevelStateStatus(
        Request $request,
        UserStudyLevel $userStudyLevel
    ): Response {
        return $this->updateStateEntity($request, $userStudyLevel);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("userStudyLevel/create")
     * @param Request $request
     * @return Response
     */
    public function createUserStudyLevel(Request $request): Response
    {

        if (($result = $this->saveToEntity($request, UserStudyLevel::class)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle user Study Level information
     * @Rest\Post("userStudyLevel/update")
     * @param Request $request
     * @return Response
     */
    public function updateUserStudyLevel(Request $request): Response
    {
        if (!$this->catalogService->existEntity($request, UserStudyLevel::class))
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND'
            ]);

        if (($result = $this->saveToEntity($request, UserStudyLevel::class)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    public function saveToEntity(Request $request, $baseEntity)
    {

        $parameters = [
            'data' => json_decode($request->getContent(), true),
            'setFields' => $this->catalogService->getPropertiesEntity($baseEntity),
            'validarCampos' => ['name'],
            'baseEntity' => $baseEntity,
        ];

        $this->catalogService->setDefaultData($parameters);

        if (($result = $this->catalogService->saveToEntity()) instanceof Response) return $result;

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }
    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("classroomvirtual-Type/all")
     * @return Response
     */
    public function getAllClassroomvirtualType(): Response
    {
        $data = [];
        $classroomvirtualType = $this->em->getRepository(ClassroomvirtualType::class)->findAll();
        foreach ($classroomvirtualType as $type) {

            $data[] = [
                'id' => $type->getId(),
                'name' => $type->getName(),
                'state' => $type->isState()
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("classroomvirtual-Type/{id}/state")
     */
    public function changeClassroomvirtualTypeStatus(
        Request $request,
        ClassroomvirtualType $classroomvirtualType
    ): Response {
        return $this->updateStateEntity($request, $classroomvirtualType);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("classroomvirtual-Type/update")
     * @param Request $request
     * @return Response
     */
    public function updateClassroomvirtualType(Request $request): Response
    {
        if (!$this->catalogService->existEntity($request, ClassroomvirtualType::class))
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND'
            ]);

        if (($result = $this->saveToEntity($request, ClassroomvirtualType::class)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    private function updateStateEntity(
        Request $request,
        $baseEntity
    ): Response {
        $parameters['baseEntity'] = $baseEntity;
        $this->catalogService->setDefaultData($parameters);
        if (($result = $this->catalogService->changeState($request)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    public function removeEntity(Request $request, $baseEntity): Response
    {
        $content = json_decode($request->getContent(), true);
        $id = $content['id'] ?? -1;

        if (($result = $this->catalogService->deleteToEntity($id, $baseEntity)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("type-identification/all")
     * @return Response
     */
    public function getAllTypeIdentification(): Response
    {
        $data = [];
        $typeIdentification = $this->em->getRepository(TypeIdentification::class)->findAll();
        foreach ($typeIdentification as $type) {
            $translations = [];
            /** @var TypeIdentificationTranslation $translation */

            foreach ($type->getTranslations() as $translation) {
                $name = $translation->getName();
                $description = $translation->getDescription();
                $translations[] = [
                    'locale' => $translation->getLocale(),
                    'name' => $name ?? '',
                    'description' => $description  ?? ''
                ];
            }
            $data[] = [
                'id' => $type->getId(),
                'name' => $type->getName(),
                'description' => $type->getDescription(),
                'active' => $type->isActive(),
                'main' => $type->isMain(),
                'mask' => $type->getMask(),
                'translations' => $translations
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("type-identification/{id}/active")
     */
    public function changeTypeIdentificationActiveStatus(Request $request, TypeIdentification $typeIdentification): Response
    {
        $content = json_decode($request->getContent(), true);
        $active = $content['active'] ?? false;
        $typeIdentification->setActive($active);
        $this->em->persist($typeIdentification);
        $this->em->flush();
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("type-identification/{id}/main")
     */
    public function changeTypeIdentificationMainStatus(Request $request, TypeIdentification $typeIdentification): Response
    {
        $content = json_decode($request->getContent(), true);
        $main = $content['main'] ?? false;
        $isMain = $main == 1 ? true : false;

        $typeIdentificationMains = $this->em->getRepository(TypeIdentification::class)->findAll(['main' => 1]);


        foreach ($typeIdentificationMains as $typeIdentificationMain) {
            $typeIdentificationMain->setMain(false);
            $this->em->persist($typeIdentificationMain);           
        }


        $typeIdentification->setMain($isMain ?? false);
        $this->em->persist($typeIdentification);
        

        $this->em->flush();
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("type-identification/update")
     * @param Request $request
     * @return Response
     */
    public function updateTypeIdentification(Request $request): Response
    {
        $content = json_decode($request->getContent(), true);
        $id = $content['id'] ?? -1;
        $typeIdentification = $this->em->getRepository(TypeIdentification::class)->find($id);
        if (!$typeIdentification) return $this->sendResponse([
            'status' => Response::HTTP_ACCEPTED,
            'error' => true,
            'data' => 'NOT_FOUND'
        ]);

        if (($result = $this->saveTypeIdentification($request, $typeIdentification)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    public function saveTypeIdentification(Request $request, TypeIdentification $typeIdentification)
    {
        $content = json_decode($request->getContent(), true);
        $name = $content['name'] ?? null;
        $description = $content['description'] ?? null;
        $active = $content['active'] ?? false;
        $mask = $content['mask'] ?? null;
        $main = $content['main'] ?? false;

        if ($main && !$typeIdentification->isMain()) {
            $typeIdentificationMain = $this->em->getRepository(TypeIdentification::class)->findOneBy(['main' => 1]);

            $typeIdentificationMain->setMain(false);
            $this->em->persist($typeIdentificationMain);
        }

        $errors = [];
        if (empty($name)) $errors[] = 'Name required';
        if (count($errors)) return $this->sendResponse([
            'status' => Response::HTTP_ACCEPTED,
            'error' => true,
            'data' => $errors
        ]);
        $typeIdentification->setName($name)
            ->setDescription($description)
            ->setActive($active)
            ->setMask($mask)
            ->setMain($main);

        $translations = $content['translations'];
        foreach ($translations as $data) {
            /** @var TypeIdentificationTranslation $translation */
            $translation = $this->em->getRepository(TypeIdentificationTranslation::class)->findOneBy([
                'translatable' => $typeIdentification,
                'locale' => $data['locale']
            ]);
            $name = $data['name'] ?? null;
            $description = $data['description'] ?? null;
            if (empty($name) && empty($description)) {
                if ($translation) $this->em->remove($translation);
                continue;
            }
            if (!$translation) {
                $translation = new TypeIdentificationTranslation();
                $translation->setTranslatable($typeIdentification);
                $translation->setLocale($data['locale']);
            }
            $translation->setName($name)
                ->setDescription($description);
            $this->em->persist($translation);
        }

        $this->em->persist($typeIdentification);
        $this->em->flush();
        return true;
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("type-identification/create")
     * @param Request $request
     * @return Response
     */
    public function createTypeIdentification(Request $request): Response
    {
        $content = json_decode($request->getContent(), true);
        $name = $content['name'] ?? null;
        $description = $content['description'] ?? null;
        $active = $content['active'] ?? false;
        $mask = $content['mask'] ?? null;
        $main = $content['main'] ?? false;

        $typeIdentificationMain = $this->em->getRepository(TypeIdentification::class)->findOneBy(['main' => 1]);
        if ($typeIdentificationMain) {
            $typeIdentificationMain->setMain(false);
            $this->em->persist($typeIdentificationMain);
        }

        $typeIdentification = new TypeIdentification();
        $typeIdentification->setName($name)
            ->setDescription($description)
            ->setActive($active)
            ->setMask($mask)
            ->setMain($main);
        $this->em->persist($typeIdentification);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

     /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("comandfixtures/all")
     */
    public function executeComandFixtures()
    {
        $executeComando = new ComandFixturesServices();
        $executeComando->execute();
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("announcement-modality/all")
     * @return Response
     */
    public function getAllTypeModalities(): Response
    {
        $data = [];
        $translations = [];
        $modalities = $this->em->getRepository(AnnouncementModality::class)->findAll();
        foreach ($modalities as $modality) {
            /** @var AnnouncementModalityTranslation $translation */
            foreach ($modality->getTranslations() as $translation) {
                $name = $translation->getName();
                $translations[] = [
                    'locale' => $translation->getLocale(),
                    'name' => $name,
                ];
            }

            $data[] = [
                'id' => $modality->getId(),
                'name' => $modality->getName(),
                'isActive' => $modality->isIsActive(),
                'translations' => $translations
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("announcement-modality/create")
     * @param Request $request
     * @return Response
     */
    public function createModality(Request $request): Response
    {
        if (($result = $this->saveModality($request)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle type money information
     * @Rest\Post("announcement-modality/update")
     * @param Request $request
     * @return Response
     */
    public function updateModality(Request $request): Response
    {
        try {
            if (!$this->catalogService->existEntity($request, AnnouncementModality::class))
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'NOT_FOUND'
                ]);
            if (($result = $this->saveModality($request)) instanceof Response) return $result;
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    public function saveModality(Request $request) {
        try {
            $parameters = [
                'data' => json_decode($request->getContent(), true),
                'parameters'=>[
                    'fieldsToTranslate' => ['name'],
                    'setFieldsToTranslations' => ['name' => 'setName']
                ],
                'setFields' => $this->catalogService->getPropertiesEntity(AnnouncementModality::class),
                'baseEntity' => AnnouncementModality::class,
                'validarCampos' => ['name',],
                'translationEntity' => AnnouncementModalityTranslation::class,
            ];

            $this->catalogService->setDefaultData($parameters);

            if (($result = $this->catalogService->saveToEntity()) instanceof Response) return $result;

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("announcement-modality/{id}/active")
     */
    public function changeModalityActiveStatus(Request $request, AnnouncementModality $AnnouncementModality): Response
    {
        $AnnouncementModality->setIsActive(!$AnnouncementModality->isIsActive());
        $this->em->persist($AnnouncementModality);
        $this->em->flush();
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    
}
