<?php

namespace App\Form\Type\Admin;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserExtraType extends AbstractType
{
    private $extraFields;

    public function __construct($extraFields)
    {
        $this->extraFields = $extraFields;
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        foreach($this->extraFields as $field => $params) {
            $builder->add($field, $this->getFieldClass($params["type"]), $params["options"]);
        }


    }

    public function getFieldClass ($type)
    {
        switch ($type) {
            case "choice":
                return ChoiceType::class;
            case "date":
                return DateType::class;
            default:
                return null;
        }
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => 'App\Entity\UserExtra'
        ));
    }
}
