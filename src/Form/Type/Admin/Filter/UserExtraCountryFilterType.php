<?php

namespace App\Form\Type\Admin\Filter;

use App\Service\SettingsService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserExtraCountryFilterType extends AbstractType
{
    protected SettingsService $settings;

    /**
     * @param SettingsService $settings
     */
    public function __construct (SettingsService $settings)
    {
        $this->settings    = $settings;
    }

    public function configureOptions (OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'choices' => $this->settings->get('app.user.extrafields')['country']['options']['choices'],
        ]);
    }


    public function getParent (): string
    {
        return ChoiceType::class;
    }
}
