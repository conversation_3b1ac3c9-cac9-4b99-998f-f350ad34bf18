<?php

namespace App\Form\Type\Admin\Filter;

use App\Entity\ProfessionalCategory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserFilterCategoryFilterType extends AbstractType
{
    protected EntityManagerInterface $em;


    /**
     * @param EntityManagerInterface $entityManager
     */
    public function __construct (EntityManagerInterface $entityManager)
    {
        $this->em        = $entityManager;
    }


    public function configureOptions (OptionsResolver $resolver)
    {
        $professionalCategoryRepository = $this->em->getRepository(ProfessionalCategory::class);
        $categories                     = $professionalCategoryRepository->findAll();
        $choices                        = [];
        foreach ($categories as $category)
        {
            $choices[$category->getCode() . ' - ' . $category->getName()] = $category->getId();
        }

        $resolver->setDefaults(['choices' => $choices]);
    }


    public function getParent(): string
    {
        return ChoiceType::class;
    }
}
