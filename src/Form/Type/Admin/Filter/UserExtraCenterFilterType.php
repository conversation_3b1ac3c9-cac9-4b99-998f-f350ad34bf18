<?php

namespace App\Form\Type\Admin\Filter;

use App\Entity\Center;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserExtraCenterFilterType extends AbstractType
{
    protected EntityManagerInterface $em;


    /**
     * @param EntityManagerInterface $entityManager
     */
    public function __construct (EntityManagerInterface $entityManager)
    {
        $this->em        = $entityManager;
    }


    public function configureOptions (OptionsResolver $resolver)
    {
        $centerRepository = $this->em->getRepository(Center::class);
        $centers          = $centerRepository->findAll();
        $choices              = [];
        foreach ($centers as $center)
        {
            $choices[$center->getCode() . ' - ' . $center->getName()] = $center->getId();
        }

        $resolver->setDefaults(['choices' => $choices]);
    }


    public function getParent(): string
    {
        return ChoiceType::class;
    }
}
