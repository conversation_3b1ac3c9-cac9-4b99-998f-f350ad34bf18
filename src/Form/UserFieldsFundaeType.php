<?php

namespace App\Form;

use App\Entity\UserCompany;
use App\Entity\UserFieldsFundae;
use App\Entity\UserProfessionalCategory;
use App\Entity\UserStudyLevel;
use App\Entity\UserWorkCenter;
use App\Entity\UserWorkDepartment;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class UserFieldsFundaeType extends AbstractType
{
    private TranslatorInterface $translator;
    private EntityManagerInterface $em;
    public function __construct(TranslatorInterface $translator, EntityManagerInterface $em)
    {
        $this->translator = $translator;
        $this->em = $em;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('socialSecurityNumber', TextType::class, [
                'label' => $this->translator->trans('user_fields_fundae.social_security_number', [], 'messages'),
                'attr' => [
                    'class' => 'col-xs-12'
                ]
            ])
            ->add('gender', ChoiceType::class, [
                'label' => $this->translator->trans('user_fields_fundae.gender', [], 'messages'),
                'choices' => [
                    $this->translator->trans('user.gender.m', [], 'messages') => 'M',
                    $this->translator->trans('user.gender.f', [], 'messages') => 'F'
                ]
            ])
            ->add('emailWork', EmailType::class, [
                'label' => $this->translator->trans('user_fields_fundae.email_work', [], 'messages'),
                'attr' => [
                    'class' => 'col-xs-12'
                ]
            ])
            ->add('birthdate', DateType::class, [
                'label' => $this->translator->trans('user_fields_fundae.birthdate', [], 'messages'),
                'attr' => [
                    'class' => 'col-xs-12'
                ],
                'widget' => 'single_text'
            ])
            ->add('dni', TextType::class, [
                'label' => $this->translator->trans('user_fields_fundae.dni', [], 'messages'),
                'attr' => [
                    'class' => 'col-xs-12'
                ]
            ])
            ->add('contributionAccount', TextType::class, [
                'label' => $this->translator->trans('user_fields_fundae.contribution_account', [], 'messages'),
                'attr' => [
                    'class' => 'col-xs-12'
                ]
            ])
            ->add('incapacity', CheckboxType::class, [
                'label' => $this->translator->trans('user_fields_fundae.incapacity', [], 'messages')
            ])
            ->add('victimOfTerrorism', CheckboxType::class, [
                'label' => $this->translator->trans('user_fields_fundae.victim_of_terrorism', [], 'messages')
            ])
            ->add('genderViolence', CheckboxType::class, [
                'label' => $this->translator->trans('user_fields_fundae.gender_violence', [], 'messages')
            ])
            ->add('userCompany', EntityType::class, [
                'class' => UserCompany::class,
                'label' => $this->translator->trans('fundae_catalogs.user_company.label_in_singular', [], 'messages'),
                'choice_label' => static function($choice, string $key, $value) {
                    return $choice ? $choice->getName() : null;
                }
            ])
            ->add('userProfessionalCategory', EntityType::class, [
                'class' => UserProfessionalCategory::class,
                'label' => $this->translator->trans('fundae_catalogs.user_professional_category.label_in_singular', [], 'messages'),
                'choice_label' => static function($choice, string $key, $value) {
                    return $choice ? $choice->getName() : null;
                }
            ])
            ->add('userStudyLevel', EntityType::class, [
                'class' => UserStudyLevel::class,
                'label' => $this->translator->trans('fundae_catalogs.user_study_level.label_in_singular', [], 'messages'),
                'choice_label' => static function($choice, string $key, $value) {
                    return $choice ? $choice->getName() : null;
                }
            ])
            ->add('userWorkCenter', EntityType::class, [
                'class' => UserWorkCenter::class,
                'label' => $this->translator->trans('fundae_catalogs.user_work_center.label_in_singular', [], 'messages'),
                'choice_label' => static function($choice, string $key, $value) {
                    return $choice ? $choice->getName() : null;
                }
            ])
            ->add('userWorkDepartment', EntityType::class, [
                'class' => UserWorkDepartment::class,
                'label' => $this->translator->trans('fundae_catalogs.user_work_department.label_in_singular', [], 'messages'),
                'choice_label' => static function($choice, string $key, $value) {
                    return $choice ? $choice->getName() : 'Select Choice';
                }
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => UserFieldsFundae::class,
        ]);
    }
}
