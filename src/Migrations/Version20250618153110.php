<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250618153110 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create purchasable_item table for V2 Purchase domain';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            CREATE TABLE purchasable_item (
                id CHAR(36) NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT NOT NULL,
                price_amount INT NOT NULL,
                price_currency VARCHAR(3) NOT NULL,
                resource_type VARCHAR(50) NOT NULL,
                resource_id CHAR(36) NOT NULL,
                created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)',
                updated_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
                deleted_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
                PRIMARY KEY(id),
                UNIQUE INDEX unique_resource (resource_type, resource_id)
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE purchasable_item');
    }
}
