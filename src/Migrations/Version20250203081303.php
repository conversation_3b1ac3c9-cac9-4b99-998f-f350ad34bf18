<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250203081303 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE lti_chapter ADD COLUMN `lti_tool_id` INT NOT NULL');
        $this->addSql('CREATE TABLE `lti_tool` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `tools_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
            `name` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
            `audience` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
            `oidc_authentication_url` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
            `launch_url` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
            `deep_linking_url` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
            `client_id` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
            `deployments_ids` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
            `platform_jwks_url` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
            `tool_jwks_url` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
            `platform` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
            `identifier` varchar(45) NOT NULL,
            `platform_key_chain` varchar(45) NOT NULL,
            PRIMARY KEY (`id`)
          ) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('drop TABLE lti_chapter');
    }
}
