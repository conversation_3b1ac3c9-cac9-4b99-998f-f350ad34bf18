<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\FilesTask;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<FilesTask>
 *
 * @method FilesTask|null find($id, $lockMode = null, $lockVersion = null)
 * @method FilesTask|null findOneBy(array $criteria, array $orderBy = null)
 * @method FilesTask[]    findAll()
 * @method FilesTask[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FilesTaskRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FilesTask::class);
    }

    public function add(FilesTask $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(FilesTask $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
