<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\LibraryFile;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<LibraryFile>
 *
 * @method LibraryFile|null find($id, $lockMode = null, $lockVersion = null)
 * @method LibraryFile|null findOneBy(array $criteria, array $orderBy = null)
 * @method LibraryFile[]    findAll()
 * @method LibraryFile[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LibraryFileRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LibraryFile::class);
    }

    public function add(LibraryFile $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(LibraryFile $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
