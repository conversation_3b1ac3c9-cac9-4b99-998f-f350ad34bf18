<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\NpsQuestion;
use App\Entity\NpsQuestionTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method NpsQuestion|null find($id, $lockMode = null, $lockVersion = null)
 * @method NpsQuestion|null findOneBy(array $criteria, array $orderBy = null)
 * @method NpsQuestion[]    findAll()
 * @method NpsQuestion[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class NpsQuestionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, NpsQuestion::class);
    }

    public function getNpsTranslation($npsQuestion, $locale)
    {
        $query = $this->_em->createQueryBuilder();

        $query->select('npt.question as question');
        $query->from(NpsQuestionTranslation::class, 'npt');
        $query->where('npt.translatable = :npsQuestion');
        $query->andWhere('npt.locale = :locale');

        $query
            ->setParameter('npsQuestion', $npsQuestion)
            ->setParameter('locale', $locale);

        return $query->getQuery()->getOneOrNullResult();
    }

    public function getNpsQuestionByCourse($course, $locale)
    {
        $query = $this->_em->createQueryBuilder();

        $query->select('nps.id as id, nps.type as type, npt.question as question');
        $query->from(NpsQuestion::class, 'nps');
        $query->join('nps.course', 'c');
        $query->join('nps.translations', 'npt');
        $query->where('c.id = :course');
        $query->andWhere('npt.locale = :locale');

        $query
            ->setParameter('course', $course)
            ->setParameter('locale', $locale);

        return $query->getQuery()->getResult();
    }
}
