<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\TypeVideo;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method TypeVideo|null find($id, $lockMode = null, $lockVersion = null)
 * @method TypeVideo|null findOneBy(array $criteria, array $orderBy = null)
 * @method TypeVideo[]    findAll()
 * @method TypeVideo[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TypeVideoRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TypeVideo::class);
    }
}
