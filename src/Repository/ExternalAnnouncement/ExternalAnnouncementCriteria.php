<?php

declare(strict_types=1);

namespace App\Repository\ExternalAnnouncement;

class ExternalAnnouncementCriteria
{
    private \DateTimeImmutable $from;
    private \DateTimeImmutable $to;

    public function setFrom(\DateTimeImmutable $from): void
    {
        $this->from = $from;
    }

    public function setTo(\DateTimeImmutable $to): void
    {
        $this->to = $to;
    }

    public function getFrom(): \DateTimeImmutable
    {
        return $this->from;
    }

    public function getTo(): \DateTimeImmutable
    {
        return $this->to;
    }
}
