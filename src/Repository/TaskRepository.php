<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Task;
use App\Entity\User;
use App\Service\SlotManagerService;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;

/**
 * @method Task|null find($id, $lockMode = null, $lockVersion = null)
 * @method Task|null findOneBy(array $criteria, array $orderBy = null)
 * @method Task[]    findAll()
 * @method Task[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TaskRepository extends ServiceEntityRepository
{
    protected EntityManagerInterface $em;
    protected SlotManagerService $slotManagerService;
    protected LoggerInterface $logger;

    public function __construct(
        ManagerRegistry $registry,
        EntityManagerInterface $em,
        SlotManagerService $slotManagerService,
        LoggerInterface $logger
    ) {
        parent::__construct($registry, Task::class);
        $this->em = $em;
        $this->slotManagerService = $slotManagerService;
        $this->logger = $logger;
    }

    public function newTask($task = '', $params = []): Task
    {
        $model = new Task();
        $model->setTask($task);
        $model->setCreatedAt(new \DateTime());
        $model->setStatus(Task::TASK_PENDING);

        if (isset($params['type'])) {
            $model->setType($params['type']);
        }

        $model->setParams($params);

        $this->em->persist($model);
        $this->em->flush();

        return $model;
    }

    /**
     * Counts the number of pending Tasks for a user.
     * Tasks with TASK_TIMEOUT and startedAt more than 1 hour ago are not considered pending.
     */
    public function countPendingTasksByUser(?User $user): int
    {
        try {
            if (!$user) {
                return 0;
            }

            $qb = $this->createQueryBuilder('t')
                ->select('COUNT(t.id)')
                ->leftJoin('t.export', 'e')
                ->where('t.createdBy = :user')
                ->andWhere('t.deletedAt IS NULL')
                ->andWhere('t.status IN (:pendingStatuses)')
                ->setParameter('user', $user)
                ->setParameter('pendingStatuses', [Task::TASK_PENDING, Task::TASK_INPROGRESS]);

            // Add timeout tasks that started less than the configured timeout period ago
            $timeoutSeconds = $this->slotManagerService->getTaskTimeoutSeconds();
            $timeoutAgo = new \DateTimeImmutable('-' . $timeoutSeconds . ' seconds');
            $qb->orWhere('t.createdBy = :user AND t.deletedAt IS NULL AND t.status = :timeoutStatus AND t.startedAt IS NOT NULL AND t.startedAt > :timeoutAgo')
                ->setParameter('timeoutStatus', Task::TASK_TIMEOUT)
                ->setParameter('timeoutAgo', $timeoutAgo);

            return (int) $qb->getQuery()->getSingleScalarResult();
        } catch (\Exception $e) {
            $this->logger->error('[src/Repository/TaskRepository.php] countPendingTasksByUser() error: ' . $e->getMessage());
            return 0;
        }
    }
}
