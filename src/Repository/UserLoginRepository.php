<?php

namespace App\Repository;

use App\Admin\Traits\StatsQueryFiltersTrait;
use App\Entity\User;
use App\Entity\UserLogin;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Driver\Exception;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method UserLogin|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserLogin|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserLogin[]    findAll()
 * @method UserLogin[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserLoginRepository extends ServiceEntityRepository
{
    use StatsQueryFiltersTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserLogin::class);
    }

    public function getDailyLogins(array $conditions = [])
    {
        $query = $this->createQueryBuilder('ul')
            ->select(['count(ul.id) as count', "DATE_FORMAT(ul.createdAt, '%Y-%m-%d') as date"])
            ->groupBy('date');

        $this->setSearchFilters($query, 'ul', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('ul.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere('ul.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

         return $query
            ->getQuery()
            ->getResult();
    }


    public function getDailyLoginsByUser($user)
    {
        return $this->createQueryBuilder('ul')
            ->select(['count(ul.id) as count', "DATE_FORMAT(ul.createdAt, '%Y-%m-%d') as date"])
            ->andWhere('ul.user = :user')
            ->setParameter('user', $user)
            ->groupBy('date')
            ->getQuery()
            ->getResult();
    }


    public function getActiveUsersLastMonth(array $conditions = [])
    {
        $query = $this->createQueryBuilder('ul')
            ->select(['count(distinct(ul.user)) as count'])
            ->leftJoin('ul.user', 'user')
            ->andWhere('JSON_CONTAINS(user.roles, :role) = 1')
            ->andWhere('ul.createdAt >= :created')
            ->setParameter('role', '"ROLE_USER"')
            ->setParameter('created', new \DateTime('-1 month'));

        $this->setSearchFilters($query, 'ul', $conditions);

        return $query->getQuery()
            ->getSingleScalarResult();
    }


    public function getLoginOnceUsers(array $conditions = [])
    {
        $query = $this->createQueryBuilder('ul')
            ->select(['count(distinct(ul.user)) as count'])
            ->leftJoin('ul.user', 'user')
            ->andWhere('JSON_CONTAINS(user.roles, :role) = 1')
            ->setParameter('role', '"ROLE_USER"');

        $this->setSearchFilters($query, 'ul', $conditions);

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('ul.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere('ul.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()
            ->getSingleScalarResult();
    }


    public function countByUserFromDate(User $user, $dateTime)
    {
        return $this->createQueryBuilder('ul')
            ->select(['count(ul.id) as count'])
            ->andWhere('ul.createdAt >= :date')
            ->andWhere('ul.user = :user')
            ->setParameter('date', $dateTime)
            ->setParameter('user', $user)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function devicesSesion(array $conditions = [])
    {

        $query = $this->createQueryBuilder('ul')
            ->select('count(ul.device) as y, ul.device as name')
            ->andWhere("ul.device != ''")
            ->groupBy('ul.device');

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('ul.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere('ul.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        $this->setSearchFilters($query, 'ul', $conditions);

        return $query->getQuery()
            ->getResult();
    }

    public function loginSesion(array $conditions = [])
    {

        $query = $this->createQueryBuilder('ul')
            ->select('count(ul)');

        $query->leftJoin('ul.user', 'u');
        $query->leftJoin('u.extra', 'ue');

        if (!empty($conditions['dateFrom']))
            $query->andWhere('ul.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        if (!empty($conditions['dateTo']))
            $query->andWhere('ul.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);


        $this->setQueryFilters($query, $conditions);

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function loginDistinctSesion(array $conditions = [])
    {

        $query = $this->createQueryBuilder('ul')
            ->select('count(Distinct(ul.user))');

        $query->leftJoin('ul.user', 'u');
        $query->leftJoin('u.extra', 'ue');

        if (!empty($conditions['dateFrom']))
            $query->andWhere('ul.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        if (!empty($conditions['dateTo']))
            $query->andWhere('ul.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);


        $this->setQueryFilters($query, $conditions);

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function countSesionDevice($device, $conditions)
    {
        $query = $this->createQueryBuilder('ul')
            ->select(['count(ul.device) as count'])
            ->andWhere('ul.device = :device')
            ->setParameter('device', $device);

        $query->leftJoin('ul.user', 'u');
        $query->leftJoin('u.extra', 'ue');

        if (!empty($conditions['dateFrom']))
            $query->andWhere('ul.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        if (!empty($conditions['dateTo']))
            $query->andWhere('ul.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);


        $this->setQueryFilters($query, $conditions);

        return $query->getQuery()
            ->getSingleScalarResult();
    }

    public function getUniqueLoginsByMaxDate(\DateTime $dateTime)
    {
        $query = $this->createQueryBuilder('ul')
            ->select('count(distinct(ul.user))')
            ->where('ul.createdAt < :date')
            ->setParameter('date', $dateTime->format('Y-m-d'));

        return $query->getQuery()
            ->getSingleScalarResult();
    }


    public function getLoginHeatMap($conditions = []): array
    {
        $data = [];
        for ($week = 0; $week <= 6; $week++) {
            for ($hour = 0; $hour <= 22; $hour += 2) {
                $time                      = $hour / 2;
                $data[$week . '_' . $time] = [
                    'weekday' => $week,
                    'time'    => $time,
                    'count'   => 0,
                ];
            }
        }

        $query = $this->createQueryBuilder('ul')
            ->select("count(ul.id) as count, weekday( ul.createdAt ) AS weekday,
                    CASE
                            WHEN HOUR ( ul.createdAt ) < 2 THEN	0 
                            WHEN HOUR ( ul.createdAt ) < 4 THEN	1 
                            WHEN HOUR ( ul.createdAt ) < 6 THEN	2 
                            WHEN HOUR ( ul.createdAt ) < 8 THEN	3 
                            WHEN HOUR ( ul.createdAt ) < 10 THEN	4 
                            WHEN HOUR ( ul.createdAt ) < 12 THEN	5 
                            WHEN HOUR ( ul.createdAt ) < 14 THEN	6 
                            WHEN HOUR ( ul.createdAt ) < 16 THEN	7 
                            WHEN HOUR ( ul.createdAt ) < 18 THEN	8 
                            WHEN HOUR ( ul.createdAt ) < 20 THEN	9 
                            WHEN HOUR ( ul.createdAt ) < 22 THEN	10 
                            ELSE 11 
                        END AS time ")
            ->groupBy('weekday, time');

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('ul.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }

        if (!empty($conditions['dateTo'])) {
            $query->andWhere('ul.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        $query = $this->setSearchFilters($query, 'ul', $conditions);


        $loginData = $query->getQuery()->getResult();
    

        foreach ($loginData as $loginItem) {
            $data[$loginItem['weekday'] . '_' . $loginItem['time']]['count'] = (int)$loginItem['count'];
        }

        

        return array_values($data);
    }
}
