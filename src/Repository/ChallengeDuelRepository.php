<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChallengeDuel;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method ChallengeDuel|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChallengeDuel|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChallengeDuel[]    findAll()
 * @method ChallengeDuel[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChallengeDuelRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChallengeDuel::class);
    }

    /**
     * @return ChallengeDuel[] Returns an array of ChallengeDuel objects
     */
    public function getPreviousRivals($user, $challenge)
    {
        $query = $this->createQueryBuilder('d');
        $query
            ->select('IDENTITY(d.user1) as userId')
            ->Where('d.user2 = :user')
            ->andWhere('d.challenge = :challenge')
            ->orderBy('d.updatedAt', 'DESC')
            ->setParameters([
                'user' => $user,
                'challenge' => $challenge
            ])
            ->setMaxResults(2);

        return $query->getQuery()->getResult();
    }

    /**
     * @return ChallengeDuel[] Returns an array of ChallengeDuel objects
     */
    public function findOpenedDuels($user, $challenge, $rival1, $rival2)
    {
        $query = $this->createQueryBuilder('c');
        $query
           ->Where('c.user1 != :user')
           ->andWhere($query->expr()->isNull('c.winner'))
           ->andWhere('c.challenge = :challenge')
           ->andWhere($query->expr()->isNull('c.user2'))
           ->join('c.challengeDuelQuestions', 'cd')
           ->andWhere($query->expr()->isNotNull('cd.answerUser1'))
           ->andWhere($query->expr()->notIn('c.user1', $rival1))
           ->andWhere($query->expr()->notIn('c.user1', $rival2))
           ->setParameters([
               'user' => $user,
               'challenge' => $challenge
           ])
            ->setMaxResults(1);

        return $query->getQuery()->getResult();
    }

    /**
     * @return ChallengeDuel[] Returns an array of ChallengeDuel objects
     */
    public function getUserHistoryByChallengeId($user, $challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
           ->Where('c.user1 = :user OR c.user2 = :user')
           ->andWhere('c.challenge = :challenge')
           ->andWhere($query->expr()->isNotNull('c.winner'))
           ->orderBy('c.updatedAt', 'DESC')
           ->setParameters([
               'user' => $user,
               'challenge' => $challenge
           ]);

        return $query->getQuery()->getResult();
    }

    /**
     * @return ChallengeDuel[] Returns an array of ChallengeDuel objects
     */
    public function getUserDuelByChallengeIdUser1($user, $challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
            ->select('IDENTITY(cdq.answerUser1) as id')
            ->join('c.challengeDuelQuestions', 'cdq')
            ->Where('c.user1 = :user')
            ->andWhere('c.challenge = :challenge')
            ->setParameters([
                'user' => $user,
                'challenge' => $challenge
            ]);

        return $query->getQuery()->getResult();
    }

    /**
     * @return ChallengeDuel[] Returns an array of ChallengeDuel objects
     */
    public function getUserDuelByChallengeIdUser2($user, $challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
            ->select('IDENTITY(cdq.answerUser2) as id')
            ->join('c.challengeDuelQuestions', 'cdq')
            ->Where('c.user2 = :user')
            ->andWhere('c.challenge = :challenge')
            ->setParameters([
                'user' => $user,
                'challenge' => $challenge
            ]);

        return $query->getQuery()->getResult();
    }

    /**
     * @return ChallengeDuel[] Returns an array of ChallengeDuel objects
     */
    public function getUserDuelWinByChallengeIdUser1($user, $challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
           ->Where('c.user1 = :user')
           ->andWhere('c.challenge = :challenge')
           ->andWhere('c.winner = 1')
           ->setParameters([
               'user' => $user,
               'challenge' => $challenge
           ]);

        return $query->getQuery()->getResult();
    }

    /**
     * @return ChallengeDuel[] Returns an array of ChallengeDuel objects
     */
    public function getUserDuelWinByChallengeIdUser2($user, $challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
           ->Where('c.user2 = :user')
           ->andWhere('c.challenge = :challenge')
            ->andWhere('c.winner = 2')
           ->setParameters([
               'user' => $user,
               'challenge' => $challenge
           ]);

        return $query->getQuery()->getResult();
    }

    /**
     * @return ChallengeDuel[] Returns an array of ChallengeDuel objects
     */
    public function getUserDuelLoseByChallengeIdUser1($user, $challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
           ->Where('c.user1 = :user')
           ->andWhere('c.challenge = :challenge')
            ->andWhere('c.winner = 2')
           ->setParameters([
               'user' => $user,
               'challenge' => $challenge
           ]);

        return $query->getQuery()->getResult();
    }

    /**
     * @return ChallengeDuel[] Returns an array of ChallengeDuel objects
     */
    public function getUserDuelLoseByChallengeIdUser2($user, $challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
           ->Where('c.user2 = :user')
           ->andWhere('c.challenge = :challenge')
            ->andWhere('c.winner = 1')
           ->setParameters([
               'user' => $user,
               'challenge' => $challenge
           ]);

        return $query->getQuery()->getResult();
    }

    /**
     * @return ChallengeDuel[] Returns an array of ChallengeDuel objects
     */
    public function getUserDuelTieByChallengeId($user, $challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
           ->Where('c.user1 = :user OR c.user2 = :user')
           ->andWhere('c.challenge = :challenge')
           ->andWhere('c.winner = -1 OR c.winner = 0')
           ->setParameters([
               'user' => $user,
               'challenge' => $challenge
           ]);

        return $query->getQuery()->getResult();
    }

    /**
     * @return ChallengeDuel[] Returns an array of ChallengeDuel objects
     */
    public function getUserDuelLeftByChallengeId($user, $challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
           ->Where('c.user1 = :user OR c.user2 = :user')
           ->andWhere('c.challenge = :challenge')
           ->andWhere('c.winner = -5')
           ->setParameters([
               'user' => $user,
               'challenge' => $challenge
           ]);

        return $query->getQuery()->getResult();
    }

    /**
     * @return int return total number of duels for this challenge
     */
    public function getCountDuelsByIdChallenge($challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
            ->select('count(c.id)')
           ->Where('c.challenge = :challenge')
           ->setParameters([
               'challenge' => $challenge
           ]);

        return $query->getQuery()->getSingleScalarResult();
    }

    /**
     * @return int return total number of duels for this user in this challenge
     */
    public function getAllDuelsByChallengeByUser($user, $challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
           ->select('count(c.id)')
           ->Where('c.challenge = :challenge')
            ->andWhere('c.user1 = :user OR c.user2 = :user')
           ->setParameters([
               'challenge' => $challenge,
               'user' => $user
           ]);

        return $query->getQuery()->getSingleScalarResult();
    }

    /**
     * @return int return total number of duels for this user in this challenge
     */
    public function getAllDuelsFinishedByChallengeByUser($user, $challenge)
    {
        $query = $this->createQueryBuilder('c');
        $query
            ->select('count(c.id)')
            ->Where('c.challenge = :challenge')
            ->andWhere('c.winner IS NOT NULL')
            ->andWhere('c.user1 = :user OR c.user2 = :user')
            ->setParameters([
                'challenge' => $challenge,
                'user' => $user
            ]);

        return $query->getQuery()->getSingleScalarResult();
    }
}
