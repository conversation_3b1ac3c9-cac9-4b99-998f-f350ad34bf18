<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Chapter;
use App\Entity\LtiChapter;
use App\Entity\LtiTool;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<LtiChapter>
 *
 * @method LtiChapter|null find($id, $lockMode = null, $lockVersion = null)
 * @method LtiChapter|null findOneBy(array $criteria, array $orderBy = null)
 * @method LtiChapter[]    findAll()
 * @method LtiChapter[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LtiChapterRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LtiChapter::class);
    }

    public function add(LtiChapter $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(LtiChapter $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getIdenfierFromChapter($chapterId)
    {
        return $this->createQueryBuilder('lc')
            ->select('lc.identifier, lt.launchUrl, lt.name, lt.deploymentsIds')
            ->leftJoin(
                Chapter::class,
                'c',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'c.id = lc.chapter'
            )
            ->leftJoin(
                LtiTool::class,
                'lt',
                \Doctrine\ORM\Query\Expr\Join::WITH,
                'lt.id = lc.ltiTool'
            )
            ->andWhere('c.id = :chapter')
            ->setParameter('chapter', $chapterId)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
}
