<?php

namespace App\Repository;

use App\Entity\HistoryDeliveryTask;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<HistoryDeliveryTask>
 *
 * @method HistoryDeliveryTask|null find($id, $lockMode = null, $lockVersion = null)
 * @method HistoryDeliveryTask|null findOneBy(array $criteria, array $orderBy = null)
 * @method HistoryDeliveryTask[]    findAll()
 * @method HistoryDeliveryTask[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class HistoryDeliveryTaskRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, HistoryDeliveryTask::class);
    }

    public function add(HistoryDeliveryTask $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(HistoryDeliveryTask $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getHistoryDeliveryTask($taskUser){
		$query = $this->createQueryBuilder('h');

		$query->where('h.taskUser = :taskUser')
			->andWhere('h.state > :state')
			->setParameter('taskUser', $taskUser)
			->setParameter('state', 0);

		return $query->getQuery()->getResult();

	}
}
