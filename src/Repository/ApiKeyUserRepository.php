<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ApiKeyUser;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ApiKeyUser>
 *
 * @method ApiKeyUser|null find($id, $lockMode = null, $lockVersion = null)
 * @method ApiKeyUser|null findOneBy(array $criteria, array $orderBy = null)
 * @method ApiKeyUser[]    findAll()
 * @method ApiKeyUser[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ApiKeyUserRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ApiKeyUser::class);
    }

    public function add(ApiKeyUser $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ApiKeyUser $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findByApiKey(string $apiKey): ?ApiKeyUser
    {
        return $this->findOneBy(['apiKey' => $apiKey, 'active' => true]);
    }
}
