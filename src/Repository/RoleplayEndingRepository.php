<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\RoleplayEnding;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<RoleplayEnding>
 *
 * @method RoleplayEnding|null find($id, $lockMode = null, $lockVersion = null)
 * @method RoleplayEnding|null findOneBy(array $criteria, array $orderBy = null)
 * @method RoleplayEnding[]    findAll()
 * @method RoleplayEnding[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RoleplayEndingRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RoleplayEnding::class);
    }

    public function add(RoleplayEnding $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(RoleplayEnding $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getMaxOrder($projectId)
    {
        $qb = $this->createQueryBuilder('e');
        $qb->select('MAX(e.order) as max_order')
            ->where('e.project = :project')
            ->setParameter('project', $projectId);

        try {
            $result = $qb->getQuery()->getSingleResult();
        } catch (NoResultException|NonUniqueResultException $e) {
            $result = ['max_order' => 0];
        }

        return $result['max_order'] + 1;
    }
}
