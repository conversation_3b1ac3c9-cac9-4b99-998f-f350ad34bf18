<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\UrlShortener;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\HttpFoundation\Request;

/**
 * @extends ServiceEntityRepository<UrlShortener>
 *
 * @method UrlShortener|null find($id, $lockMode = null, $lockVersion = null)
 * @method UrlShortener|null findOneBy(array $criteria, array $orderBy = null)
 * @method UrlShortener[]    findAll()
 * @method UrlShortener[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UrlShortenerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UrlShortener::class);
    }

    public function add(UrlShortener $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(UrlShortener $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Get or Generate a short URL for specific element.
     */
    public function getShortUrl(Request $request, string $longUrl): string
    {
        $requestUri = $request->getRequestUri();                // return path, e.g /admin/course/1/share
        $uri = $request->getUri();                              // return full uri, e.g https://example.com/admin/course/1/share
        $baseUrl = str_replace($requestUri, '', $uri);  // get base uri, e.g https://example.com

        $urlShortener = $this->findOneBy(['longUrl' => $longUrl]);
        if (!$urlShortener) {
            $urlShortener = new UrlShortener();
            $shortUrl = $this->validateAndGenerateShortUrl($urlShortener);
            $urlShortener->setLongUrl($longUrl)
                ->setShortUrl($shortUrl);
            $this->_em->persist($urlShortener);
            $this->_em->flush();
        }

        return "$baseUrl/campus/?share=" . $urlShortener->getShortUrl();
    }

    public function getLongUrl(string $shortUrl): ?string
    {
        $urlShortener = $this->findOneBy(['shortUrl' => $shortUrl]);
        if (!$shortUrl) {
            return null;
        }

        return $urlShortener->getLongUrl();
    }

    private function validateAndGenerateShortUrl(): string
    {
        $stringSpace = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        $maxLetters = mb_strlen($stringSpace, '8bit') - 1;
        $res = [];

        for ($i = 0; $i < 6; ++$i) {
            $res[] = $stringSpace[random_int(0, $maxLetters)];
        }
        $value = implode('', $res);

        $res = $this->findOneBy(['shortUrl' => $value]);
        if ($res) {
            return $this->validateAndGenerateShortUrl();
        } else {
            return $value;
        }
    }
}
