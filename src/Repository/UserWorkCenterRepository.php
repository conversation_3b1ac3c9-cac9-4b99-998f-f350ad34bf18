<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\UserWorkCenter;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<UserWorkCenter>
 *
 * @method UserWorkCenter|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserWorkCenter|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserWorkCenter[]    findAll()
 * @method UserWorkCenter[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserWorkCenterRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserWorkCenter::class);
    }

    public function add(UserWorkCenter $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(UserWorkCenter $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findAllOrderByName(): array
    {
        $query = $this->createQueryBuilder('s')
            ->select('s')
            ->orderBy('s.name', 'ASC');

        return $query->getQuery()->getResult();
    }
}
