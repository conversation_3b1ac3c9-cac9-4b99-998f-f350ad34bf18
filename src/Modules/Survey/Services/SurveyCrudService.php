<?php

declare(strict_types=1);

namespace App\Modules\Survey\Services;

use App\Admin\Traits\FilterValuesTrait;
use App\Admin\Traits\SerializerTrait;
use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\Nps;
use App\Entity\NpsQuestion;
use App\Entity\NpsQuestionDetail;
use App\Entity\NpsQuestionDetailTranslation;
use App\Entity\NpsQuestionTranslation;
use App\Entity\Survey;
use App\Entity\SurveyAnnouncement;
use App\Entity\SurveyCourse;
use App\Entity\SurveyTranslation;
use App\Modules\Common\Services\BaseService;
use App\Modules\Survey\Repository\SurveyCourseModuleRepository;
use App\Modules\Survey\Repository\SurveyModuleRepository;
use App\Service\SettingsService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\NotSupported;
use Symfony\Component\Security\Core\Security;

class SurveyCrudService extends BaseService
{
    use SerializerTrait;
    use FilterValuesTrait;
    private SurveyModuleRepository $surveyRepository;

    private SurveyCourseModuleRepository $surveyCourseModuleRepository;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        Security $security,
        SurveyModuleRepository $surveyRepository,
        SurveyCourseModuleRepository $surveyCourseModuleRepository
    ) {
        parent::__construct($em, $settings, $security);
        $this->surveyRepository = $surveyRepository;
        $this->surveyCourseModuleRepository = $surveyCourseModuleRepository;
    }

    public function getSurveys($page)
    {
        $query = $this->surveyRepository->getTotalSurvey();
        $totalItemsQuery = clone $query;
        $totalItems = $totalItemsQuery->select('count(s.id) as total')->getQuery()->getSingleScalarResult();
        $pageSize = 10;

        $surveys = $this->surveyRepository->getSurvey($pageSize, $page);

        return [
            'items' => $this->getSurveysData($surveys),
            'total-items' => (int) $totalItems,
            'isSuperAdmin' => $this->getUser()->isSuperAdmin(),
        ];
    }

    public function getSurveysData($surveys)
    {
        foreach ($surveys as $current) {
            $survey = $this->getSurveyInfo($current['id']);
            $translations = $this->getTranslationSurvey($survey);
            $row = [
                'id' => $current['id'],
                'name' => $current['name'],
                'active' => $current['active'],
                'firstName' => $current['firstName'],
                'lastName' => $current['lastName'],
                'isMain' => $current['isMain'],
                'avatar' => $current['avatar'],
                'totalQuestions' => $current['totalQuestions'],
                'translations' => $translations,
                'warningLocales' => $this->setWarningLocales($translations),
            ];

            $datos[] = $row;
        }

        return $datos;
    }

    public function formatSurveyStructure(Survey $survey): array
    {
        $translations = $this->getTranslationSurvey($survey);

        return [
            'id' => $survey->getId(),
            'name' => $survey->getName(),
            'description' => $survey->getDescription(),
            'applyTo' => $survey->getApplyTo(),
            'meta' => $survey->getMeta(),
            'isMain' => $survey->isIsMain(),
            'translations' => $translations,
            'warningLocales' => $this->setWarningLocales($translations),
        ];
    }

    public function setWarningLocales(array $translations)
    {
        $warningLocales = [];

        foreach ($translations as $translation) {
            if (empty($translation['name'])) {
                $warningLocales[$translation['locale']] = true;
            } else {
                $warningLocales[$translation['locale']] = false;
            }
        }
        $warningLocales[$this->getUser()->getLocale()] = false;

        return $warningLocales;
    }

    public function getTranslationSurvey(Survey $survey): array
    {
        $translations = [];
        $locale = $this->getUser()->getLocale();
        $locales = $this->settings->get('app.languages');
        $surveyLocal = [];
        /** @var SurveyTranslation $translation */
        foreach ($survey->getTranslations() as $translation) {
            $translations[] = [
                'locale' => $translation->getLocale(),
                'name' => $translation->getName() ?? '',
                'description' => $translation->getDescription() ?? '',
            ];
            array_push($surveyLocal, $translation->getLocale());
        }

        if (!\in_array($locale, $surveyLocal)) {
            $translations[] = [
                'locale' => $locale,
                'name' => $survey->getName(),
                'description' => $survey->getDescription(),
            ];
            array_push($surveyLocal, $locale);
        }

        foreach ($locales as $local) {
            if (!\in_array($local, $surveyLocal)) {
                $translations[] = [
                    'locale' => $local,
                    'name' => '',
                    'description' => '',
                ];
            }
        }

        return $translations;
    }

    public function initTranslations(): array
    {
        $translations = [];
        $locales = $this->settings->get('app.languages');

        foreach ($locales as $local) {
            $translations[] = [
                'locale' => $local,
                'name' => '',
                'description' => '',
                'text' => ''
            ];
        }

        return $translations;
    }

    public function getCoursesForIds($courseIds)
    {
        $courseQuery = $this->em->getRepository(Course::class)->createQueryBuilder('c');
        $courses = $courseQuery->select('c')
            ->andWhere($courseQuery->expr()->in('c.id', $courseIds))
            ->orderBy('c.id', 'ASC')
            ->getQuery()
            ->getResult();

        return $courses;
    }

    /**
     * @throws NotSupported
     */
    public function getArrayCollectionNpsQuestion($questionsData, Survey $survey): ArrayCollection
    {
        $position = 1;
        $npsQuestions = new ArrayCollection();
        foreach ($questionsData as $index => $q) {
            $question = $this->findOrCreateQuestion($q['id']);

            $question->setQuestion($q['statement'])
                ->setType($q['type'])
                ->setActive($q['active'])
                ->setRandomOrder($q['randomOrder'])
                ->setMain($q['main'])
                ->setPosition($index + 1)
                ->setSource(0)
                ->setSurvey($survey);

            if (\in_array($q['type'], [NpsQuestion::TYPE_CHECKBOX, NpsQuestion::TYPE_RADIO])) {
                $detailPosition = 0;

                foreach ($q['questions'] as $d) {
                    $detail = !empty($d['id'])
                        ? $this->em->getRepository(NpsQuestionDetail::class)->find($d['id'])
                        : new NpsQuestionDetail();

                    if ($value = $this->getValueQuestionDetail($d['translations'])) {
                        $detail->setNpsQuestion($question)
                            ->setValue($value)
                            ->setPosition($detailPosition++);

                        $question->addNpsQuestionDetail($detail);
                    }

                    $this->addNpsQuestionDetailTranslation($detail, $d['translations']);
                }
            }

            $npsQuestions->add($question);
            $this->addNpsQuestionTranslation($question, $q['translations']);
            ++$position;
        }

        return $npsQuestions;
    }

    private function getValueQuestionDetail(array $translations): string
    {
        $locale = $this->getUser()->getLocale();
        $value = '';
        foreach ($translations as $translation) {
            if ($translation['locale'] === $locale) {
                $value = $translation['answer'];
            }
        }

        return $value;
    }

    private function addNpsQuestionTranslation(NpsQuestion $question, array $translations): void
    {
        foreach ($translations as $t) {
            $tLocale = $t['locale'] ?? null;
            $tQuestion = $t['question'] ?? null;
            $translation = $this->em->getRepository(NpsQuestionTranslation::class)->findOneBy([
                'translatable' => $question,
                'locale' => $tLocale,
            ]);

            if (empty($tQuestion)) {
                if ($translation) {
                    $this->em->remove($translation);
                }
                continue;
            }

            if (!$translation) {
                $translation = new NpsQuestionTranslation();
                $translation->setTranslatable($question);
                $translation->setLocale($tLocale);
            }

            $translation->setQuestion($tQuestion);

            $this->em->persist($translation);
        }
    }

    private function addNpsQuestionDetailTranslation(NpsQuestionDetail $npsQuestionDetail, array $translations): void
    {
        foreach ($translations as $t) {
            $tLocale = $t['locale'] ?? null;
            $tValue = isset($t['answer']) ? $t['answer'] : null;

            $translation = $this->em->getRepository(NpsQuestionDetailTranslation::class)->findOneBy([
                'translatable' => $npsQuestionDetail,
                'locale' => $tLocale,
            ]);

            if (empty($tValue)) {
                if ($translation) {
                    $this->em->remove($translation);
                }
                continue;
            }

            if (!$translation) {
                $translation = new NpsQuestionDetailTranslation();
                $translation->setTranslatable($npsQuestionDetail);
                $translation->setLocale($tLocale);
            }

            $translation->setValue($tValue);

            $this->em->persist($translation);
        }
    }

    /**
     * @throws NotSupported
     */
    private function findOrCreateQuestion($id): NpsQuestion
    {
        if (!empty($id)) {
            $question = $this->em->getRepository(NpsQuestion::class)->find($id);

            return $question ?: new NpsQuestion();
        }

        return new NpsQuestion();
    }

    public function getNpsQuestionDetaillsNpsQuestion(NpsQuestion $question)
    {
        $npsQuestionDetail = $this->em->getRepository(NpsQuestionDetail::class)->getQuestionsBySurvey($question);
        $locales = $this->settings->get('app.languages');

        $questions = [];

        foreach ($npsQuestionDetail as $detail) {
            $npsQuestionDetail = $this->em->getRepository(NpsQuestionDetail::class)->find($detail['id']);

            $translations = [];
            foreach ($locales as $locale) {
                $translations[] = [
                    'locale' => $locale,
                    'answer' => $this->em->getRepository(NpsQuestionDetail::class)->getNpsQuestionDetailTranslation($npsQuestionDetail, $locale),
                ];
            }

            $questions[] = [
                'id' => $detail['id'],
                'value' => $detail['value'],
                'translations' => $translations
            ];
        }

        return $questions;
    }

    public function addCourseToSurvey(Survey $survey, Course $course)
    {
        $surveyCourse = $this->getSurveyCourseForCourseAndSurvey($survey, $course);
        if (!$surveyCourse) {
            $surveyCourse = new SurveyCourse();
            $surveyCourse->setSurvey($survey)
                ->setCourse($course);
            $this->em->persist($surveyCourse);
            $this->em->flush();
        }
    }

    public function getSurveyCourseForCourseAndSurvey(Survey $survey, Course $course)
    {
        return $this->em->getRepository(SurveyCourse::class)->findOneBy(['survey' => $survey, 'course' => $course]);
    }

    public function getSurveyAnnoucementForAnnoucementAndSurvey(Survey $survey, Announcement $announcement)
    {
        return $this->em->getRepository(SurveyAnnouncement::class)->findOneBy(['announcement' => $announcement, 'survey' => $survey]);
    }

    public function getSurveyInfo($id)
    {
        return $this->em->getRepository(Survey::class)->findOneBy(['id' => $id]);
    }

    public function delTranslationSurvey(Survey $survey)
    {
        /** @var SurveyTranslation $translation */
        foreach ($survey->getTranslations() as $translation) {
            $this->em->remove($translation);
        }
        $this->em->flush();
    }

    public function cloneSurvey(Survey $survey): void
    {
        $clone = clone $survey;
        $clone->setId(null)
            ->setName($survey->getName() . ' - Clone')
            ->setIsMain(false)
            ->setCreatedAt(new \DateTime())
            ->setUpdatedAt(new \DateTime())
            ->setNpsQuestions(new ArrayCollection())
        ;
        $questions = $survey->getNpsQuestions();
        foreach ($questions as $q) {
            $question = clone $q;
            $question->setId(null);
            $clone->addNpsQuestion($question);
            $this->cloneNpsQuestionDetail($question);
        }
        $this->em->persist($clone);
        $this->em->flush();

        $this->cloneNpsQuestionTranslation($survey, $clone);
    }

    public function getNpsQuestionsSurvey(Survey $survey)
    {
        $questions = $this->em->getRepository(NpsQuestion::class)->findBy(['survey' => $survey], ['position' => 'ASC']);
        $questionsData = [];

        foreach ($questions as $question) {
            $d = $this->formatNpsQuestionStructure($question);
            if (NpsQuestion::TYPE_CHECKBOX === $question->getType() || NpsQuestion::TYPE_RADIO === $question->getType()) {
                $d['questions'] = $this->getNpsQuestionDetaillsNpsQuestion($question);
            }
            $questionsData[] = $d;
        }

        return $questionsData;
    }

    public function cloneNpsQuestionTranslation(Survey $survey, Survey $cloneSurvey): void
    {
        $questionsSurvey = $this->em->getRepository(NpsQuestion::class)->findBy(['survey' => $survey], ['position' => 'ASC']);
        $questionsCloneSurvey = $this->em->getRepository(NpsQuestion::class)->findBy(['survey' => $cloneSurvey], ['position' => 'ASC']);
        foreach ($questionsSurvey as $index => $question) {
            $npsQuestionTranslations = $this->em->getRepository(NpsQuestionTranslation::class)->findBy(['translatable' => $question]);
            foreach ($npsQuestionTranslations as $npsQuestionTranslation) {
                $translation = clone $npsQuestionTranslation;
                $translation->setTranslatable($questionsCloneSurvey[$index]);
                $this->em->persist($translation);
            }
        }
        $this->cloneNpsQuestionDetailTraslations($questionsSurvey, $questionsCloneSurvey);

        $this->em->flush();
    }

    public function cloneNpsQuestionDetail(NpsQuestion $npsQuestion): void
    {
        $npsQuestionDetails = $this->em->getRepository(NpsQuestionDetail::class)->findBy(['npsQuestion' => $npsQuestion]);
        foreach ($npsQuestionDetails as $npsQuestionDetail) {
            $cloneNpsQuestionDetail = clone $npsQuestionDetail;
            $this->em->persist($cloneNpsQuestionDetail);
        }
    }

    public function cloneNpsQuestionDetailTraslations(array $questionsSurvey, array $questionsCloneSurvey): void
    {
        foreach ($questionsSurvey as $index => $question) {
            $npsQuestionDetails = $this->em->getRepository(NpsQuestionDetail::class)->findBy(['npsQuestion' => $question], ['position' => 'ASC']);
            $cloneNpsQuestionDetails = $this->em->getRepository(NpsQuestionDetail::class)
                ->findBy(['npsQuestion' => $questionsCloneSurvey[$index]], ['position' => 'ASC']);

            foreach ($npsQuestionDetails as $indexDetail => $npsQuestionDetail) {
                $npsQuestionDetailTranslations = $this->em->getRepository(NpsQuestionDetailTranslation::class)->findBy(['translatable' => $npsQuestionDetail]);
                foreach ($npsQuestionDetailTranslations as $npsQuestionDetailTranslation) {
                    $translation = clone $npsQuestionDetailTranslation;
                    $translation->setTranslatable($cloneNpsQuestionDetails[$indexDetail]);
                    $this->em->persist($translation);
                }

                $this->em->flush();
            }
        }
    }

    public function formatNpsQuestionStructure(NpsQuestion $question): array
    {
        $translations = $this->getNpsQuestionsTranslate($question);

        return [
            'id' => $question->getId(),
            'statement' => $question->getQuestion(),
            'position' => $question->getPosition(),
            'active' => $question->isActive(),
            'main' => $question->getMain(),
            'type' => $question->getType(),
            'randomOrder' => $question->isRandomOrder(),
            'isConfidential' => $question->isIsConfidential(),
            'isRequired' => $question->isIsRequired(),
            'questions' => [],
            'translations' => $translations,
            'warnings' => $this->setWarningnpsQuestionLocales($translations),
        ];
    }

    public function getNpsQuestionsTranslate($npsQuestion)
    {
        $translations = [];

        $locales = $this->settings->get('app.languages');

        foreach ($locales as $local) {
            $translations[] = [
                'locale' => $local,
                'question' => $this->surveyCourseModuleRepository->getQuestionTranslation($npsQuestion, $local),
            ];
        }

        return $translations;
    }

    public function setWarningnpsQuestionLocales(array $translations)
    {
        $warningLocales = [];

        foreach ($translations as $translation) {
            if (empty($translation['question'])) {
                $warningLocales[$translation['locale']] = true;
            } else {
                $warningLocales[$translation['locale']] = false;
            }
        }
        $warningLocales[$this->getUser()->getLocale()] = false;

        return $warningLocales;
    }

    public function deleteCourseFromSurvey(Survey $survey, Course $course)
    {
        $surveyCourse = $this->getSurveyCourseForCourseAndSurvey($survey, $course);
        if ($surveyCourse) {
            $this->em->remove($surveyCourse);
            $this->em->flush();

            return true;
        }

        return false;
    }

    public function addAnnouncementToSurvey(Survey $survey, Announcement $announcement)
    {
        $surveyAnnouncement = $this->getSurveyAnnoucementForAnnoucementAndSurvey($survey, $announcement);
        if (!$surveyAnnouncement) {
            $surveyAnnouncement = new SurveyAnnouncement();
            $surveyAnnouncement->setSurvey($survey)
                ->setAnnouncement($announcement);
            $this->em->persist($surveyAnnouncement);
            $this->em->flush();

            return true;
        }

        return false;
    }

    public function deleteAnnouncementFromSurvey(Survey $survey, Announcement $announcement)
    {
        $surveyAnnouncement = $this->getSurveyAnnoucementForAnnoucementAndSurvey($survey, $announcement);
        if ($surveyAnnouncement) {
            $this->em->remove($surveyAnnouncement);
            $this->em->flush();
        }
    }

    public function deleteSurveyQuestion(NpsQuestion $question)
    {
        $this->em->remove($question);
        $this->em->flush();
    }

    public function deleteSurvey(Survey $survey)
    {
        $this->em->remove($survey);
        $this->em->flush();
    }

    public function hasSurveyQuestionAssociated(Survey $survey): bool
    {
        $surveyQuestion = $this->em->getRepository(NpsQuestion::class)->findOneBy(['survey' => $survey]);
        $nps = $this->em->getRepository(Nps::class)->findOneBy(['question' => $surveyQuestion]);

        return $nps
            || $this->em->getRepository(SurveyCourse::class)->findOneBy(['survey' => $survey])
            || $this->em->getRepository(SurveyAnnouncement::class)->findOneBy(['survey' => $survey]);
    }

    public function getCoursesBySurvey(Survey $survey): array
    {
        return $this->em->getRepository(SurveyCourse::class)->getCoursesBySurvey($survey);
    }

    public function getAnnouncementsBySurvey(Survey $survey): array
    {
        return $this->em->getRepository(SurveyAnnouncement::class)->getAnnouncementsBySurvey($survey);
    }

    public function setSurveyData(Survey $survey, array $content = []): Survey
    {
        $userLocale = $this->getUser()->getLocale();
        $translations = $content['survey']['translations'];
        $name = $this->getNameSurveyByLocale($userLocale, $translations);
        $description = $this->getDescriptionSurveyByLocale($userLocale, $translations);

        $applyTo = $content['applyTo'] ?? Survey::APPLY_ALL;

        $survey->setName($name)
            ->setDescription($description)
            ->setApplyTo($applyTo)

        ;

        $coursesData = $content['courses'] ?? [];
        $announcementsData = $content['announcements'] ?? [];

        if ($coursesData) {
            $this->applyToCurrentCourse($survey, $coursesData);
        }else{
            $this->emptyCurrentCourse($survey);
        }

        if ($announcementsData) {
            $this->applyToCurrentAnnouncements($survey, $announcementsData);
        }else{
            $this->emptyCurrentAnnouncements($survey);
        }

        $questionsData = $content['questions'] ?? [];
        $npsQuestions = $this->getArrayCollectionNpsQuestion($questionsData, $survey);
        $survey->setNpsQuestions($npsQuestions);

        $this->em->persist($survey);
        $this->saveSurveyTranslations($survey, $translations);
        $this->em->flush();

        return $survey;
    }

    public function getNameSurveyByLocale($userLocale, $translations)
    {
        $name = '';
        foreach ($translations as $translation) {
            if ($userLocale === $translation['locale']) {
                $name = $translation['name'];
            }
        }

        return $name;
    }

    public function getDescriptionSurveyByLocale($userLocale, $translations)
    {
        $description = '';
        foreach ($translations as $translation) {
            if ($userLocale === $translation['locale']) {
                $description = $translation['description'];
            }
        }

        return $description;
    }

    public function saveSurveyTranslations(Survey $survey, array $translations)
    {
        foreach ($translations as $t) {
            $tLocale = $t['locale'] ?? null;
            $tName = $t['name'] ?? null;
            $tDescription = $t['description'] ?? null;
            $translation = $this->em->getRepository(SurveyTranslation::class)->findOneBy([
                'translatable' => $survey,
                'locale' => $tLocale,
            ]);

            if (empty($tName) && empty($tDescription)) {
                if ($translation) {
                    $this->em->remove($translation);
                }
                continue;
            }

            if (!$translation) {
                $translation = new SurveyTranslation();
                $translation->setTranslatable($survey);
                $translation->setLocale($tLocale);
            }

            $translation->setName($tName)
                ->setDescription($tDescription)
            ;

            $this->em->persist($translation);
        }

        $this->em->flush();
    }

    public function getQuestionsNpsMain(): array
    {
        $questionsDefault = $this->surveyCourseModuleRepository->getQuestionNpsMain();
        $questions = [];
        foreach ($questionsDefault as $question) {
            $questions[] = $this->formatNpsQuestionStructure($question);
        }

        $questions = array_map(function ($question) {
            $question['id'] = -1 * $question['id'];

            return $question;
        }, $questions);

        return $questions;
    }

    private function findOrCreateNpsQuestionDetail(array $d): NpsQuestionDetail
    {
        if (!empty($d['id'])) {
            $detail = $this->em->getRepository(NpsQuestionDetail::class)->find($d['id']);

            return $detail ?: new NpsQuestionDetail();
        }

        return new NpsQuestionDetail();
    }

    private function saveNpsQuestionDetails(NpsQuestion $question, array $q): void
    {
        if (NpsQuestion::TYPE_CHECKBOX === $q['type'] || NpsQuestion::TYPE_RADIO === $q['type']) {
            $detailPosition = 0;
            foreach ($q['questions'] as $d) {
                $detail = $this->findOrCreateNpsQuestionDetail($d);

                if (!empty($d['value'])) {
                    $detail->setNpsQuestion($question)
                        ->setValue($d['value'])
                        ->setPosition($detailPosition)
                    ;
                    $question->addNpsQuestionDetail($detail);
                    ++$detailPosition;
                }
            }
        }
    }

    private function getArrayIdsForData(array $aDatas): array
    {
        $dataIds = [];
        foreach ($aDatas as $c) {
            $dataIds[] = $c['id'];
        }

        return $dataIds;
    }

    public function getDefaultQuestion(): array
    {
        return $this->surveyCourseModuleRepository->getQuestionsSurveyByDefault($this->getUser()->getLocale());
    }

    private function applyToCurrentCourse(Survey $survey, $coursesData): void
    {
        $courseIds = array_unique($this->getArrayIdsForData($coursesData), SORT_NUMERIC);
        $currentCourses = $survey->getSurveyCourses();

        foreach ($currentCourses as $current) {
            $found = false;
            $index = -1;
            for ($i = 0; $i < \count($courseIds); ++$i) {
                if ($courseIds[$i] == $current->getCourse()->getId()) {
                    $found = true;
                    $index = $i;
                    break;
                }
            }

            if ($found) {
                array_splice($courseIds, $index, 1);
            } else {
                $survey->removeSurveyCourse($current);
            }
        }

        $this->addSurveyCourse($survey, $courseIds);
    }

    private function emptyCurrentCourse(Survey $survey): void
    {
        $currentCourses = $survey->getSurveyCourses();
        foreach($currentCourses as $currentCourse){
            $survey->removeSurveyCourse($currentCourse);
        }
    } 

    private function emptyCurrentAnnouncements(Survey $survey): void
    {
        $currentAnnouncements = $survey->getSurveyAnnouncements();
        foreach($currentAnnouncements as $currentAnnouncement){
            $survey->removeSurveyAnnouncement($currentAnnouncement);
        }
    } 

    private function applyToCurrentAnnouncements(Survey $survey, $announcementsData): void
    {
        $announcementIds = array_unique($this->getArrayIdsForData($announcementsData), SORT_NUMERIC);
        $currentAnnouncements = $survey->getSurveyAnnouncements();

        foreach ($currentAnnouncements as $announcement) {
            $found = false;
            $index = -1;
            for ($i = 0; $i < \count($announcementIds); ++$i) {
                if ($announcementIds[$i] == $announcement->getAnnouncement()->getId()) {
                    $found = true;
                    $index = $i;
                    break;
                }
            }
            if ($found) {
                array_splice($announcementIds, $index, 1);
            } else {
                $survey->removeSurveyAnnouncement($announcement);
            }
        }

        $this->addSurveyAnnouncement($survey, $announcementIds);
    }

    private function addSurveyCourse(Survey $survey, $courseIds): void
    {
        if (!empty($courseIds)) {
            $courses = $this->em->getRepository(Course::class)->getCourseInfoForIds($courseIds);
            /** @var Course $course */
            foreach ($courses as $course) {
                $surveyCourse = new SurveyCourse();
                $surveyCourse->setSurvey($survey)
                    ->setCourse($course);
                $survey->addSurveyCourse($surveyCourse);
            }
        }
    }

    private function addSurveyAnnouncement(Survey $survey, $announcementIds)
    {
        if (!empty($announcementIds)) {
            $announcements = $this->em->getRepository(Announcement::class)
                ->getAnnoucementForInAnnoucementsIds($announcementIds);
            /** @var Announcement $announcement */
            foreach ($announcements as $announcement) {
                $surveyAnnouncement = new SurveyAnnouncement();

                $surveyAnnouncement->setSurvey($survey)
                    ->setAnnouncement($announcement);
                $survey->addSurveyAnnouncement($surveyAnnouncement);
            }
        }
    }

    public function setSurveyApplyTo(Survey $survey, array $content): void
    {
        if (!empty($content['applyTo'])) {
            $survey->setApplyTo($content['applyTo']);
            $this->em->persist($survey);
            $this->em->flush();
        }
    }

    public function saveSurveyQuestion(Survey $survey, $request): int
    {
        $question = $this->findOrCreateQuestion($request->get('id', null));
        $question->setQuestion($request->get('statement'))
            ->setType($request->get('type'))
            ->setActive($this->getValueAsBoolean($request->get('active')))
            ->setMain(false)
            ->setRandomOrder($this->getValueAsBoolean($request->get('randomOrder')))
            ->setPosition($request->get('position', 0))
            ->setSource($request->get('source', 0))
        ;

        $survey->addNpsQuestion($question);

        $this->em->persist($question);
        $this->em->flush();

        return $question->getId();
    }

    public function activateQuestion(NpsQuestion $question, $content): bool
    {
        if (isset($content['active'])) {
            $question->setActive($content['active']);
            $this->em->persist($question);
            $this->em->flush();

            return true;
        }

        return false;
    }

    public function changePosition(array $questions): void
    {
        foreach ($questions as $question) {
            $npsQuestion = $this->em->getRepository(NpsQuestion::class)->find($question['id']);
            $npsQuestion->setPosition($question['position']);
            $this->em->persist($npsQuestion);
        }
        $this->em->flush();
    }

    public function changeIsConfidential(NpsQuestion $question, $isConfidential): void
    {
        $question->setIsConfidential($isConfidential);
        $this->em->persist($question);
        $this->em->flush();
    }

    public function changeIsRequired(NpsQuestion $question, $isRequired): void
    {
        $question->setIsRequired($isRequired);
        $this->em->persist($question);
        $this->em->flush();
    }

    public function activateSurvey(Survey $survey, $active): void
    {
        $survey->setActive($active);
        $this->em->persist($survey);
        $this->em->flush();
    }
}
