<?php

declare(strict_types=1);

namespace App\Modules\Survey\Controller;

use App\Admin\Traits\FilterValuesTrait;
use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\VueAppDefaultConfiguration;
use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\NpsQuestion;
use App\Entity\Survey;
use App\Entity\SurveyAnnouncement;
use App\Entity\SurveyCourse;
use App\Modules\Common\Controller\BaseVueController;
use App\Modules\Survey\Services\SurveyCrudService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class SurveyCrudController extends BaseVueController
{
    use VueAppDefaultConfiguration;
    use SerializerTrait;
    use FilterValuesTrait;

    private AdminContextProvider $context;
    private JWTManager $JWTManager;
    private SurveyCrudService $surveyCrudService;

    public function __construct(
        AdminContextProvider $context,
        JWTManager $JWTManager,
        SettingsService $settings,
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        TranslatorInterface $translator,
        SurveyCrudService $crudService
    ) {
        parent::__construct($settings, $em, $logger, $requestStack, $translator);

        $this->context = $context;
        $this->JWTManager = $JWTManager;
        $this->surveyCrudService = $crudService;
    }

    public static function getEntityFqcn(): string
    {
        return Survey::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud->overrideTemplate('crud/index', 'admin/survey/app.html.twig');
    }

    /**
     * @Rest\Get("/admin/surveys/{page}", requirements={"page"="\d+"})
     *
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function getSurveys(Request $request, int $page = 1): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->surveyCrudService->getSurveys($page),
        ]);
    }

    /**
     * @Rest\Patch("/admin/survey/{id}/activate", requirements={"id"="\d+"})
     */
    public function activateSurvey(Request $request, Survey $survey): Response
    {
        $content = json_decode($request->getContent(), true);

        if (!isset($content['active']) || !\is_bool($content['active'])) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'data' => 'The "active" field is required and must be a boolean.',
            ]);
        }

        $this->surveyCrudService->activateSurvey($survey, $content['active']);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'Survey activation status updated successfully.',
        ]);
    }

    /**
     * @Rest\Delete("/admin/survey/{id}", requirements={"id"="\d+"})
     */
    public function deleteSurvey(Survey $survey): Response
    {
        if ($this->surveyCrudService->hasSurveyQuestionAssociated($survey)) {
            return $this->sendResponse([
                'status' => Response::HTTP_CONFLICT,
                'error' => true,
                'data' => 'The survey has questions associated.',
            ]);
        }

        try {
            $this->surveyCrudService->deleteSurvey($survey);

            return $this->sendResponse([
                'status' => Response::HTTP_NO_CONTENT,
                'error' => false,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    }

    /**
     * @Rest\Post("/admin/survey/{id}/clone", requirements={"id"="\d+"})
     */
    public function cloneSurvey(Survey $survey): Response
    {
        $this->surveyCrudService->cloneSurvey($survey);

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
        ]);
    }

    /**
     * @return Survey|Response
     */
    private function setSurveyData(Survey $survey, array $content = [])
    {
        try {
            $userLocale = $this->getUser()->getLocale();
            $translations = $content['survey']['translations'];

            $name = $this->surveyCrudService->getNameSurveyByLocale($userLocale, $translations);

            if (empty($name)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => 'Survey name is required',
                ]);
            }

            return $this->surveyCrudService->setSurveyData($survey, $content);
        } catch (\Exception $e) {
            $errorMessage = \sprintf('Error on line  %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());

            return $this->sendResponse(['error' => true, 'status' => Response::HTTP_INTERNAL_SERVER_ERROR, 'message' => $errorMessage]);
        }
    }

    /**
     * @Rest\Post("/admin/survey/{id}/update", name="admin_api_update_survey", requirements={"id"="\d+"})
     */
    public function updateSurvey(Survey $survey, Request $request): Response
    {
        try {
            $result = $this->setSurveyData($survey, json_decode($request->getContent(), true));
            if ($result instanceof Response) {
                return $result;
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $survey->getId(),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    }

    /**
     * @Rest\Post("/admin/api/v1/survey", name="admin_api_create_survey")
     */
    public function saveNewSurvey(Request $request): Response
    {
        try {
            $survey = new Survey();
            $survey->setActive(false);
            $result = $this->setSurveyData($survey, json_decode($request->getContent(), true));
            if ($result instanceof Response) {
                return $result;
            }

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false,
                'data' => $result->getId(),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    }

    /**
     * @Rest\Get("/admin/survey/{id}", requirements={"id"="\d+"})
     *
     * @deprecated
     * @see SurveyController
     */
    public function getSurvey(Survey $survey): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'survey' => $this->surveyCrudService->formatSurveyStructure($survey),
                'questions' => $this->surveyCrudService->getNpsQuestionsSurvey($survey),
                'courses' => $this->surveyCrudService->getCoursesBySurvey($survey),
                'announcements' => $this->surveyCrudService->getAnnouncementsBySurvey($survey),
                'isSuperAdmin' => $this->getUser()->isSuperAdmin(),
                'userLocale' =>  $this->getUser()->getLocale(),
            ],
        ]);
    }

    /**
     * @Rest\Post("/admin/survey/question/{id}/activate")
     */
    public function activateQuestion(Request $request, NpsQuestion $question): Response
    {
        $code = Response::HTTP_ACCEPTED;
        if ($this->surveyCrudService->activateQuestion($question, json_decode($request->getContent(), true))) {
            $code = Response::HTTP_OK;
        }

        return $this->sendResponse([
            'status' => $code,
            'error' => false,
        ]);
    }

    private function validateSurveyTypeAndStatement(Request $request)
    {
        $statement = $request->get('statement');
        if (empty($statement)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Statement is required',
            ]);
        }
        $type = $request->get('type');
        if (empty($type)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Question Type is required',
            ]);
        }

        return true;
    }

    /**
     * @Rest\Post("/admin/survey/{survey}/save-question", name="admin_save_survey_question")
     */
    public function saveSurveyQuestion(Request $request, Survey $survey): Response
    {
        try {
            if (($result = $this->validateSurveyTypeAndStatement($request)) instanceof Response) {
                return $result;
            }

            $question_id = $this->surveyCrudService->saveSurveyQuestion($survey, $request);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $question_id,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Post("/admin/survey/{id}/apply-to")
     */
    public function setSurveyApplyTo(Request $request, Survey $survey): Response
    {
        $this->surveyCrudService->setSurveyApplyTo($survey, json_decode($request->getContent(), true));

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'OK',
        ]);
    }

    /**
     * @Rest\Get("/admin/survey/manual-data")
     */
    public function getSurveyManualData(): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'courses' => $this->em->getRepository(Course::class)->getCourseInfo(),
                'announcements' => $this->em->getRepository(Announcement::class)->getAnnouncementInfo(),
            ],
        ]);
    }

    /**
     * @Rest\Get("/admin/survey/{survey}/courses-announcements")
     */
    public function getSelectedCoursesAndAnnouncements(Survey $survey): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'courses' => $this->em->getRepository(SurveyCourse::class)->getCoursesBySurvey($survey),
                'announcements' => $this->em->getRepository(SurveyAnnouncement::class)->getAnnouncementAllInfoBySurvey($survey),
            ],
        ]);
    }

    /**
     * @Rest\Post("/admin/survey/{survey}/course/{course}", name="admin_add_course_to_survey")
     */
    public function addCourseToSurvey(Survey $survey, Course $course): Response
    {
        $this->surveyCrudService->addCourseToSurvey($survey, $course);

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
            'data' => 'Course in survey',
        ]);
    }

    /**
     * @Rest\Delete("/admin/survey/{survey}/course/{course}", name="admin_delete_course_from_survey")
     */
    public function deleteCourseFromSurvey(Survey $survey, Course $course): Response
    {
        $code = Response::HTTP_ACCEPTED;
        if ($this->surveyCrudService->deleteCourseFromSurvey($survey, $course)) {
            $code = Response::HTTP_OK;
        }

        return $this->sendResponse([
            'status' => $code,
            'error' => false,
            'data' => 'Survey Course deleted',
        ]);
    }

    /**
     * @Rest\Post("/admin/survey/{survey}/announcement/{announcement}", name="admin_add_announcement_to_survey")
     */
    public function addAnnouncementToSurvey(Survey $survey, Announcement $announcement): Response
    {
        $code = Response::HTTP_ACCEPTED;
        if ($this->surveyCrudService->addAnnouncementToSurvey($survey, $announcement)) {
            $code = Response::HTTP_CREATED;
        }

        return $this->sendResponse([
            'status' => $code,
            'error' => false,
            'data' => 'Survey announcement created',
        ]);
    }

    /**
     * @Rest\Delete("/admin/survey/{survey}/announcement/{announcement}", name="admin_delete_announcement_from_survey")
     */
    public function deleteAnnouncementFromSurvey(Survey $survey, Announcement $announcement): Response
    {
        $this->surveyCrudService->deleteAnnouncementFromSurvey($survey, $announcement);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'Survey Announcement deleted',
        ]);
    }

    /**
     * @Rest\Delete("/admin/survey/question/{id}")
     */
    public function deleteSurveyQuestion(NpsQuestion $question): Response
    {
        $this->surveyCrudService->deleteSurveyQuestion($question);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'DELETED',
        ]);
    }

    /**
     * @Rest\put("/admin/survey/changePosition", name="survey_admin_changePosition")
     */
    public function changePosition(Request $request): Response
    {
        try {
            $questions = json_decode($request->getContent(), true)['questions'];
            $this->surveyCrudService->changePosition($questions);

            return $this->sendResponse([
                'status' => 200,
                'error' => false,
                'data' => $questions,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => 500,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Get("/admin/survey/initForm", name="admin_initForm")
     */
    public function surveyInitForm(): Response
    {
        $translations = $this->surveyCrudService->initTranslations();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'translations' => $translations,
                'questionsMain' => $this->surveyCrudService->getQuestionsNpsMain(),
                'warningLocales' => $this->surveyCrudService->setWarningLocales($translations),
                'isSuperAdmin' => $this->getUser()->isSuperAdmin(),
            ],
        ]);
    }

    /**
     * @Rest\Put("/admin/survey/question/{id}/isRequired", name="survey_admin_isRequired")
     */
    public function changeIsRequiered(NpsQuestion $question, Request $request): Response
    {
        try {
            $this->surveyCrudService->changeIsRequired($question, json_decode($request->getContent(), true)['isRequired']);

            return $this->sendResponse([
                'status' => 200,
                'error' => false,
                'data' => $question->getId(),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => 500,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Put("/admin/survey/question/{id}/isConfidential", name="survey_admin_changeIsConfidential")
     */
    public function changeIsConfidential(NpsQuestion $question, Request $request): Response
    {
        try {
            $this->surveyCrudService->changeIsConfidential($question, json_decode($request->getContent(), true)['isConfidential']);

            return $this->sendResponse([
                'status' => 200,
                'error' => false,
                'data' => $question->getId(),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => 500,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Put("/admin/survey/{id}/isMain", name="survey_admin_changeIsMain")
     */
    public function changeIsMain(Survey $survey): Response
    {
        try {
            $foundMain = $this->em->getRepository(Survey::class)->findOneBy(['isMain' => true]);

            if ($foundMain) {
                $foundMain->setIsMain(false);
                $this->em->persist($foundMain);
            }

            $survey->setIsMain(true);
            $this->em->persist($survey);
            $this->em->flush();

            return $this->sendResponse([
                'status' => 200,
                'error' => false,
                'data' => $survey->getId(),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => 500,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }
}
