<?php

declare(strict_types=1);

namespace App\V2\Application\Query\Admin;

use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Bus\Query;

readonly class GetAnnouncementManagers implements Query
{
    public function __construct(
        private AnnouncementManagerCriteria $criteria,
        private bool $withManagers = false,
    ) {
    }

    public function getCriteria(): AnnouncementManagerCriteria
    {
        return $this->criteria;
    }

    public function needsManagers(): bool
    {
        return $this->withManagers;
    }
}
