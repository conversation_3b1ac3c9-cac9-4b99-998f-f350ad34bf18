<?php

declare(strict_types=1);

namespace App\V2\Application\Helper\Time;

/**
 * Helper class for formatting and converting time/duration values.
 * Handles conversion between different time units and formatting for display.
 * All methods are static as this helper has no dependencies.
 */
class HourFormatterHelper
{
    /**
     * Convert minutes to hours.
     */
    public static function minutesToHours(?int $minutes): ?float
    {
        if (null === $minutes || $minutes <= 0) {
            return null;
        }

        return $minutes / 60;
    }
}
