<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\Repository\AnnouncementRepository as LegacyAnnouncementRepository;
use App\Service\SettingsService;
use App\V2\Application\Hydrator\Announcement\Manager\AnnouncementManagerHydratorCollection;
use App\V2\Application\Query\Admin\GetAnnouncementManagers;
use App\V2\Domain\Announcement\Exceptions\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerHydrationCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Announcement\Manager\Exceptions\GetAnnouncementManagerException;
use App\V2\Domain\Announcement\Manager\Exceptions\ManagerNotAuthorizedException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;

readonly class GetAnnouncementManagersHandler
{
    public function __construct(
        private AnnouncementManagerRepository $announcementManagerRepository,
        private LegacyAnnouncementRepository $legacyAnnouncementRepository,
        private AnnouncementManagerHydratorCollection $hydratorCollection,
        private SettingsService $settingsService
    ) {
    }

    /**
     * @throws AnnouncementNotFoundException
     * @throws InfrastructureException
     * @throws GetAnnouncementManagerException
     * @throws ManagerNotAuthorizedException
     */
    public function handle(GetAnnouncementManagers $query): AnnouncementManagerCollection
    {
        // Check if the feature is enabled
        if (!$this->settingsService->get('app.announcement.managers.sharing')) {
            throw new ManagerNotAuthorizedException();
        }

        $criteria = $query->getCriteria();

        $announcement = $this->legacyAnnouncementRepository->findOneBy(['id' => $criteria->getAnnouncementId()]);
        if (null === $announcement) {
            throw new AnnouncementNotFoundException();
        }

        $announcementManagerCollection = $this->announcementManagerRepository
            ->findBy($criteria);

        $hydrationCriteria = AnnouncementManagerHydrationCriteria::createEmpty();

        if ($query->needsManagers()) {
            $hydrationCriteria->withUser();
        }

        if (!$hydrationCriteria->isEmpty()) {
            try {
                $this->hydratorCollection->hydrate(
                    collection: $announcementManagerCollection,
                    criteria: $hydrationCriteria,
                );
            } catch (HydratorException $e) {
                throw GetAnnouncementManagerException::fromPrevious($e);
            }
        }

        if ($query->needsManagers()) {
            $this->sortManagersByNameAndLastName($announcementManagerCollection);
        }

        return $announcementManagerCollection;
    }

    /**
     * @throws CollectionException
     */
    private function sortManagersByNameAndLastName(AnnouncementManagerCollection $collection): void
    {
        $sortedItems = $collection->all();

        usort($sortedItems, function ($a, $b) {
            $managerA = $a->getManager();
            $managerB = $b->getManager();

            if (!$managerA || !$managerB) {
                return 0;
            }

            $firstNameComparison = strcasecmp($managerA->getFirstName(), $managerB->getFirstName());
            if (0 !== $firstNameComparison) {
                return $firstNameComparison;
            }

            return strcasecmp($managerA->getLastName(), $managerB->getLastName());
        });

        $collection->replace($sortedItems);
    }
}
