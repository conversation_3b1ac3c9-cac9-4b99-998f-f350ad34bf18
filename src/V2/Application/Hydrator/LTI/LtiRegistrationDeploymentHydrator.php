<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\LTI;

use App\V2\Application\Hydrator\Hydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Domain\LTI\LtiDeploymentCollection;
use App\V2\Domain\LTI\LtiDeploymentCriteria;
use App\V2\Domain\LTI\LtiDeploymentRepository;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationHydrationCriteria;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Uuid\UuidCollection;

class LtiRegistrationDeploymentHydrator implements Hydrator
{
    private const HydratorPriority PRIORITY = HydratorPriority::First;

    public function __construct(
        private readonly LtiDeploymentRepository $ltiDeploymentRepository,
    ) {
    }

    public function getPriority(): HydratorPriority
    {
        return self::PRIORITY;
    }

    public function supports(HydrationCriteria $criteria): bool
    {
        return $criteria instanceof LtiRegistrationHydrationCriteria && $criteria->needsDeployments();
    }

    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof LtiRegistrationCollection) {
            return;
        }

        if ($collection->isEmpty()) {
            return;
        }

        $ids = $collection->reduce(
            callback: function (array $carry, LtiRegistration $registration) {
                $carry[] = $registration->getId();

                return $carry;
            },
            initial: []
        );

        $ids = array_unique($ids);

        $deployments = $this->ltiDeploymentRepository->findBy(
            LtiDeploymentCriteria::createEmpty()
                ->filterByRegistrationIds(new UuidCollection($ids))
        );

        $deploymentsByRegistration = [];
        foreach ($deployments->all() as $deployment) {
            $deploymentsByRegistration[$deployment->getRegistrationId()->value()][] = $deployment;
        }

        foreach ($collection->all() as $registration) {
            $registration->setDeployments(
                new LtiDeploymentCollection(
                    $deploymentsByRegistration[$registration->getId()->value()] ?? []
                )
            );
        }
    }
}
