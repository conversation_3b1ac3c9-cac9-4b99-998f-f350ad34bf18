<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\Announcement\Manager;

use App\Entity\User;
use App\V2\Application\Hydrator\Hydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Domain\Announcement\Manager\AnnouncementManager;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerHydrationCriteria;
use App\V2\Domain\Announcement\Manager\Manager;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Email\Email;
use App\V2\Domain\Shared\Email\InvalidEmailException;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

class AnnouncementManagerHydrator implements Hydrator
{
    private const HydratorPriority PRIORITY = HydratorPriority::First;

    public function __construct(
        private readonly UserRepository $userRepository
    ) {
    }

    public function getPriority(): HydratorPriority
    {
        return self::PRIORITY;
    }

    public function supports(HydrationCriteria $criteria): bool
    {
        return $criteria instanceof AnnouncementManagerHydrationCriteria && $criteria->needsUser();
    }

    /**
     * @throws CriteriaException
     * @throws CollectionException
     * @throws InvalidEmailException
     */
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof AnnouncementManagerCollection) {
            return;
        }

        if ($collection->isEmpty()) {
            return;
        }

        $ids = array_reduce(
            $collection->all(),
            function (array $carry, AnnouncementManager $announcementManager): array {
                $carry[] = $announcementManager->getUserId();

                return $carry;
            },
            []
        );

        $ids = array_unique($ids);

        $users = $this->userRepository->findBy(
            UserCriteria::createByIds(new IdCollection($ids))
        );

        if ($users->isEmpty()) {
            return;
        }

        $announcementManagerByUserId = [];

        foreach ($collection->all() as $announcementManager) {
            $announcementManagerByUserId[$announcementManager->getUserId()->value()][] = $announcementManager;
        }

        /** @var User $user */
        foreach ($users->all() as $user) {
            /** @var AnnouncementManager $announcementManager */
            foreach ($announcementManagerByUserId[$user->getId()] as $announcementManager) {
                $announcementManager->setManager($this->fromUserToManager($user));
            }
        }
    }

    /**
     * @throws InvalidEmailException
     */
    private function fromUserToManager(User $user): Manager
    {
        return new Manager(
            id: new Id($user->getId()),
            email: new Email($user->getEmail()),
            firstName: $user->getFirstName(),
            lastName: $user->getLastName(),
        );
    }
}
