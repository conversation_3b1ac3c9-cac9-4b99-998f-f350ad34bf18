<?php

declare(strict_types=1);

namespace App\V2\Application\DTO\User;

use App\V2\Domain\Shared\Collection\PaginatedCollection;

/**
 * @extends PaginatedCollection<UserListItemDTO>
 */
class PaginatedUserListItemDTOCollection extends PaginatedCollection
{
    public function __construct(
        private readonly UserListItemDTOCollection $collection,
        int $total,
    ) {
        parent::__construct($total);
    }

    public function getCollection(): UserListItemDTOCollection
    {
        return $this->collection;
    }
}
