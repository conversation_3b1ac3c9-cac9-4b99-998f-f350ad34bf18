<?php

declare(strict_types=1);

namespace App\V2\Domain\Course\Creator;

use App\V2\Domain\Shared\Criteria\Criteria;

/**
 * @extends Criteria<CourseCreatorCriteria>
 */
class CourseCreatorCriteria extends Criteria
{
    private ?int $userId = null;
    private ?int $courseId = null;

    public function isEmpty(): bool
    {
        return null === $this->courseId && null === $this->userId;
    }

    public function filterByUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function filterByCourseId(int $courseId): self
    {
        $this->courseId = $courseId;

        return $this;
    }

    public function getCourseId(): ?int
    {
        return $this->courseId;
    }
}
