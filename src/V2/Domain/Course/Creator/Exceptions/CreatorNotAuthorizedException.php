<?php

declare(strict_types=1);

namespace App\V2\Domain\Course\Creator\Exceptions;

use App\Entity\Course;
use App\Entity\User;
use App\V2\Domain\Shared\Exception\NotAuthorizedException;
use Symfony\Component\Security\Core\User\UserInterface;

class CreatorNotAuthorizedException extends NotAuthorizedException
{
    public static function userNotAuthorized(UserInterface|User $user, Course $course): self
    {
        return new self(
            \sprintf(
                'User %s is not authorized to perform this action for course %s',
                $user->__toString(),
                $course->__toString()
            )
        );
    }
}
