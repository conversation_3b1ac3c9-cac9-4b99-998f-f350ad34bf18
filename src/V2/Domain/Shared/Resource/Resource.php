<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Resource;

use App\V2\Domain\Shared\Identifier;

readonly class Resource
{
    public function __construct(
        private ResourceType $type,
        private Identifier $id,
    ) {
    }

    public function getType(): ResourceType
    {
        return $this->type;
    }

    public function getId(): Identifier
    {
        return $this->id;
    }

    public function equals(Resource $resource): bool
    {
        return $this->type === $resource->type
            && $this->id->equals($resource->getId());
    }
}
