<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Uuid;

use App\V2\Domain\Shared\Identifier;

readonly class Uuid implements Identifier
{
    /**
     * @throws InvalidUuidException
     */
    public function __construct(private string $value)
    {
        $this->validate();
    }

    public function value(): string
    {
        return $this->value;
    }

    public function equals(Identifier $uuid): bool
    {
        return $uuid instanceof self && $this->value() === $uuid->value();
    }

    public function __toString(): string
    {
        return $this->value();
    }

    /**
     * @throws InvalidUuidException
     */
    private function validate(): void
    {
        if (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/', $this->value())) {
            throw new InvalidUuidException($this->value());
        }
    }
}
