<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Financial;

use App\V2\Domain\Shared\Financial\Exception\CurrencyException;

final readonly class Money
{
    private function __construct(
        private int $amount,
        private Currency $currency,
    ) {
    }

    public function __toString(): string
    {
        return \sprintf('%.2f %s', $this->formattedValue(), $this->currency->code()->name);
    }

    public static function create(int $amount, ?Currency $currency = null): self
    {
        return new self($amount, $currency ?? Currency::EUR());
    }

    public static function createFromFloat(float|int $amount, ?Currency $currency = null): self
    {
        if (\is_float($amount) && (!is_finite($amount) || is_nan($amount))) {
            throw new \InvalidArgumentException('Amount must be a finite number');
        }

        $cents = \is_int($amount) ? $amount * 100 : (int) round($amount * 100);

        return new self($cents, $currency ?? Currency::EUR());
    }

    public function value(): int
    {
        return $this->amount;
    }

    public function currency(): Currency
    {
        return $this->currency;
    }

    public function formattedValue(): float
    {
        return $this->amount / 100;
    }

    private function assertSameCurrency(Money $other): void
    {
        if (!$this->currency->equals($other->currency)) {
            throw new CurrencyException(
                \sprintf(
                    'Cannot operate on Money objects with different currencies: %s and %s',
                    (string) $this->currency,
                    (string) $other->currency
                )
            );
        }
    }

    public function equals(Money $other): bool
    {
        return $this->amount === $other->amount
            && $this->currency->equals($other->currency);
    }

    public function add(Money $other): self
    {
        $this->assertSameCurrency($other);

        return self::create($this->amount + $other->amount, $this->currency);
    }

    public function subtract(Money $other): self
    {
        $this->assertSameCurrency($other);

        return self::create($this->amount - $other->amount, $this->currency);
    }

    public function isZero(): bool
    {
        return 0 === $this->amount;
    }

    public function lessThan(Money $other): bool
    {
        $this->assertSameCurrency($other);

        return $this->amount < $other->amount;
    }

    public function lessThanOrEqual(Money $other): bool
    {
        $this->assertSameCurrency($other);

        return $this->amount <= $other->amount;
    }

    public function greaterThan(Money $other): bool
    {
        $this->assertSameCurrency($other);

        return $this->amount > $other->amount;
    }

    public function greaterThanOrEqual(Money $other): bool
    {
        $this->assertSameCurrency($other);

        return $this->amount >= $other->amount;
    }
}
