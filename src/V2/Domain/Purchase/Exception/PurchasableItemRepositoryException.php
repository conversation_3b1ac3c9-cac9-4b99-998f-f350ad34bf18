<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase\Exception;

use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Resource\Resource;

class PurchasableItemRepositoryException extends InfrastructureException
{
    public static function duplicateResource(Resource $resource): self
    {
        return new self(\sprintf(
            'Resource with id %s and type %s already exists',
            $resource->getId()->value(),
            $resource->getType()->name
        ));
    }

    public static function fromPrevious(\Throwable $previous): self
    {
        return new self($previous->getMessage(), $previous->getCode(), $previous);
    }
}
