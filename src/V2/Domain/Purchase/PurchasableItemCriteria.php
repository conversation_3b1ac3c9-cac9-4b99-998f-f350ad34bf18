<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Shared\Criteria\CriteriaWithUuid;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Resource\Resource;

class PurchasableItemCriteria extends CriteriaWithUuid
{
    private ?string $search = null;
    private ?Resource $resource = null;
    private ?Money $minPrice = null;
    private ?Money $maxPrice = null;

    public function isEmpty(): bool
    {
        return parent::isEmpty()
            && null === $this->search
            && null === $this->resource
            && null === $this->minPrice
            && null === $this->maxPrice;
    }

    public function filterBySearch(string $search): self
    {
        $this->search = $search;

        return $this;
    }

    public function getSearch(): ?string
    {
        return $this->search;
    }

    public function filterByResource(Resource $resource): self
    {
        $this->resource = $resource;

        return $this;
    }

    public function getResource(): ?Resource
    {
        return $this->resource;
    }

    public function filterByMinPrice(Money $minPrice): self
    {
        $this->minPrice = $minPrice;

        return $this;
    }

    public function getMinPrice(): ?Money
    {
        return $this->minPrice;
    }

    public function filterByMaxPrice(Money $maxPrice): self
    {
        $this->maxPrice = $maxPrice;

        return $this;
    }

    public function getMaxPrice(): ?Money
    {
        return $this->maxPrice;
    }
}
