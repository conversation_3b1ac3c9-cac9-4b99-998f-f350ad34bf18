<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Announcement\Manager;

use App\V2\Domain\Announcement\Manager\AnnouncementManager;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Announcement\Manager\Manager;

class AnnouncementManagerTransformer
{
    public static function fromCollectionToArray(AnnouncementManagerCollection $collection): array
    {
        return array_map(
            fn (AnnouncementManager $announcementManager) => $announcementManager->getManager()
                ? self::fromManagerToArray($announcementManager->getManager())
                : [
                    'id' => $announcementManager->getUserId()->value(),
                ],
            $collection->all()
        );
    }

    public static function fromManagerToArray(Manager $manager): array
    {
        return [
            'id' => $manager->getId()->value(),
            'email' => $manager->getEmail()->value(),
            'name' => $manager->getFirstName(),
            'lastName' => $manager->getLastName(),
        ];
    }
}
