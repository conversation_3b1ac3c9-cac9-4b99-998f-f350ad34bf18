<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\User;

use App\V2\Domain\Shared\Criteria\InvalidSortException;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Infrastructure\Shared\QueryParamTransformer\LifeCycleSortableTransformer;
use App\V2\Infrastructure\Shared\QueryParamTransformer\SortableTransformer;

class UserSortableTransformer extends SortableTransformer
{
    private const array SORTABLE_FIELDS = [
        'id' => 'id',
        'first_name' => 'firstName',
        'last_name' => 'lastName',
        'email' => 'email',
    ];

    public static function getSortableFields(): array
    {
        return self::SORTABLE_FIELDS;
    }

    public static function toSortableField(string $sortBy): SortableField
    {
        if (\array_key_exists($sortBy, LifeCycleSortableTransformer::getSortableFields())) {
            return LifeCycleSortableTransformer::toSortableField($sortBy);
        }

        $sortableFields = self::getSortableFields();

        if (!\array_key_exists($sortBy, $sortableFields)) {
            throw new InvalidSortException($sortBy);
        }

        return new SortableField($sortableFields[$sortBy]);
    }
}
