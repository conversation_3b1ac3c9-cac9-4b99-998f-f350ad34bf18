<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Purchase;

use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Persistence\DBALCommonCriteriaBuilder;
use App\V2\Infrastructure\Persistence\DBALDateTimeFormatter;
use App\V2\Infrastructure\Shared\Financial\CurrencyCodeTransformer;
use App\V2\Infrastructure\Shared\Resource\ResourceTypeTransformer;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALPurchasableItemRepository implements PurchasableItemRepository
{
    public function __construct(
        private Connection $connection,
        private string $purchasableItemTableName,
    ) {
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws InvalidUuidException
     * @throws InvalidCurrencyCodeException
     * @throws CriteriaException
     */
    #[\Override]
    public function put(PurchasableItem $item): void
    {
        try {
            $this->findOneBy(
                PurchasableItemCriteria::createEmpty()
                    ->filterByResource($item->getResource())
            );
            throw PurchasableItemRepositoryException::duplicateResource($item->getResource());
        } catch (PurchasableItemNotFoundException) {
        }

        try {
            $this->findOneBy(PurchasableItemCriteria::createById($item->getId()));

            $this->update($item);
        } catch (PurchasableItemNotFoundException) {
            $this->insert($item);
        }
    }

    private function update(PurchasableItem $item): void
    {
        try {
            $this->connection->update(
                table: $this->purchasableItemTableName,
                data: $this->fromPurchasableItemToArray($item),
                criteria: ['id' => $item->getId()->value()]
            );
        } catch (DBALException $e) {
            throw PurchasableItemRepositoryException::fromPrevious($e);
        }
    }

    private function insert(PurchasableItem $item): void
    {
        try {
            $this->connection->insert(
                table: $this->purchasableItemTableName,
                data: $this->fromPurchasableItemToArray($item),
            );
        } catch (DBALException $e) {
            throw PurchasableItemRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchasableItemNotFoundException
     * @throws PurchasableItemRepositoryException
     * @throws InvalidCurrencyCodeException
     */
    #[\Override]
    public function findOneBy(PurchasableItemCriteria $criteria): PurchasableItem
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new PurchasableItemNotFoundException();
            }

            return $this->fromArrayToPurchasableItem($result);
        } catch (DBALException $e) {
            throw PurchasableItemRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PurchasableItemRepositoryException
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws InvalidCurrencyCodeException
     */
    #[\Override]
    public function findBy(PurchasableItemCriteria $criteria): PurchasableItemCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new PurchasableItemCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToPurchasableItem($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw PurchasableItemRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PurchasableItemRepositoryException
     */
    #[\Override]
    public function delete(PurchasableItem $item): void
    {
        try {
            $item->markAsDeleted();

            $this->update($item);
        } catch (DBALException $e) {
            throw PurchasableItemRepositoryException::fromPrevious($e);
        }
    }

    private function fromPurchasableItemToArray(PurchasableItem $item): array
    {
        return [
            'id' => $item->getId()->value(),
            'name' => $item->getName(),
            'description' => $item->getDescription(),
            'price_amount' => $item->getPrice()->value(),
            'price_currency' => CurrencyCodeTransformer::toString($item->getPrice()->currency()->code()),
            'resource_type' => ResourceTypeTransformer::toString($item->getResource()->getType()),
            'resource_id' => $item->getResource()->getId()->value(),
            'created_at' => DBALDateTimeFormatter::format($item->getCreatedAt()),
            'updated_at' => DBALDateTimeFormatter::format($item->getUpdatedAt()),
            'deleted_at' => DBALDateTimeFormatter::format($item->getDeletedAt()),
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws InvalidCurrencyCodeException
     */
    private function fromArrayToPurchasableItem(array $data): PurchasableItem
    {
        return new PurchasableItem(
            id: new Uuid($data['id']),
            name: $data['name'],
            description: $data['description'],
            price: Money::create(
                amount: (int) $data['price_amount'],
                currency: new Currency(CurrencyCodeTransformer::fromString($data['price_currency']))
            ),
            resource: new Resource(
                type: ResourceTypeTransformer::fromString($data['resource_type']),
                id: new Id((int) $data['resource_id'])
            ),
            createdAt: DBALDateTimeFormatter::parse($data['created_at']),
            updatedAt: DBALDateTimeFormatter::parse($data['updated_at']),
            deletedAt: DBALDateTimeFormatter::parse($data['deleted_at']),
        );
    }

    private function getQueryBuilderByCriteria(PurchasableItemCriteria $criteria): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->purchasableItemTableName, 't')
            ->andWhere('t.deleted_at IS NULL');

        if (null !== $criteria->getSearch()) {
            $queryBuilder->andWhere('t.name LIKE :search')
                ->setParameter('search', '%' . $criteria->getSearch() . '%');
        }

        if (null !== $criteria->getResource()) {
            $queryBuilder->andWhere('t.resource_type = :resourceType')
                ->andWhere('t.resource_id = :resourceId')
                ->setParameter('resourceType', ResourceTypeTransformer::toString($criteria->getResource()->getType()))
                ->setParameter('resourceId', $criteria->getResource()->getId()->value());
        }

        if (null !== $criteria->getMinPrice()) {
            $queryBuilder->andWhere('t.price_amount >= :minPrice')
                ->setParameter('minPrice', $criteria->getMinPrice()->value());
        }

        if (null !== $criteria->getMaxPrice()) {
            $queryBuilder->andWhere('t.price_amount <= :maxPrice')
                ->setParameter('maxPrice', $criteria->getMaxPrice()->value());
        }

        DBALCommonCriteriaBuilder::filterByCommonCriteria(
            criteria: $criteria,
            queryBuilder: $queryBuilder,
        );

        return $queryBuilder;
    }
}
