<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Purchase;

use App\V2\Domain\Purchase\Exception\PurchasableItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchasableItemRepositoryException;
use App\V2\Domain\Purchase\PurchasableItem;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Purchase\PurchasableItemCriteria;
use App\V2\Domain\Purchase\PurchasableItemRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Infrastructure\Persistence\InMemoryCommonCriteriaBuilder;

class InMemoryPurchasableItemRepository implements PurchasableItemRepository
{
    private array $items = [];

    /**
     * @throws CollectionException
     * @throws PurchasableItemRepositoryException
     */
    #[\Override]
    public function put(PurchasableItem $item): void
    {
        try {
            $this->findOneBy(
                PurchasableItemCriteria::createEmpty()
                    ->filterByResource($item->getResource())
            );
            throw PurchasableItemRepositoryException::duplicateResource($item->getResource());
        } catch (PurchasableItemNotFoundException) {
            $this->items[$item->getId()->value()] = $item;
        }
    }

    /**
     * @throws PurchasableItemNotFoundException
     * @throws CollectionException
     */
    #[\Override]
    public function findOneBy(PurchasableItemCriteria $criteria): PurchasableItem
    {
        $purchasableItems = $this->filterByCriteria($criteria);

        if (empty($purchasableItems)) {
            throw new PurchasableItemNotFoundException();
        }

        return reset($purchasableItems);
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function findBy(PurchasableItemCriteria $criteria): PurchasableItemCollection
    {
        return new PurchasableItemCollection(
            array_values($this->filterByCriteria($criteria))
        );
    }

    #[\Override]
    public function delete(PurchasableItem $item): void
    {
        unset($this->items[$item->getId()->value()]);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(PurchasableItemCriteria $criteria): array
    {
        $items = $this->items;

        $items = array_filter(
            $items,
            fn (PurchasableItem $item) => (
                null === $criteria->getSearch()
                    || str_contains($item->getName(), $criteria->getSearch())
            ) && (
                null === $criteria->getResource()
                    || $criteria->getResource()->equals($item->getResource())
            ) && (
                null === $criteria->getMinPrice()
                    || $item->getPrice()->greaterThanOrEqual($criteria->getMinPrice())
            ) && (
                null === $criteria->getMaxPrice()
                    || $item->getPrice()->lessThanOrEqual($criteria->getMaxPrice())
            )
        );

        return InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $items);
    }
}
