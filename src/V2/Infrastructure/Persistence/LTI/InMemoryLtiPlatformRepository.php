<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\LTI;

use App\V2\Domain\LTI\Exceptions\LtiPlatformNotFoundException;
use App\V2\Domain\LTI\LtiPlatform;
use App\V2\Domain\LTI\LtiPlatformCollection;
use App\V2\Domain\LTI\LtiPlatformCriteria;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Domain\Shared\Collection\CollectionException;

class InMemoryLtiPlatformRepository implements LtiPlatformRepository
{
    private LtiPlatformCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new LtiPlatformCollection([]);
    }

    #[\Override]
    public function put(LtiPlatform $platform): void
    {
        $items = $this->collection->allIndexedById();
        $items[$platform->getId()->value()] = clone $platform;
        $this->collection->replace($items);
    }

    #[\Override]
    public function findOneBy(LtiPlatformCriteria $criteria): LtiPlatform
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new LtiPlatformNotFoundException();
        }

        return $result->first();
    }

    #[\Override]
    public function findBy(LtiPlatformCriteria $criteria): LtiPlatformCollection
    {
        return $this->filterByCriteria($criteria);
    }

    #[\Override]
    public function delete(LtiPlatform $platform): void
    {
        $items = $this->collection->allIndexedById();
        unset($items[$platform->getId()->value()]);
        $this->collection->replace($items);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(LtiPlatformCriteria $criteria): LtiPlatformCollection
    {
        $collection = $this->collection->filter(
            fn (LtiPlatform $platform) => (
                null === $criteria->getId()
                    || $criteria->getId()->equals($platform->getId())
            ) && (
                null === $criteria->getRegistrationId()
                    || $criteria->getRegistrationId()->equals($platform->getRegistrationId())
            ) && (
                null === $criteria->getIds()
                    || $criteria->getIds()->contains($platform->getId())
            ) && (
                null === $criteria->getRegistrationIds()
                || $criteria->getRegistrationIds()->contains($platform->getRegistrationId())
            ) && (
                null === $criteria->getAudience()
                    || $criteria->getAudience() === $platform->getAudience()
            ),
        );

        return $collection->map(
            fn (LtiPlatform $platform) => new LtiPlatform(
                id: $platform->getId(),
                registrationId: $platform->getRegistrationId(),
                name: $platform->getName(),
                audience: $platform->getAudience(),
                oidcAuthenticationUrl: $platform->getOidcAuthenticationUrl(),
                oauth2AccessTokenUrl: $platform->getOauth2AccessTokenUrl(),
                jwksUrl: $platform->getJwksUrl(),
            )
        );
    }
}
