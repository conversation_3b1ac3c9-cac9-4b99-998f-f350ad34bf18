<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\LTI;

use App\V2\Domain\LTI\Exceptions\LtiToolNotFoundException;
use App\V2\Domain\LTI\LtiTool;
use App\V2\Domain\LTI\LtiToolCollection;
use App\V2\Domain\LTI\LtiToolCriteria;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Domain\Shared\Collection\CollectionException;

class InMemoryLtiToolRepository implements LtiToolRepository
{
    private LtiToolCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new LtiToolCollection([]);
    }

    #[\Override]
    public function put(LtiTool $ltiTool): void
    {
        $tools = $this->collection->allIndexedById();
        $tools[$ltiTool->getId()->value()] = $ltiTool;
        $this->collection->replace($tools);
    }

    #[\Override]
    public function findOneBy(LtiToolCriteria $criteria): LtiTool
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new LtiToolNotFoundException();
        }

        return $result->first();
    }

    public function findBy(LtiToolCriteria $criteria): LtiToolCollection
    {
        return $this->filterByCriteria($criteria);
    }

    #[\Override]
    public function delete(LtiTool $ltiTool): void
    {
        $tools = $this->collection->allIndexedById();
        unset($tools[$ltiTool->getId()->value()]);
        $this->collection->replace($tools);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(LtiToolCriteria $criteria): LtiToolCollection
    {
        $collection = $this->collection->filter(
            fn (LtiTool $ltiTool) => (
                null === $criteria->getId()
                    || $criteria->getId()->equals($ltiTool->getId())
            ) && (
                null === $criteria->getRegistrationId()
                    || $criteria->getRegistrationId()->equals($ltiTool->getRegistrationId())
            ) && (
                null === $criteria->getRegistrationIds()
                    || $criteria->getRegistrationIds()->contains($ltiTool->getRegistrationId())
            ) && (
                null === $criteria->getAudience()
                    || $criteria->getAudience() === $ltiTool->getAudience()
            ),
        );

        return $collection->map(
            fn (LtiTool $ltiTool) => new LtiTool(
                id: $ltiTool->getId(),
                registrationId: $ltiTool->getRegistrationId(),
                name: $ltiTool->getName(),
                audience: $ltiTool->getAudience(),
                oidcInitiationUrl: $ltiTool->getOidcInitiationUrl(),
                launchUrl: $ltiTool->getLaunchUrl(),
                deepLinkingUrl: $ltiTool->getDeepLinkingUrl(),
                jwksUrl: $ltiTool->getJwksUrl(),
            )
        );
    }
}
