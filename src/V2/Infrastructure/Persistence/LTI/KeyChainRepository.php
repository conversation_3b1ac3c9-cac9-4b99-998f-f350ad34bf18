<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\LTI;

use App\V2\Infrastructure\LTI\CoreKeyChainGenerator;
use OAT\Library\Lti1p3Core\Security\Key\KeyChainInterface;
use OAT\Library\Lti1p3Core\Security\Key\KeyChainRepositoryInterface;

/**
 * Currently, is working on a single key pair. Based on requirements (a need to have multiple keys),
 * it can be modified to work with collections.
 */
readonly class KeyChainRepository implements KeyChainRepositoryInterface
{
    public function __construct(
        private CoreKeyChainGenerator $coreKeyChainGenerator
    ) {
    }

    public function find(string $identifier): ?KeyChainInterface
    {
        if (CoreKeyChainGenerator::KEY_IDENTIFIER === $identifier) {
            return $this->coreKeyChainGenerator->generateCoreKeyChain();
        }

        return null;
    }

    public function findByKeySetName(string $keySetName): array
    {
        if (CoreKeyChainGenerator::KEY_SET_NAME === $keySetName) {
            return [$this->coreKeyChainGenerator->generateCoreKeyChain()];
        }

        return [];
    }
}
