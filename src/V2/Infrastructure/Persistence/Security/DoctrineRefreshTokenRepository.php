<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Security;

use App\Entity\RefreshToken;
use App\V2\Domain\Security\Exceptions\RefreshTokenNotFoundException;
use App\V2\Domain\Security\RefreshTokenCriteria;
use App\V2\Domain\Security\RefreshTokenRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Gesdinet\JWTRefreshTokenBundle\Entity\RefreshTokenRepository as LegacyRefreshTokenRepository;

readonly class DoctrineRefreshTokenRepository implements RefreshTokenRepository
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    private function getRepository(): EntityRepository|LegacyRefreshTokenRepository
    {
        return $this->em->getRepository(RefreshToken::class);
    }

    #[\Override]
    public function put(RefreshToken $token): void
    {
        try {
            $this->em->persist($token);
            $this->em->flush();
        } catch (\Exception $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findOneBy(RefreshTokenCriteria $criteria): RefreshToken
    {
        try {
            return $this->createQueryBuilder($criteria)
                ->setMaxResults(1)
                ->getQuery()
                ->getSingleResult();
        } catch (NoResultException) {
            throw new RefreshTokenNotFoundException();
        } catch (NonUniqueResultException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InfrastructureException
     */
    public function delete(RefreshToken $refreshToken): void
    {
        try {
            $this->em->remove($refreshToken);
            $this->em->flush();
        } catch (\Exception $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    private function createQueryBuilder(RefreshTokenCriteria $criteria): QueryBuilder
    {
        $queryBuilder = $this->getRepository()
            ->createQueryBuilder('t');

        if (null !== $criteria->getToken()) {
            $queryBuilder->andWhere('t.refreshToken = :token')
                ->setParameter('token', $criteria->getToken());
        }

        return $queryBuilder;
    }
}
