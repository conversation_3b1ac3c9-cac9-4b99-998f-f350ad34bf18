<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Security;

use App\Entity\RefreshToken;
use App\V2\Domain\Security\Exceptions\RefreshTokenNotFoundException;
use App\V2\Domain\Security\RefreshTokenCriteria;
use App\V2\Domain\Security\RefreshTokenRepository;

class InMemoryRefreshTokenRepository implements RefreshTokenRepository
{
    private array $tokens = [];
    private int $nextId = 1;

    public function put(RefreshToken $token): void
    {
        if (null === $token->getId()) {
            $token->setId($this->nextId++);
        }

        $this->tokens[$token->getRefreshToken()] = clone $token;
    }

    public function findOneBy(RefreshTokenCriteria $criteria): RefreshToken
    {
        $result = $this->filterByCriteria($criteria);

        if (empty($result)) {
            throw new RefreshTokenNotFoundException();
        }

        return reset($result);
    }

    public function delete(RefreshToken $refreshToken): void
    {
        unset($this->tokens[$refreshToken->getRefreshToken()]);
    }

    private function filterByCriteria(RefreshTokenCriteria $criteria): array
    {
        return array_filter(
            $this->tokens,
            fn (RefreshToken $token) => (null === $criteria->getToken()
                || $token->getRefreshToken() === $criteria->getToken())
        );
    }
}
