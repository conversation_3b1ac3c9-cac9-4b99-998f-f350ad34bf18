<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Shared\QueryParamTransformer;

use App\V2\Domain\Shared\Criteria\InvalidSortException;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortDirection;

abstract class SortableTransformer
{
    abstract public static function getSortableFields(): array;

    /**
     * @throws InvalidSortException
     */
    abstract public static function toSortableField(string $sortBy): SortableField;

    public static function toSortDirection(string $sortDirection): SortDirection
    {
        return match ($sortDirection) {
            'desc' => SortDirection::DESC,
            default => SortDirection::ASC,
        };
    }
}
