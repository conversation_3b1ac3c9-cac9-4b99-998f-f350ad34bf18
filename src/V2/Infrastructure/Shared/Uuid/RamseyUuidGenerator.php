<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Shared\Uuid;

use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\Shared\Uuid\UuidGenerator;

class RamseyUuidGenerator implements UuidGenerator
{
    /**
     * @throws InvalidUuidException
     */
    public function generate(): Uuid
    {
        return new Uuid(\Ramsey\Uuid\Uuid::uuid4()->toString());
    }
}
