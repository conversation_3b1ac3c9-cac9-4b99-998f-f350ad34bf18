<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Admin;

use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Constraints\Collection;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Optional;
use Symfony\Component\Validator\Constraints\Type;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class GetCreatorsValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validateGetCreatorsRequest(array $data): void
    {
        $constraints = new Collection([
            'search' => [
                new Optional([
                    new NotBlank(),
                    new Type('string'),
                ]),
            ],
            'page' => [
                new Optional([
                    new NotBlank(),
                    new Type('digit'),
                    new Callback(callback: function ($page, ExecutionContextInterface $context) {
                        if ($page < 1) {
                            $context->buildViolation('Page must be greater than 0.')->addViolation();
                        }

                        if (!isset($context->getRoot()['page_size'])) {
                            $context->buildViolation('Page size is required when page is provided.')->addViolation();
                        }
                    }),
                ]),
            ],
            'page_size' => [
                new Optional([
                    new NotBlank(),
                    new Type('digit'),
                    new Callback(callback: function ($pageSize, ExecutionContextInterface $context) {
                        if ($pageSize < 1) {
                            $context->buildViolation('Page size must be greater than 0.')->addViolation();
                        }

                        if (!isset($context->getRoot()['page'])) {
                            $context->buildViolation('Page is required when page size is provided.')->addViolation();
                        }
                    }),
                ]),
            ],
        ]);

        self::validate($data, $constraints);
    }
}
