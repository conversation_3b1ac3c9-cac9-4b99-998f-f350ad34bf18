<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI\Transformer;

use App\V2\Domain\LTI\LtiTool;
use OAT\Library\Lti1p3Core\Tool\Tool as CoreTool;
use OAT\Library\Lti1p3Core\Tool\ToolInterface;

class LtiToolTransformer
{
    public static function fromLtiToolToCoreTool(LtiTool $ltiTool): ToolInterface
    {
        return new CoreTool(
            identifier: $ltiTool->getId()->value(),
            name: $ltiTool->getName(),
            audience: $ltiTool->getAudience(),
            oidcInitiationUrl: $ltiTool->getOidcInitiationUrl()->value(),
            launchUrl: $ltiTool->getLaunchUrl()?->value(),
            deepLinkingUrl: $ltiTool->getDeepLinkingUrl()?->value(),
        );
    }
}
