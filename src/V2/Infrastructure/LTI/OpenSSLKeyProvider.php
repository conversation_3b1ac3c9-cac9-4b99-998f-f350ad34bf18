<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI;

use App\V2\Domain\LTI\Exceptions\LtiException;
use App\V2\Domain\LTI\LtiKeyProvider;
use App\V2\Domain\Shared\Exception\InfrastructureException;

readonly class OpenSSLKeyProvider implements LtiKeyProvider
{
    public const string ALGORITHM = 'RS256';
    private string $privateKeyFileName;
    private string $publicKeyFileName;

    public function __construct(
        private string $ltiKeysDir,
    ) {
        $this->privateKeyFileName = $this->ltiKeysDir . 'private.key';
        $this->publicKeyFileName = $this->ltiKeysDir . 'public.key';
    }

    public function getAlgorithm(): string
    {
        return self::ALGORITHM;
    }

    #[\Override]
    public function generateKeys(): void
    {
        if (!\extension_loaded('openssl')) {
            throw new InfrastructureException('openssl extension is not loaded.');
        }

        if (!file_exists($this->ltiKeysDir)) {
            if (!mkdir($this->ltiKeysDir, 0755, true)) {
                throw new InfrastructureException("Failed to create directory: {$this->ltiKeysDir}");
            }
        }

        $config = [
            'digest_alg' => 'sha256',
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
            'private_key_bits' => 2048,
        ];

        $privateKey = openssl_pkey_new($config);
        if (false === $privateKey) {
            throw new InfrastructureException('Failed to generate key.');
        }

        $keyDetails = openssl_pkey_get_details($privateKey);
        if (false === $keyDetails) {
            throw new InfrastructureException('Failed to get private key details.');
        }

        $publicKey = $keyDetails['key'];

        if (!openssl_pkey_export($privateKey, $privateKeyPem)) {
            throw new InfrastructureException('Failed to export private key.');
        }

        if (false === file_put_contents($this->publicKeyFileName, $publicKey)) {
            throw new InfrastructureException('Failed to write public key.');
        }

        if (false === file_put_contents($this->privateKeyFileName, $privateKeyPem)) {
            throw new InfrastructureException('Failed to write private key.');
        }

        if (!chmod($this->publicKeyFileName, 0600)) {
            throw new InfrastructureException('Failed to chmod public key file.');
        }
        if (!chmod($this->privateKeyFileName, 0600)) {
            throw new InfrastructureException('Failed to chmod private key file.');
        }
    }

    /**
     * @throws LtiException
     */
    public function getPublicKeyFile(): string
    {
        if (!file_exists($this->publicKeyFileName)) {
            // Avoid key generation on the fly
            throw LtiException::noPublicKeyFound();
        }

        return 'file://' . $this->publicKeyFileName;
    }

    /**
     * @throws LtiException
     */
    public function getPrivateKeyFile(): string
    {
        if (!file_exists($this->privateKeyFileName)) {
            // Avoid key generation on the fly
            throw LtiException::noPrivateKeyFound();
        }

        return 'file://' . $this->privateKeyFileName;
    }
}
