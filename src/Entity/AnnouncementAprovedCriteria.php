<?php

namespace App\Entity;

use App\Repository\AnnouncementAprovedCriteriaRepository;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=AnnouncementAprovedCriteriaRepository::class)
 */
class AnnouncementAprovedCriteria
{
    use Blamable, Timestampable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=0)
     */
    private $value;

    /**
     * @ORM\ManyToOne(targetEntity=Announcement::class, inversedBy="announcementAprovedCriterias")
     */
    private $announcement;

    /**
     * @ORM\ManyToOne(targetEntity=AnnouncementCriteria::class, inversedBy="announcementAprovedCriterias")
     */
    private $announcementCriteria;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $extra;

    public function __construct()
    {
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(string $value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getAnnouncement(): ?Announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement(?Announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }

    public function getAnnouncementCriteria(): ?AnnouncementCriteria
    {
        return $this->announcementCriteria;
    }

    public function setAnnouncementCriteria(?AnnouncementCriteria $announcementCriteria): self
    {
        $this->announcementCriteria = $announcementCriteria;

        return $this;
    }

    public function getExtra(): ?string
    {
        return $this->extra;
    }

    public function setExtra(?string $extra): self
    {
        $this->extra = $extra;

        return $this;
    }

    public function __toString()
    {
        return $this->getId() . '';
    }

    public function __clone()
    {
        $this->id = null;
       
    }
}
