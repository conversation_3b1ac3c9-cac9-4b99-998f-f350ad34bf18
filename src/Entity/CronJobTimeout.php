<?php

declare(strict_types=1);

namespace App\Entity;

use <PERSON>trine\ORM\Mapping as ORM;

/**
 * Entity to store timeouts for cron commands.
 *
 * @ORM\Entity(repositoryClass="App\Repository\CronJobTimeoutRepository")
 *
 * @ORM\Table(name="cron_job_timeout")
 */
class CronJobTimeout
{
    use AtAndBy;
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * Relationship with the CronJob entity.
     *
     * @ORM\OneToOne(targetEntity="Cron\CronBundle\Entity\CronJob")
     *
     * @ORM\JoinColumn(name="cron_job_id", referencedColumnName="id", nullable=true)
     */
    private $cronJob;

    /**
     * Command name (for example, 'task:execute').
     *
     * @ORM\Column(type="string", length=255, unique=true)
     */
    private $command;

    /**
     * Maximum execution time in seconds.
     *
     * @ORM\Column(type="integer")
     */
    private $timeout;

    /**
     * Get id.
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * Get command.
     */
    public function getCommand(): ?string
    {
        return $this->command;
    }

    /**
     * Set command.
     */
    public function setCommand(string $command): self
    {
        $this->command = $command;

        return $this;
    }

    /**
     * Get timeout.
     */
    public function getTimeout(): ?int
    {
        return $this->timeout;
    }

    /**
     * Set timeout.
     */
    public function setTimeout(int $timeout): self
    {
        $this->timeout = $timeout;

        return $this;
    }

    /**
     * Get cronJob.
     */
    public function getCronJob(): ?\Cron\CronBundle\Entity\CronJob
    {
        return $this->cronJob;
    }

    /**
     * Set cronJob.
     */
    public function setCronJob(?\Cron\CronBundle\Entity\CronJob $cronJob): self
    {
        $this->cronJob = $cronJob;

        return $this;
    }
}
