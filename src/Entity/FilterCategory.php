<?php

namespace App\Entity;

use App\Repository\FilterCategoryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;



/**
 * @ORM\Entity(repositoryClass=FilterCategoryRepository::class)
 */
class FilterCategory implements TranslatableInterface
{
    use TranslatableTrait;
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * * @Groups({"detail", "user_area", "admin_area","ranking", "library"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"details", "user_area","filter_excel","ranking", "library"})
     */
    private $name;

    /**
     * @ORM\ManyToOne(targetEntity=FilterCategory::class, inversedBy="children")
     */
    private $parent;

    /**
     * @ORM\OneToMany(targetEntity=FilterCategory::class, mappedBy="parent")
     */
    private $children;

    /**
     * @ORM\OneToMany(targetEntity=Filter::class, mappedBy="filterCategory", cascade={"persist", "remove"})
     * @ORM\OrderBy({"name" = "ASC"})
     * @Groups({"library"})
     */
    private $filters;

    /**
     * @ORM\Column(type="integer")
     */
    private $sort;

    /**
     * @ORM\Column(type="boolean")
     */
    private $is_ranking;
    

    public function __construct()
    {
        $this->children = new ArrayCollection();
        $this->filters = new ArrayCollection();
        $this->is_ranking = false;
        $this->sort = 0;

    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function __toString()
    {
        return $this->name;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function setParent(?self $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    /**
     * @return Collection|self[]
     */
    public function getChildren(): Collection
    {
        return $this->children;
    }

    public function addChild(self $child): self
    {
        if (!$this->children->contains($child)) {
            $this->children[] = $child;
            $child->setParent($this);
        }

        return $this;
    }

    public function removeChild(self $child): self
    {
        if ($this->children->removeElement($child)) {
            // set the owning side to null (unless already changed)
            if ($child->getParent() === $this) {
                $child->setParent(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Filter[]
     */
    public function getFilters(): Collection
    {
        return $this->filters;
    }

    public function addFilter(Filter $filter): self
    {
        if (!$this->filters->contains($filter)) {
            $this->filters[] = $filter;
            $filter->setFilterCategory($this);
        }

        return $this;
    }

    public function removeFilter(Filter $filter): self
    {
        if ($this->filters->removeElement($filter)) {
            // set the owning side to null (unless already changed)
            if ($filter->getFilterCategory() === $this) {
                $filter->setFilterCategory(null);
            }
        }

        return $this;
    }

    // /**
    //  * @return Collection|FilterCategory[]
    //  * @Groups({"library"})
    //  */
    // public function getTranslations(): Collection
    // {
    //     return $this->translations;
    // }

    public function getSort(): ?int
    {
        return $this->sort;
    }

    public function setSort(int $sort): self
    {
        $this->sort = $sort;

        return $this;
    }

    public function isIsRanking(): ?bool
    {
        return $this->is_ranking;
    }

    public function setIsRanking(bool $is_ranking): self
    {
        $this->is_ranking = $is_ranking;

        return $this;
    }
}
