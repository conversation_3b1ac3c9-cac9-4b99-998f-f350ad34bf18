<?php

namespace App\Entity;

use App\Repository\UserFieldsFundaeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=UserFieldsFundaeRepository::class)
 */
class UserFieldsFundae
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\OneToOne(targetEntity=User::class, inversedBy="userFieldsFundae", cascade={"persist", "remove"})
     * @ORM\JoinColumn(nullable=false)
     */
    private ?User $user = null;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $socialSecurityNumber;

    /**
     * @ORM\Column(type="string", length=50, nullable=true)
     */
    private ?string $gender;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $emailWork;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private ?\DateTimeInterface $birthdate;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private ?bool $incapacity = false;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private ?bool $victimOfTerrorism = false;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private ?bool $genderViolence = false;

    /**
     * @ORM\Column(type="string", length=50, nullable=true)
     */
    private ?string $dni;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $contributionAccount;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $updatedAt;

    /**
     * @ORM\ManyToOne(targetEntity=UserCompany::class, inversedBy="userFieldsFundaes")
     */
    private $userCompany;

    /**
     * @ORM\ManyToOne(targetEntity=UserProfessionalCategory::class, inversedBy="userFieldsFundaes")
     */
    private $userProfessionalCategory;

    /**
     * @ORM\ManyToOne(targetEntity=UserWorkCenter::class, inversedBy="userFieldsFundaes")
     */
    private $userWorkCenter;

    /**
     * @ORM\ManyToOne(targetEntity=UserWorkDepartment::class, inversedBy="userFieldsFundaes")
     */
    private $userWorkDepartment;

    /**
     * @ORM\ManyToOne(targetEntity=UserStudyLevel::class, inversedBy="userFieldsFundaes")
     */
    private $userStudyLevel;

    /**
     * @ORM\Column(type="string", length=30, nullable=true)
     */
    private ?string $telephone = null;

    /**
     * @ORM\ManyToOne(targetEntity=FilesManager::class, inversedBy="userFieldsFundaes")
     */
    private ?FilesManager $cv_filesManager = null;

    public function __construct()
    {
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getSocialSecurityNumber(): ?string
    {
        return $this->socialSecurityNumber;
    }

    public function setSocialSecurityNumber(?string $socialSecurityNumber): self
    {
        $this->socialSecurityNumber = $socialSecurityNumber;

        return $this;
    }

    public function getGender(): ?string
    {
        return $this->gender;
    }

    public function setGender(?string $gender): self
    {
        $this->gender = $gender;

        return $this;
    }

    public function getEmailWork(): ?string
    {
        return $this->emailWork;
    }

    public function setEmailWork(?string $emailWork): self
    {
        $this->emailWork = $emailWork;

        return $this;
    }

    public function getBirthdate(): ?\DateTimeInterface
    {
        return $this->birthdate;
    }

    public function setBirthdate(?\DateTimeInterface $birthdate): self
    {
        $this->birthdate = $birthdate;

        return $this;
    }

    public function isIncapacity(): ?bool
    {
        return $this->incapacity;
    }

    public function setIncapacity(?bool $incapacity): self
    {
        $this->incapacity = $incapacity;

        return $this;
    }

    public function isVictimOfTerrorism(): ?bool
    {
        return $this->victimOfTerrorism;
    }

    public function setVictimOfTerrorism(?bool $victimOfTerrorism): self
    {
        $this->victimOfTerrorism = $victimOfTerrorism;

        return $this;
    }

    public function isGenderViolence(): ?bool
    {
        return $this->genderViolence;
    }

    public function setGenderViolence(?bool $genderViolence): self
    {
        $this->genderViolence = $genderViolence;

        return $this;
    }

    public function getDni(): ?string
    {
        return $this->dni;
    }

    public function setDni(?string $dni): self
    {
        $this->dni = $dni;

        return $this;
    }

    public function getContributionAccount(): ?string
    {
        return $this->contributionAccount;
    }

    public function setContributionAccount(?string $contributionAccount): self
    {
        $this->contributionAccount = $contributionAccount;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * @param mixed $createdAt
     */
    public function setCreatedAt($createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * @param mixed $updatedAt
     */
    public function setUpdatedAt($updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    public function getUserCompany(): ?UserCompany
    {
        return $this->userCompany;
    }

    public function setUserCompany(?UserCompany $userCompany): self
    {
        $this->userCompany = $userCompany;

        return $this;
    }

    public function getUserProfessionalCategory(): ?UserProfessionalCategory
    {
        return $this->userProfessionalCategory;
    }

    public function setUserProfessionalCategory(?UserProfessionalCategory $userProfessionalCategory): self
    {
        $this->userProfessionalCategory = $userProfessionalCategory;

        return $this;
    }

    public function getUserWorkCenter(): ?UserWorkCenter
    {
        return $this->userWorkCenter;
    }

    public function setUserWorkCenter(?UserWorkCenter $userWorkCenter): self
    {
        $this->userWorkCenter = $userWorkCenter;

        return $this;
    }

    public function getUserWorkDepartment(): ?UserWorkDepartment
    {
        return $this->userWorkDepartment;
    }

    public function setUserWorkDepartment(?UserWorkDepartment $userWorkDepartment): self
    {
        $this->userWorkDepartment = $userWorkDepartment;

        return $this;
    }

    public function getUserStudyLevel(): ?UserStudyLevel
    {
        return $this->userStudyLevel;
    }

    public function setUserStudyLevel(?UserStudyLevel $userStudyLevel): self
    {
        $this->userStudyLevel = $userStudyLevel;

        return $this;
    }

    public function getAllFieldCompleted(): bool {
        if (empty($this->socialSecurityNumber)) return false;
        if (empty($this->gender)) return false;
        if (empty($this->emailWork)) return false;
        if (empty($this->birthdate)) return false;
        if (empty($this->dni)) return false;
        if (empty($this->contributionAccount)) return false;
        if (!$this->userCompany) return false;
        if (!$this->userProfessionalCategory) return false;
        if (!$this->userWorkCenter) return false;
        if (!$this->userWorkDepartment) return false;
        if (!$this->userStudyLevel) return false;

        return true;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(?string $telephone): self
    {
        $this->telephone = $telephone;

        return $this;
    }

    public function getCvFilesManager(): ?FilesManager
    {
        return $this->cv_filesManager;
    }

    public function setCvFilesManager(?FilesManager $cv_filesManager): self
    {
        $this->cv_filesManager = $cv_filesManager;

        return $this;
    }
}
