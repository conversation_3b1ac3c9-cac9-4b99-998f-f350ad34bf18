<?php

namespace App\Entity;

use App\Repository\MainCourseEvaluationRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=MainCourseEvaluationRepository::class)
 */
class MainCourseEvaluation
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\OneToOne(targetEntity=Course::class, inversedBy="mainCourseEvaluation", cascade={"persist", "remove"})
     */
    private $course;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isMainNps;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCourse(): ?Course
    {
        return $this->course;
    }

    public function setCourse(?Course $course): self
    {
        $this->course = $course;

        return $this;
    }

    public function isIsMainNps(): ?bool
    {
        return $this->isMainNps;
    }

    public function setIsMainNps(?bool $isMainNps): self
    {
        $this->isMainNps = $isMainNps;

        return $this;
    }
}
