<?php

declare(strict_types=1);

namespace App\Entity;

use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use App\Repository\TypeDiplomaRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 *
 * @ORM\Entity(repositoryClass=TypeDiplomaRepository::class)S
 */
class TypeDiploma
{
    use Blamable;
    use Timestampable;

    public const APPLY_ALL = 1;
    public const APPLY_COURSES = 2;
    public const APPLY_ANNOUNCEMENTS = 3;

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"list"})
     * @Groups({"update-course"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"list"})
     * @Groups({"update-course"})
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"list"})
     * @Groups({"update-course"})
     */
    private $description;

    /**
     * @ORM\Column(type="boolean")
     *
     * @Groups({"update-course"})
     */
    private $active;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"list"})
     * @Groups({"update-course"})
     */
    private $extra;

    /**
     * @ORM\OneToMany(targetEntity=Announcement::class, mappedBy="typeDiploma", cascade={"persist", "remove"})
     */
    private $announcements;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     *
     * @Groups({"update-course"})
     */
    private $isMain;

    /**
     * @ORM\OneToMany(targetEntity=Course::class, mappedBy="typeDiploma")
     */
    private $courses;

    /**
     * @ORM\Column(type="smallint", nullable=true)
     */
    private $applyTo = self::APPLY_ALL;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
        $this->active = 1;
        $this->announcements = new ArrayCollection();
        $this->courses = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getExtra(): ?string
    {
        return $this->extra;
    }

    public function setExtra(?string $extra): self
    {
        $this->extra = $extra;

        return $this;
    }

    /**
     * @return Collection|Announcement[]
     */
    public function getAnnouncements(): Collection
    {
        return $this->announcements;
    }

    public function addAnnouncement(Announcement $announcement): self
    {
        if (!$this->announcements->contains($announcement)) {
            $this->announcements[] = $announcement;
            $announcement->setTypeDiploma($this);
        }

        return $this;
    }

    public function removeAnnouncement(Announcement $announcement): self
    {
        if ($this->announcements->removeElement($announcement)) {
            // set the owning side to null (unless already changed)
            if ($announcement->getTypeDiploma() === $this) {
                $announcement->setTypeDiploma(null);
            }
        }

        return $this;
    }

    public function __toString()
    {
        return $this->getId() . '-diploma-type';
    }

    public function isIsMain(): ?bool
    {
        return $this->isMain;
    }

    public function setIsMain(?bool $isMain): self
    {
        $this->isMain = $isMain;

        return $this;
    }

    /**
     * @return Collection<int, Course>
     */
    public function getCourses(): Collection
    {
        return $this->courses;
    }

    public function addCourse(Course $course): self
    {
        if (!$this->courses->contains($course)) {
            $this->courses[] = $course;
            $course->setTypeDiploma($this);
        }

        return $this;
    }

    public function removeCourse(Course $course): self
    {
        if ($this->courses->removeElement($course)) {
            // set the owning side to null (unless already changed)
            if ($course->getTypeDiploma() === $this) {
                $course->setTypeDiploma(null);
            }
        }

        return $this;
    }

    public function getApplyTo(): ?int
    {
        return $this->applyTo;
    }

    public function setApplyTo(?int $applyTo): self
    {
        $this->applyTo = $applyTo;

        return $this;
    }
}
