<?php

namespace App\Entity;

use App\Repository\TypeMoneyRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;

/**
 * @ORM\Entity(repositoryClass=TypeMoneyRepository::class)
 */
class TypeMoney implements TranslatableInterface
{
    use TranslatableTrait; 

    /**
     * @ORM\Id
     * @ORM\GeneratedValue 
     * @ORM\Column(type="integer")
     * @Groups({"list"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"list"})
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=20)
     * @Groups({"list"})
     */
    private $symbol;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"list"})
     */
    private $country;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"list"})
     */
    private $fractionalUnit;

    /**
     * @ORM\Column(type="string", length=50)
    * @Groups({"list"})
     */
    private $codeIso;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementGroup::class, mappedBy="typeMoney")
     */
    private $announcementGroups;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementGroupSession::class, mappedBy="typeMoney")
     */
    private $announcementGroupSessions;

    /**
     * @ORM\Column(type="boolean")
     */
    private $state;

    public function __construct()
    {
        $this->announcementGroups = new ArrayCollection();
        $this->announcementGroupSessions = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }
    
    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getSymbol(): ?string
    {
        return $this->symbol;
    }

    public function setSymbol(string $symbol): self
    {
        $this->symbol = $symbol;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getFractionalUnit(): ?string
    {
        return $this->fractionalUnit;
    }

    public function setFractionalUnit(string $fractionalUnit): self
    {
        $this->fractionalUnit = $fractionalUnit;

        return $this;
    }

    public function getCodeIso(): ?string
    {
        return $this->codeIso;
    }

    public function setCodeIso(string $codeIso): self
    {
        $this->codeIso = $codeIso;

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementGroup>
     */
    public function getAnnouncementGroups(): Collection
    {
        return $this->announcementGroups;
    }

    public function addAnnouncementGroup(AnnouncementGroup $announcementGroup): self
    {
        if (!$this->announcementGroups->contains($announcementGroup)) {
            $this->announcementGroups[] = $announcementGroup;
            $announcementGroup->setTypeMoney($this);
        }

        return $this;
    }

    public function removeAnnouncementGroup(AnnouncementGroup $announcementGroup): self
    {
        if ($this->announcementGroups->removeElement($announcementGroup)) {
            // set the owning side to null (unless already changed)
            if ($announcementGroup->getTypeMoney() === $this) {
                $announcementGroup->setTypeMoney(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementGroupSession>
     */
    public function getAnnouncementGroupSessions(): Collection
    {
        return $this->announcementGroupSessions;
    }

    public function addAnnouncementGroupSession(AnnouncementGroupSession $announcementGroupSession): self
    {
        if (!$this->announcementGroupSessions->contains($announcementGroupSession)) {
            $this->announcementGroupSessions[] = $announcementGroupSession;
            $announcementGroupSession->setTypeMoney($this);
        }

        return $this;
    }

    public function removeAnnouncementGroupSession(AnnouncementGroupSession $announcementGroupSession): self
    {
        if ($this->announcementGroupSessions->removeElement($announcementGroupSession)) {
            // set the owning side to null (unless already changed)
            if ($announcementGroupSession->getTypeMoney() === $this) {
                $announcementGroupSession->setTypeMoney(null);
            }
        }

        return $this;
    }

    public function isState(): ?bool
    {
        return $this->state;
    }

    public function setState(bool $state): self
    {
        $this->state = $state;

        return $this;
    }

     /**
     * @Groups({"list"})
     * @return String
     */
    public function getFullName()
    {
        return  ' (' . $this->symbol . ') ' . $this->name ;
    }
}
