<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\UserRoleplayProjectRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=UserRoleplayProjectRepository::class)
 *
 * @ORM\HasLifecycleCallbacks()
 */
class UserRoleplayProject
{
    use AtAndBy;

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private ?int $id;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="roleplayProjects")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private ?User $user;

    /**
     * @ORM\OneToOne(targetEntity=UserCourseChapter::class, cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private ?UserCourseChapter $userCourseChapter;

    /**
     * @ORM\ManyToOne(targetEntity=RoleplayProject::class)
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private ?RoleplayProject $project;

    /**
     * @ORM\Column(type="json")
     */
    private array $answers = [];

    public function __toString()
    {
        return $this->getProject()->getTitle();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getUserCourseChapter(): ?UserCourseChapter
    {
        return $this->userCourseChapter;
    }

    public function setUserCourseChapter(UserCourseChapter $userCourseChapter): self
    {
        $this->userCourseChapter = $userCourseChapter;

        return $this;
    }

    public function getProject(): ?RoleplayProject
    {
        return $this->project;
    }

    public function setProject(?RoleplayProject $project): self
    {
        $this->project = $project;

        return $this;
    }

    public function getAnswers(): ?array
    {
        return $this->answers;
    }

    public function setAnswers(array $answers): self
    {
        $this->answers = $answers;

        return $this;
    }
}
