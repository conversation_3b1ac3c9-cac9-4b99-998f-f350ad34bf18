<?php

namespace App\Entity;

use App\Repository\TrueOrFalseRepository;
use Doctrine\ORM\Mapping as ORM;
use Serializable;
use Symfony\Component\Serializer\Annotation\Groups;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

//nota: se utiliza @Groups({"roulette"... porque con él se llama la información de todos los juegos en general

/**
 * @ORM\Entity(repositoryClass=TrueOrFalseRepository::class)
 * @Vich\Uploadable()
 */
class TrueOrFalse  implements Serializable
{
    use \App\Behavior\Imageable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"roulette", "trueOrFalse"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Chapter::class, inversedBy="trueOrFalses")
     */
    private $chapter;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"roulette", "trueOrFalse"})
     */
    private $image;

    /**
     * @Vich\UploadableField(mapping="image_game_trueOrFalse", fileNameProperty="route")
     */
    private $imageFile;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"roulette", "trueOrFalse"})
     */
    private $text;

    /**
     * @ORM\Column(type="boolean")
     * @Groups({"roulette", "trueOrFalse"})
     */
    private $result;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Groups({"roulette", "trueOrFalse"})
     */
    private $trueorfalse_id;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Groups({"roulette", "trueOrFalse"})
     */
    private $time;

    /**
     * @ORM\Column(type="boolean")
     * @Groups({"roulette", "trueOrFalse"})
     */
    private $categorized;


    public function __clone()
    {
        $this->id = null;

        // clone image
        if ($this->getUploadsFolder()) {
            $this->cloneImage();
        }

    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getChapter(): ?Chapter
    {
        return $this->chapter;
    }

    public function setChapter(?Chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    public function getRoute(): ?string
    {
        return $this->image;
    }

    public function setRoute(?string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getText(): ?string
    {
        return $this->text;
    }

    public function setText(string $text): self
    {
        $this->text = $text;

        return $this;
    }

    public function getResult(): ?bool
    {
        return $this->result;
    }

    public function setResult(bool $result): self
    {
        $this->result = $result;

        return $this;
    }

    /**
     * @param mixed $imageFile
     * @return mixed
     */
    public function setImageFile($imageFile): self
    {
        $this->imageFile = $imageFile;
        return $this;
    }

    public function getImageFile()
    {
        return $this->imageFile;
    }

    public function getTrueorfalseId(): ?int
    {
        return $this->trueorfalse_id;
    }

    public function setTrueorfalseId(?int $trueorfalse_id): self
    {
        $this->trueorfalse_id = $trueorfalse_id;

        return $this;
    }

    public function getTime(): ?int
    {
        return $this->time;
    }

    public function setTime(?int $time): self
    {
        $this->time = $time;

        return $this;
    }

    public function serialize()
    {
        return serialize($this->getId());
    }

    public function unserialize($serialized)
    {
        $this->id = unserialize($serialized);
    }

    public function getCategorized(): ?bool
    {
        return $this->categorized;
    }

    public function setCategorized(bool $categorized): self
    {
        $this->categorized = $categorized;

        return $this;
    }
}
