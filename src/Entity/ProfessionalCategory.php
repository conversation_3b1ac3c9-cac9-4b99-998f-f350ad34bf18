<?php

namespace App\Entity;

use App\Repository\ProfessionalCategoryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=ProfessionalCategoryRepository::class)
 */
class ProfessionalCategory
{
    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     * @Groups({"detail", "announcement", "admin_area","audience"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"user_area", "announcement", "admin_area","audience"})
     */
    private $code;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"detail"})
     * @Groups({"user_area", "announcement", "admin_area", "audience"})
     */
    private $name;

    /**
     * @ORM\ManyToMany(targetEntity=Course::class, mappedBy="categories")
     */
    private $courses;

    /**
     * @ORM\ManyToOne(targetEntity=ProfessionalCategory::class, inversedBy="children")
     * @Groups({"admin_area"})
     */
    private $parent;

    /**
     * @ORM\OneToMany(targetEntity=ProfessionalCategory::class, mappedBy="parent")
     */
    private $children;

    public function __construct()
    {
        $this->courses = new ArrayCollection();
        $this->children = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->getFullName();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection|Course[]
     */
    public function getCourses(): Collection
    {
        return $this->courses;
    }

    public function addCourse(Course $course): self
    {
        if (!$this->courses->contains($course)) {
            $this->courses[] = $course;
            $course->addCategory($this);
        }

        return $this;
    }

    public function removeCourse(Course $course): self
    {
        if ($this->courses->contains($course)) {
            $this->courses->removeElement($course);
            $course->removeCategory($this);
        }

        return $this;
    }


    public function getFullName()
    {
        $fullName = [];

        if($this->getCode()) $fullName[] =$this->getCode();
        $fullName[] = $this->getName();

        return implode(': ', $fullName);

    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function setParent(?self $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    /**
     * @return Collection|self[]
     */
    public function getChildren(): Collection
    {
        return $this->children;
    }

    public function addChild(self $child): self
    {
        if (!$this->children->contains($child)) {
            $this->children[] = $child;
            $child->setParent($this);
        }

        return $this;
    }

    public function removeChild(self $child): self
    {
        if ($this->children->removeElement($child)) {
            // set the owning side to null (unless already changed)
            if ($child->getParent() === $this) {
                $child->setParent(null);
            }
        }

        return $this;
    }
}
