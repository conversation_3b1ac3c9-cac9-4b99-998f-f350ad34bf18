<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\RoleplayBeginningRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\SerializedName;

/**
 * @ORM\Entity(repositoryClass=RoleplayBeginningRepository::class)
 *
 * @ORM\Table(name="roleplay_beginning")
 */
class RoleplayBeginning
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups ({"project:detail", "project:visor", "beginning:create"})
     */
    private ?int $id;

    /**
     * @ORM\ManyToOne(targetEntity=RoleplayProject::class, inversedBy="beginnings")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private ?RoleplayProject $project;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private string $code;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups ({"project:detail", "project:visor", "beginning:create"})
     */
    private string $type = 'image';

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups ({"project:detail", "project:visor", "beginning:create"})
     */
    private ?string $background;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups ({"project:detail", "project:visor", "beginning:create"})
     */
    private ?string $description;

    /**
     * @ORM\Column(name="`order`", type="integer")
     *
     * @Groups ({"project:detail", "project:visor", "beginning:create"})
     */
    private ?int $order;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"project:detail", "project:visor", "scene:create"})
     */
    private ?string $attachedTag;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"project:detail", "project:visor", "scene:create"})
     */
    private ?string $attached;

    public function __construct()
    {
        $this->code = uniqid();
    }

    public function __clone()
    {
        $this->id = null;
        $this->code = uniqid();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProject(): ?RoleplayProject
    {
        return $this->project;
    }

    public function setProject(?RoleplayProject $project): self
    {
        $this->project = $project;

        return $this;
    }

    /**
     * @Groups ({"project:detail", "beginning:create"})
     *
     * @SerializedName("key")
     */
    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getBackground(): ?string
    {
        return $this->background;
    }

    public function setBackground(?string $background): self
    {
        $this->background = $background;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getOrder(): ?int
    {
        return $this->order;
    }

    public function setOrder(int $order): self
    {
        $this->order = $order;

        return $this;
    }

    public function isValid(): bool
    {
        return (bool) $this->getBackground();
    }

    public function getAttachedTag(): ?string
    {
        return $this->attachedTag;
    }

    public function setAttachedTag(?string $attachedTag): self
    {
        $this->attachedTag = $attachedTag;

        return $this;
    }

    public function getAttached(): ?string
    {
        return $this->attached;
    }

    public function setAttached(?string $attached): self
    {
        $this->attached = $attached;

        return $this;
    }
}
