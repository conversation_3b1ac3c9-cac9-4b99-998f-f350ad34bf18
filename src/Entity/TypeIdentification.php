<?php

namespace App\Entity;

use App\Repository\TypeIdentificationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use phpDocumentor\Reflection\Types\Integer;

/**
 * @ORM\Entity(repositoryClass=TypeIdentificationRepository::class)
 */
class TypeIdentification  implements TranslatableInterface
{
    use TranslatableTrait;
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $description;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $active;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $mask;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $main;

    /**
     * @ORM\OneToMany(targetEntity=UserIdentification::class, mappedBy="typeIdentification")
     */
    private $userIdentifications;

    public function __construct()
    {
        $this->userIdentifications = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setId($id): self
    {
        $this->id = $id;
        return $this;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(?bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getMask(): ?string
    {
        return $this->mask;
    }

    public function setMask(?string $mask): self
    {
        $this->mask = $mask;

        return $this;
    }

    public function isMain(): ?bool
    {
        return $this->main;
    }

    public function setMain(?bool $main): self
    {
        $this->main = $main;

        return $this;
    }

    /**
     * @return Collection<int, UserIdentification>
     */
    public function getUserIdentifications(): Collection
    {
        return $this->userIdentifications;
    }

    public function addUserIdentification(UserIdentification $userIdentification): self
    {
        if (!$this->userIdentifications->contains($userIdentification)) {
            $this->userIdentifications[] = $userIdentification;
            $userIdentification->setTypeIdentification($this);
        }

        return $this;
    }

    public function removeUserIdentification(UserIdentification $userIdentification): self
    {
        if ($this->userIdentifications->removeElement($userIdentification)) {
            // set the owning side to null (unless already changed)
            if ($userIdentification->getTypeIdentification() === $this) {
                $userIdentification->setTypeIdentification(null);
            }
        }

        return $this;
    }
}
