<?php

namespace App\Entity;

use App\Repository\AnnouncementObservationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;

/**
 * @ORM\HasLifecycleCallbacks()
 * @ORM\Entity(repositoryClass=AnnouncementObservationRepository::class)
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class AnnouncementObservation
{
    use AtAndBy;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Announcement::class, inversedBy="announcementObservations")
     * @ORM\JoinColumn(nullable=false)
     */
    private ?Announcement $announcement;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $courseStatus;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $comunicadoFundae = false;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $comunicadoAbilitia = false;

    /**
     * @ORM\Column(type="float")
     */
    private float $providerCost = 0.0;

    /**
     * @ORM\Column(type="float")
     */
    private float $hedimaManagementCost = 0.0;

    /**
     * @ORM\Column(type="float")
     */
    private float $travelAndMaintenanceCost = 0.0;

    /**
     * @ORM\Column(type="float")
     */
    private float $totalCost = 0.0;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $economicModule;

    /**
     * @ORM\Column(type="float")
     */
    private float $finalPax = 0.0;

    /**
     * @ORM\Column(type="float")
     */
    private float $maximumBonus = 0.0;

    /**
     * @ORM\Column(type="float")
     */
    private float $subsidizedAmount = 0.0;

    /**
     * @ORM\Column(type="float")
     */
    private float $privateAmount = 0.0;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private ?string $providerInvoiceNumber;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private ?string $hedimaManagementInvoiceNumber;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $travelAndMaintenance;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $invoiceStatus = null;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private ?string $observations = null;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementObservationDocument::class, mappedBy="announcementObservation", orphanRemoval=true)
     */
    private $announcementObservationDocuments;

    public function __construct()
    {
        $this->announcementObservationDocuments = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAnnouncement(): ?Announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement(?Announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }

    public function getProviderCost(): ?float
    {
        return $this->providerCost;
    }

    public function setProviderCost(float $providerCost): self
    {
        $this->providerCost = $providerCost;

        return $this;
    }

    public function getHedimaManagementCost(): ?float
    {
        return $this->hedimaManagementCost;
    }

    public function setHedimaManagementCost(float $hedimaManagementCost): self
    {
        $this->hedimaManagementCost = $hedimaManagementCost;

        return $this;
    }

    public function getTravelAndMaintenanceCost(): ?float
    {
        return $this->travelAndMaintenanceCost;
    }

    public function setTravelAndMaintenanceCost(float $travelAndMaintenanceCost): self
    {
        $this->travelAndMaintenanceCost = $travelAndMaintenanceCost;

        return $this;
    }

    public function getTotalCost(): ?float
    {
        return $this->totalCost;
    }

    public function setTotalCost(float $totalCost): self
    {
        $this->totalCost = $totalCost;

        return $this;
    }

    public function getFinalPax(): ?float
    {
        return $this->finalPax;
    }

    public function setFinalPax(float $finalPax): self
    {
        $this->finalPax = $finalPax;

        return $this;
    }

    public function getMaximumBonus(): ?float
    {
        return $this->maximumBonus;
    }

    public function setMaximumBonus(float $maximumBonus): self
    {
        $this->maximumBonus = $maximumBonus;

        return $this;
    }

    public function getSubsidizedAmount(): ?float
    {
        return $this->subsidizedAmount;
    }

    public function setSubsidizedAmount(float $subsidizedAmount): self
    {
        $this->subsidizedAmount = $subsidizedAmount;

        return $this;
    }

    public function getPrivateAmount(): ?float
    {
        return $this->privateAmount;
    }

    public function setPrivateAmount(float $privateAmount): self
    {
        $this->privateAmount = $privateAmount;

        return $this;
    }

    public function getProviderInvoiceNumber(): ?string
    {
        return $this->providerInvoiceNumber;
    }

    public function setProviderInvoiceNumber(?string $providerInvoiceNumber): self
    {
        $this->providerInvoiceNumber = $providerInvoiceNumber;

        return $this;
    }

    public function getHedimaManagementInvoiceNumber(): ?string
    {
        return $this->hedimaManagementInvoiceNumber;
    }

    public function setHedimaManagementInvoiceNumber(?string $hedimaManagementInvoiceNumber): self
    {
        $this->hedimaManagementInvoiceNumber = $hedimaManagementInvoiceNumber;

        return $this;
    }

    public function getInvoiceStatus(): ?string
    {
        return $this->invoiceStatus;
    }

    public function setInvoiceStatus(string $invoiceStatus): self
    {
        $this->invoiceStatus = $invoiceStatus;

        return $this;
    }

    public function getObservations(): ?string
    {
        return $this->observations;
    }

    public function setObservations(?string $observations): self
    {
        $this->observations = $observations;

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementObservationDocument>
     */
    public function getAnnouncementObservationDocuments(): Collection
    {
        return $this->announcementObservationDocuments;
    }

    public function addAnnouncementObservationDocument(AnnouncementObservationDocument $announcementObservationDocument): self
    {
        if (!$this->announcementObservationDocuments->contains($announcementObservationDocument)) {
            $this->announcementObservationDocuments[] = $announcementObservationDocument;
            $announcementObservationDocument->setAnnouncementObservation($this);
        }

        return $this;
    }

    public function removeAnnouncementObservationDocument(AnnouncementObservationDocument $announcementObservationDocument): self
    {
        if ($this->announcementObservationDocuments->removeElement($announcementObservationDocument)) {
            // set the owning side to null (unless already changed)
            if ($announcementObservationDocument->getAnnouncementObservation() === $this) {
                $announcementObservationDocument->setAnnouncementObservation(null);
            }
        }

        return $this;
    }

    public function isComunicadoFundae(): ?bool
    {
        return $this->comunicadoFundae;
    }

    public function setComunicadoFundae(bool $comunicadoFundae): self
    {
        $this->comunicadoFundae = $comunicadoFundae;

        return $this;
    }

    public function isComunicadoAbilitia(): ?bool
    {
        return $this->comunicadoAbilitia;
    }

    public function setComunicadoAbilitia(bool $comunicadoAbilitia): self
    {
        $this->comunicadoAbilitia = $comunicadoAbilitia;

        return $this;
    }

    public function getTravelAndMaintenance(): ?string
    {
        return $this->travelAndMaintenance;
    }

    public function setTravelAndMaintenance(?string $travelAndMaintenance): self
    {
        $this->travelAndMaintenance = $travelAndMaintenance;

        return $this;
    }

    public function getEconomicModule(): ?string
    {
        return $this->economicModule;
    }

    public function setEconomicModule(?string $economicModule): self
    {
        $this->economicModule = $economicModule;

        return $this;
    }

    public function getCourseStatus(): ?string
    {
        return $this->courseStatus;
    }

    public function setCourseStatus(?string $courseStatus): self
    {
        $this->courseStatus = $courseStatus;

        return $this;
    }

    public function __toString()
    {
        return $this->getId() . "";
    }
}
