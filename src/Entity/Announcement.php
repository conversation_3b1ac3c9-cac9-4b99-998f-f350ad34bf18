<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\AnnouncementRepository;
use App\Utils\TimeZoneConverter\TimeZoneConverterInterface;
use App\Utils\TimeZoneConverter\TimeZoneConverterTrait;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

/**
 * All datetime fields affected by timezone field.
 *
 * @ORM\HasLifecycleCallbacks()
 *
 * @ORM\Entity(repositoryClass=AnnouncementRepository::class)
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class Announcement implements TimeZoneConverterInterface
{
    use AtAndBy;
    use TimeZoneConverterTrait;

    public const CHAT_CHANNEL_FORUM = ChatChannel::TYPE_FORUM;
    public const CHAT_CHANNEL_DIRECT = ChatChannel::TYPE_DIRECT;
    public const CHAT_CHANNEL_GROUP_CHAT = ChatChannel::TYPE_GROUP_CHAT;

    public const CHAT_CHANNEL_TYPES = [
        self::CHAT_CHANNEL_FORUM,
        self::CHAT_CHANNEL_DIRECT,
        self::CHAT_CHANNEL_GROUP_CHAT,
    ];

    /**
     * Allowed fields after update.
     */

    /**
     * @ORM\Id()
     *
     * @ORM\GeneratedValue()
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"progress", "update"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Course::class, inversedBy="announcements")
     *
     * @ORM\JoinColumn(nullable=false)
     *
     * @Groups({"detail", "announcement"})
     */
    private $course;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private $code;

    /**
     * @ORM\Column(type="datetime")
     *
     * @Groups({"progress", "update"})
     */
    private $startAt;

    /**
     * @ORM\Column(type="datetime")
     *
     * @Groups({"progress", "update"})
     */
    private $finishAt;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=2, nullable=true)
     *
     * @Groups({"update"})
     */
    private float $totalHours;

    /**
     * @ORM\Column(type="integer")
     */
    private ?int $usersPerGroup = 0;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $objectiveAndContents;

    /**
     * @ORM\Column(type="boolean")
     *
     * @Groups({"update"})
     */
    private $subsidized;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private $actionType;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private $actionCode;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $denomination;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $contactPerson;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $contactPersonEmail;

    /**
     * @ORM\Column(type="string", length=20, nullable=true)
     */
    private $contactPersonTelephone;

    /**
     * @ORM\Column(type="datetime_immutable", nullable=true)
     */
    private $notifiedAt;

    /**
     * Fields not allowed, remove then when not used.
     */

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementUser::class, mappedBy="announcement", cascade={"persist", "remove"})
     */
    private $called;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @Groups({"update"})
     */
    private $maxUsers = 0;

    /**
     * @ORM\ManyToOne(targetEntity=User::class)
     *
     * @Groups({"update"})
     */
    private $subsidizer;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"update"})
     */
    private $formativeActionType;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"update"})
     */
    private $format;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"update"})
     */
    private $place;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"update"})
     */
    private $trainingCenter;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"update"})
     */
    private $trainingCenterAddress;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"update"})
     */
    private $trainingCenterNif;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"update"})
     */
    private $trainingCenterTeacher;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"update"})
     */
    private $trainingCenterTeacherDni;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"update"})
     */
    private $trainingCenterPhone;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"update"})
     */
    private $trainingCenterEmail;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"update"})
     */
    private $subsidizerEntity;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementTutor::class, mappedBy="announcement", orphanRemoval=true,  cascade={"persist", "remove"})
     */
    private $tutors;

    /**
     * @ORM\OneToMany(targetEntity=MaterialCourse::class, mappedBy="announcement")
     */
    private $materialCourses;

    /**
     * @ORM\OneToMany(targetEntity=TaskCourse::class, mappedBy="announcement")
     */
    private $taskCourses;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"update"})
     */
    private $generalInformation;

    /**
     * @ORM\OneToMany(targetEntity=Challenge::class, mappedBy="announcement")
     */
    private $challenges;

    /**
     * @ORM\ManyToMany(targetEntity=NpsQuestion::class, mappedBy="announcement")
     */
    private $npsQuestions;

    /**
     * @ORM\OneToOne(targetEntity=SessionsAnnouncement::class, mappedBy="announcement", cascade={"persist", "remove"})
     */
    private $sessionsAnnouncement;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementObservation::class, mappedBy="announcement", orphanRemoval=true)
     */
    private $announcementObservations;

    /**
     * @ORM\OneToMany(targetEntity=SurveyAnnouncement::class, mappedBy="announcement", orphanRemoval=true)
     */
    private $surveyAnnouncements;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementTemporalization::class, mappedBy="announcement", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $temporalizations;

    /**
     * @ORM\OneToOne(targetEntity=AnnouncementDidaticGuide::class, mappedBy="announcement", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $didaticGuide;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementGroup::class, mappedBy="announcement", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $announcementGroups;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementAprovedCriteria::class, mappedBy="announcement", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $announcementAprovedCriterias;

    /**
     * @ORM\OneToOne(targetEntity=AnnouncementInspectorAccess::class, mappedBy="announcement", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $announcementInspectorAccess;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isConfirmationRequiredDiploma;

    /**
     * @ORM\ManyToOne(targetEntity=TypeDiploma::class, inversedBy="announcements")
     */
    private $typeDiploma;

    /**
     * @ORM\OneToMany(targetEntity=UserHistoryDownloadDiploma::class, mappedBy="announcement")
     */
    private $userHistoryDownloadDiplomas;

    /**
     * @ORM\OneToMany(targetEntity=UserTime::class, mappedBy="announcement")
     */
    private $userTimes;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementNotification::class, mappedBy="announcement")
     */
    private $announcementNotifications;

    /**
     * @ORM\OneToMany(targetEntity=EmailNotification::class, mappedBy="emailNotificationAnnouncement")
     */
    private $emailNotificationAnnouncement;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementConfiguration::class, mappedBy="announcement", orphanRemoval=true)
     */
    private $announcementConfigurations;

    /**
     * @ORM\OneToMany(targetEntity=Nps::class, mappedBy="announcement")
     */
    private $nps;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementManager::class, mappedBy="announcement", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $announcementManagers;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private $extra = [];

    /**
     * If timezone is specified, the data is stored in utc 0
     * otherwise is using the server timezone.
     *
     * @ORM\Column(type="string", length=150, nullable=true)
     */
    private $timezone;

    public const STATUS_CONFIGURATION = 'CONFIGURATION'; // TAB CONFIGURACION
    /**
     * Send notifications.
     */
    public const STATUS_ACTIVE = 'ACTIVE'; // TAB ACTIVE -> EN_PROCESO
    public const STATUS_IN_PROGRESS = 'IN_PROGRESS'; // Not saved in BD, calculated in realtime

    public const STATUS_FINISHED = 'FINISHED'; // TAB FINISHED

    /**
     * Send notifications
     * CRON -> Pass to ARCHIVED status.
     */
    public const STATUS_INACTIVE = 'INACTIVE'; // TAB ARCHIVED []
    public const STATUS_ARCHIVED = 'ARCHIVED'; // TAB ARCHIVED
    public const STATUS_ALL = 'ALL'; // For filtering all statuses
    public const STATUS_OTHERS = 'OTHERS'; // For filtering unknown or other statuses

    public const STATUS_LIST = [
        self::STATUS_CONFIGURATION,
        self::STATUS_ACTIVE,
        self::STATUS_IN_PROGRESS,
        self::STATUS_FINISHED,
        self::STATUS_INACTIVE,
        self::STATUS_ARCHIVED,
    ];

    /** @var array[] Define the posible next status based on a current status */
    public const STATUS_RELATIONS = [
        self::STATUS_CONFIGURATION => [self::STATUS_ACTIVE, self::STATUS_ARCHIVED],
        self::STATUS_ACTIVE => [self::STATUS_INACTIVE, self::STATUS_FINISHED],
        self::STATUS_INACTIVE => [self::STATUS_ARCHIVED],
        self::STATUS_FINISHED => [self::STATUS_ARCHIVED],
    ];

    /**
     * [CONFIGURATION] | [ACTIVE] | [INACTIVE] | [FINISHED] | [ARCHIVED]
     *       |-------------->|---------->|           |----------->|
     *       |                           |----------------------->|
     *       |               |---------------------->|
     *       |--------------------------------------------------->|.
     *
     * @ORM\Column(type="string", nullable=true, length=40)
     */
    private ?string $status = null;

    public function __construct()
    {
        $this->called = new ArrayCollection();
        $this->challenges = new ArrayCollection();
        $this->tutors = new ArrayCollection();
        $this->subsidized = 0;
        $this->materialCourses = new ArrayCollection();
        $this->taskCourses = new ArrayCollection();
        $this->npsQuestions = new ArrayCollection();
        $this->announcementObservations = new ArrayCollection();
        $this->surveyAnnouncements = new ArrayCollection();
        $this->temporalizations = new ArrayCollection();
        $this->announcementAprovedCriterias = new ArrayCollection();
        $this->announcementGroups = new ArrayCollection();
        $this->userHistoryDownloadDiplomas = new ArrayCollection();
        $this->userTimes = new ArrayCollection();
        $this->announcementNotifications = new ArrayCollection();
        $this->announcementConfigurations = new ArrayCollection();
        $this->nps = new ArrayCollection();
        $this->announcementManagers = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->course->getName() . ': ' . $this->startAt->format('Y-m-d H:i:s') . ' - ' . $this->finishAt->format('Y-m-d H:i:s');
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getStartAt(): ?\DateTimeInterface
    {
        return $this->startAt;
    }

    public function setStartAt(\DateTimeInterface $startAt): self
    {
        $this->startAt = $startAt;

        return $this;
    }

    public function getFinishAt(): ?\DateTimeInterface
    {
        return $this->finishAt;
    }

    public function setFinishAt(\DateTimeInterface $finishAt): self
    {
        $this->finishAt = $finishAt;

        return $this;
    }

    public function getCourse(): ?Course
    {
        return $this->course;
    }

    public function setCourse(?Course $course): self
    {
        $this->course = $course;

        return $this;
    }

    public function setCalled(ArrayCollection $called): self
    {
        $this->called = $called;

        return $this;
    }

    /**
     * @return Collection|AnnouncementUser[]
     */
    public function getCalled(): Collection
    {
        return $this->called;
    }

    public function addCalled(AnnouncementUser $called): self
    {
        if (!$this->called->contains($called)) {
            $this->called[] = $called;
        }

        return $this;
    }

    public function removeCalled(AnnouncementUser $called): self
    {
        if ($this->called->contains($called)) {
            $this->called->removeElement($called);
        }

        return $this;
    }

    /**
     * @Assert\Callback
     */
    public function validate(ExecutionContextInterface $context)
    {
        if ($this->startAt > $this->finishAt) {
            $context
                ->buildViolation('The start date must be earlier than the end date')
                ->atPath('startAt')
                ->addViolation();
        }
    }

    public function getSubsidized(): ?bool
    {
        return $this->subsidized;
    }

    public function setSubsidized(bool $subsidized): self
    {
        $this->subsidized = $subsidized;
        //        if (!$this->subsidized) {
        //            $this->actionType = null;
        //            $this->actionCode = null;
        //            $this->denomination = null;
        //            $this->contactPerson = null;
        //            $this->contactPersonEmail = null;
        //            $this->contactPersonTelephone = null;
        //        }

        return $this;
    }

    public function getMaxUsers(): ?int
    {
        return $this->maxUsers ?? 0;
    }

    public function setMaxUsers(?int $maxUsers): self
    {
        $this->maxUsers = $maxUsers;

        return $this;
    }

    public function getSubsidizer(): ?User
    {
        return $this->subsidizer;
    }

    public function setSubsidizer(?User $subsidizer): self
    {
        $this->subsidizer = $subsidizer;

        return $this;
    }

    public function getFormativeActionType(): ?string
    {
        return $this->formativeActionType;
    }

    public function setFormativeActionType(?string $formativeActionType): self
    {
        $this->formativeActionType = $formativeActionType;

        return $this;
    }

    public function getFormat(): ?string
    {
        return $this->format;
    }

    public function setFormat(?string $format): self
    {
        $this->format = $format;

        return $this;
    }

    public function getTotalHours(): ?float
    {
        return $this->totalHours;
    }

    public function setTotalHours(?float $totalHours): self
    {
        $this->totalHours = $totalHours;

        return $this;
    }

    public function getPlace(): ?string
    {
        return $this->place;
    }

    public function setPlace(?string $place): self
    {
        $this->place = $place;

        return $this;
    }

    public function getTrainingCenter(): ?string
    {
        return $this->trainingCenter;
    }

    public function setTrainingCenter(?string $trainingCenter): self
    {
        $this->trainingCenter = $trainingCenter;

        return $this;
    }

    public function getTrainingCenterAddress(): ?string
    {
        return $this->trainingCenterAddress;
    }

    public function setTrainingCenterAddress(?string $trainingCenterAddress): self
    {
        $this->trainingCenterAddress = $trainingCenterAddress;

        return $this;
    }

    public function getTrainingCenterNif(): ?string
    {
        return $this->trainingCenterNif;
    }

    public function setTrainingCenterNif(?string $trainingCenterNif): self
    {
        $this->trainingCenterNif = $trainingCenterNif;

        return $this;
    }

    public function getTrainingCenterTeacher(): ?string
    {
        return $this->trainingCenterTeacher;
    }

    public function setTrainingCenterTeacher(?string $trainingCenterTeacher): self
    {
        $this->trainingCenterTeacher = $trainingCenterTeacher;

        return $this;
    }

    public function getTrainingCenterTeacherDni(): ?string
    {
        return $this->trainingCenterTeacherDni;
    }

    public function setTrainingCenterTeacherDni(?string $trainingCenterTeacherDni): self
    {
        $this->trainingCenterTeacherDni = $trainingCenterTeacherDni;

        return $this;
    }

    public function getTrainingCenterPhone(): ?string
    {
        return $this->trainingCenterPhone;
    }

    public function setTrainingCenterPhone(?string $trainingCenterPhone): self
    {
        $this->trainingCenterPhone = $trainingCenterPhone;

        return $this;
    }

    public function getTrainingCenterEmail(): ?string
    {
        return $this->trainingCenterEmail;
    }

    public function setTrainingCenterEmail(?string $trainingCenterEmail): self
    {
        $this->trainingCenterEmail = $trainingCenterEmail;

        return $this;
    }

    public function getSubsidizerEntity(): ?string
    {
        return $this->subsidizerEntity;
    }

    public function setSubsidizerEntity(?string $subsidizerEntity): self
    {
        $this->subsidizerEntity = $subsidizerEntity;

        return $this;
    }

    /**
     * @return Collection|AnnouncementTutor[]
     */
    public function getTutors(): Collection
    {
        return $this->tutors;
    }

    public function setTutors(ArrayCollection $tutors): self
    {
        $this->tutors = $tutors;

        return $this;
    }

    public function addTutor(AnnouncementTutor $tutor): self
    {
        if (!$this->tutors->contains($tutor)) {
            $this->tutors[] = $tutor;
            $tutor->setAnnouncement($this);
        }

        return $this;
    }

    public function removeTutor(AnnouncementTutor $tutor): self
    {
        if ($this->tutors->removeElement($tutor)) {
            // set the owning side to null (unless already changed)
            if ($tutor->getAnnouncement() === $this) {
                $tutor->setAnnouncement(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Challenge[]
     */
    public function getChallenges(): Collection
    {
        return $this->challenges;
    }

    public function addChallenge(Challenge $challenge): self
    {
        if (!$this->challenges->contains($challenge)) {
            $this->challenges[] = $challenge;
            $challenge->setAnnouncement($this);
        }

        return $this;
    }

    public function removeChallenge(Challenge $challenge): self
    {
        if ($this->challenges->removeElement($challenge)) {
            // set the owning side to null (unless already changed)
            if ($challenge->getAnnouncement() === $this) {
                $challenge->setAnnouncement(null);
            }
        }

        return $this;
    }

    /**
     * @return bool
     */
    public function isStarted()
    {
        return $this->startAt < new \DateTime('now');
    }

    /**
     * @return Collection<int, MaterialCourse>
     */
    public function getMaterialCourses(): Collection
    {
        return $this->materialCourses;
    }

    public function addMaterialCourse(MaterialCourse $materialCourse): self
    {
        if (!$this->materialCourses->contains($materialCourse)) {
            $this->materialCourses[] = $materialCourse;
            $materialCourse->setAnnouncement($this);
        }

        return $this;
    }

    public function removeMaterialCourse(MaterialCourse $materialCourse): self
    {
        if ($this->materialCourses->removeElement($materialCourse)) {
            // set the owning side to null (unless already changed)
            if ($materialCourse->getAnnouncement() === $this) {
                $materialCourse->setAnnouncement(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, TaskCourse>
     */
    public function getTaskCourses(): Collection
    {
        return $this->taskCourses;
    }

    public function addTaskCourse(TaskCourse $taskCourse): self
    {
        if (!$this->taskCourses->contains($taskCourse)) {
            $this->taskCourses[] = $taskCourse;
            $taskCourse->setAnnouncement($this);
        }

        return $this;
    }

    public function removeTaskCourse(TaskCourse $taskCourse): self
    {
        if ($this->taskCourses->removeElement($taskCourse)) {
            // set the owning side to null (unless already changed)
            if ($taskCourse->getAnnouncement() === $this) {
                $taskCourse->setAnnouncement(null);
            }
        }

        return $this;
    }

    public function getGeneralInformation(): ?string
    {
        return $this->generalInformation;
    }

    public function setGeneralInformation(?string $generalInformation): self
    {
        $this->generalInformation = $generalInformation;

        return $this;
    }

    /**
     * @return Collection<int, NpsQuestion>
     */
    public function getNpsQuestions(): Collection
    {
        return $this->npsQuestions;
    }

    public function addNpsQuestion(NpsQuestion $npsQuestion): self
    {
        if (!$this->npsQuestions->contains($npsQuestion)) {
            $this->npsQuestions[] = $npsQuestion;
            $npsQuestion->addAnnouncement($this);
        }

        return $this;
    }

    public function removeNpsQuestion(NpsQuestion $npsQuestion): self
    {
        if ($this->npsQuestions->removeElement($npsQuestion)) {
            $npsQuestion->removeAnnouncement($this);
        }

        return $this;
    }

    public function getSessionsAnnouncement(): ?SessionsAnnouncement
    {
        return $this->sessionsAnnouncement;
    }

    public function setSessionsAnnouncement(?SessionsAnnouncement $sessionsAnnouncement): self
    {
        // unset the owning side of the relation if necessary
        if (null === $sessionsAnnouncement && null !== $this->sessionsAnnouncement) {
            $this->sessionsAnnouncement->setAnnouncement(null);
        }

        // set the owning side of the relation if necessary
        if (null !== $sessionsAnnouncement && $sessionsAnnouncement->getAnnouncement() !== $this) {
            $sessionsAnnouncement->setAnnouncement($this);
        }

        $this->sessionsAnnouncement = $sessionsAnnouncement;

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementObservation>
     */
    public function getAnnouncementObservations(): Collection
    {
        return $this->announcementObservations;
    }

    public function addAnnouncementObservation(AnnouncementObservation $announcementObservation): self
    {
        if (!$this->announcementObservations->contains($announcementObservation)) {
            $this->announcementObservations[] = $announcementObservation;
            $announcementObservation->setAnnouncement($this);
        }

        return $this;
    }

    public function removeAnnouncementObservation(AnnouncementObservation $announcementObservation): self
    {
        if ($this->announcementObservations->removeElement($announcementObservation)) {
            // set the owning side to null (unless already changed)
            if ($announcementObservation->getAnnouncement() === $this) {
                $announcementObservation->setAnnouncement(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, SurveyAnnouncement>
     */
    public function getSurveyAnnouncements(): Collection
    {
        return $this->surveyAnnouncements;
    }

    public function addSurveyAnnouncement(SurveyAnnouncement $surveyAnnouncement): self
    {
        if (!$this->surveyAnnouncements->contains($surveyAnnouncement)) {
            $this->surveyAnnouncements[] = $surveyAnnouncement;
            $surveyAnnouncement->setAnnouncement($this);
        }

        return $this;
    }

    public function removeSurveyAnnouncement(SurveyAnnouncement $surveyAnnouncement): self
    {
        if ($this->surveyAnnouncements->removeElement($surveyAnnouncement)) {
            // set the owning side to null (unless already changed)
            if ($surveyAnnouncement->getAnnouncement() === $this) {
                $surveyAnnouncement->setAnnouncement(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementTemporalization>
     */
    public function getTemporalizations(): Collection
    {
        return $this->temporalizations;
    }

    public function setTemporalizations($temporalizations): self
    {
        $old = $this->getTemporalizations();
        foreach ($old as $o) {
            if (!\in_array($o, $temporalizations)) {
                $this->removeTemporalization($o);
            }
        }
        foreach ($temporalizations as $t) {
            $this->addTemporalization($t);
        }

        return $this;
    }

    public function addTemporalization(AnnouncementTemporalization $temporalization): self
    {
        if (!$this->temporalizations->contains($temporalization)) {
            $this->temporalizations[] = $temporalization;
            $temporalization->setAnnouncement($this);
        }

        return $this;
    }

    public function removeTemporalization(AnnouncementTemporalization $temporalization): self
    {
        if ($this->temporalizations->removeElement($temporalization)) {
            // set the owning side to null (unless already changed)
            if ($temporalization->getAnnouncement() === $this) {
                $temporalization->setAnnouncement(null);
            }
        }

        return $this;
    }

    public function getDidaticGuide(): ?AnnouncementDidaticGuide
    {
        return $this->didaticGuide;
    }

    public function setDidaticGuide(?AnnouncementDidaticGuide $didaticGuide): self
    {
        // unset the owning side of the relation if necessary
        if (null === $didaticGuide && null !== $this->didaticGuide) {
            $this->didaticGuide->setAnnouncement(null);
        }

        // set the owning side of the relation if necessary
        if (null !== $didaticGuide && $didaticGuide->getAnnouncement() !== $this) {
            $didaticGuide->setAnnouncement($this);
        }

        $this->didaticGuide = $didaticGuide;

        return $this;
    }

    public function getAnnouncementInspectorAccess(): ?AnnouncementInspectorAccess
    {
        return $this->announcementInspectorAccess;
    }

    public function setAnnouncementInspectorAccess(AnnouncementInspectorAccess $announcementInspectorAccess): self
    {
        // set the owning side of the relation if necessary
        if ($announcementInspectorAccess->getAnnouncement() !== $this) {
            $announcementInspectorAccess->setAnnouncement($this);
        }

        $this->announcementInspectorAccess = $announcementInspectorAccess;

        return $this;
    }

    public function setAnnouncementGroups($announcementGroups): self
    {
        $oldGroups = $this->getAnnouncementGroups();
        foreach ($oldGroups as $o) {
            if (!\in_array($o, $announcementGroups)) {
                $this->removeAnnouncementGroup($o);
            }
        }
        foreach ($announcementGroups as $g) {
            $this->addAnnouncementGroup($g);
        }

        return $this;
    }

    /**
     * @return Collection<AnnouncementGroup>
     */
    public function getAnnouncementGroups(): Collection
    {
        return $this->announcementGroups;
    }

    public function addAnnouncementGroup(AnnouncementGroup $announcementGroup): self
    {
        if (!$this->announcementGroups->contains($announcementGroup)) {
            $this->announcementGroups[] = $announcementGroup;
            $announcementGroup->setAnnouncement($this);
        }

        return $this;
    }

    public function removeAnnouncementGroup(AnnouncementGroup $announcementGroup): self
    {
        if ($this->announcementGroups->removeElement($announcementGroup)) {
            // set the owning side to null (unless already changed)
            if ($announcementGroup->getAnnouncement() === $this) {
                $announcementGroup->setAnnouncement(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, announcementAprovedCriteria>
     */
    public function getAnnouncementAprovedCriterias(): Collection
    {
        return $this->announcementAprovedCriterias;
    }

    public function setAnnouncementAprovedCriteria($criteria): self
    {
        $old = $this->getAnnouncementAprovedCriterias();
        foreach ($old as $o) {
            if (!\in_array($o, $criteria)) {
                $this->removeAnnouncementAprovedCriteria($o);
            }
        }
        foreach ($criteria as $c) {
            $this->addAnnouncementAprovedCriteria($c);
        }

        return $this;
    }

    public function addAnnouncementAprovedCriteria(AnnouncementAprovedCriteria $announcementAprovedCriteria): self
    {
        if (!$this->announcementAprovedCriterias->contains($announcementAprovedCriteria)) {
            $this->announcementAprovedCriterias[] = $announcementAprovedCriteria;
            $announcementAprovedCriteria->setAnnouncement($this);
        }

        return $this;
    }

    public function removeAnnouncementAprovedCriteria(AnnouncementAprovedCriteria $announcementAprovedCriteria): self
    {
        if ($this->announcementAprovedCriterias->removeElement($announcementAprovedCriteria)) {
            // set the owning side to null (unless already changed)
            if ($announcementAprovedCriteria->getAnnouncement() === $this) {
                $announcementAprovedCriteria->setAnnouncement(null);
            }
        }

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function isIsConfirmationRequiredDiploma(): ?bool
    {
        return $this->isConfirmationRequiredDiploma;
    }

    public function setIsConfirmationRequiredDiploma(?bool $isConfirmationRequiredDiploma): self
    {
        $this->isConfirmationRequiredDiploma = $isConfirmationRequiredDiploma;

        return $this;
    }

    public function getUsersPerGroup(): ?int
    {
        return $this->usersPerGroup;
    }

    public function setUsersPerGroup(int $usersPerGroup): self
    {
        $this->usersPerGroup = $usersPerGroup;

        return $this;
    }

    public function getObjectiveAndContents(): ?string
    {
        return $this->objectiveAndContents;
    }

    public function setObjectiveAndContents(?string $objectiveAndContents): self
    {
        $this->objectiveAndContents = $objectiveAndContents;

        return $this;
    }

    public function getActionType(): ?string
    {
        return $this->actionType;
    }

    public function setActionType(string $actionType): self
    {
        $this->actionType = $actionType;

        return $this;
    }

    public function getActionCode(): ?string
    {
        return $this->actionCode;
    }

    public function setActionCode(?string $actionCode): self
    {
        $this->actionCode = $actionCode;

        return $this;
    }

    public function getDenomination(): ?string
    {
        return $this->denomination;
    }

    public function setDenomination(?string $denomination): self
    {
        $this->denomination = $denomination;

        return $this;
    }

    public function getContactPerson(): ?string
    {
        return $this->contactPerson;
    }

    public function setContactPerson(?string $contactPerson): self
    {
        $this->contactPerson = $contactPerson;

        return $this;
    }

    public function getContactPersonEmail(): ?string
    {
        return $this->contactPersonEmail;
    }

    public function setContactPersonEmail(?string $contactPersonEmail): self
    {
        $this->contactPersonEmail = $contactPersonEmail;

        return $this;
    }

    public function getContactPersonTelephone(): ?string
    {
        return $this->contactPersonTelephone;
    }

    public function setContactPersonTelephone(?string $contactPersonTelephone): self
    {
        $this->contactPersonTelephone = $contactPersonTelephone;

        return $this;
    }

    public function getTypeDiploma(): ?TypeDiploma
    {
        return $this->typeDiploma;
    }

    public function setTypeDiploma(?TypeDiploma $typeDiploma): self
    {
        $this->typeDiploma = $typeDiploma;

        return $this;
    }

    /**
     * @return Collection<int, UserHistoryDownloadDiploma>
     */
    public function getUserHistoryDownloadDiplomas(): Collection
    {
        return $this->userHistoryDownloadDiplomas;
    }

    public function addUserHistoryDownloadDiploma(UserHistoryDownloadDiploma $userHistoryDownloadDiploma): self
    {
        if (!$this->userHistoryDownloadDiplomas->contains($userHistoryDownloadDiploma)) {
            $this->userHistoryDownloadDiplomas[] = $userHistoryDownloadDiploma;
            $userHistoryDownloadDiploma->setAnnouncement($this);
        }

        return $this;
    }

    public function removeUserHistoryDownloadDiploma(UserHistoryDownloadDiploma $userHistoryDownloadDiploma): self
    {
        if ($this->userHistoryDownloadDiplomas->removeElement($userHistoryDownloadDiploma)) {
            // set the owning side to null (unless already changed)
            if ($userHistoryDownloadDiploma->getAnnouncement() === $this) {
                $userHistoryDownloadDiploma->setAnnouncement(null);
            }
        }

        return $this;
    }

    public function getTemporalizationsData(): array
    {
        $configurations = $this->getAnnouncementConfigurations();
        $isEnabled = false;

        foreach ($configurations as $conf) {
            if (AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION === $conf->getConfiguration()->getId()) {
                $isEnabled = true;
            }
        }

        if (!$isEnabled) {
            return [];
        }
        $temporizations = $this->temporalizations;
        $temporizationData = [];
        foreach ($temporizations as $temporization) {
            $temporizationData[] = [
                'name' => ($chapter = $temporization->getChapter()) ? $chapter->getTitle() : null,
                'startAt' => $temporization->getStartedAt(),
                'finishAt' => $temporization->getFinishedAt(),
            ];
        }

        return $temporizationData;
    }

    /**
     * @return Collection<int, UserTime>
     */
    public function getUserTimes(): Collection
    {
        return $this->userTimes;
    }

    public function addUserTime(UserTime $userTime): self
    {
        if (!$this->userTimes->contains($userTime)) {
            $this->userTimes[] = $userTime;
            $userTime->setAnnouncement($this);
        }

        return $this;
    }

    public function removeUserTime(UserTime $userTime): self
    {
        if ($this->userTimes->removeElement($userTime)) {
            // set the owning side to null (unless already changed)
            if ($userTime->getAnnouncement() === $this) {
                $userTime->setAnnouncement(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementNotification>
     */
    public function getAnnouncementNotifications(): Collection
    {
        return $this->announcementNotifications;
    }

    public function addAnnouncementNotification(AnnouncementNotification $announcementNotification): self
    {
        if (!$this->announcementNotifications->contains($announcementNotification)) {
            $this->announcementNotifications[] = $announcementNotification;
            $announcementNotification->setAnnouncement($this);
        }

        return $this;
    }

    public function removeAnnouncementNotification(AnnouncementNotification $announcementNotification): self
    {
        if ($this->announcementNotifications->removeElement($announcementNotification)) {
            // set the owning side to null (unless already changed)
            if ($announcementNotification->getAnnouncement() === $this) {
                $announcementNotification->setAnnouncement(null);
            }
        }

        return $this;
    }

    public function getNotifiedAt(): ?\DateTimeImmutable
    {
        return $this->notifiedAt;
    }

    public function setNotifiedAt(?\DateTimeImmutable $notifiedAt): self
    {
        $this->notifiedAt = $notifiedAt;

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementConfiguration>
     */
    public function getAnnouncementConfigurations(): Collection
    {
        return $this->announcementConfigurations;
    }

    public function addAnnouncementConfiguration(AnnouncementConfiguration $announcementConfiguration): self
    {
        if (!$this->announcementConfigurations->contains($announcementConfiguration)) {
            $this->announcementConfigurations[] = $announcementConfiguration;
            $announcementConfiguration->setAnnouncement($this);
        }

        return $this;
    }

    public function removeAnnouncementConfiguration(AnnouncementConfiguration $announcementConfiguration): self
    {
        if ($this->announcementConfigurations->removeElement($announcementConfiguration)) {
            // set the owning side to null (unless already changed)
            if ($announcementConfiguration->getAnnouncement() === $this) {
                $announcementConfiguration->setAnnouncement(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Nps>
     */
    public function getNps(): Collection
    {
        return $this->nps;
    }

    public function addNp(Nps $np): self
    {
        if (!$this->nps->contains($np)) {
            $this->nps[] = $np;
            $np->setAnnouncement($this);
        }

        return $this;
    }

    public function removeNp(Nps $np): self
    {
        if ($this->nps->removeElement($np)) {
            // set the owning side to null (unless already changed)
            if ($np->getAnnouncement() === $this) {
                $np->setAnnouncement(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementManager>
     */
    public function getAnnouncementManagers(): Collection
    {
        return $this->announcementManagers;
    }

    public function addAnnouncementManager(AnnouncementManager $announcementManager): self
    {
        if (!$this->announcementManagers->contains($announcementManager)) {
            $this->announcementManagers[] = $announcementManager;
            $announcementManager->setAnnouncement($this);
        }

        return $this;
    }

    public function removeAnnouncementManager(AnnouncementManager $announcementManager): self
    {
        if ($this->announcementManagers->removeElement($announcementManager)) {
            // set the owning side to null (unless already changed)
            if ($announcementManager->getAnnouncement() === $this) {
                $announcementManager->setAnnouncement(null);
            }
        }

        return $this;
    }

    public function getTimezone(): ?string
    {
        return $this->timezone ?? 'Europe/Madrid';
    }

    public function setTimezone(?string $timezone): self
    {
        $this->timezone = $timezone;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getTimezoneOrDefault(): string
    {
        return $this->getTimezone() ?? 'Europe/Madrid';
    }

    public function getExtra(): array
    {
        return $this->extra ?? [];
    }

    public function setExtra(array $extra): self
    {
        $this->extra = $extra;

        return $this;
    }

    public static function canChangeStatus(Announcement $announcement, string $nextStatus): bool
    {
        $status = $announcement->getStatus() ?? Announcement::STATUS_CONFIGURATION;
        if (!\in_array($nextStatus, Announcement::STATUS_RELATIONS[$status])) {
            return false;
        }

        if (Announcement::STATUS_ACTIVE === $status && Announcement::STATUS_INACTIVE === $nextStatus) {
            $tz = new \DateTimeZone($announcement->getTimezoneOrDefault());
            $current = new \DateTimeImmutable('now', $tz);

            if ($current >= $announcement->getStartAt() && $current <= $announcement->getFinishAt()) { // Announcement is in progress
                return false;
            }
        }

        return true;
    }

    public function __clone()
    {
        $this->id = null;
        $this->code = null;
        $this->called = new ArrayCollection();
        $this->challenges = new ArrayCollection();
        $this->tutors = new ArrayCollection();
        $this->subsidized = 0;
        $this->materialCourses = new ArrayCollection();
        $this->taskCourses = new ArrayCollection();
        $this->npsQuestions = new ArrayCollection();
        $this->announcementObservations = new ArrayCollection();
        $this->surveyAnnouncements = new ArrayCollection();
        $this->temporalizations = new ArrayCollection();
        $this->announcementGroups = new ArrayCollection();
        $this->announcementAprovedCriterias = new ArrayCollection();
        $this->announcementInspectorAccess = null;
        $this->userHistoryDownloadDiplomas = new ArrayCollection();
        $this->userTimes = new ArrayCollection();
        $this->announcementNotifications = new ArrayCollection();
        $this->announcementConfigurations = new ArrayCollection();
        $this->nps = new ArrayCollection();
        $this->emailNotificationAnnouncement = new ArrayCollection();
        $this->announcementManagers = new ArrayCollection();
        $this->didaticGuide = null;
        $this->notifiedAt = null;
    }
}
