<?php

namespace App\Entity;

use App\Repository\AnswersVideoQuizRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=AnswersVideoQuizRepository::class)
 */
class AnswersVideoQuiz
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"videoquiz"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"videoquiz"})
     */
    private $answer;

    /**
     * @ORM\ManyToOne(targetEntity=Videopreguntas::class, inversedBy="answersVideoQuizzes")
     */
    private $question;

    /**
     * @ORM\Column(type="boolean")
     * @Groups({"videoquiz"})
     */
    private $isCorrect;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAnswer(): ?string
    {
        return $this->answer;
    }

    public function setAnswer(string $answer): self
    {
        $this->answer = $answer;

        return $this;
    }

    public function getQuestion(): ?Videopreguntas
    {
        return $this->question;
    }

    public function setQuestion(?Videopreguntas $question): self
    {
        $this->question = $question;

        return $this;
    }

    public function isIsCorrect(): ?bool
    {
        return $this->isCorrect;
    }

    public function setIsCorrect(bool $isCorrect): self
    {
        $this->isCorrect = $isCorrect;

        return $this;
    }
}
