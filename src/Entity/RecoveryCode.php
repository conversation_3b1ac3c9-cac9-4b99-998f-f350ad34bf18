<?php

namespace App\Entity;

use App\Repository\RecoveryCodeRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=RecoveryCodeRepository::class)
 */
class RecoveryCode
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="date")
     */
    private $dateRecovery;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $codeActivation;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="recoveryCodes")
     * @ORM\JoinColumn(nullable=false)
     */
    private $user;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $email;

    /**
     * @ORM\Column(type="integer")
     */
    private $state;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDateRecovery(): ?\DateTimeInterface
    {
        return $this->dateRecovery;
    }

    public function setDateRecovery(\DateTimeInterface $dateRecovery): self
    {
        $this->dateRecovery = $dateRecovery;

        return $this;
    }

    public function getCodeActivation(): ?string
    {
        return $this->codeActivation;
    }

    public function setCodeActivation(string $codeActivation): self
    {
        $this->codeActivation = $codeActivation;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getState(): ?int
    {
        return $this->state;
    }

    public function setState(int $state): self
    {
        $this->state = $state;

        return $this;
    }
}
