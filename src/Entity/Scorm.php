<?php

namespace App\Entity;

use App\Repository\ScormRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Vich\UploaderBundle\Mapping\Annotation as Vich;
use ZipArchive;
use SplFileInfo;

/**
 * @ORM\Entity(repositoryClass=ScormRepository::class)
 * @Vich\Uploadable()
 */
class Scorm
{

    public function __construct()
    {
        $this->rawScore   = 75;
        $this->allowReset = false;
    }

    public const SCORM_STATUS = 'cmi.core.lesson_status';
    public const SCORM_COMPLETION = 'cmi.completion_status';
    public const SCORM_CORE_COMPLETION = 'cmi.core.completion_status';
    public const SCORM_STATUS_PASSED = 'passed';
    public const SCORM_STATUS_COMPLETED = 'completed';
    public const SCORM_STATUS_COMPLETE = 'complete';
    public const SCORM_SCORE_RAW = 'cmi.core.score.raw';
    public const SCORM_SCORE_MAX = 'cmi.core.score.max';
    public const SCORM_SCORE_MIN = 'cmi.core.score.min';

    public const SCORM_LESSON_LOCATION = 'cmi.core.lesson_location';
    public const SCORM_SUSPEND_DATA = 'cmi.suspend_data';
    public const SCORM_LESSON_LOCATION_INIT = '';
    public const SCORM_EMPTY_PARAM_VALUE = '';

    public const SCORM_CORE_COMPLETION_DEFAULT_VALUE = 100;
    public const SCORM_SCORE_RAW_DEFAULT_VALUE = 100;
    public const SCORM_SCORE_RAW_DISABLED = null;


    public const SCORM_DIR = DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . 'scorms';

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     * @Groups({"scorm", "progress"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"scorm", "progress"})
     */
    private $folder;

    /**
     * @Vich\UploadableField(mapping="scorm_package", fileNameProperty="folder")
     *
     * var File $scormPackage
     */
    private $scormPackage;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"scorm", "progress"})
     */
    private $entryPoint;

    /**
     * @ORM\OneToOne(targetEntity=Chapter::class, inversedBy="scorm", cascade={"persist"})
     * @ORM\JoinColumn(nullable=false)
     */
    private $chapter;

    /**
     * @ORM\Column(type="json", nullable=true)
     * @Groups({"scorm", "progress"})
     */
    private $menu;

    /**
     * @ORM\Column(type="boolean")
     */
    private $showMenu;
    
    /**
     * @ORM\Column(type="integer", options={"default": 75})
     */
    private $rawScore;

    /**
     * @ORM\Column(type="boolean", options={"default": false})
     */
    private $allowReset;


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFolder(): ?string
    {
        return $this->folder;
    }

    public function setFolder(?string $folder): self
    {
        $this->folder = $folder;

        return $this;
    }

    /**
     * @return File
     */
    public function getScormPackage()
    {
        return $this->scormPackage;
    }

    /**
     * @param mixed $scormPackage
     */
    public function setScormPackage($scormPackage): void
    {
        $this->scormPackage = $scormPackage;
    }

    public function getEntryPoint(): ?string
    {
        return $this->entryPoint;
    }

    public function setEntryPoint(?string $entryPoint): self
    {
        $this->entryPoint = $entryPoint;

        return $this;
    }

    public function getChapter(): ?Chapter
    {
        return $this->chapter;
    }

    public function setChapter(Chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    public function getMenu(): ?array
    {
        return $this->menu;
    }

    public function setMenu(?array $menu): self
    {
        $this->menu = $menu;

        return $this;
    }

    public function getAllowReset(): ?bool
    {
        return $this->allowReset;
    }
    
    public function setAllowReset(?bool $allowReset): self
    {
        $this->allowReset = $allowReset;

        return $this;
    }   

    public function getRawScore(): ?int
    {
        return $this->rawScore;
    }
    
    public function setRawScore(?int $rawScore): self
    {
        $this->rawScore = $rawScore;
        return $this;
    }   

    public function resetRawScore(): self
    {
        $this->rawScore = 0;
        return $this;
    }   


    public function getRoute(): ?string
    {
        return getcwd(). self::SCORM_DIR . DIRECTORY_SEPARATOR . $this->folder . DIRECTORY_SEPARATOR;
    }

    public function hasManifest(): ?bool
    {
        return file_exists($this->getRoute() . 'imsmanifest.xml');
    }

    private function hasCSF(): ?bool
    {
        return file_exists($this->getRoute() . 'CSF.xml');
    }

    public function unzipScorm ()
    {
        $zip = new ZipArchive();
        if($zip->open(getcwd(). self::SCORM_DIR . DIRECTORY_SEPARATOR . 'packages' . DIRECTORY_SEPARATOR . $this->getFolder()) == TRUE) {
            $folder = new SplFileInfo($this->getFolder());
            $folder = $folder->getBasename('.' . $folder->getExtension());
            $zip->extractTo(getcwd(). Scorm::SCORM_DIR .  DIRECTORY_SEPARATOR . $folder .  DIRECTORY_SEPARATOR);
            $zip->close();

            $this->setFolder($folder);

            $this->createMenu();
        }
    }

    /**
     * Recibe un objeto Scorm y devuelve el menu ya en html del mismo
     *
     * @param Scorm $scorm
     * @return string
     */
    function createMenu()
    {
        $menus = [];
        if($this->hasManifest()) {
            $this->setEntryPoint(null);
            $manifest = simplexml_load_file($this->getRoute() . 'imsmanifest.xml');
            $namespaces = $manifest->getDocNamespaces();
            $manifest->registerXPathNamespace('a', $namespaces['']);

            $organizations = $manifest->organizations;
            foreach ($organizations->organization as $organization) {

                $title = trim($organization->title);

                $menu = [
                    'title' => (empty($title)) ? (string)$organization["identifier"] : $title,
                    'submenu' => $this->getItems($organization, $manifest)
                ];

                $menus[] = $menu;
            }
        }
        else $menus = [$this->getRoute() . 'imsmanifest.xml'];

        $this->menu = $menus;
    }

    /**
     * Recibe el XML del nodo padre completo y devuelve el árbol del items en un array
     *
     * @param $xml
     * @param $manifest
     * @return array
     */
    private function getItems($xml, $manifest)
    {
        $items = [];

        foreach ($xml->item as $item) {

            if(isset($item['identifierref'])){
                $resource = $manifest->xpath('//a:resource[@identifier="'. $item['identifierref'] .'"]');
                $href = $resource[0]['href'];
                if(isset($item['parameters'])) $href .= $item['parameters'];

                if(is_null($this->getEntryPoint())) $this->setEntryPoint($href);
            }

            $title = trim($item->title);

            $item = [
                'title' => (empty($title)) ? (string)$item["identifier"] : $title,
                'href'  => (isset($href)) ? (string) $href : '',
                'submenu' => $this->getItems($item, $manifest)
            ];

            $items[] = $item;
        }

        return $items;
    }


    /**
     * @Assert\Callback
     * @param ExecutionContextInterface $context
     */
    public function validate(ExecutionContextInterface $context)
    {
        if (! in_array($this->scormPackage->getMimeType(), array(
            'application/zip',
        ))) {
            $context
                ->buildViolation('Wrong file type (zip - '.$this->scormPackage->getMimeType() . ' - ' . getcwd(). self::SCORM_DIR . '\\packages\\' . $this->scormPackage->getClientOriginalName().')')
                ->atPath('fileName')
                ->addViolation()
            ;
        }
    }

    public function getShowMenu(): ?bool
    {
        return $this->showMenu;
    }

    public function setShowMenu(bool $showMenu): self
    {
        $this->showMenu = $showMenu;

        return $this;
    }
}
