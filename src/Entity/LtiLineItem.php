<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\LtiLineItemRepository;
use Doctrine\ORM\Mapping as ORM;
use OAT\Library\Lti1p3Ags\Model\LineItem\LineItem;
use OAT\Library\Lti1p3Ags\Model\LineItem\LineItemInterface;
use OAT\Library\Lti1p3Ags\Model\LineItem\LineItemSubmissionReview;
use OAT\Library\Lti1p3Ags\Model\LineItem\LineItemSubmissionReviewInterface;
use OAT\Library\Lti1p3Core\Util\Collection\Collection;
use OAT\Library\Lti1p3Core\Util\Collection\CollectionInterface;
use Ramsey\Uuid\Uuid;

/**
 * @ORM\Entity(repositoryClass=LtiLineItemRepository::class)
 */
class LtiLineItem
{
    /**
     * Local.
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=40, nullable=true)
     */
    private ?string $identifier;

    /**
     * @ORM\Column(type="float")
     */
    private int $scoreMaximum = 0;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private ?string $label = null;

    /**
     * @ORM\Column(type="string", length=40, nullable=true)
     */
    private ?string $resourceId = null;

    /**
     * @ORM\Column(type="string", length=40, nullable=true)
     */
    private ?string $resourceLinkId = null;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private ?string $tag = null;

    /**
     * @ORM\Column(type="datetime_immutable", nullable=true)
     */
    private ?\DateTimeImmutable $startDateTime = null;

    /**
     * @ORM\Column(type="datetime_immutable", nullable=true)
     */
    private ?\DateTimeImmutable $endDateTime = null;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $submissionReview = null;

    /**
     * @ORM\Column(type="json")
     */
    private array $additionalProperties = [];

    /**
     * @ORM\Column(type="datetime_immutable")
     */
    private ?\DateTimeImmutable $createdAt;

    public function __construct()
    {
        $this->identifier = Uuid::uuid4()->toString();
        $this->createdAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getScoreMaximum(): float
    {
        return $this->scoreMaximum ?? 0;
    }

    public function setScoreMaximum(float $scoreMaximum): self
    {
        $this->scoreMaximum = $scoreMaximum;

        return $this;
    }

    public function getLabel(): string
    {
        return $this->label ?? '';
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getResourceId(): ?string
    {
        return $this->resourceId;
    }

    public function setResourceId(?string $resourceId): self
    {
        $this->resourceId = $resourceId;

        return $this;
    }

    public function getResourceLinkId(): ?string
    {
        return $this->resourceLinkId;
    }

    public function setResourceLinkId(?string $resourceLinkId): self
    {
        $this->resourceLinkId = $resourceLinkId;

        return $this;
    }

    public function getTag(): ?string
    {
        return $this->tag;
    }

    public function setTag(?string $tag): self
    {
        $this->tag = $tag;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getIdentifier(): ?string
    {
        return $this->identifier;
    }

    public function setIdentifier(?string $identifier): self
    {
        $this->identifier = $identifier;

        return $this;
    }

    public function getResourceIdentifier(): ?string
    {
        return $this->resourceId;
    }

    public function setResourceIdentifier(?string $resourceIdentifier): self
    {
        $this->resourceId = $resourceIdentifier;

        return $this;
    }

    public function getResourceLinkIdentifier(): ?string
    {
        return $this->resourceLinkId;
    }

    public function setResourceLinkIdentifier(?string $resourceLinkIdentifier): self
    {
        $this->resourceLinkId = $resourceLinkIdentifier;

        return $this;
    }

    public function getStartDateTime(): ?\DateTimeInterface
    {
        return $this->startDateTime;
    }

    public function setStartDateTime(?\DateTimeInterface $startDateTime): self
    {
        $this->startDateTime = $startDateTime;

        return $this;
    }

    public function getEndDateTime(): ?\DateTimeInterface
    {
        return $this->endDateTime;
    }

    public function setEndDateTime(?\DateTimeInterface $endDateTime): self
    {
        $this->endDateTime = $endDateTime;

        return $this;
    }

    public function getSubmissionReview(): ?LineItemSubmissionReviewInterface
    {
        //        return $this->submissionReview;
        return null;
    }

    public function setSubmissionReview(?LineItemSubmissionReviewInterface $submissionReview): self
    {
        $this->submissionReview = null;

        return $this;
    }

    public function getAdditionalProperties(): CollectionInterface
    {
        return (new Collection())->add($this->additionalProperties);
    }

    public function setAdditionalProperties(CollectionInterface $additionalProperties): self
    {
        $this->additionalProperties = $additionalProperties->all();

        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->getResourceLinkIdentifier() . '/lineitems/' . $this->resourceId;
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->getIdentifier(),
            'startDateTime' => $this->startDateTime,
            'endDateTime' => $this->endDateTime,
            'scoreMaximum' => $this->scoreMaximum,
            'label' => $this->label,
            'tag' => $this->tag,
            'resourceId' => $this->resourceId,
            'resourceLinkId' => $this->resourceLinkId,
            'submissionReview' => $this->submissionReview
        ];
    }

    public function copy(LineItemInterface $lineItem): LineItemInterface
    {
        $this
            ->setScoreMaximum($lineItem->getScoreMaximum())
            ->setLabel($lineItem->getLabel())
            ->setResourceIdentifier($lineItem->getResourceIdentifier())
            ->setResourceLinkIdentifier($lineItem->getResourceLinkIdentifier())
            ->setTag($lineItem->getTag())
            ->setStartDateTime($lineItem->getStartDateTime())
            ->setEndDateTime($lineItem->getEndDateTime())
            ->setSubmissionReview($lineItem->getSubmissionReview())
            ->setAdditionalProperties($lineItem->getAdditionalProperties());

        return $this->getLineItem();
    }

    public function getLineItem(): LineItemInterface
    {
        $submissionReview = null;
        if (!empty($this->submissionReview)) {
            $submissionReview = new LineItemSubmissionReview(
                $submissionReview['status'] ?? [],
                $submissionReview['label'] ?? null,
                $submissionReview['url'] ?? null,
                $submissionReview['customProperties'] ?? []
            );
        }

        return new LineItem(
            $this->scoreMaximum,
            $this->label,
            $this->identifier,
            $this->resourceId,
            $this->resourceLinkId,
            $this->tag,
            $this->startDateTime,
            $this->endDateTime,
            $submissionReview,
            $this->additionalProperties
        );
    }

    public static function fromLineItemInterface(LineItemInterface $lineItem)
    {
        return (new self())
            ->setScoreMaximum($lineItem->getScoreMaximum())
            ->setLabel($lineItem->getLabel())
            ->setIdentifier($lineItem->getIdentifier())
            ->setResourceIdentifier($lineItem->getResourceIdentifier())
            ->setResourceLinkIdentifier($lineItem->getResourceLinkIdentifier())
            ->setTag($lineItem->getTag())
            ->setStartDateTime($lineItem->getStartDateTime())
            ->setEndDateTime($lineItem->getEndDateTime())
            ->setSubmissionReview($lineItem->getSubmissionReview())
            ->setAdditionalProperties($lineItem->getAdditionalProperties())
        ;
    }
}
