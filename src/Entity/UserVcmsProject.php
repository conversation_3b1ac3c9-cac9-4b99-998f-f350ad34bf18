<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\UserVcmsProjectRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=UserVcmsProjectRepository::class)
 *
 * @ORM\HasLifecycleCallbacks()
 */
class UserVcmsProject
{
    use AtAndBy;

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="vcmsProjects")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $user;

    /**
     * @ORM\ManyToOne(targetEntity=VcmsProject::class)
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $project;

    /**
     * @ORM\Column(type="json")
     */
    private $actionsData = [];

    /**
     * @ORM\OneToOne(targetEntity=UserCourseChapter::class, cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $userCourseChapter;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getProject(): ?VcmsProject
    {
        return $this->project;
    }

    public function setProject(?VcmsProject $project): self
    {
        $this->project = $project;

        return $this;
    }

    public function getActionsData(): ?array
    {
        return $this->actionsData;
    }

    public function setActionsData(array $actionsData): self
    {
        $this->actionsData = $actionsData;

        return $this;
    }

    public function getUserCourseChapter(): ?UserCourseChapter
    {
        return $this->userCourseChapter;
    }

    public function setUserCourseChapter(UserCourseChapter $userCourseChapter): self
    {
        $this->userCourseChapter = $userCourseChapter;

        return $this;
    }

    public function __toString()
    {
        return $this->getProject()->getTitle();
    }
}
