<?php

namespace App\Entity;

use App\Repository\MessageAttachmentRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=MessageAttachmentRepository::class)
 * @Vich\Uploadable
 */
class MessageAttachment
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"list", "detail"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Message::class, inversedBy="messageAttachments")
     * @ORM\JoinColumn(nullable=false)
     */
    private $message;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $filename;

    /**
     * @Vich\UploadableField(mapping="message_attachment", fileNameProperty="filename", mimeType="mimeType")
     */
    private $filenameFile;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"list", "detail"})
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"list", "detail"})
     */
    private $mimeType;


    public function getId (): ?int
    {
        return $this->id;
    }


    public function getMessage (): ?Message
    {
        return $this->message;
    }


    public function setMessage (?Message $message): self
    {
        $this->message = $message;

        return $this;
    }


    public function getFilename (): ?string
    {
        return $this->filename;
    }


    public function setFilename (?string $filename): self
    {
        $this->filename = $filename;

        return $this;
    }


    /**
     * @param mixed $filenameFile
     * @return mixed
     */
    public function setFilenameFile ($filenameFile): self
    {
        $this->filenameFile = $filenameFile;

        if ($filenameFile)
        {
            $this->fileSize  = filesize($filenameFile);
            $this->updatedAt = new \DateTime();
        }

        return $this;
    }


    public function getFilenameFile ()
    {
        return $this->filenameFile;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function setMimeType(?string $mimeType): self
    {
        $this->mimeType = $mimeType;

        return $this;
    }
}
