<?php

namespace App\Entity;

use App\Repository\CategorizeAnswersRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=CategorizeAnswersRepository::class)
 */
class CategorizeAnswers
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"categorize"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Categorize::class, inversedBy="categorizeAnswers")
     */
    private $categorize;

    /**
     * @ORM\ManyToOne(targetEntity=CategorizeOptions::class, inversedBy="categorizeAnswers", cascade={"persist"})
     * @Groups({"categorize"})
     */
    private $options;

    /**
     * @ORM\Column(type="boolean")
     * @Groups({"categorize"})
     */
    private $correct;


    public function __clone ()
    {
        $this->id = null;

        // clone categorize options
        $categorizeOptions = $this->getOptions();
        $this->options = clone $categorizeOptions;
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCategorize(): ?Categorize
    {
        return $this->categorize;
    }

    public function setCategorize(?Categorize $categorize): self
    {
        $this->categorize = $categorize;

        return $this;
    }

    public function getOptions(): ?CategorizeOptions
    {
        return $this->options;
    }

    public function setOptions(?CategorizeOptions $options): self
    {
        $this->options = $options;

        return $this;
    }

    public function isCorrect(): ?bool
    {
        return $this->correct;
    }

    public function setCorrect(bool $correct): self
    {
        $this->correct = $correct;

        return $this;
    }
}
