<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\PptRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=PptRepository::class)
 *
 * @ORM\Table(name="ppt")
 *
 * @Vich\Uploadable()
 */
class Ppt
{
    use Uplodeable;

    public const PDF_DIR = DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . 'packages' . DIRECTORY_SEPARATOR . 'ppt';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $ppt;

    /**
     * @Vich\UploadableField(mapping="ppt_package", fileNameProperty="ppt")
     *
     * var File $pptPackage
     */
    private $pptPackage;

    /**
     * @ORM\OneToOne(targetEntity=Chapter::class, inversedBy="ppt", cascade={"persist"})
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $chapter;

    /**
     * @ORM\Column(type="boolean")
     */
    private $is_downloadable = false;

    /**
     * @ORM\Column(type="smallint", length=3)
     */
    private $slides = 0;

    public function __clone()
    {
        $this->id = null;

        // clone file
        if ($this->getUploadsFolder()) {
            $this->cloneFile();
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPpt(): ?string
    {
        return $this->ppt;
    }

    public function setPpt(?string $ppt): self
    {
        $this->ppt = $ppt;

        return $this;
    }

    /**
     * @return File
     */
    public function getPptPackage()
    {
        return $this->pptPackage;
    }

    public function setPptPackage($pptPackage): void
    {
        $this->pptPackage = $pptPackage;
    }

    public function getChapter(): ?Chapter
    {
        return $this->chapter;
    }

    public function setChapter(Chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    public function getSlides(): ?int
    {
        return $this->slides;
    }

    public function setSlides(int $slides): self
    {
        $this->slides = $slides;

        return $this;
    }

    public function cloneFile()
    {
        $originalExtension = strtolower(pathinfo($this->getPpt(), PATHINFO_EXTENSION));
        $originalName = substr(pathinfo($this->getPpt(), PATHINFO_FILENAME), 14); // 14 => uniqid() length
        $fileName =
            \uniqid()
            . '_'
            . $originalName
            . '.' . $originalExtension;

        $source = $this->getUploadsFolder() . '/' . $this->getPpt();
        $destination = $this->getUploadsFolder() . '/' . $fileName;

        while (is_file($destination)) {
            $fileName =
                \uniqid()
                . '_'
                . $originalName
                . '.' . $originalExtension;

            $destination = $this->getUploadsFolder() . '/' . $fileName;
        }

        if (is_file($source)) {
            copy($source, $destination);
            $this->setPpt($fileName);
        }
    }

    /**
     * @Assert\Callback
     */
    public function validate(ExecutionContextInterface $context)
    {
        $acceptedMimeTypes = [
            'application/vnd.ms-powerpoint',        // .ppt
            'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
        ];

        if (!\in_array($this->pptPackage->getMimeType(), $acceptedMimeTypes)) {
            $context
                ->buildViolation('Wrong file type (PowerPoint - ' . $this->pptPackage->getMimeType() . ' - ' . getcwd() . self::PDF_DIR . '\\packages\\' . $this->pptPackage->getClientOriginalName() . ')')
                ->atPath('fileName')
                ->addViolation()
            ;
        }
    }

    public function getIsDownloadable(): ?bool
    {
        return !empty($this->is_downloadable);
    }

    public function setIsDownloadable(bool $is_downloadable): self
    {
        $this->is_downloadable = $is_downloadable;

        return $this;
    }
}
