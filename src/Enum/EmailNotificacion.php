<?php

namespace App\Enum;

interface EmailNotificacion
{
  const ITINERARY                       = 'itinerary';
  const ANNOUNCED                       = 'announced';
  const ASSIGNED_ANNOUNCEMENT           = 'assignedAnnouncement';
  const ANNOUNCEMENT_STARTED            = 'announcementStarted';
  const ANNOUNCEMENT_TO_FINISH          = 'announcementToFinish';
  const MISSING_COURSE_REQUIREMENTS     = 'missingCourseRequirements';
  const CERTIFICATION_READY             = 'certificationReady';
  const CERTIFICATION_PENDING_DOWNLOAD  = 'certificationPendingDownload';
  const ASSIGNED_ITINERARY              = 'assignedItinerary';
  const NEW_CHAT                        = 'newChat';
  const DIRECT_MESSAGE                  = 'directMessage';
  const APPROVED_HOMEWORK               = 'approvedHomework';
  const REJECTED_HOMEWORK               = 'rejectedHomework';
  const ASSIGNED_TOURNAMENT             = 'assignedTournament';
  const CLOSED_TOURNAMENT               = 'closedTournament';
  const TOURNAMENT_POINTS               = 'tournamentPoints';
  const NEWS_PAPER_ARCHIVE              = 'newspaperArchive';
  const ANNOUNCEMENT                    = 'announcement';
  const SUCCESS                         = 'success';
  const ERROR                           = 'error';
  const WARNING                         = 'warning';
  const INFO                            = 'info';
  const ARCHIVE                        = 'archive';
  const CHALLENGE                       = 'challenge';


  const EMAIL_NOTIFICATIONS = [
    self::ITINERARY,
    self::ANNOUNCED,
    self::ASSIGNED_ANNOUNCEMENT,
    self::ANNOUNCEMENT_STARTED,
    self::ANNOUNCEMENT_TO_FINISH,
    self::MISSING_COURSE_REQUIREMENTS,
    self::CERTIFICATION_READY,
    self::CERTIFICATION_PENDING_DOWNLOAD,
    self::ASSIGNED_ITINERARY,
    self::NEW_CHAT,
    self::DIRECT_MESSAGE,
    self::APPROVED_HOMEWORK,
    self::REJECTED_HOMEWORK,
    self::ASSIGNED_TOURNAMENT,
    self::CLOSED_TOURNAMENT,
    self::TOURNAMENT_POINTS,
    self::NEWS_PAPER_ARCHIVE,
    self::ANNOUNCEMENT,
    self::SUCCESS,
    self::ERROR,
    self::WARNING,
    self::INFO,
    self::ARCHIVE,
    self::CHALLENGE
  ];
}
