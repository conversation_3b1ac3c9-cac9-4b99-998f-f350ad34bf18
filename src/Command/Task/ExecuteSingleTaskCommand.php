<?php

declare(strict_types=1);

namespace App\Command\Task;

use App\Entity\Task;
use App\Service\TaskCron\TaskExecutorService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use <PERSON>ymfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

/**
 * Command to execute a specific task by its ID.
 * This command is used internally by ExecuteTaskCommand to execute tasks in separate processes.
 */
class ExecuteSingleTaskCommand extends Command
{
    private const MAX_TIMEOUT = 7200;

    protected static $defaultName = 'task:execute-single';
    protected static $defaultDescription = 'Execute a single task by ID';

    private EntityManagerInterface $em;
    private TaskExecutorService $taskExecutorService;
    private LoggerInterface $logger;

    public function __construct(
        EntityManagerInterface $em,
        TaskExecutorService $taskExecutorService,
        LoggerInterface $logger
    ) {
        $this->em = $em;
        $this->taskExecutorService = $taskExecutorService;
        $this->logger = $logger;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('taskId', InputArgument::REQUIRED, 'The ID of the task to execute');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        set_time_limit(self::MAX_TIMEOUT);
        $io = new SymfonyStyle($input, $output);
        $taskId = $input->getArgument('taskId');

        $task = $this->em->getRepository(Task::class)->find($taskId);

        if (!$task) {
            $io->error(\sprintf('Task with ID %s not found', $taskId));

            return Command::FAILURE;
        }

        try {
            $io->text(\sprintf('Executing task with ID %s', $taskId));

            // Execute the task
            $this->taskExecutorService->execute($task);

            $io->success(\sprintf('Task with ID %s executed successfully', $taskId));

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error(\sprintf('Error executing task with ID %s: %s', $taskId, $e->getMessage()));

            return Command::FAILURE;
        }
    }
}
