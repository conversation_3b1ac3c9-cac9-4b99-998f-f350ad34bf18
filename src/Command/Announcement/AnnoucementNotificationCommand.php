<?php

declare(strict_types=1);

namespace App\Command\Announcement;

use App\Service\Annoucement\CronJob\AnnouncementNotificationCronService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class AnnoucementNotificationCommand extends Command
{
    public function __construct(
        private readonly AnnouncementNotificationCronService $announcementNotificationCronService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('annoucement:notification:execute')
            ->setDescription('Revisa las notificaciones que están pendientes de enviar');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        try {
            $this->announcementNotificationCronService->execute();
            $io->success('Las notificaciones de la convocatoria han sido revisadas');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error($e->getMessage());

            return Command::FAILURE;
        }
    }
}
