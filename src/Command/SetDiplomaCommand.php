<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\TypeDiploma;
use App\Service\Diploma\DiplomaService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SetDiplomaCommand extends Command
{
    protected static $defaultName = 'app:set-diploma';

    public function __construct(
        private readonly DiplomaService $diplomaService,
        private readonly EntityManagerInterface $em
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setDescription('Set Preview of default diploma to client');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $typeDiploma = $this->em->getRepository(TypeDiploma::class)->findOneBy(['isMain' => 1, 'active' => 1]);

        if ($typeDiploma) {
            $this->diplomaService->generateDiplomaForPreview($typeDiploma);
        }

        return Command::SUCCESS;
    }
}
