<?php

declare(strict_types=1);

namespace App\Exception;

class TaskNotFoundException extends \Exception
{
    private int $statusCode;

    public function __construct(string $message = 'No se encontró la tarea especificada', int $code = 0)
    {
        parent::__construct($message, $code);
    }

    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    // Basic exception for when no pending tasks are found
}
