<?php

namespace App\Resources\Traits\Catalog;

use App\Entity\Catalog;
use App\Entity\CatalogTranslation;
use App\Resources\DataFixtureBase\Catalog\CatalogData;

trait CatalogTrait
{
    protected function getDefaultData(): array
    {
        $parameters = self::PARAMETERS_BASE;
        $parameters['fieldState'] = null;
        $parameters['setFields'] = array_merge( self::PARAMETERS_BASE['setFields'],[
            'component'=>'setComponent', 'relation'=>'setRelation','route'=>'setRoute',
            'service' => 'setService'
        ]);
        $parameters['parametersQuery'] = ['component'];

        return [
            'data' => CatalogData::DEFAULT_DATA,
            'parameters' => $parameters,
            'baseEntity' => Catalog::class,
            'translationEntity' => CatalogTranslation::class,
        ];
    }
}