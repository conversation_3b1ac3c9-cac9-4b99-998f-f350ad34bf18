<?php

namespace App\Resources\Traits\Catalog;

use App\Entity\ChapterType;
use App\Entity\ChapterTypeTranslation;
use App\Resources\DataFixtureBase\Course\ChapterTypeData;

trait ChapterTypeTrait
{
    protected function getDefaultData(): array
    {
        $parameters = self::PARAMETERS_BASE;
        $parameters['setFields'] = ['name'=>'setName','type'=>'setType','video'=>'setVideo','videoEn'=>'setVideoEn',
            'icon'=>'setIcon','playerurl'=>'setPlayerurl','percentage_completed'=>'setPercentageCompleted',
            'code'=>'setCode', 'description' => 'setDescription', 'normalized'=>'setNormalized'];
        $parameters['parametersQuery'] = ['id'];
        $parameters['fileTrans'] = 'chapters';

        return [
            'data' => ChapterTypeData::DEFAULT_DATA,
            'parameters' => $parameters,
            'baseEntity' => ChapterType::class,
            'translationEntity' => ChapterTypeTranslation::class,
        ];
    }

}