<?php

namespace App\Resources\Traits\Catalog;

use App\Entity\TypeCourse;
use App\Entity\TypeCourseTranslation;
use App\Resources\DataFixtureBase\Course\TypeCourseData;

trait TypeCourseTrait
{
    protected function getDefaultData(): array
    {
        $parameters = self::PARAMETERS_BASE;
        $parameters['setFields'] = array_merge(
            $parameters['setFields'],
            ['code' => 'setCode', 'denomination' => 'setDenomination']
        );

        return [
            'data' => TypeCourseData::DEFAULT_DATA,
            'parameters' => $parameters,
            'baseEntity' => TypeCourse::class,
            'translationEntity' => TypeCourseTranslation::class,
        ];
    }
}