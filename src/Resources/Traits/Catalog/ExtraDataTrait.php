<?php

declare(strict_types=1);

namespace App\Resources\Traits\Catalog;

use App\Entity\ExtraData;
use App\Resources\DataFixtureBase\Announcement\ExtraDataData;

trait ExtraDataTrait
{
    protected function getDefaultData(): array
    {
        $parameters = self::PARAMETERS_BASE;

        $parameters['setFields'] = [
            'name' => 'setName',
            'description' => 'setDescription',
            'active' => 'setActive',
        ];

        return [
            'data' => ExtraDataData::DEFAULT_DATA,
            'parameters' => $parameters,
            'baseEntity' => ExtraData::class,
            'translationEntity' => null,
        ];
    }
}
