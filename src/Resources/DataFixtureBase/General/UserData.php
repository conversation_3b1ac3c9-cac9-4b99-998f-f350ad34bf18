<?php

declare(strict_types=1);

namespace App\Resources\DataFixtureBase\General;

class UserData
{
    public const EMAIL = '<EMAIL>';
    

    public const DEFAULT_DATA = [
        [
            'id' => 1,
            'firstName' => 'Soporte',
            'lastName' => 'Gestionet',
            'roles' => ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN'],
            'email' => self::EMAIL,
            'password' => '34syl34rn1ng',
            'active' => true,
            'open' => true,
            'locale' => 'es',
            'validated' => true,
            'meta' => [],
            'starteam' => false,
            'custom_filters' => [],
            'remote_roles' => [],
        ],
        [
            'id' => 2,
            'firstName' => 'Inspector',
            'lastName' => 'Inspector',
            'roles' => ['ROLE_USER', 'ROLE_INSPECTOR'],
            'email' => '<EMAIL>',
            'password' => '34syl34rn1ng',
            'active' => true,
            'open' => true,
            'locale' => 'es',
            'validated' => true,
            'meta' => [],
            'starteam' => false,
            'custom_filters' => [],
            'remote_roles' => [],
        ],
    ];
}
