<?php

namespace App\Resources\DataFixtureBase\Announcement;

use App\Entity\AnnouncementConfigurationType;
use App\Enum\AnnouncementConfigurationType as EnumAnnouncementConfigurationType;
use App\Enum\ConfigurationClientAnnouncement as EnumConfigurationClientAnnouncement;

class AnnouncementConfigurationTypeData
{
    const DEFAULT_DATA = [
        [
            'id' => 1,
            'name' => 'announcement_configuration_type.temporalizacion.name',
            'description' => 'announcement_configuration_type.temporalizacion.description',
            'active' => false,
            'image' => 'temporalization.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::TEMPORALIZATION,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_TEMPORALIZATION_CODE
        ],
        [
            'id' => 2,
            'name' => 'announcement_configuration_type.curso_bonificado.name',
            'description' => 'announcement_configuration_type.curso_bonificado.description',
            'active' => false,
            'image' => 'bonification.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::BONIFICATION,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_BONOS_CODE
        ],
        [
            'id' => 3,
            'name' => 'announcement_configuration_type.chat.name',
            'description' => 'announcement_configuration_type.chat.description',
            'active' => true,
            'image' => 'chat.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::COMMUNICATION,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_CHAT_CODE
        ],
        [
            'id' => 4,
            'name' => 'announcement_configuration_type.notificaciones.name',
            'description' => 'announcement_configuration_type.notificaciones.description',
            'active' => true,
            'image' => 'notification.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::COMMUNICATION,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_NOTIFICATION_CODE
        ],
        [
            'id' => 5,
            'name' => 'announcement_configuration_type.mensajeria.name',
            'description' => 'announcement_configuration_type.mensajeria.description',
            'active' => false,
            'image' => 'message.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::COMMUNICATION,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_MESSAGE_CODE
        ],
        [
            'id' => 6,
            'name' => 'announcement_configuration_type.foros.name',
            'description' => 'announcement_configuration_type.foros.description',
            'active' => false,
            'image' => 'foro.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::COMMUNICATION,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_FORUM_CODE
        ],
        [
            'id' => 7,
            'name' => 'announcement_configuration_type.diploma.name',
            'description' => 'announcement_configuration_type.diploma.description',
            'active' => true,
            'image' => 'diploma.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::CERTIFICATE,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_CERTIFICATE_CODE
        ],
        [
            'id' => 8,
            'name' => 'announcement_configuration_type.tutor_alerts.name',
            'description' => 'announcement_configuration_type.tutor_alerts.description',
            'active' => true,
            'image' => 'alert.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::ALERT,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_ALERTS_CODE
        ],
        [
            'id' => 9,
            'name' => 'announcement_configuration_type.encuesta_satisfaccion.name',
            'description' => 'announcement_configuration_type.encuesta_satisfaccion.description',
            'active' => true,
            'image' => 'survey.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::SURVEY,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_SURVEY_CODE
        ],
        [
            'id' => 10,
            'name' => 'announcement_configuration_type.finalizar_convocatoria.name',
            'description' => 'announcement_configuration_type.finalizar_convocatoria.description',
            'active' => true,
            'image' => 'access_content.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::ACCESS_CONTENT,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_COURSE_WILL_REMAIN_ACTIVE_CODE
        ],
        [
            'id' => 11,
            'name' => 'announcement_configuration_type.firma_digital.name',
            'description' => 'announcement_configuration_type.firma_digital.description',
            'active' => true,
            'image' => 'signature.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::DIGITAL_SIGNATURE,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_DIGITAL_SIGNATURE_CODE
        ],
        [
            'id' => 12,
            'name' => 'announcement_configuration_type.gestion_costes.name',
            'description' => 'announcement_configuration_type.gestion_costes.description',
            'active' => false,
            'image' => 'cost.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::COST,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_COSTES_CODE
        ],
        [
            'id' => AnnouncementConfigurationType::ID_ENABLE_EMAIL_NOTIFICATION_ON_ANNOUNCEMENT,
            'name' => 'announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.name',
            'description' => 'announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.description',
            'active' => true,
            'image' => 'alert.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::NOTIFICATION_ACTIVATE_ANNOUNCEMENT,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_EMAIL_NOTIFICATION_CODE
        ],
        [
            'id' => AnnouncementConfigurationType::ID_ENABLE_NOTIFICATION_ON_ANNOUNCEMENT,
            'name' => 'announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.name',
            'description' => 'announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.description',
            'active' => true,
            'image' => 'alert.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::NOTIFICATION_ACTIVATE_ANNOUNCEMENT,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_NOTIFICATION_CAMPUS_CODE
        ],
        [
            'id' => AnnouncementConfigurationType::ID_INCLUDE_OBJ_CONTENT_CERTIFICATE,
            'name' => 'announcement_configuration_type.objetivos_contenidos.name',
            'description' => 'announcement_configuration_type.objetivos_contenidos.description',
            'active' => true,
            'image' => 'diploma.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::CERTIFICATE,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_OBJETIVES_AND_CONTENTS_CERTIFICATE_CODE
        ],
        [
            'id' => AnnouncementConfigurationType::ID_INCLUDE_DNI_IN_CERTIFICATE,
            'name' => 'announcement_configuration_type.dni.name',
            'description' => 'announcement_configuration_type.dni.description',
            'active' => true,
            'image' => 'diploma.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::CERTIFICATE,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_DNI_CERTIFICATE_CODE
        ],
        [
            'id' => AnnouncementConfigurationType::ID_TEMPLATE_XLSX_IBEROSTAR,
            'name' => 'announcement_configuration_type.template_excel.name',
            'description' => 'announcement_configuration_type.template_excel.description',
            'active' => false,
            'image' => 'diploma.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::CONFIGURATION_IBEROSTAR,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_TEMPLATE_XLSX_IBEROSTAR_CODE
        ],
        [
            'id' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_EXPORT_REPORT_ZIP,
            'name' => 'announcement_configuration_type.report_zip.name',
            'description' => 'announcement_configuration_type.report_zip.description',
            'active' => false,
            'image' => 'diploma.svg',
            'nameConfiguration' => EnumConfigurationClientAnnouncement::REPORT,
            'code' => EnumAnnouncementConfigurationType::ANNOUNCEMENT_EXPORT_REPORT_ZIP_CODE
        ],
    ];

}