<?php

namespace App\Resources\DataFixtureBase\Announcement;

use App\Enum\AnnouncementStepCreation as EnumAnnouncementStepCreation;
use App\Enum\TypeCourse as EnumTypeCourse;

class AnnouncementStepCreationData
{
    const DEFAULT_DATA = [
        [
            'id' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COURSE,
            'name' => 'ANNOUNCEMENT_COURSE',
            'description' => 'announcement_step_creation.ANNOUNCEMENT_COURSE.description',
            'active' => true,
            'position' => 1,
            'extra' => [
                'component' => 'AnnouncementCourse'
            ]
        ],
        [
            'id' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GENERAL_INFO,
            'name' => 'ANNOUNCEMENT_GENERAL_INFO',
            'description' => 'announcement_step_creation.ANNOUNCEMENT_GENERAL_INFO.description',
            'active' => true,
            'position' => 2,
            'extra' => [
                EnumTypeCourse::INTERN => ['component' => 'AnnouncementGeneralInfo'],
                EnumTypeCourse::EXTERN => ['component' => 'AnnouncementGeneralInfoExtern'],
            ]
        ],
        [
            'id' => EnumAnnouncementStepCreation::ANNOUNCEMENT_BONUS,
            'name' => 'ANNOUNCEMENT_BONUS',
            'description' => 'announcement_step_creation.ANNOUNCEMENT_BONUS.description',
            'active' => true,
            'position' => 3,
            'extra' => [
                'component' => 'AnnouncementBonus'
            ]
        ],
        [
            'id' => EnumAnnouncementStepCreation::ANNOUNCEMENT_STUDENTS,
            'name' => 'ANNOUNCEMENT_STUDENTS',
            'description' => 'announcement_step_creation.ANNOUNCEMENT_STUDENTS.description',
            'active' => true,
            'position' => 4,
            'extra' => [
                'component' => 'AnnouncementStudents'
            ]
        ],
        [
            'id' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GROUPS,
            'name' => 'ANNOUNCEMENT_GROUPS',
            'description' => 'announcement_step_creation.ANNOUNCEMENT_GROUPS.description',
            'active' => true,
            'position' => 5,
            'extra' => [
                'component' => 'AnnouncementGroups'
            ]
        ],
        [
            'id' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COMMUNICATION,
            'name' => 'ANNOUNCEMENT_COMMUNICATION',
            'description' => 'announcement_step_creation.ANNOUNCEMENT_COMMUNICATION.description',
            'active' => true,
            'position' => 6,
            'extra' => [
                'component' => 'AnnouncementCommunication'
            ]
        ],
        [
            'id' => EnumAnnouncementStepCreation::ANNOUNCEMENT_SURVEY,
            'name' => 'ANNOUNCEMENT_SURVEY',
            'description' => 'announcement_step_creation.ANNOUNCEMENT_SURVEY.description',
            'active' => true,
            'position' => 7,
            'extra' => [
                'component' => 'AnnouncementSurvey'
            ]
        ],
        [
            'id' => EnumAnnouncementStepCreation::ANNOUNCEMENT_CERTIFICATE,
            'name' => 'ANNOUNCEMENT_CERTIFICATE',
            'description' => 'announcement_step_creation.ANNOUNCEMENT_CERTIFICATE.description',
            'active' => true,
            'position' => 8,
            'extra' => [
                'component' => 'AnnouncementCertificate'
            ]
        ],
        [
            'id' => EnumAnnouncementStepCreation::ANNOUNCEMENT_ALERTS,
            'name' => 'ANNOUNCEMENT_ALERTS',
            'description' => 'announcement_step_creation.ANNOUNCEMENT_ALERTS.description',
            'active' => true,
            'position' => 9,
            'extra' => [
                'component' => 'AnnouncementAlerts'
            ]
        ],
    ];

}