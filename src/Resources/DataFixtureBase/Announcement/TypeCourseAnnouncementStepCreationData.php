<?php

namespace App\Resources\DataFixtureBase\Announcement;

use App\Entity\TypeCourse;
use App\Enum\AnnouncementConfigurationType;
use App\Enum\AnnouncementStepCreation as EnumAnnouncementStepCreation;

class TypeCourseAnnouncementStepCreationData
{

    const DEFAULT_DATA = [
        [
            'id' => 1,
            'name' => 'type_course_announcement_step_creation.seleccionar_curso.name',
            'description' => 'type_course_announcement_step_creation.seleccionar_curso.description',
            'active' => true,
            'isRequired' => true, // Cuando esta en true, en ningun momento desactivar
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COURSE,
            'position' => 1,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION,
            'extra' => [],
            'configurations' => []
        ],
        [
            'id' => 2,
            'name' => 'type_course_announcement_step_creation.convocatoria.name',
            'description' => 'type_course_announcement_step_creation.convocatoria.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GENERAL_INFO,
            'position' => 2,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_COURSE_WILL_REMAIN_ACTIVE,
                AnnouncementConfigurationType::ANNOUNCEMENT_TEMPORALIZATION,
                AnnouncementConfigurationType::ANNOUNCEMENT_COSTES,
            ]
        ],
        [
            'id' => 3,
            'name' => 'type_course_announcement_step_creation.bonificacion.name',
            'description' => 'type_course_announcement_step_creation.bonificacion.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_BONUS,
            'position' => 3,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_BONOS
            ]
        ],
        [
            'id' => 4,
            'name' => 'type_course_announcement_step_creation.alumnado.name',
            'description' => 'type_course_announcement_step_creation.alumnado.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_STUDENTS,
            'position' => 4,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION,
            'extra' => [],
            'configurations' => []
        ],
        [
            'id' => 5,
            'name' => 'type_course_announcement_step_creation.grupos.name',
            'description' => 'type_course_announcement_step_creation.grupos.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GROUPS,
            'position' => 5,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION,
            'extra' => [],
            'configurations' => []
        ],

        [
            'id' => 6,
            'name' => 'type_course_announcement_step_creation.comunicacion.name',
            'description' => 'type_course_announcement_step_creation.comunicacion.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COMMUNICATION,
            'position' => 6,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_CHAT,
                AnnouncementConfigurationType::ANNOUNCEMENT_NOTIFICATION,
                AnnouncementConfigurationType::ANNOUNCEMENT_MESSAGE,
                AnnouncementConfigurationType::ANNOUNCEMENT_FORUM,
            ]
        ],
        [
            'id' => 7,
            'name' => 'type_course_announcement_step_creation.encuesta.name',
            'description' => 'type_course_announcement_step_creation.encuesta.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_SURVEY,
            'position' => 7,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_SURVEY
            ]

        ],
        [
            'id' => 8,
            'name' => 'type_course_announcement_step_creation.diploma.name',
            'description' => 'type_course_announcement_step_creation.diploma.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_CERTIFICATE,
            'position' => 8,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_OBJETIVES_AND_CONTENTS_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_DNI_CERTIFICATE,
            ]

        ],
        [
            'id' => 9,
            'name' => 'type_course_announcement_step_creation.alertas.name',
            'description' => 'type_course_announcement_step_creation.alertas.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_ALERTS,
            'position' => 9,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_ALERTS
            ]
        ],


        [
            'id' => 10,
            'name' => 'type_course_announcement_step_creation.seleccionar_curso.name',
            'description' => 'type_course_announcement_step_creation.seleccionar_curso.description',
            'active' => true,
            'isRequired' => true, // Cuando esta en true, en ningun momento desactivar
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COURSE,
            'position' => 1,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 11,
            'name' => 'type_course_announcement_step_creation.convocatoria.name',
            'description' => 'type_course_announcement_step_creation.convocatoria.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GENERAL_INFO,
            'position' => 2,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_COURSE_WILL_REMAIN_ACTIVE,
                AnnouncementConfigurationType::ANNOUNCEMENT_DIGITAL_SIGNATURE,
                AnnouncementConfigurationType::ANNOUNCEMENT_COSTES
            ]
        ],
        [
            'id' => 12,
            'name' => 'type_course_announcement_step_creation.bonificacion.name',
            'description' => 'type_course_announcement_step_creation.bonificacion.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_BONUS,
            'position' => 3,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_BONOS
            ]

        ],
        [
            'id' => 13,
            'name' => 'type_course_announcement_step_creation.alumnado.name',
            'description' => 'type_course_announcement_step_creation.alumnado.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_STUDENTS,
            'position' => 4,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 14,
            'name' => 'type_course_announcement_step_creation.grupos.name',
            'description' => 'type_course_announcement_step_creation.grupos.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GROUPS,
            'position' => 5,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL,
            'extra' => [],
            'configurations' => []

        ],

        [
            'id' => 15,
            'name' => 'type_course_announcement_step_creation.comunicacion.name',
            'description' => 'type_course_announcement_step_creation.comunicacion.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COMMUNICATION,
            'position' => 6,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 16,
            'name' => 'type_course_announcement_step_creation.encuesta.name',
            'description' => 'type_course_announcement_step_creation.encuesta.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_SURVEY,
            'position' => 7,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_SURVEY
            ]

        ],
        [
            'id' => 17,
            'name' => 'type_course_announcement_step_creation.diploma.name',
            'description' => 'type_course_announcement_step_creation.diploma.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_CERTIFICATE,
            'position' => 8,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_OBJETIVES_AND_CONTENTS_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_DNI_CERTIFICATE,
            ]

        ],
        [
            'id' => 18,
            'name' => 'type_course_announcement_step_creation.alertas.name',
            'description' => 'type_course_announcement_step_creation.alertas.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_ALERTS,
            'position' => 9,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_ALERTS
            ]
        ],

        [
            'id' => 19,
            'name' => 'type_course_announcement_step_creation.seleccionar_curso.name',
            'description' => 'type_course_announcement_step_creation.seleccionar_curso.description',
            'active' => true,
            'isRequired' => true, // Cuando esta en true, en ningun momento desactivar
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COURSE,
            'position' => 1,
            'typeCourse' => TypeCourse::TYPE_MIXTO,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 20,
            'name' => 'type_course_announcement_step_creation.convocatoria.name',
            'description' => 'type_course_announcement_step_creation.convocatoria.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GENERAL_INFO,
            'position' => 2,
            'typeCourse' => TypeCourse::TYPE_MIXTO,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_COURSE_WILL_REMAIN_ACTIVE,
                AnnouncementConfigurationType::ANNOUNCEMENT_TEMPORALIZATION, 
                AnnouncementConfigurationType::ANNOUNCEMENT_DIGITAL_SIGNATURE,
                AnnouncementConfigurationType::ANNOUNCEMENT_COSTES
            ]

        ],
        [
            'id' => 21,
            'name' => 'type_course_announcement_step_creation.bonificacion.name',
            'description' => 'type_course_announcement_step_creation.bonificacion.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_BONUS,
            'position' => 3,
            'typeCourse' => TypeCourse::TYPE_MIXTO,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_BONOS
            ]

        ],
        [
            'id' => 22,
            'name' => 'type_course_announcement_step_creation.alumnado.name',
            'description' => 'type_course_announcement_step_creation.alumnado.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_STUDENTS,
            'position' => 4,
            'typeCourse' => TypeCourse::TYPE_MIXTO,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 23,
            'name' => 'type_course_announcement_step_creation.grupos.name',
            'description' => 'type_course_announcement_step_creation.grupos.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GROUPS,
            'position' => 5,
            'typeCourse' => TypeCourse::TYPE_MIXTO,
            'extra' => [],
            'configurations' => []

        ],

        [
            'id' => 24,
            'name' => 'type_course_announcement_step_creation.comunicacion.name',
            'description' => 'type_course_announcement_step_creation.comunicacion.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COMMUNICATION,
            'position' => 6,
            'typeCourse' => TypeCourse::TYPE_MIXTO,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_CHAT,
                AnnouncementConfigurationType::ANNOUNCEMENT_NOTIFICATION,
                AnnouncementConfigurationType::ANNOUNCEMENT_MESSAGE,
                AnnouncementConfigurationType::ANNOUNCEMENT_FORUM,
            ]
        ],
        [
            'id' => 25,
            'name' => 'type_course_announcement_step_creation.encuesta.name',
            'description' => 'type_course_announcement_step_creation.encuesta.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_SURVEY,
            'position' => 7,
            'typeCourse' => TypeCourse::TYPE_MIXTO,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_SURVEY
            ]

        ],
        [
            'id' => 26,
            'name' => 'type_course_announcement_step_creation.diploma.name',
            'description' => 'type_course_announcement_step_creation.diploma.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_CERTIFICATE,
            'position' => 8,
            'typeCourse' => TypeCourse::TYPE_MIXTO,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_OBJETIVES_AND_CONTENTS_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_DNI_CERTIFICATE,
            ]

        ],
        [
            'id' => 27,
            'name' => 'type_course_announcement_step_creation.alertas.name',
            'description' => 'type_course_announcement_step_creation.alertas.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_ALERTS,
            'position' => 9,
            'typeCourse' => TypeCourse::TYPE_MIXTO,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_ALERTS
            ]

        ],

        [
            'id' => 28,
            'name' => 'type_course_announcement_step_creation.seleccionar_curso.name',
            'description' => 'type_course_announcement_step_creation.seleccionar_curso.description',
            'active' => true,
            'isRequired' => true, // Cuando esta en true, en ningun momento desactivar
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COURSE,
            'position' => 1,
            'typeCourse' =>  TypeCourse::TYPE_AULA_VIRTUAL,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 29,
            'name' => 'type_course_announcement_step_creation.convocatoria.name',
            'description' => 'type_course_announcement_step_creation.convocatoria.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GENERAL_INFO,
            'position' => 2,
            'typeCourse' =>  TypeCourse::TYPE_AULA_VIRTUAL,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_COSTES
            ]

        ],
        [
            'id' => 30,
            'name' => 'type_course_announcement_step_creation.bonificacion.name',
            'description' => 'type_course_announcement_step_creation.bonificacion.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_BONUS,
            'position' => 3,
            'typeCourse' =>  TypeCourse::TYPE_AULA_VIRTUAL,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_BONOS
            ]

        ],
        [
            'id' => 31,
            'name' => 'type_course_announcement_step_creation.alumnado.name',
            'description' => 'type_course_announcement_step_creation.alumnado.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_STUDENTS,
            'position' => 4,
            'typeCourse' =>  TypeCourse::TYPE_AULA_VIRTUAL,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 32,
            'name' => 'type_course_announcement_step_creation.grupos.name',
            'description' => 'type_course_announcement_step_creation.grupos.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GROUPS,
            'position' => 5,
            'typeCourse' =>  TypeCourse::TYPE_AULA_VIRTUAL,
            'extra' => [],
            'configurations' => []

        ],

        [
            'id' => 33,
            'name' => 'type_course_announcement_step_creation.comunicacion.name',
            'description' => 'type_course_announcement_step_creation.comunicacion.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COMMUNICATION,
            'position' => 6,
            'typeCourse' =>  TypeCourse::TYPE_AULA_VIRTUAL,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_CHAT,
                AnnouncementConfigurationType::ANNOUNCEMENT_NOTIFICATION,
                AnnouncementConfigurationType::ANNOUNCEMENT_MESSAGE,
                AnnouncementConfigurationType::ANNOUNCEMENT_FORUM,
            ]

        ],
        [
            'id' => 34,
            'name' => 'type_course_announcement_step_creation.encuesta.name',
            'description' => 'type_course_announcement_step_creation.encuesta.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_SURVEY,
            'position' => 7,
            'typeCourse' =>  TypeCourse::TYPE_AULA_VIRTUAL,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_SURVEY
            ]

        ],
        [
            'id' => 35,
            'name' => 'type_course_announcement_step_creation.diploma.name',
            'description' => 'type_course_announcement_step_creation.diploma.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_CERTIFICATE,
            'position' => 8,
            'typeCourse' =>  TypeCourse::TYPE_AULA_VIRTUAL,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_OBJETIVES_AND_CONTENTS_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_DNI_CERTIFICATE,
            ]

        ],
        [
            'id' => 36,
            'name' => 'type_course_announcement_step_creation.alertas.name',
            'description' => 'type_course_announcement_step_creation.alertas.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_ALERTS,
            'position' => 9,
            'typeCourse' =>  TypeCourse::TYPE_AULA_VIRTUAL,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_ALERTS
            ]

        ],
        [
            'id' => 37,
            'name' => 'type_course_announcement_step_creation.seleccionar_curso.name',
            'description' => 'type_course_announcement_step_creation.seleccionar_curso.description',
            'active' => false,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COURSE,
            'position' => 1,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION_EXTERN,
            'extra' => [],
            'configurations' => []
        ],
        [
            'id' => 38,
            'name' => 'type_course_announcement_step_creation.convocatoria.name',
            'description' => 'type_course_announcement_step_creation.convocatoria.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GENERAL_INFO,
            'position' => 2,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION_EXTERN,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_COURSE_WILL_REMAIN_ACTIVE,
                AnnouncementConfigurationType::ANNOUNCEMENT_TEMPORALIZATION,
                AnnouncementConfigurationType::ANNOUNCEMENT_COSTES,
            ]
        ],
        [
            'id' => 39,
            'name' => 'type_course_announcement_step_creation.bonificacion.name',
            'description' => 'type_course_announcement_step_creation.bonificacion.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_BONUS,
            'position' => 3,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION_EXTERN,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_BONOS
            ]
        ],
        [
            'id' => 40,
            'name' => 'type_course_announcement_step_creation.alumnado.name',
            'description' => 'type_course_announcement_step_creation.alumnado.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_STUDENTS,
            'position' => 4,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION_EXTERN,
            'extra' => [],
            'configurations' => []
        ],
        [
            'id' => 41,
            'name' => 'type_course_announcement_step_creation.grupos.name',
            'description' => 'type_course_announcement_step_creation.grupos.description',
            'active' => false,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GROUPS,
            'position' => 5,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION_EXTERN,
            'extra' => [],
            'configurations' => []
        ],

        [
            'id' => 42,
            'name' => 'type_course_announcement_step_creation.comunicacion.name',
            'description' => 'type_course_announcement_step_creation.comunicacion.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COMMUNICATION,
            'position' => 6,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION_EXTERN,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_CHAT,
                AnnouncementConfigurationType::ANNOUNCEMENT_NOTIFICATION,
                AnnouncementConfigurationType::ANNOUNCEMENT_MESSAGE,
                AnnouncementConfigurationType::ANNOUNCEMENT_FORUM,
            ]
        ],
        [
            'id' => 43,
            'name' => 'type_course_announcement_step_creation.encuesta.name',
            'description' => 'type_course_announcement_step_creation.encuesta.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_SURVEY,
            'position' => 7,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION_EXTERN,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_SURVEY
            ]

        ],
        [
            'id' => 44,
            'name' => 'type_course_announcement_step_creation.diploma.name',
            'description' => 'type_course_announcement_step_creation.diploma.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_CERTIFICATE,
            'position' => 8,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION_EXTERN,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_OBJETIVES_AND_CONTENTS_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_DNI_CERTIFICATE,
            ]

        ],
        [
            'id' => 45,
            'name' => 'type_course_announcement_step_creation.alertas.name',
            'description' => 'type_course_announcement_step_creation.alertas.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_ALERTS,
            'position' => 9,
            'typeCourse' => TypeCourse::TYPE_TELEFORMACION_EXTERN,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_ALERTS
            ]
        ],
        [
            'id' => 46,
            'name' => 'type_course_announcement_step_creation.seleccionar_curso.name',
            'description' => 'type_course_announcement_step_creation.seleccionar_curso.description',
            'active' => false,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COURSE,
            'position' => 1,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL_EXTERN,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 47,
            'name' => 'type_course_announcement_step_creation.convocatoria.name',
            'description' => 'type_course_announcement_step_creation.convocatoria.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GENERAL_INFO,
            'position' => 2,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL_EXTERN,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_COURSE_WILL_REMAIN_ACTIVE,
                AnnouncementConfigurationType::ANNOUNCEMENT_DIGITAL_SIGNATURE,
                AnnouncementConfigurationType::ANNOUNCEMENT_COSTES
            ]
        ],
        [
            'id' => 48,
            'name' => 'type_course_announcement_step_creation.bonificacion.name',
            'description' => 'type_course_announcement_step_creation.bonificacion.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_BONUS,
            'position' => 3,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL_EXTERN,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_BONOS
            ]

        ],
        [
            'id' => 49,
            'name' => 'type_course_announcement_step_creation.alumnado.name',
            'description' => 'type_course_announcement_step_creation.alumnado.description',
            'active' => true,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_STUDENTS,
            'position' => 4,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL_EXTERN,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 50,
            'name' => 'type_course_announcement_step_creation.grupos.name',
            'description' => 'type_course_announcement_step_creation.grupos.description',
            'active' => false,
            'isRequired' => true,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GROUPS,
            'position' => 5,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL_EXTERN,
            'extra' => [],
            'configurations' => []

        ],

        [
            'id' => 51,
            'name' => 'type_course_announcement_step_creation.comunicacion.name',
            'description' => 'type_course_announcement_step_creation.comunicacion.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_COMMUNICATION,
            'position' => 6,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL_EXTERN,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 52,
            'name' => 'type_course_announcement_step_creation.encuesta.name',
            'description' => 'type_course_announcement_step_creation.encuesta.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_SURVEY,
            'position' => 7,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL_EXTERN,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_SURVEY
            ]

        ],
        [
            'id' => 53,
            'name' => 'type_course_announcement_step_creation.diploma.name',
            'description' => 'type_course_announcement_step_creation.diploma.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_CERTIFICATE,
            'position' => 8,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL_EXTERN,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_OBJETIVES_AND_CONTENTS_CERTIFICATE,
                AnnouncementConfigurationType::ANNOUNCEMENT_DNI_CERTIFICATE,
            ]

        ],
        [
            'id' => 54,
            'name' => 'type_course_announcement_step_creation.alertas.name',
            'description' => 'type_course_announcement_step_creation.alertas.description',
            'active' => false,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_ALERTS,
            'position' => 9,
            'typeCourse' => TypeCourse::TYPE_PRESENCIAL_EXTERN,
            'extra' => [],
            'configurations' => [
                AnnouncementConfigurationType::ANNOUNCEMENT_ALERTS
            ]
        ],
        [
            'id' => 55,
            'name' => 'type_course_announcement_step_creation.convocatoria.name',
            'description' => 'type_course_announcement_step_creation.convocatoria.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GENERAL_INFO,
            'position' => 1,
            'typeCourse' =>  TypeCourse::TYPE_MIXTO_EXTERN,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 56,
            'name' => 'type_course_announcement_step_creation.alumnado.name',
            'description' => 'type_course_announcement_step_creation.alumnado.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_STUDENTS,
            'position' => 2,
            'typeCourse' =>  TypeCourse::TYPE_MIXTO_EXTERN,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 57,
            'name' => 'type_course_announcement_step_creation.convocatoria.name',
            'description' => 'type_course_announcement_step_creation.convocatoria.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_GENERAL_INFO,
            'position' => 1,
            'typeCourse' =>  TypeCourse::TYPE_AULA_VIRTUAL_EXTERN,
            'extra' => [],
            'configurations' => []

        ],
        [
            'id' => 58,
            'name' => 'type_course_announcement_step_creation.alumnado.name',
            'description' => 'type_course_announcement_step_creation.alumnado.description',
            'active' => true,
            'isRequired' => false,
            'step' => EnumAnnouncementStepCreation::ANNOUNCEMENT_STUDENTS,
            'position' => 2,
            'typeCourse' =>  TypeCourse::TYPE_AULA_VIRTUAL_EXTERN,
            'extra' => [],
            'configurations' => []

        ],

    ];

}