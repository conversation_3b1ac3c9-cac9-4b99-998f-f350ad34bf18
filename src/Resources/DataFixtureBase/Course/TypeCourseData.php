<?php

namespace App\Resources\DataFixtureBase\Course;

use App\Enum\TypeCourse as EnumTypeCourse;

class TypeCourseData
{

    const DEFAULT_DATA = [
        [
            'id' => 1,
            'name' => 'type_course.teleformacion.name',
            'description' => 'type_course.teleformacion.description',
            'active' => true,
            'code' => 'online',
            'denomination' => EnumTypeCourse::INTERN

        ],
        [
            'id' => 2,
            'name' => 'type_course.presencial.name',
            'description' => 'type_course.presencial.description',
            'active' => false,
            'code' => 'on_site',
            'denomination' => EnumTypeCourse::INTERN
        ],
        [
            'id' => 3,
            'name' => 'type_course.mixto.name',
            'description' => 'type_course.mixto.description',
            'active' => false,
            'code' => 'mixed',
            'denomination' => EnumTypeCourse::INTERN
        ],
        [
            'id' => 4,
            'name' => 'type_course.aula_virtual.name',
            'description' => 'type_course.aula_virtual.description',
            'active' => false,
            'code' => 'virtual_classroom',
            'denomination' => EnumTypeCourse::INTERN
        ],

        [
            'id' => 5,
            'name' => 'type_course.teleformacion.name',
            'description' => 'type_course.teleformacion.description',
            'active' => false,
            'code' => 'online',
            'denomination' => EnumTypeCourse::EXTERN

        ],
        [
            'id' => 6,
            'name' => 'type_course.presencial.name',
            'description' => 'type_course.presencial.description',
            'active' => false,
            'code' => 'on_site',
            'denomination' => EnumTypeCourse::EXTERN
        ],
        [
            'id' => 7,
            'name' => 'type_course.mixto.name',
            'description' => 'type_course.mixto.description',
            'active' => false,
            'code' => 'mixed',
            'denomination' => EnumTypeCourse::EXTERN
        ],
        [
            'id' => 8,
            'name' => 'type_course.aula_virtual.name',
            'description' => 'type_course.aula_virtual.description',
            'active' => false,
            'code' => 'virtual_classroom',
            'denomination' => EnumTypeCourse::EXTERN
        ],

    ];

}