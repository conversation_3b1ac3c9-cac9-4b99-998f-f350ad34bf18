# See dos how to configure the bundle: https://symfony.com/doc/current/bundles/LiipImagineBundle/basic-usage.html
liip_imagine:
    # valid drivers options include "gd" or "gmagick" or "imagick"
    driver: "gd"
    twig:
        mode: lazy
    resolvers:
        default:
            web_path: ~

    filter_sets:
        cache: ~
        thumb:
            quality: 90
            filters:
                thumbnail: { size: [450, 450], mode: inset }

        small:
            quality: 100
            filters:
                thumbnail: { size: [ 450, 450 ], mode: inset }

        medium:
            quality: 100
            filters:
                thumbnail: { size: [ 650, 650 ], mode: inset }

        large:
            quality: 100
            filters:
                thumbnail: { size: [ 1000, 1000 ], mode: inset }
