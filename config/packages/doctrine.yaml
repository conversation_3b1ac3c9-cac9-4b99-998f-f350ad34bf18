doctrine:
    dbal:
        url: '%env(resolve:DATABASE_URL)%'

        # IMPORTANT: You MUST configure your server version,
        # either here or in the DATABASE_URL env var (see .env file)
        #server_version: '5.7'
    orm:
        auto_generate_proxy_classes: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        auto_mapping: true
        mappings:
            App:
                is_bundle: false
                type: annotation
                dir: '%kernel.project_dir%/src/Entity'
                prefix: 'App\Entity'
                alias: App
        filters:
            softdeleteable:
                class: Gedmo\SoftDeleteable\Filter\SoftDeleteableFilter
                enabled: true

        dql:
            datetime_functions:
                date_format: DoctrineExtensions\Query\Mysql\DateFormat
                date_of_week: DoctrineExtensions\Query\Mysql\DayOfWeek
                weekday: DoctrineExtensions\Query\Mysql\WeekDay
                hour: DoctrineExtensions\Query\Mysql\Hour

            string_functions:
                JSON_CONTAINS: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonContains
                JSON_SEARCH: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonSearch
                JSON_EXTRACT: Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonExtract
                group_concat: DoctrineExtensions\Query\Mysql\GroupConcat
                MONTH: DoctrineExtensions\Query\Mysql\Month
                YEAR: DoctrineExtensions\Query\Mysql\Year
                JSON_UNQUOTE: App\DQL\JsonUnquoteFunction


            numeric_functions:
                rand: App\DQL\RandFunction
                round: App\DQL\RoundFunction
                ifnull: DoctrineExtensions\Query\Mysql\IfNull
