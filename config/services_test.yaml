imports:
  - { resource: services/test/sso.yaml }

  - { resource: services/test/ranking.yaml }

  - { resource: services/test/easylearning.yaml }

  - { resource: services/test/announcement.yaml }

parameters:
  app.authentication_logs: ''

services:
  App\EntityListener\DeletedByListener:
    tags: [ ]

  App\Tests\Functional\Mock\InMemoryMailer:
    public: true

  Symfony\Component\Mailer\MailerInterface:
    alias: App\Tests\Functional\Mock\InMemoryMailer
    public: true


  ## V2 Services
  App\V2\Application\Log\Logger:
    alias: 'App\V2\Infrastructure\Log\MonologLogger'


  ## Repositories
  App\V2\Domain\Course\Creator\CourseCreatorRepository:
    alias: App\V2\Infrastructure\Persistence\Course\Creator\InMemoryCourseCreatorRepository

  App\V2\Domain\Security\RefreshTokenRepository:
    alias: App\V2\Infrastructure\Persistence\Security\InMemoryRefreshTokenRepository

  App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository:
    alias: App\V2\Infrastructure\Persistence\Announcement\Manager\InMemoryAnnouncementManagerRepository

  App\V2\Domain\Purchase\PurchasableItemRepository:
    alias: App\V2\Infrastructure\Persistence\Purchase\InMemoryPurchasableItemRepository
    public: true

  App\V2\Infrastructure\Security\LexitJwtToken:
    public: true
    arguments:
      - '@lexik_jwt_authentication.jwt_manager'
      - '@App\V2\Domain\Security\RefreshTokenRepository'
      - '@App\V2\Domain\User\UserRepository'
      - 10

  App\V2\Domain\LTI\LtiRegistrationRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\InMemoryLtiRegistrationRepository
  App\V2\Domain\LTI\LtiDeploymentRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\InMemoryLtiDeploymentRepository
  App\V2\Domain\LTI\LtiToolRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\InMemoryLtiToolRepository
  App\V2\Domain\LTI\LtiPlatformRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\InMemoryLtiPlatformRepository
