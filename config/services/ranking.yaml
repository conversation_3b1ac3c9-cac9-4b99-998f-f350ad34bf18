parameters:
  ranking.filters: [2, 4]
  #individual, global, both
  ranking.mode: global
  ranking.useLevel: true
  ranking.levels:
    level1:
      start: 0
      end: 5000
    level2:
      start: 5001
      end: 10000
    level3:
      start: 10001
      end: 15000
    level4:
      start: 15001
      end: 20000
    level5:
      start: 20001
      end: 30000
    level6:
      start: 30001
      end: 40000
    level7:
      start: 40001
      end: 50000
    master:
      start: 50001
      end: false
      stars: true
      step: 10000
