const { exec } = require("child_process");
const util = require("util");

const execPromise = util.promisify(exec);

async function runNodeCommand(command, startMessage, successMessage) {
  const spinner = startSpinner(startMessage);
  try {
    await execPromise(command);
    clearInterval(spinner);
    process.stdout.write(`\r${' '.repeat(startMessage.length + 2)}\r`);
    process.stdout.write(`✅ ${successMessage}\n`);
  } catch (error) {
    clearInterval(spinner);
    process.stdout.write(`\r${' '.repeat(startMessage.length + 2)}\r`);
    console.error(`❌ Error al ejecutar '${command}': ${error.message}`);
    throw error;
  }
}

function startSpinner(message) {
  const spinnerChars = ["|", "/", "-", "\\"];
  let index = 0;

  return setInterval(() => {
    process.stdout.write(`\r${message} ${spinnerChars[index]}`);
    index = (index + 1) % spinnerChars.length;
  }, 100);
}

async function main() {
  try {
    await runNodeCommand(
      "node ./assets/downloadLocales.js",
      "🕒 Iniciando descarga de traducciones de Vue...",
      "Traducciones de Vue descargadas"
    );

    await runNodeCommand(
      "make locales",
      "🕒 Iniciando descarga de traducciones de Symfony...",
      "Traducciones de Symfony descargadas"
    );

    console.log("✅ Traducciones completadas.");
  } catch (error) {
    console.error("Error durante el proceso de construcción:", error.message);
  }
}


main();
