export const VIEWS = {
  default: import.meta.glob('@/contexts/**/views/*.vue', { eager: true }),
  clients: import.meta.glob('@/clients/**/views/*.vue', { eager: true }),
}

export const IMAGES = {
  default: import.meta.glob('@/contexts/**/shared/assets/images/*.(png|jpg|jpeg|svg|ico|webp)', {
    import: 'default',
    eager: true,
  }),
  clients: import.meta.glob('@/clients/**/shared/assets/images/*.(png|jpg|jpeg|svg|ico|webp)', {
    import: 'default',
    eager: true,
  }),
}

export const APP_COMPONENT = {
  AppHeader: import.meta.glob('@/contexts/layout/components/AppHeader.vue', {
    import: 'default',
    eager: true,
  }),
  AppFooter: import.meta.glob('@/contexts/layout/components/AppFooter.vue', {
    import: 'default',
    eager: true,
  }),
}
