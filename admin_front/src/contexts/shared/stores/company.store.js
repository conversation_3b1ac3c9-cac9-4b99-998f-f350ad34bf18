import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import ApiService from '@/core/services/api.service.js'
import { LISTS_API_ROUTES } from '@/contexts/shared/constants/lists.constants.js'
import SelectOptionModel from '@/contexts/shared/models/selectOption.model.js'
import CompanyModel from '@/contexts/shared/models/company.model.js'

export const useCompanyListStore = defineStore('companyListStore', () => {
  const companyList = ref([])
  async function loadCompanyList() {
    const { error, data } = await ApiService.get(LISTS_API_ROUTES.COMPANIES)
    companyList.value = (error ? [] : data || []).map((company) => new CompanyModel(company))
  }

  const companyOptions = computed(() =>
    companyList.value.map((company) => new SelectOptionModel({ label: company.id, value: company.name }))
  )

  return {
    companyList,
    companyOptions,
    loadCompanyList,
  }
})
