import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import ApiService from '@/core/services/api.service.js'
import { LISTS_API_ROUTES } from '@/contexts/shared/constants/lists.constants.js'
import SelectOptionModel from '@/contexts/shared/models/selectOption.model.js'
import RoleModel from '@/contexts/shared/models/role.model.js'
export const useRoleListStore = defineStore('roleListStore', () => {
  const roleList = ref([])
  async function loadRoleList() {
    const { error, data } = await ApiService.get(LISTS_API_ROUTES.ROLES)
    roleList.value = (error ? [] : data || []).map((role) => new RoleModel(role))
  }

  const roleOptions = computed(() =>
    roleList.value.map((role) => new SelectOptionModel({ label: role.key, value: role.name }))
  )

  return {
    roleList,
    roleOptions,
    loadRoleList,
  }
})
