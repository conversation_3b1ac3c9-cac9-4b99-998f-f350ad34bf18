<template>
  <div class="HomeFilters">
    <div class="container">
      <slot />
      <DynamicInput
        v-for="filter in filters"
        :key="filter.key"
        v-model="filter.value"
        :item="filter"
        :disabled="disabled"
      />
    </div>
    <div class="container filterResume">
      <b>
        {{ $t('FILTERS_APPLIED') }} <span>({{ filtersApplied }})</span>
      </b>
      <div class="buttonContainer">
        <BaseButton @click="applyFilters"><Icon icon="check" /> {{ $t('APPLY_FILTERS') }}</BaseButton>
        <BaseButton
          type="danger"
          @click="clearFilters"
        >
          <Icon icon="times" /> {{ $t('CATALOG.SETTING.CLEAR_FILTERS') }}
        </BaseButton>
      </div>
    </div>
  </div>
</template>

<script setup>
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import DynamicInput from '@/contexts/shared/components/DynamicInput.vue'
import { computed } from 'vue'
import { INPUT_TYPES } from '@/contexts/shared/constants/input.constants.js'

const emit = defineEmits(['clear', 'apply'])
const props = defineProps({
  filters: {
    type: Array,
    default: () => [],
  },
  slotFilters: {
    type: Object,
    default: () => ({}),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})
function applyFilters() {
  if (props.disabled) return null
  emit('apply')
}
function clearFilters() {
  if (props.disabled) return null
  emit('clear')
}
const filtersApplied = computed(() => {
  const totalExternal = Object.values(props.slotFilters).reduce(
    (acc, filter) => acc + (filter.type === INPUT_TYPES.BOOLEAN || !!filter.getInputValue().value ? 1 : 0),
    0
  )
  return totalExternal + props.filters.reduce((acc, filter) => acc + (!!filter.getInputValue().value ? 1 : 0), 0)
})
</script>

<style scoped lang="scss">
@use '@/contexts/shared/assets/styles/_breakpoints' as breakpoint;
.HomeFilters {
  padding: 2rem 1rem;
  background-color: var(--color-primary-lightest);

  .container:not(.filterResume) {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem 2rem;

    & > * {
      width: 100%;
      max-width: 30%;

      @media #{breakpoint.$breakpoint-md} {
        max-width: 45%;
      }

      @media #{breakpoint.$breakpoint-xs} {
        max-width: 100%;
      }
    }
  }

  .filterResume,
  .buttonContainer {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }

  .filterResume {
    padding: 1rem 0 0;
  }

  .buttonContainer {
    margin-left: auto;
    margin-right: 0;
  }

  b {
    span {
      color: var(--color-primary);
    }
  }
}
</style>
