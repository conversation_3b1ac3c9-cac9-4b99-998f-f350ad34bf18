<template>
  <div
    ref="dropdownMenuRef"
    v-on-click-outside="handlingClose"
    class="DropdownMenu"
  >
    <BaseButton
      :class="{ open }"
      :disabled="disabled"
      @click="open = !open"
      ><Icon icon="ellipsis"
    /></BaseButton>
    <div
      class="menu"
      :class="{ open: open && !disabled, [direction]: true }"
      @click="open = false"
    >
      <slot />
    </div>
  </div>
</template>

<script setup>
import { nextTick, ref, watch } from 'vue'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'

defineProps({
  disabled: { type: Boolean, default: false },
})
const open = ref(false)
const dropdownMenuRef = ref()
const direction = ref('')
function handlingClose() {
  if (open.value) open.value = false
}

function findParentWithOverflow(el) {
  let element = el
  while (!!element) {
    const style = window.getComputedStyle(element)
    const overflow = style.getPropertyValue('overflow')
    if (style && (overflow.indexOf('auto') > -1 || overflow.indexOf('hidden') > -1)) {
      return element
    }
    element = element.parentElement
  }
  return null
}

watch(open, (newVal) => {
  if (newVal && !direction.value) {
    nextTick(() => {
      const parent = findParentWithOverflow(dropdownMenuRef.value)
      direction.value = parent && parent.scrollHeight > parent.clientHeight ? 'top' : 'bottom'
    })
  }
})
</script>

<style scoped lang="scss">
.DropdownMenu {
  width: fit-content;
  position: relative;

  .BaseButton {
    background-color: var(--color-neutral-mid-light);
    color: var(--color-neutral-dark);

    &.open {
      background-color: var(--color-neutral-mid);
    }

    &:hover {
      background-color: var(--color-neutral-mid);
    }
  }

  .menu {
    display: none;
    flex-direction: column;
    background-color: var(--color-neutral-lightest);
    z-index: 1;
    min-width: 170px;
    position: absolute;
    right: 0;
    border: 1px solid var(--color-neutral-mid);
    border-radius: 3px;
    padding: 3px;
    overflow: hidden;

    &.open {
      display: flex;
    }

    & > * {
      padding: 0.25rem 1rem;
      cursor: pointer;
      border-radius: 3px;

      &:hover {
        background-color: var(--color-primary-lighter);
      }
    }

    :deep(.danger) {
      color: var(--color-neutral-lightest);
      background-color: var(--color-danger);

      &:hover {
        background-color: var(--color-danger-hover);
      }
    }

    &.top {
      bottom: 100%;
    }
  }
}
</style>
