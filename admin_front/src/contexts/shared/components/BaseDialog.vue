<template>
  <div class="BaseDialog">
    <BaseModal
      v-if="openDialog"
      :title="title"
      :show-close-button="false"
      size="s"
      @close="emit('close')"
    >
      <p
        v-if="content"
        class="content"
      >
        {{ content }}
      </p>
      <div class="buttonContainer">
        <BaseButton
          :type="okButtonType"
          @click="emit('confirm')"
          >{{ $t('ALERTIFY.OK') }}</BaseButton
        >
        <BaseButton
          :type="cancelButtonType"
          @click="emit('close')"
          >{{ $t('ALERTIFY.CANCEL') }}</BaseButton
        >
      </div>
    </BaseModal>
  </div>
</template>

<script setup>
import BaseButton from '@/contexts/shared/components/BaseButton.vue'

const emit = defineEmits(['close', 'confirm'])
import BaseModal from '@/contexts/shared/components/BaseModal.vue'
defineProps({
  title: {
    type: String,
    default: '',
  },
  content: {
    type: String,
    default: '',
  },
  openDialog: {
    type: Boolean,
    default: false,
  },
  okButtonType: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'danger'].includes(value),
  },
  cancelButtonType: {
    type: String,
    default: 'danger',
    validator: (value) => ['primary', 'success', 'warning', 'danger'].includes(value),
  },
})
</script>

<style scoped lang="scss">
.BaseDialog {
  .BaseModal {
    place-content: start;
    justify-content: center;
    padding: 3rem 1rem;
    background-color: rgba(255, 255, 255, 0.1);

    :deep(.modalContent) {
      border: 1px solid var(--color-neutral-mid-dark);
    }

    :deep(header) {
      text-align: center;
      grid-template-columns: 1fr;
      background-color: var(--color-neutral-lightest);
      color: var(--color-neutral-darker);
    }

    :deep(main) {
      height: fit-content;
      min-height: unset;
      padding: 0;
    }
  }

  .content {
    padding: 0 1rem 1rem;
    margin: 0;
    color: var(--color-neutral-mid-dark);
    text-align: center;
  }

  .buttonContainer {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }
}
</style>
