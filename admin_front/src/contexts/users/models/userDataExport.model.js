export class UserDataExport {
  constructor({ name = '', from = '', to = '', filters = [] } = {}) {
    this.name = name || ''
    this.from = from || ''
    this.to = to || ''
    this.filters = filters || []
  }

  getPayloadData() {
    const data = { name: this.name }
    if (!!this.from) data.from = this.from
    if (!!this.to) data.to = this.to
    this.filters.forEach((filter) => {
      const payload = filter.getInputValue()
      if (!!payload.value) {
        if (!data.filters) data.filters = []
        data.filters.push(payload)
      }
    })
    return data
  }
}
