import { defineStore } from 'pinia'
import ApiService from '@/core/services/api.service.js'
import { USER_API_ROUTES, USER_ROLE_NAMES } from '@/contexts/users/constants/users.constants.js'
import { reactive, ref } from 'vue'
import { Pagination } from '@/contexts/shared/models/pagination.model.js'
import { toast } from 'vue3-toastify'
import { UserHome } from '@/contexts/users/models/userHome.model.js'
import { UserFilters } from '@/contexts/users/models/userFilters.model.js'
import { INPUT_TYPES } from '@/contexts/shared/constants/input.constants.js'
import { useI18n } from 'vue-i18n'

export const userHomeStore = defineStore('userHomeStore', () => {
  const userListData = reactive({
    data: [],
    isLoading: false,
    pagination: new Pagination(),
  })

  async function loadUserList(payload) {
    userListData.isLoading = true
    const { data, error } = await ApiService.post(USER_API_ROUTES.HOME.LIST, payload)
    userListData.isLoading = false
    if (error) return toast.error(error.message)
    userListData.data = data.data.map((user) => new UserHome(user))
    userListData.pagination.totalItems = data.metadata?.pagination?.total_items || 0
    userListData.pagination.totalPages = data.metadata?.pagination?.total_pages || 0
  }

  async function impersonateUser(payload) {
    const { data, error } = await ApiService.post(USER_API_ROUTES.HOME.IMPERSONATE, payload)
    if (error) return toast.error(error.message)
    window.open(data.url, '_self').focus()
  }

  const i18n = useI18n()
  const filterData = ref({
    dynamic: [],
    static: {
      role: new UserFilters({
        name: 'Role',
        filters: Object.keys(USER_ROLE_NAMES).map((key) => ({ id: key, name: i18n.t(USER_ROLE_NAMES[key]) })),
        type: INPUT_TYPES.SELECT,
        key: 'role',
        value: '',
      }),
      dateFrom: new UserFilters({
        name: '',
        type: INPUT_TYPES.DATE,
        key: 'dateFrom',
        value: '',
      }),
      dateTo: new UserFilters({
        name: '',
        type: INPUT_TYPES.DATE,
        key: 'dateTo',
        value: '',
      }),
      isActive: new UserFilters({
        name: '',
        type: INPUT_TYPES.BOOLEAN,
        key: 'isActive',
        value: true,
      }),
    },
  })
  const clearedFilters = ref([])
  async function loadUserFilters() {
    const { data, error } = await ApiService.post(USER_API_ROUTES.HOME.FILTER_LIST)
    if (error) return toast.error(error.message)
    filterData.value.dynamic = data.map((filter) => new UserFilters(filter))
    clearedFilters.value = data.map((filter) => new UserFilters(filter))
  }
  async function exportUser(payload) {
    const { error } = await ApiService.post(USER_API_ROUTES.HOME.EXPORT, payload)
    if (error) return toast.error(error.message)
    else return toast.success(i18n.t('SUCCESS'))
  }
  async function removeUser(payload) {
    const { error } = await ApiService.post(USER_API_ROUTES.HOME.REMOVE, payload)
    if (error) return toast.error(error.message)
  }
  async function toggleUserStatus(payload) {
    const { data, error } = await ApiService.post(USER_API_ROUTES.HOME.TOGGLE_ACTIVE, payload)
    if (error) {
      toast.error(error)
      return undefined
    }
    return data.isActive
  }

  return {
    userListData,
    filterData,
    clearedFilters,
    loadUserList,
    impersonateUser,
    removeUser,
    toggleUserStatus,
    loadUserFilters,
    exportUser,
  }
})
