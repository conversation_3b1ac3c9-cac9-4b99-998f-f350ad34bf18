import { USER_ROLE_LIST } from '@/contexts/shared/constants/user.constants.js'

export const USER_API_ROUTES = {
  HOME: {
    LIST: '/user/list',
    EXPORT: '/user_data_export',
    IMPERSONATE: '/user/impersonate',
    FILTER_LIST: '/filters/by-categories',
    TOGGLE_ACTIVE: '/user/active',
    REMOVE: '/user/remove',
  },
  FORM: {
    INIT_DATA: '/users/:id',
    EXTRA_FIELDS: '/users/extra',
    NEW_USER: '/users',
    UPDATE_USER: '/users/:id',
  },
  PROFILE: {
    COURSES: '/user/${0}/courses',
    DETAILS: '/user/detail-stats/${0}',
    CHAPTERS: '/user/${0}/chapters',
    MESSAGES: '/user/${0}/messages',
    COURSES_LIST: '/user/${0}/courses-list',
    LOGINS: '/user/${0}/logins',
    TIME: '/user/${0}/time-spent',
    ITINERARY: '/user/${0}/itinerary',
  },
}

export const USER_ROLE_NAMES = {
  [USER_ROLE_LIST.SUPER_ADMIN]: 'USER.ROLES.SUPER_ADMINISTRATOR',
  [USER_ROLE_LIST.ADMIN]: 'USER.ROLES.ADMINISTRATOR',
  [USER_ROLE_LIST.TUTOR]: 'ANNOUNCEMENT.ASSISTANCE_TUTOR',
  [USER_ROLE_LIST.MANAGER]: 'USER.ROLES.MANAGER',
  [USER_ROLE_LIST.CREATOR]: 'user.roles.creator',
  default: 'USER.ROLES.USER',
}
