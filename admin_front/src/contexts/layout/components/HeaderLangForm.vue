<template>
  <div class="HeaderLangForm">
    <div class="form">
      <header>
        Configurar idiomas
        <Icon
          class="icon"
          icon="times"
          @click.stop="emit('close')"
        />
      </header>
      <main>
        <p>Configurar idioma para el panel de administración</p>
        <BaseSelect
          v-model="user.locale"
          name="admin-lang-select"
          :is-clearable="false"
          :options="localeOptions.admin"
          @change="updateLocale"
        />
        <p>Configurar idioma para campus</p>
        <BaseSelect
          v-model="user.localeCampus"
          name="campus-lang-select"
          :is-clearable="false"
          :options="localeOptions.campus"
        />
      </main>
    </div>
    <div class="backdrop" />
  </div>
</template>

<script setup>
import BaseSelect from '@/contexts/shared/components/BaseSelect.vue'
import { inject } from 'vue'
import StorageService from '@/core/services/storage.service.js'

const { localeOptions } = inject('LayoutProps')
const user = inject('user')
const emit = defineEmits(['close'])

function updateLocale() {
  StorageService.setLocale(user.value.locale)
  location.reload()
}
</script>

<style scoped lang="scss">
.HeaderLangForm {
  cursor: initial;
  .form {
    position: absolute;
    top: 1.5rem;
    right: 1.6rem;
    background-color: var(--color-neutral-lightest);
    color: var(--color-neutral-darker);
    width: clamp(200px, 100vw, 300px);
    padding: 1.25rem;
    z-index: 3;

    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 1.2rem;

      .icon {
        font-size: 1.7rem;
        color: var(--color-neutral-mid-dark);
        cursor: pointer;
      }
    }

    :deep(.BaseSelect) {
      --vs-background-color: var(--color-neutral-mid-dark);
      --icon-color: var(--color-neutral-lightest);
      .input {
        color: var(--color-neutral-lightest);
        --vs-text-color: var(--color-neutral-lightest);
      }
    }

    .BaseSelect {
      width: 150px;
      cursor: pointer;
    }
  }

  .backdrop {
    content: ' ';
    position: fixed;
    background-color: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(3px);
    inset: 0;
    z-index: 2;
  }
}
</style>
