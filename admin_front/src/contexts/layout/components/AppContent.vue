<template>
  <div class="AppContent">
    <PageBreadcrumb />
    <SideMenu />
    <div class="contentPanel">
      <RouterView />
    </div>
  </div>
</template>

<script setup>
import SideMenu from '@/contexts/layout/components/SideMenu.vue'
import PageBreadcrumb from '@/contexts/layout/components/PageBreadcrumb.vue'
</script>

<style scoped lang="scss">
@use '@/contexts/shared/assets/styles/_breakpoints' as breakpoint;
.AppContent {
  width: clamp(300px, 100svw, 1700px);
  margin: 0 auto;
  padding: 1rem;
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: auto 1fr;

  .PageBreadcrumb,
  .contentPanel {
    grid-column-start: 2;
  }

  .contentPanel > * {
    width: 100%;
    min-height: 90svh;
    height: fit-content;
  }

  .contentPanel {
    background-color: var(--color-neutral-lighter);
  }

  :deep(.tableContainer) {
    width: 100%;
    max-width: calc(100svw - 10rem);
    overflow-x: scroll;
    overflow-y: hidden;
    margin-top: -3rem;
    margin-bottom: -3rem;
    padding: 3rem 0;

    table {
      width: 100%;
      border-collapse: collapse;

      td,
      th {
        border: solid var(--color-neutral-mid);
        border-width: 1px 0;
      }

      th {
        padding: 1rem 0;
      }
    }
  }

  @media #{breakpoint.$breakpoint-sm} {
    padding: 1rem 0;
    .PageBreadcrumb,
    .contentPanel {
      grid-column-start: 1;
      grid-column-end: -1;
    }

    :deep(.tableContainer) {
      max-width: unset;
    }
  }
}
</style>
