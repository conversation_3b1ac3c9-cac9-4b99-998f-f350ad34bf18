import { onMounted, reactive, ref } from 'vue'
import { PageTitleModel } from '@/contexts/shared/models/pageTitle.model.js'
import { useLTIHomeStore } from '@/contexts/superadmin/lti/stores/LTIHome.store.js'
import LTIRegisterModel from '@/contexts/superadmin/lti/models/LTIRegister.model.js'
import { storeToRefs } from 'pinia'
import { hasErrors } from '@/contexts/shared/utils/validator.utils.js'
import { useI18n } from 'vue-i18n'
import { toast } from 'vue3-toastify'
import { useRouter } from 'vue-router'

export function useLTIHomeComposable() {
  const i18n = useI18n()
  const router = useRouter()
  const links = ref([])
  const { loadLTIRegisterList, createNewLTIRegister, updateLTIRegister, removeLTIRegister } = useLTIHomeStore()
  const { registerList, isLoading } = storeToRefs(useLTIHomeStore())

  const registerForm = reactive({
    open: false,
    data: new LTIRegisterModel(),
  })
  function createNewRegister() {
    registerForm.open = true
    registerForm.data = new LTIRegisterModel()
  }

  async function updateRegister(register = new LTIRegisterModel()) {
    registerForm.open = true
    registerForm.data = new LTIRegisterModel({ id: register.id, name: register.name, client_id: register.clientId })
  }

  const selectedItem = reactive({ open: false, register: new LTIRegisterModel() })
  async function openRemoveDialog(register = new LTIRegisterModel()) {
    if (!register.id.length || register.isRemoving) return null
    selectedItem.open = true
    selectedItem.register = register
  }

  async function removeRegister() {
    if (selectedItem.register.isRemoving) return null
    selectedItem.register.isRemoving = true
    const success = await removeLTIRegister(selectedItem.register.id)
    if (success) {
      await loadLTIRegisterList()
      selectedItem.open = false
    }
    selectedItem.register.isRemoving = false
  }

  async function goToConfiguration(id = '') {
    router.push({ name: 'super-admin-lti-form', params: { id } }).catch()
  }

  function validateForm() {
    registerForm.data.setErrors()
    const schema = registerForm.data.getValidationSchema()
    const errors = hasErrors(registerForm.data.getPayload(), schema)
    if (errors.length)
      registerForm.data.setErrors(errors.reduce((acc, cur) => ({ ...acc, [cur.errorKey]: i18n.t(cur.message) }), {}))
    return !!errors.length
  }

  async function submitForm() {
    const hasErrors = validateForm()
    if (registerForm.data.isUpdating || hasErrors) return null

    registerForm.data.isUpdating = true
    const { id } = registerForm.data

    const success = await (id
      ? updateLTIRegister(id, registerForm.data.getPayload())
      : createNewLTIRegister(registerForm.data.getPayload()))

    if (success) {
      await loadLTIRegisterList()
      registerForm.open = false
      toast.success(i18n.t('CATALOG.SAVED'))
    }
  }

  onMounted(async () => {
    links.value = [new PageTitleModel({ id: 1, title: 'LTI' })]
    await loadLTIRegisterList()
  })

  return {
    links,
    isLoading,
    registerList,
    registerForm,
    selectedItem,
    createNewRegister,
    submitForm,
    updateRegister,
    openRemoveDialog,
    removeRegister,
    goToConfiguration,
  }
}
