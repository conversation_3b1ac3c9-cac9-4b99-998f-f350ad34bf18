import { computed, onMounted, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import TabModel from '@/contexts/shared/models/tab.model.js'
import { PageTitleModel } from '@/contexts/shared/models/pageTitle.model.js'
import { useLTIFormStore } from '@/contexts/superadmin/lti/stores/LTIForm.store.js'
import { storeToRefs } from 'pinia'
import LTIPlatformTab from '@/contexts/superadmin/lti/components/LTIPlatformTab.vue'
import LTIToolTab from '@/contexts/superadmin/lti/components/LTIToolTab.vue'
import LTIDeploymentsTab from '@/contexts/superadmin/lti/components/LTIDeploymentsTab.vue'
import { hasErrors } from '@/contexts/shared/utils/validator.utils.js'
import LTIDeploymentModel from '@/contexts/superadmin/lti/models/LTIDeployment.model.js'

export function useLTIFormComposable() {
  const {
    initFormData,
    loadDeploymentList,
    savePlatform,
    saveTool,
    createDeployment,
    updateDeployment,
    deleteDeployment,
  } = useLTIFormStore()
  const { formData, isLoading } = storeToRefs(useLTIFormStore())
  const i18n = useI18n()
  const links = ref([])
  const route = useRoute()
  const router = useRouter()
  const tabsConfig = reactive({
    tabs: [],
    current: 'platform',
  })

  const currentTabView = computed(
    () =>
      ({
        platform: LTIPlatformTab,
        tool: LTIToolTab,
        deployments: LTIDeploymentsTab,
      })[tabsConfig.current] || LTIPlatformTab
  )

  const tabContent = computed(
    () =>
      ({
        platform: { content: formData.value.platform },
        tool: { content: formData.value.tool },
        deployments: {
          content: formData.value.deployments,
          form: deploymentModal,
          dialog: deploymentRemoveDialog,
          isLoading: isLoading.value,
        },
      })[tabsConfig.current] || {}
  )

  const tabEmitsHandler = computed(
    () =>
      ({
        platform: { save: validateAndSavePlatform },
        tool: { save: validateAndSaveTool },
        deployments: {
          openForm: openDeploymentForm,
          openDialog: openDeploymentDialog,
          closeForm,
          closeDialog,
          submit: validateAndSaveDeployment,
          remove: removeDeployment,
        },
      })[tabsConfig.current] || {}
  )

  function validateForm(model) {
    model.setErrors()
    const schema = model.getValidationSchema()
    const errors = hasErrors(model.getPayload(), schema)
    if (errors.length)
      model.setErrors(errors.reduce((acc, cur) => ({ ...acc, [cur.errorKey]: i18n.t(cur.message) }), {}))
    return !!errors.length
  }

  async function validateAndSavePlatform() {
    const withErrors = validateForm(formData.value.platform)
    if (withErrors || formData.value.platform.isUpdating) return null
    formData.value.platform.isUpdating = true
    await savePlatform(formData.value.platform.getPayload())
    formData.value.platform.isUpdating = false
  }
  async function validateAndSaveTool() {
    const withErrors = validateForm(formData.value.tool)
    if (withErrors || formData.value.tool.isUpdating) return null
    formData.value.tool.isUpdating = true
    await saveTool(formData.value.tool.getPayload())
    formData.value.tool.isUpdating = false
  }

  const deploymentModal = reactive({
    open: false,
    data: new LTIDeploymentModel(),
  })

  function openDeploymentForm(deployment = {}) {
    deploymentModal.data = new LTIDeploymentModel({
      id: deployment?.id,
      name: deployment?.name,
      deployment_id: deployment?.deploymentId,
    })
    deploymentModal.open = true
  }

  function closeForm() {
    deploymentModal.data = new LTIDeploymentModel()
    deploymentModal.open = false
  }

  async function validateAndSaveDeployment() {
    const withErrors = validateForm(deploymentModal.data)
    if (withErrors || deploymentModal.data.isUpdating) return null
    deploymentModal.data.isUpdating = true
    const success = await (!deploymentModal.data.id
      ? createDeployment(deploymentModal.data.getPayload())
      : updateDeployment(deploymentModal.data.id, deploymentModal.data.getPayload()))
    deploymentModal.data.isUpdating = false
    if (success) {
      await loadDeploymentList()
      closeForm()
    }
  }

  const deploymentRemoveDialog = reactive({
    open: false,
    data: new LTIDeploymentModel(),
  })

  function openDeploymentDialog(deployment = new LTIDeploymentModel()) {
    if (!deployment?.id || deployment.isRemoving) return null
    deploymentRemoveDialog.open = true
    deploymentRemoveDialog.data = deployment
  }

  function closeDialog() {
    deploymentRemoveDialog.data = new LTIDeploymentModel()
    deploymentRemoveDialog.open = false
  }

  async function removeDeployment() {
    if (!deploymentRemoveDialog.data.id || deploymentRemoveDialog.data.isRemoving) return null
    deploymentRemoveDialog.data.isRemoving = true
    const success = await deleteDeployment(deploymentRemoveDialog.data.id)
    deploymentRemoveDialog.data.isRemoving = false
    if (success) {
      await loadDeploymentList()
      closeDialog()
    }
  }

  onMounted(async () => {
    links.value = [new PageTitleModel({ id: 1, title: 'LTI', name: 'super-admin-lti' })]
    const { id } = route.params
    if (!id) return router.push({ name: 'super-admin-lti' }).catch()
    await initFormData(id)

    if (!formData.value.fails) {
      tabsConfig.tabs = [
        new TabModel({ key: 'platform', title: i18n.t('LTI.FORM.TAB1') }),
        new TabModel({ key: 'tool', title: i18n.t('LTI.FORM.TAB2') }),
        new TabModel({ key: 'deployments', title: i18n.t('LTI.FORM.TAB3') }),
      ]
      links.value.push(new PageTitleModel({ id: 2, title: formData.value.register.name }))
      await loadDeploymentList()
    }
  })

  return {
    links,
    formData,
    tabsConfig,
    currentTabView,
    tabContent,
    tabEmitsHandler,
  }
}
