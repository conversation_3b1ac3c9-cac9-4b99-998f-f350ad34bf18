import { defineStore } from 'pinia'
import { ref } from 'vue'
import ApiService from '@/core/services/api.service.js'
import { LTI_API_ROUTES } from '@/contexts/superadmin/lti/constants/LTI.constants.js'
import { toast } from 'vue3-toastify'
import LTIRegisterModel from '@/contexts/superadmin/lti/models/LTIRegister.model.js'
import { useI18n } from 'vue-i18n'

export const useLTIHomeStore = defineStore('ltiHomeStore', () => {
  const i18n = useI18n()
  const registerList = ref([])
  const isLoading = ref(false)
  async function loadLTIRegisterList() {
    isLoading.value = true
    const { error, data } = await ApiService.get(LTI_API_ROUTES.HOME.LIST)
    if (error) return toast.error(error.message)
    registerList.value = data.map((register) => new LTIRegisterModel(register))
    isLoading.value = false
  }
  async function createNewLTIRegister(payload = {}) {
    const { error } = await ApiService.post(LTI_API_ROUTES.HOME.CREATE, payload)
    if (error) {
      toast.error(error)
      return false
    }
    return true
  }
  async function updateLTIRegister(id = '', payload = {}) {
    const { error } = await ApiService.put(ApiService.setParams(LTI_API_ROUTES.HOME.UPDATE, { id }), payload)
    if (error) {
      toast.error(error)
      return false
    }
    return true
  }
  async function removeLTIRegister(id = '') {
    const { error } = await ApiService.delete(ApiService.setParams(LTI_API_ROUTES.HOME.REMOVE, { id }))
    if (error) {
      toast.error(error)
      return false
    }
    toast.success(i18n.t('DELETE_SUCCESS'))
    return true
  }

  return {
    registerList,
    isLoading,
    loadLTIRegisterList,
    createNewLTIRegister,
    updateLTIRegister,
    removeLTIRegister,
  }
})
