<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 22.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="300px" height="300px" viewBox="0 0 300 300" style="enable-background:new 0 0 300 300;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#A0B7DD;}
	.st1{fill:#8F9DC4;}
	.st2{fill:#FAF3EB;}
	.st3{fill:#F2F2F2;}
	.st4{filter:url(#Filter_1);}
	.st5{fill:#D9A88A;}
	.st6{fill:#CBCBCB;}
	.st7{fill:#F2B88E;}
	.st8{fill:#E0E0E0;}
	.st9{opacity:0.2;}
	.st10{filter:url(#Filter_2);}
	.st11{fill:#FFD4AF;}
	.st12{fill:#E8AC83;}
	.st13{fill:#E5A86D;}
	.st14{fill:#FFC58E;}
	.st15{fill:#E2B289;}
	.st16{fill:#FFFFFF;}
	.st17{fill:#CCCCCC;fill-opacity:0.298;}
	.st18{fill:#623214;fill-opacity:0.302;}
	.st19{fill:#FFFFFF;fill-opacity:0.2;}
	.st20{fill:#CFCFCF;}
	.st21{fill:#E2B289;fill-opacity:0.698;}
	.st22{fill:#383534;}
	.st23{fill:#FFFFFF;fill-opacity:0.498;}
</style>
<filter  color-interpolation-filters="sRGB" height="140%" id="Filter_2" width="140%" x="-20%" y="-20%">
	<feColorMatrix  in="SourceGraphic" result="result1" type="matrix" values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0.1 0">
		</feColorMatrix>
</filter>
<filter  color-interpolation-filters="sRGB" height="140%" id="Filter_1" width="140%" x="-20%" y="-20%">
	<feColorMatrix  in="SourceGraphic" result="result1" type="matrix" values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0.6 0">
		</feColorMatrix>
</filter>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer1_0_MEMBER_0_FILL">
			<rect y="-0.5" class="st0" width="300" height="300.9"/>
		</g>
	</g>
</g>
<g transform="matrix( 0.59246826171875, 0, 0, 0.59246826171875, -181,-37.2) ">
	<g>
		<g id="Layer1_0_MEMBER_1_MEMBER_0_FILL">
			<path class="st1" d="M502.3,443.3c-1.5-5.6-4.5-10.3-9.1-13.9c-4.7-3.7-10-5.5-16-5.5c-5.6,0-10.7,1.6-15.2,4.9
				c-1.2-10.6-5.8-19.4-13.8-26.6c-8-7.2-17.4-10.8-28.2-10.8c-11.7,0-21.6,4.1-29.9,12.4s-12.4,18.2-12.4,29.9l0.1,1.4
				c-3.4-2.7-7.4-4-11.8-4c-4,0-7.7,1.1-11,3.4c-3.2,2.3-5.5,5.2-6.9,8.9H502.3z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, -175,95) ">
	<g>
		<g id="Layer1_0_MEMBER_1_MEMBER_1_FILL">
			<path class="st2" d="M252.1,132.2c-4-2-8.4-3.4-13.3-4c0.3-1,0.4-1.9,0.4-2.8c0-2.6-0.9-4.9-2.8-6.8c-1.9-1.9-4.2-2.8-6.8-2.8
				c-2.1,0-4,0.6-5.6,1.8c-0.9-1.6-2.1-2.9-3.5-4c-1.5-1.1-3-2-4.8-2.4c-0.9-4.3-3.1-7.8-6.5-10.5c-3.4-2.8-7.3-4.2-11.8-4.2
				c-3.7,0-7.1,1-10.2,3c-3,1.9-5.3,4.5-6.8,7.8l-2-0.1c-1.2,0-2.3,0.1-3.4,0.2v32.1h86C258.9,136.6,256,134.2,252.1,132.2z
				 M207,136.7c-0.1,0-0.2,0-0.3-0.1l0.3-0.3V136.7z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_0_FILL">
			<path class="st3" d="M151.1,250.4c-22.5,0-41.7,5.3-57.5,16c-14,9.4-21.8,20.6-23.5,33.6h161.7c-1.7-13-9.5-24.2-23.4-33.6
				C192.5,255.7,173.4,250.4,151.1,250.4z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1.54559326171875, -0.04827880859375, -0.0791168212890625, -1.655029296875, 115.35,281.65) ">
	<g transform="matrix( 1, 0, 0, 1, 0,0) ">
		<g class="st4">
			<g id="flash0_ai_Modo_de_aislamiento__Path__2_0_Layer0_0_FILL_1_">
				<path class="st5" d="M43.1,12.9c0,0,0.4,0.7,1.2,2.1c0.6,1.1,1.1,1.6,1.5,1.6c0.9-0.1,1.2-0.9,1-2.4c-0.5-1.7-0.8-2.7-0.9-3.1
					c-0.5-1.6-0.8-2.9-1.1-3.7c-0.5-1.4-1-2.5-1.7-3.4c-0.8-1.1-1.8-1.9-2.9-2.3c-0.6-0.2-1.9-0.5-3.9-0.8c-6.4-1-13.6-1-21.7-0.2
					C9.2,1.1,5.4,2.9,3.2,6.1c-2,2.9-3.1,6-3.1,9.5c-0.1,1.8,0.3,3.6,1,5.3c0.5,1.1,1.1,1.8,1.9,1.9c0.8,0.2,1.4-0.3,1.9-1.5
					c0.2-0.6,0.6-1.7,1-3.3c0.4-1.4,0.8-2.4,1.1-3.2c1.3-3.1,3.4-5.1,6.6-6c1.5-0.4,3.3-0.6,5.4-0.6c1.2,0.1,3,0.2,5.5,0.3
					c0.5,0,2.6-0.2,6.2-0.6c2.7-0.3,4.8-0.2,6.2,0.2c1.4,0.4,2.6,1,3.6,1.8C40.8,10.5,41.8,11.4,43.1,12.9z"/>
			</g>
		</g>
	</g>
</g>
<g transform="matrix( 1.0972900390625, 0, 0, 1.1046142578125, -13.1,-22.5) ">
	<g>
		<g id="Layer0_1_MEMBER_1_MEMBER_0_FILL">
			<path class="st6" d="M113.6,224.8c-2.8,2.2-5.5,4.7-7.9,7.6c-3,3.4-4.6,6.3-5,8.5c-0.3,1.6,1,4.6,3.9,9c3.1,4.7,7,9.2,11.5,13.4
				c12.5,11.6,24.6,16.3,36.3,14c9.1-1.8,17.3-6.6,24.8-14.4c4-4.2,6.1-6.4,6.1-6.5c1.7-1.4,3.1-1.5,4.1-0.3
				c2.2-1.1,3.1-3.9,2.7-8.2c-0.5-6-4.2-12.9-11.1-20.6c-6.5-7.4-15.5-11.9-26.8-13.7C138.3,211.6,125.4,215.3,113.6,224.8z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_1_MEMBER_1_MEMBER_1_FILL">
			<path class="st7" d="M148.1,214.9c-6.7,0.4-13,2.1-18.9,5.1c-6.5,3.2-11.3,8.1-14.3,14.7c-1.1,2.6-1.5,5.2-1,7.9
				c0.8,4.6,2.2,8.9,4.3,13.1c3.9,4.8,8.3,9.1,13.2,12.9c3.6,2.8,7.3,5.4,11.2,7.7c3.3,1.9,7,2.6,11.2,2.1c3.8-0.5,7.5-1.9,11.1-4.2
				c1.3-0.9,4.5-2.4,9.6-4.6c3.5-1.5,6-3.4,7.5-5.8c0.7-1.1,1.4-4.9,1.9-11.6c0.6-7.3,1.1-11.2,1.3-11.8c0.8-2.8-0.3-5.4-3.2-7.8
				c-4.1-3.3-6.5-5.7-7.2-7.1c-1.4-2.9-3.6-5.1-6.6-6.5C161.9,215.9,155.2,214.6,148.1,214.9z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1.70233154296875, -0.053192138671875, -0.0791168212890625, -1.655029296875, -124.65,1109.95) ">
	<g>
		<g id="Layer0_1_MEMBER_1_MEMBER_2_MEMBER_0_FILL">
			<path class="st8" d="M193.7,482.9l-26.5,0.9c-0.3-0.5-0.4-0.9-0.5-1.3c0,0.3,0,0.8,0.2,1.3l-9.7,0.3c-0.2,0.1-0.3,0.2-0.5,0.3
				c-0.6,0.3-1.2,0.7-1.8,1.1c-5.6,3.7-8.3,8.2-8,13.6c0,0.9,0.1,1.7,0.2,2.6l0,0c0.7,6.1,2.7,12,6.1,17.8c1.4,2.4,2.8,4.4,4.2,6
				c1.3,1.5,2.2,2.1,2.6,2c0-0.4,0-0.8,0-1.2c0.1-3.1,1-6.3,2.6-9.6c0.5-0.9,1-1.9,1.6-2.8c0.6-0.9,1.2-1.8,1.9-2.6
				c0-0.1,0.1-0.1,0.1-0.2c2.4-3,5.5-5.7,9.1-8.2c2.7-1.8,5.1-3.6,7.3-5.5c0.3-0.3,0.6-0.5,0.9-0.8c0.2-0.2,0.4-0.3,0.5-0.5
				c0.3-0.3,0.7-0.6,1-0.9c0.5-0.5,1-1,1.5-1.4c0,0,0.1-0.1,0.1-0.1c0.1-0.1,0.3-0.3,0.4-0.4c1.4-1.5,2.7-3,3.7-4.5
				c1.6-2.3,2.5-4.2,2.9-5.6C193.7,483,193.7,483,193.7,482.9z"/>
		</g>
	</g>
	<g id="flash0_ai_Modo_de_aislamiento__Path__4_0_Layer0_0_FILL" class="st9">
		<path d="M195.6,499.7c-1-0.9-3.1-2.4-6.3-4.4c-0.6-0.4-1.3-0.8-2.1-1.3c-0.1-0.1-0.3-0.2-0.4-0.3c-0.4-0.2-0.7-0.4-1.1-0.7
			c-3.3-1.9-7.3-4.3-12-7c-1.3-0.7-2.6-1.4-3.9-2.2c-0.1-0.1-0.3-0.2-0.4-0.2l-2,0.1c-0.3-0.5-0.4-0.9-0.5-1.3c0,0.3,0,0.8,0.2,1.3
			c0,0.1,0,0.2,0.1,0.3c0.1,0.5,0.3,1,0.6,1.6c0.6,1.4,1.1,2.5,1.7,3.1c-3,2-5.9,4.8-8.5,8.2c-2.7,3.6-4.2,6.8-4.7,9.7
			c1.9-4.5,4.8-8.5,8.7-12c2.2-2,4.3-3.6,6.3-4.6c0.8,0.5,2.2,1.5,4.4,3c-1.5,0.5-3.4,2.2-6,5.1c-0.4,0.4-0.8,0.9-1.2,1.4
			c-0.4,0.5-0.9,1-1.3,1.6c0,0-0.1,0.1-0.1,0.1c0,0,0,0,0,0c-2.5,3.2-4.3,6-5.3,8.4c-0.6,1.4-0.9,2.7-1,3.9c0.5-1.2,1.1-2.4,1.7-3.6
			c0.7-1.4,1.6-2.8,2.5-4c1.5-2.1,3.2-4.1,5-5.9c1.5-1.5,3-2.7,4.3-3.6c1.2-0.9,2.3-1.5,3.3-1.9c0.6,0.4,1.2,0.7,1.8,1.1
			c1,0.5,1.9,1,2.9,1.5c0.2,0.1,0.5,0.2,0.7,0.3c2.5,1.1,5,1.8,7.5,2.2c1.3,0.2,2.5,0.3,3.5,0.3c0.4,0,0.8,0,1.2-0.1
			C195.2,499.7,195.4,499.7,195.6,499.7z"/>
	</g>
</g>
<g transform="matrix( 1.70233154296875, -0.053192138671875, -0.0791168212890625, -1.655029296875, 102.85,303.45) ">
	<g transform="matrix( 1, 0, 0, 1, 0,0) ">
		<g class="st10">
		</g>
	</g>
</g>
<g transform="matrix( 1.70233154296875, -0.053192138671875, -0.0791168212890625, -1.655029296875, -124.65,1109.95) ">
	<g>
		<g id="Layer0_1_MEMBER_1_MEMBER_2_MEMBER_2_FILL">
			<path class="st8" d="M218.5,503L218.5,503c-0.4-2.8-1.2-5.8-2.5-9.1c-1.8-4.5-4.5-8.3-8.2-11.3c-0.1-0.1-0.2-0.2-0.3-0.2
				l-40.3,1.3c0,0.1,0.1,0.2,0.1,0.2c0.7,1.3,2.1,2.7,4,4.4c2.1,1.9,4.6,3.6,7.5,5.1c1.1,0.6,2.3,1.3,3.6,1.9
				c0.6,0.3,1.1,0.5,1.7,0.8c0.3,0.1,0.5,0.2,0.8,0.3c1.7,0.7,3.5,1.4,5.4,2.1c1.2,0.4,2.4,0.8,3.4,1.3c4.3,1.7,7.6,3.6,10.1,5.6
				c0.8,0.6,1.4,1.3,2,1.9c0.5,0.6,1,1.2,1.4,1.8c0.2,0.3,0.3,0.5,0.5,0.8c0.9,1.5,1.4,3.2,1.6,5.2c0.1,1,0.2,2.3,0.3,4
				c0,0.1,0,0.2,0,0.3c0.7,0.1,1.6-0.3,2.9-1c1.6-1,2.9-2.3,3.9-4C218.4,511.3,219,507.5,218.5,503z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1.70233154296875, -0.053192138671875, -0.0791168212890625, -1.655029296875, -124.65,1109.95) ">
	<g>
		<g id="Layer0_1_MEMBER_1_MEMBER_2_MEMBER_3_FILL">
			<path class="st3" d="M206.5,490.6c-1.6-3-4-5.5-7.3-7.6c-0.1-0.1-0.2-0.1-0.2-0.2c0,0-0.1-0.1-0.1-0.1l-26.7,0.9
				c0,0.1,0.1,0.2,0.1,0.2c0.4,0.8,0.8,1.6,1.4,2.3c0.8,1,1.8,1.9,3,2.6c3.8,2.3,7.2,4.1,10,5.1c0.1,0,0.2,0.1,0.3,0.1
				c0.1,0,0.2,0.1,0.3,0.1c3.9,1.3,7.6,1.8,11.1,1.3c1-0.1,3.1-0.4,6.2-0.7C206.9,494,207.5,492.6,206.5,490.6z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1.70233154296875, -0.053192138671875, -0.0791168212890625, -1.655029296875, -124.65,1109.95) ">
	<g>
		<g id="Layer0_1_MEMBER_1_MEMBER_2_MEMBER_4_FILL">
			<path class="st3" d="M163.9,491.2c1.1-1.3,1.7-2.6,1.9-3.8c0.3-1.6-0.5-2-2.4-1.4c-1.7,0.6-3.2,2-4.3,4.3c-1.1,2.3-1.5,4.3-1.1,6
				C159.9,495.3,161.9,493.6,163.9,491.2z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1.70233154296875, -0.053192138671875, -0.0791168212890625, -1.655029296875, -124.65,1109.95) ">
	<g>
		<g id="Layer0_1_MEMBER_1_MEMBER_2_MEMBER_5_FILL">
			<path class="st3" d="M172.2,492.5c-0.9,0-1.8,0.1-2.6,0.3c-2,0.5-3.7,1.8-5,3.9c-1.2,1.8-1.9,4-2.1,6.6c1.3-1,3-2.6,5.1-4.7
				c1.4-1.1,2.4-2,3.1-2.6C171.8,494.7,172.3,493.6,172.2,492.5z"/>
		</g>
	</g>
</g>
<g transform="matrix( -1.277069091796875, 0, 0, 1.277069091796875, 311.75,-35.4) ">
	<g>
		<g id="Layer0_2_MEMBER_0_MEMBER_0_FILL">
			<path class="st11" d="M64.9,143.4c-2.5-2.3-5.5-3-9.1-2c-3.8,1-6.4,3.1-8,6.3c-1.4,2.8-1.8,6.1-1.1,9.9c0.6,3.6,2.1,6.9,4.3,9.9
				c2.3,3.1,5.1,5.3,8.3,6.4c3.7,1.3,8,1.8,12.9,1.4l0.3-0.4c0.8-1.3,0.8-4.9-0.2-10.8c-0.7-4.5-1.5-8.2-2.3-10.9
				C68.6,148.6,66.9,145.4,64.9,143.4z"/>
		</g>
	</g>
</g>
<g transform="matrix( -1.277069091796875, 0, 0, 1.277069091796875, 244.4,154.65) ">
	<g>
		<g id="Layer0_2_MEMBER_0_MEMBER_1_FILL">
			<path class="st12" d="M9.9,19.6c1.3-0.1,2.5-0.5,3.6-1.1c2.7-1.7,3.3-4.9,1.8-9.6C13.9,4.4,12,1.6,9.5,0.4c-2.2-1-4.6-0.7-7.1,1
				c-2.7,1.8-3.2,4.2-1.3,7.4c1.1-3,2.4-4,3.9-3c1.5,1,2.1,2.8,1.8,5.3c0,0.2-0.6,1.2-1.6,3.3c-0.5,1-0.1,2.3,1.2,3.9
				C7.1,19.2,8.3,19.7,9.9,19.6z"/>
		</g>
	</g>
</g>
<g transform="matrix( 3, 0, 0, 3, 0,0) ">
	<g>
		<g id="Layer0_2_MEMBER_1_FILL">
			<path class="st11" d="M76.9,45.1c-1.3-10.4-4.3-18.2-8.9-23.6c-0.4-0.5-0.8-1-1.1-1.3l-0.8-0.9c-5.9-4.7-11.8-6.6-17.7-5.8
				c-7.6,1-13.1,3-16.4,5.9c-3.3,2.9-5.5,6.8-6.6,11.6c-1.1,4.8-1.6,9-1.4,12.6c0.1,0.8,0.1,1.7,0.2,2.7c0,0.2,0,0.3,0.1,0.5
				c0,0.6,0.1,1.3,0.1,2c0.1,0.6,0.1,1.2,0.2,1.8c0.2,1.7,0.4,3.5,0.6,5.4c1,7.7,4.1,14.3,9.1,19.8c5.6,6.1,12.2,8.9,19.8,8.3
				c4-0.3,7.6-1.7,10.8-4.2c3-2.3,5.5-5.4,7.6-9.3c0.7-1.3,1.3-2.6,1.8-3.9l1-3c1.1-3.4,1.7-6.8,1.8-10.4c0-0.4,0.1-0.9,0.1-1.3
				C77.3,49.7,77.2,47.4,76.9,45.1z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1.277069091796875, 0, 0, 1.277069091796875, -11.85,-35.4) ">
	<g>
		<g id="Layer0_2_MEMBER_2_FILL">
			<path class="st11" d="M64.9,143.4c-2.5-2.3-5.5-3-9.1-2c-3.8,1-6.4,3.1-8,6.3c-1.4,2.8-1.8,6.1-1.1,9.9c0.6,3.6,2.1,6.9,4.3,9.9
				c2.3,3.1,5.1,5.3,8.3,6.4c3.7,1.3,8,1.8,12.9,1.4l0.3-0.4c0.8-1.3,0.8-4.9-0.2-10.8c-0.7-4.5-1.5-8.2-2.3-10.9
				C68.6,148.6,66.9,145.4,64.9,143.4z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1.277069091796875, 0, 0, 1.277069091796875, 55.5,154.65) ">
	<g>
		<g id="Layer0_2_MEMBER_3_FILL">
			<path class="st12" d="M9.9,19.6c1.3-0.1,2.5-0.5,3.6-1.1c2.7-1.7,3.3-4.9,1.8-9.6C13.9,4.4,12,1.6,9.5,0.4c-2.2-1-4.6-0.7-7.1,1
				c-2.7,1.8-3.2,4.2-1.3,7.4c1.1-3,2.4-4,3.9-3c1.5,1,2.1,2.8,1.8,5.3c0,0.2-0.6,1.2-1.6,3.3c-0.5,1-0.1,2.3,1.2,3.9
				C7.1,19.2,8.3,19.7,9.9,19.6z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_3_FILL">
			<path class="st13" d="M136.1,198c3.5,8.4,9.6,12.9,18.3,13.4c7.1,0.6,14.1,0.9,21.1,0.8c9.3-0.1,15.3-4.5,18.1-13.1
				c-7,1.9-14.2,2.7-21.5,2.6c-4.9-0.1-9.7,0.4-14.4,1.6C149.1,205.5,141.9,203.7,136.1,198z"/>
			<path class="st14" d="M136.1,198l3.1,8.2l-0.8-1.1c2.1,5.7,6.2,10.1,12.4,13c5.6,2.6,11.9,3.7,19,3.3c3.9-0.4,7.2-1.2,9.9-2.3
				c1.6-0.7,4.5-2.3,8.9-5c1.5-2.9,2.7-6,3.7-9.3c0.6-1.9,0.9-3.9,1.1-5.9c-2.8,8.6-8.8,13-18.1,13.1c-6.9,0.1-13.9-0.2-21.1-0.8
				C145.7,210.9,139.6,206.4,136.1,198z"/>
		</g>
	</g>
</g>
<g transform="matrix( 3.4051666259765625, 0.62799072265625, -0.771087646484375, 4.181121826171875, 21.3,-86.9) ">
	<g>
		<g id="Layer0_4_FILL">
			<path class="st15" d="M46.5,58.4c-0.8,1-1.3,2.2-1.5,3.5c-0.1,1.3-0.2,2.4,0,3.4c0.2,1,0.5,1.6,0.8,1.7H46c0-1.3,0.3-3.2,1-5.8
				l1.5-4.9C47.8,56.8,47.2,57.5,46.5,58.4z"/>
		</g>
	</g>
</g>
<g transform="matrix( -4.124267578125, 1.0426025390625, 1.169281005859375, 4.6253509521484375, 314.9,-136.6) ">
	<g>
		<g id="Layer0_5_FILL">
			<path class="st15" d="M46.5,58.4c-0.8,1-1.3,2.2-1.5,3.5c-0.1,1.3-0.2,2.4,0,3.4c0.2,1,0.5,1.6,0.8,1.7H46c0-1.3,0.3-3.2,1-5.8
				l1.5-4.9C47.8,56.8,47.2,57.5,46.5,58.4z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_6_MEMBER_0_FILL">
			<path class="st16" d="M171.1,182.9l-5.8,0.3c-6.7-0.2-13.3,1.1-20,3.8c-13.4,5.4-20.3,15.1-20.6,29.2l3.9-1.8
				c3.2-1.6,6-3.3,8.6-5l1.9-4.7l-0.1,4.7c1.2,0.1,3.5-0.2,7-0.8c3.6-0.7,6.3-1,7.9-1.1l-0.4-0.4c1.3,0,3.7,0.2,7,0.5
				c3.3,0.3,6,0.4,8.1,0.3c1.2-0.1,3.7-0.1,7.6-0.1c3.2-0.1,5.4-0.1,6.8-0.2l0.1,0.1c2-0.2,4.5-0.3,7.7-0.6c2.4-0.1,4.4-0.3,6-0.6
				l-1.5-4.8l3.1,6.3c3.7,2.3,6.4,3.4,8.2,3.4c-2-13.9-9.5-22.9-22.4-26.8C180.2,183.6,175.8,182.9,171.1,182.9z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_6_MEMBER_1_FILL">
			<path class="st16" d="M184.3,184.9c-4.1-1.2-8.4-1.9-13.1-2l-5.8,0.3c-6.7-0.2-13.3,1.1-20,3.8c-13.4,5.4-20.3,15.1-20.6,29.2
				l3.9-1.8c3.2-1.6,6-3.3,8.6-5l1.9-4.7l-0.1,4.7c1.2,0.1,3.5-0.2,7-0.8c3.6-0.7,6.3-1,7.9-1.1l-0.4-0.4c1.3,0,3.7,0.2,7,0.5
				c3.3,0.3,6,0.4,8.1,0.3c1.2-0.1,3.7-0.1,7.6-0.1c3.2-0.1,5.4-0.1,6.8-0.2l0.1,0.1c2-0.2,4.5-0.3,7.7-0.6c2.4-0.1,4.4-0.3,6-0.6
				l-1.5-4.8l3.1,6.3c3.7,2.3,6.4,3.4,8.2,3.4C204.6,197.7,197.2,188.8,184.3,184.9z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_6_MEMBER_1_MEMBER_0_FILL">
			<path class="st17" d="M173.8,197.8c2.9-0.2,5-0.4,6.2-0.7l0.1,0.3c1.8-0.4,4.1-0.7,7-1.1c2.2-0.3,4-0.7,5.4-1l-1.6-5.6l3.1,7.3
				c3.5,2.4,6,3.6,7.6,3.4c-0.2-1.5-0.5-2.9-0.8-4.3c-3.8-5.4-9.3-9.1-16.6-11.3c-4.1-1.2-8.4-1.9-13.1-2l-5.8,0.3
				c-6.7-0.2-13.3,1.1-20,3.8c-8.8,3.5-14.7,8.9-17.9,16.1c-0.3,2.5-0.4,5-0.3,7.8l3.6-2.4c2.8-2.1,5.3-4.2,7.6-6.4l1.6-5.6l0.1,5.5
				c1.1,0,3.2-0.4,6.3-1.4c3.3-1,5.7-1.6,7.2-1.8l-0.4-0.4c1.2-0.1,3.3,0,6.4,0.2c3,0.2,5.5,0.1,7.4-0.2
				C167.9,198.3,170.3,198.1,173.8,197.8z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1.277069091796875, 0, 0, 1.277069091796875, 82.85,-34.15) ">
	<g>
		<g id="Layer0_7_MEMBER_0_FILL">
			<path class="st11" d="M70.2,145.3c-3.9-4.7-7.6-6.6-11-5.6c-3.8,1-6.6,3.2-8.5,6.4c-1.7,2.9-2.5,6.3-2.3,10.1
				c0.2,3.7,1.3,7.1,3.3,10.1c2,3.2,4.6,5.4,7.8,6.5c5.3,1.9,9.6,2.7,12.8,2.5c0.2-0.3,0.3-0.4,0.3-0.4c2.4-0.5,4-2.9,4.8-7.4
				c0.7-3.8,0.7-6.8,0-9C75.9,153.6,73.5,149.2,70.2,145.3z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_7_MEMBER_1_FILL">
			<path class="st18" d="M173.6,186.3c-0.2,0-0.4,0-0.6,0c-2.6,0-5.9-0.7-9.9-2.1c-1.2-0.3-6.1-2.1-14.9-5.4c3.1,3.4,7,6.1,11.7,8.3
				c4.2,1.8,7.8,2.7,10.8,2.7c1.1,0,2.1-0.1,2.9-0.1c2.4-0.3,4-1.1,4.8-2.3c1-1.3,1.8-2.7,2.5-4.2c0.9-1.6,1.4-2.8,1.4-3.8
				c0-1.1-0.2-1.8-0.4-2.3c-0.3,0.7-0.6,1.4-1.1,2.3C179.2,182.5,176.8,184.8,173.6,186.3z"/>
			<path class="st19" d="M172.9,174.6c0.8,3.8,1,7.7,0.6,11.7c3.2-1.5,5.6-3.8,7.2-6.9c0.4-0.8,0.8-1.6,1.1-2.3
				c-0.3-6.3-2-12.9-5.1-19.8c-3.2-6.7-5.8-11.9-7.6-15.6c-2.1-3.8-4.9-6.8-8.3-8.9c2.7,3.7,4.6,7.7,5.7,11.9
				c1.5,5.1,2.8,10.3,3.8,15.6C171.2,165.1,172.1,169.8,172.9,174.6z"/>
		</g>
	</g>
</g>
<g transform="matrix( 5.67706298828125, 0, 0, 4.505615234375, -126.95,-65.85) ">
	<g>
		<g id="Layer0_8_FILL">
			<path class="st20" d="M44.9,38.6c0.1,0,1.3,0.4,3.8,1.2c-0.5-0.9-1.2-1.7-2.2-2.4c-1-0.6-2.1-0.8-3.3-0.6
				c-0.9,0.2-1.7,0.6-2.5,1.2c-1,0.8-1.6,1.8-2,2.9C40.8,39.2,42.9,38.4,44.9,38.6z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 6,1) ">
	<g>
		<g id="Layer0_9_FILL">
			<path class="st15" d="M171.2,82.3l0.7-0.8l8.6-0.7c-6.4-0.3-12.8-0.5-19.3-0.7c-12.5-0.2-24.9,0.1-37.4,0.9l2.3,0.6
				c2.3,0.1,4.5,0.2,6.8,0.3C145.7,83.1,158.5,83.2,171.2,82.3z"/>
		</g>
	</g>
</g>
<g transform="matrix( -4.1833648681640625, -0.5068359375, 4.5400390625, -0.66656494140625, 125.75,126.4) ">
	<g>
		<g id="Layer0_10_FILL">
			<path class="st15" d="M49.5,47.6c-0.3,0.2-0.6,0.3-0.7,0.4c-1.2,0.5-2.3,0.9-3.5,1.1c-1.1,0-1.5,0.4-1.3,1.2l2.4,0.1l-1.4,1.6
				c2.1,0,3.8-0.9,5.1-2.6c0-0.1,0.1-0.2,0.1-0.3C50.5,48.1,50.3,47.6,49.5,47.6z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1.0958251953125, -0.139251708984375, 0.1650238037109375, 1.4237518310546875, -35.2,-62.4) ">
	<g>
		<g id="Layer0_11_FILL">
			<path class="st21" d="M90,182c-0.2-0.2-0.4-0.3-0.6-0.4l3.5,6.3c0.7,1,1.6,1.9,2.4,2.8c0.9,0.9,1.9,1.7,3.1,2.4
				c0.2,0.1,0.4,0.2,0.6,0.3c0.2,0.1,0.4,0.2,0.6,0.3c0.4,0.2,0.9,0.3,1.3,0.6c0.4,0.1,0.9,0.3,1.3,0.4c0.4,0.1,0.8,0.2,1.3,0.3
				c0.4,0.1,0.8,0.2,1.3,0.3c0.4,0,0.9,0,1.3,0.1c0.5,0,0.9,0.1,1.4,0.1s1,0,1.5,0c0.4,0,0.8-0.1,1.3-0.1c0.5-0.1,1-0.3,1.5-0.4
				c0.2-0.1,0.3-0.2,0.6-0.3c0.1-0.1,0.3-0.2,0.4-0.3c0.2-0.1,0.4-0.3,0.6-0.4s0.3-0.3,0.5-0.4c0.2-0.1,0.3-0.2,0.5-0.3
				c0.1-0.1,0.2-0.1,0.3-0.3c0.1,0,0.1,0,0.2-0.1c-1.9,0.1-3.8,0.2-5.7,0.3c-0.5,0-1,0-1.5,0c-0.5-0.1-1-0.1-1.5-0.1
				c-0.5-0.1-1-0.2-1.5-0.3c-0.6-0.2-1.1-0.3-1.7-0.4c-0.5-0.2-1-0.4-1.4-0.6c-0.5-0.2-1.1-0.4-1.6-0.7c-0.5-0.3-1-0.6-1.4-0.9
				c-0.5-0.4-1-0.7-1.5-1.1c-0.4-0.3-0.7-0.6-1.1-0.9c-0.2-0.2-0.4-0.4-0.7-0.6c-0.3-0.3-0.5-0.5-0.8-0.8c-0.2-0.3-0.5-0.5-0.7-0.8
				c-0.8-0.9-1.5-1.9-2.3-2.8C91,182.8,90.5,182.4,90,182z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_12_FILL">
			<path class="st21" d="M205.2,196.9c0.2,0.2,0.4,0.3,0.5,0.5c0.1,0.2,0.3,0.4,0.5,0.7c0.1,0.2,0.3,0.4,0.5,0.6
				c0.1,0.2,0.3,0.3,0.4,0.4c0.2,0.1,0.4,0.3,0.6,0.5c0.5,0.3,1,0.5,1.6,0.8c0.5,0.1,0.9,0.2,1.4,0.3c0.6,0.1,1.1,0.2,1.6,0.3
				c0.5,0.1,1.1,0.1,1.6,0.2c0.5-0.1,0.9-0.1,1.4,0c0.5-0.1,1-0.2,1.4-0.2c0.1,0,0.2-0.1,0.4-0.1c2-0.2,3.8-0.8,5.4-2
				c0.6-0.4,0.8-0.6,0.6-0.6l3-5.9c-1.4,1.7-3.2,3-5.3,4c-1.2,0.5-2.5,0.9-3.8,1.2c-0.4,0.1-0.8,0.2-1.2,0.3c-0.6,0-1.1,0.1-1.7,0.2
				c-0.6-0.1-1.1-0.1-1.6-0.1c-0.6-0.1-1.1-0.1-1.7-0.2c-2-0.4-4.1-0.8-6.2-1.2c0.1,0.1,0.1,0.1,0.2,0.1
				C205,196.6,205.1,196.8,205.2,196.9z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_13_MEMBER_0_MEMBER_0_FILL">
			<path class="st16" d="M81.5,86.9c0.1-0.3,0.2-0.6,0.3-1c0.7-4.1,1.9-8,3.8-11.7c2.6-5.4,5.8-10,9.4-14c-1.3,0.6-1.9,1-1.7,1.1
				c0.2,0.1,0,0.2-0.6,0.5c-2.5,1.1-4.9,2.3-7.1,3.7c0,0,0,0.1,0,0.1c-1.1,1.5-2.7,3.6-4.9,6.5c-1.8,2.5-3.3,4.8-4.4,6.8
				c-0.1,0.3-1,2.1-2.8,5.6c-1.1,2.3-2.1,4.1-3.1,5.4c-0.6,0.9-1.6,2.2-2.9,3.9c-1.1,1.6-1.8,3-2.1,4.3c-0.2,0.8-0.3,2-0.3,3.7
				c0,1.6-0.1,2.8-0.3,3.6c-0.1,0.5-0.6,1.8-1.5,3.8c-0.7,1.5-1,2.8-1,3.8c0,0.5,0.2,1.1,0.4,1.8c0.1,0.4,0.3,1,0.6,1.8
				c0.6,2,0.6,5,0.1,8.8c-0.6,4.2-0.8,7.2-0.5,9c0.2,1.6,1,3.8,2.2,6.6c0.6,1.3,1.1,2.5,1.6,3.6c0.1,0.1,0.1,0.1,0.2,0.2
				c3.3,5.4,7.7,10.3,13,14.7c0.5,0.4,1.1,0.9,1.7,1.4c-0.5-2.2-0.9-4.4-1.3-6.7c-0.8-4.9-1.4-9.5-1.8-13.8l-1.3-6.9l-0.8-0.3
				c-0.2-7.6-0.3-15.1-0.3-22.7c0-5.1,0.7-10.2,2.1-15.1C79,92.3,80.1,89.5,81.5,86.9 M201.4,61.4c-0.8-0.3-1.8-0.7-3.2-1.1l1.7,1.8
				c0.9,0.8,1.9,1.9,3,3.3c13.3,14,21.7,34.4,25.2,61.3c0.1,0.9,0.2,1.8,0.3,2.7c0.1,0.3,0.1,0.7,0.2,1.1c0.1,1.4,0.2,2.9,0.3,4.3
				c0.1,3.3,0.1,6.6,0.1,9.9c0,1.1-0.1,2.3-0.2,3.4c0,0.7-0.1,1.5-0.1,2.3l4.8-4c0.4-1.3,0.7-2.6,1.1-3.8c1.7-5.6,2.5-11.3,2.4-17.1
				c0-0.7-0.1-1.5-0.1-2.2c0.9-1,1.3-2.2,1.1-3.5c-0.1-1.1-0.2-1.7-0.2-1.8c0-0.1-0.3-0.6-0.9-1.8c-1.1-1.9-1.6-3-1.8-3.3l-1.6-1.8
				c-0.4-1.8-0.8-3.5-1.3-5.3c-0.1-0.5-0.3-1-0.5-1.4c-0.2-0.5-0.3-1.1-0.3-1.7c0-3.6-0.2-6.2-0.5-7.7c-0.5-2-1.6-4.3-3.3-6.9
				c-0.7-1.2-1.6-3.4-2.7-6.8c-0.5-1.4-1.2-2.5-2.3-3.3c-1.4-0.9-2.7-1.8-3.8-2.5c-1.8-1.2-3.1-2.2-4-2.9c-1.1-1.1-2.1-2.3-3-3.8
				c0-0.1-1.8-1.3-5.3-3.6C203.1,62.6,201.4,61.4,201.4,61.4z"/>
		</g>
	</g>
</g>
<g transform="matrix( 0.0435943603515625, -0.83966064453125, 0.66949462890625, 0.022247314453125, -62.5,259.15) ">
	<g>
		<g id="Layer0_13_MEMBER_0_MEMBER_1_FILL">
			<path class="st17" d="M169.3,182.8l-5.9,0.3c-6.6-0.1-13.1,1-19.8,3.4c-8.5,3.1-14.5,7.9-17.9,14.4c-0.4,1.9-0.7,3.9-0.8,6
				c-0.1,0.4-0.1,0.7-0.1,1.2l3.5-2.1c2.8-1.8,5.3-3.7,7.6-5.7l1.9-5.1l-0.3,5.1c1,0.1,3.1-0.3,6.1-1.1c3.3-0.9,5.7-1.4,7.2-1.5
				l-0.4-0.4c1.2-0.1,3.3,0,6.4,0.3c3,0.3,5.5,0.3,7.4,0c1.1-0.1,3.5-0.3,7.1-0.5c3-0.1,5.2-0.3,6.5-0.5l0.1,0.3
				c1.9-0.3,4.4-0.6,7.5-1c2.4-0.3,4.4-0.6,5.9-0.9l-1.7-5.4l3.3,7.2c3.8,2.5,6.7,3.7,8.5,3.6c-0.2-1.5-0.5-2.9-0.9-4.3
				c-4.2-5.3-10.1-9-17.9-11.2C178.6,183.5,174.1,182.9,169.3,182.8z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_14_MEMBER_0_MEMBER_0_FILL">
			<path class="st22" d="M138.9,146.9c-3.1-5.8-6.7-8.7-10.8-8.7c-4.2,0-7.8,2.9-10.7,8.7c-0.2,0.4-0.3,0.8-0.4,1.2
				c-0.2,1.3,0.4,1.8,1.8,1.3c3-4.5,6.3-6.7,9.8-6.6c3.3,0,6.3,2.5,9,7.5c1,0.7,1.7,0.3,2.1-1.4
				C139.3,148.3,139.1,147.7,138.9,146.9z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_14_MEMBER_0_MEMBER_1_FILL">
			<path class="st22" d="M197.3,138.9c-4.2,0-7.8,2.9-10.6,8.7c-0.3,0.4-0.5,0.8-0.6,1.2c-0.1,1.3,0.6,1.8,2.1,1.3
				c2.9-4.5,6.1-6.7,9.4-6.6c3.5,0,6.5,2.5,9.1,7.5c1,0.7,1.7,0.3,2.1-1.4c-0.3-0.6-0.6-1.3-0.9-2.1
				C204.9,141.8,201.3,138.9,197.3,138.9z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_14_MEMBER_1_MEMBER_0_FILL">
			<path class="st19" d="M100.8,152.4l0.1-0.1c0,0-0.1-0.1-0.2-0.2C100.7,152.2,100.7,152.3,100.8,152.4L100.8,152.4 M144.3,121.3
				c-4.3-4.5-9.6-7.3-15.9-8.3l0,0l-27.8,38.8l0,0.1c0.1,0.1,0.1,0.2,0.1,0.3c0.1,0.1,0.1,0.1,0.2,0.2c0.9,1.3,2,2.5,3.2,3.6
				l25.9-40.9l2.9,1.1l-18.1,45.9l-9.6-5c2.6,2.2,5.5,3.9,8.8,5.1c3.3,1.2,6.8,1.7,10.5,1.5c7.5-0.2,13.8-2.9,19.1-8
				c5.2-5.2,7.7-11.3,7.5-18.3C150.8,131.2,148.6,125.8,144.3,121.3 M130.8,109.7c-1.6-0.1-2.8-0.2-3.6-0.2
				c-8.7,0-16.1,3.1-22.3,9.3c-6.2,6.2-9.3,13.6-9.3,22.3c0,3.3,0.5,6.4,1.4,9.5l2.2-0.1h0.1c0.3,0,0.6,0.2,0.9,0.6
				c0.2,0.3,0.3,0.5,0.4,0.8l27.8-38.8l0,0L130.8,109.7z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_14_MEMBER_1_MEMBER_1_FILL">
			<path class="st19" d="M211.5,146.9l0.6,0.1c1.3,0.3,2.2,1.1,2.8,2.6c0.6,1.5,0.8,3.3,0.4,5.2c-0.3,1.7-1,3.1-2,4.2
				c-0.1,0.1-0.1,0.1-0.1,0.1c-1,1.1-2.1,1.7-3.3,1.7l-0.6-0.1c-0.9-0.2-1.7-0.8-2.3-1.7c0,0-0.1-0.1-0.1-0.1
				c-0.3-0.5-0.5-1-0.6-1.5l-5,12.3c2.2,0.6,4.6,0.9,7.1,0.9c7.4,0,13.7-2.6,18.9-7.8c1-1,1.8-2,2.6-3c0-0.3,0.1-0.6,0.1-0.9
				c0.1-0.4,0.1-0.8,0.1-1.1c0.8-6.1,1.2-12.5,1.2-19c0-2.9-0.1-5.9-0.2-9c-0.3-0.5-0.6-0.9-1-1.3c-0.2-0.3-0.5-0.7-0.8-1
				c-0.7-0.8-1.4-1.6-2.1-2.4c-1.8-1.8-3.8-3.4-5.9-4.6l-10.8,26.5C210.8,146.9,211.2,146.9,211.5,146.9 M213.2,117.7
				c-1.5-0.3-3.1-0.4-4.8-0.4c-1.7,0-3.3,0.1-4.8,0.4c-1.8,0.3-3.6,0.8-5.3,1.6c-3.2,1.3-6.1,3.3-8.8,5.9c-1.2,1.2-2.3,2.4-3.2,3.7
				c-3.1,4.4-4.7,9.4-4.7,15.1c0,4.8,1.1,9.2,3.4,13.1l1.3-1.8l11.9-16.6l15.2-21.1C213.4,117.7,213.3,117.7,213.2,117.7 M217.9,119
				c-0.6-0.2-1.3-0.5-1.9-0.7l-29.6,40.8c0.2,0.2,0.4,0.4,0.6,0.7c0.9,1.1,1.6,1.9,2.2,2.6c0.1,0.2,0.3,0.3,0.4,0.4L217.9,119z"/>
		</g>
	</g>
</g>
<g transform="matrix( 0.8516845703125, -0.0257568359375, 0.0257568359375, 0.8516845703125, -134.7,-159.1) ">
	<g>
		<g id="Layer0_14_MEMBER_1_MEMBER_2_MEMBER_0_FILL">
			<path class="st16" d="M356.6,366.7c0.5,0,0.9-0.1,1.2-0.5c0.4-0.4,0.6-0.8,0.7-1.4c0.1-0.5-0.1-1-0.4-1.4
				c-4.8-6.2-10.3-9.6-16.4-10.1c-4-0.3-8.1,0.7-12.1,3.1c-2.1,1.2-3.8,2.4-5.1,3.8c-0.4,0.4-0.6,0.8-0.6,1.4c0,0.5,0.1,1,0.5,1.5
				c0.4,0.4,0.8,0.6,1.3,0.6c0.5,0,0.9-0.2,1.3-0.5c1.1-1.1,2.6-2.1,4.3-3.1c3.5-2,6.9-2.9,10.3-2.6c5,0.3,9.6,3.2,13.7,8.6
				C355.5,366.5,356,366.7,356.6,366.7z"/>
		</g>
	</g>
</g>
<g transform="matrix( 0.8516693115234375, -0.0257568359375, -0.04534912109375, 0.85382080078125, -110.85,-165.55) ">
	<g>
		<g id="Layer0_14_MEMBER_1_MEMBER_2_MEMBER_1_FILL">
			<path class="st16" d="M240.5,368.1c-0.3,0-0.6,0.3-0.8,0.6c-0.2,0.4-0.2,0.8-0.1,1.4c0.1,0.6,0.3,1,0.6,1.4
				c0.3,0.4,0.6,0.6,0.9,0.6l25.2-1l0.1-0.1c0.3,0,0.6-0.2,0.8-0.6c0.1-0.4,0.2-0.8,0.1-1.4c-0.1-0.5-0.3-1-0.6-1.4
				c-0.3-0.4-0.6-0.5-0.9-0.5L240.5,368.1z"/>
		</g>
	</g>
</g>
<g transform="matrix( 0.8516845703125, -0.0257568359375, 0.0257568359375, 0.8516845703125, -134.7,-159.1) ">
	<g>
		<g id="Layer0_14_MEMBER_1_MEMBER_2_MEMBER_2_FILL">
			<path class="st16" d="M386.2,334c-8.5,0-15.7,3-21.7,9c-6,6-9,13.2-9,21.7c0,8.5,3,15.7,9,21.7c6,6,13.2,9,21.7,9
				c8.5,0,15.7-3,21.7-9c6-6,9-13.2,9-21.7c0-8.5-3-15.7-9-21.7C401.9,337,394.7,334,386.2,334 M367.4,345.9
				c5.2-5.2,11.5-7.8,18.9-7.8c7.3,0,13.6,2.6,18.8,7.9c5.2,5.2,7.8,11.5,7.8,18.8s-2.6,13.6-7.8,18.9s-11.5,7.8-18.9,7.8
				s-13.6-2.6-18.8-7.8c-5.2-5.2-7.9-11.5-7.9-18.9C359.5,357.4,362.2,351.1,367.4,345.9z"/>
		</g>
	</g>
</g>
<g transform="matrix( 0.8516845703125, -0.0257568359375, 0.0257568359375, 0.8516845703125, -134.7,-159.1) ">
	<g>
		<g id="Layer0_14_MEMBER_1_MEMBER_2_MEMBER_3_FILL">
			<path class="st16" d="M317,334.3c-6.5-6.5-14.3-9.7-23.4-9.7c-9.1,0-16.9,3.2-23.3,9.7c-6.5,6.4-9.7,14.2-9.7,23.4
				c0,9.1,3.2,16.9,9.7,23.3c6.4,6.5,14.2,9.7,23.4,9.7c9.1,0,16.9-3.2,23.3-9.7c6.5-6.5,9.7-14.2,9.7-23.3
				C326.7,348.6,323.5,340.8,317,334.3 M293.6,328.6c8,0,14.8,2.9,20.5,8.6c5.7,5.7,8.5,12.5,8.5,20.5c0,8-2.8,14.8-8.5,20.5
				c-5.7,5.7-12.5,8.5-20.5,8.5c-8,0-14.9-2.8-20.5-8.5c-5.7-5.7-8.5-12.5-8.5-20.5c0-8,2.8-14.8,8.5-20.5
				C278.8,331.5,285.6,328.6,293.6,328.6z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_14_MEMBER_1_MEMBER_3_FILL">
			<path class="st23" d="M134.2,114.4l-2.9-1.3l-27,42.7l1.1,1.2l9.4,5.6L134.2,114.4 M129.8,110.8l-2.3-0.4l-25.9,36.9
				c0.1,0.4,0,0.8-0.2,1.1c-0.1,0.3-0.3,0.5-0.6,0.6h-0.4l-0.4,0.6l1,1.8L129.8,110.8z"/>
		</g>
	</g>
</g>
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
	<g>
		<g id="Layer0_14_MEMBER_1_MEMBER_4_FILL">
			<path class="st23" d="M218.9,123c-1-0.6-2-1.1-3.1-1.5l-25.9,39.9v0.1c3.1,3.1,6.7,5.2,10.8,6.3L218.9,123 M214.1,120.9
				c-0.8-0.2-1.5-0.4-2.3-0.6l-25.9,35.9c0.3,0.6,0.7,1.2,1.2,1.8L214.1,120.9z"/>
		</g>
	</g>
</g>
<g transform="matrix( 4.2616424560546875, -0.65338134765625, 0.68829345703125, 4.4892578125, -96.15,-21.7) ">
	<g>
		<g id="Layer0_15_FILL">
			<path class="st20" d="M60.5,39.1c0.8-0.3,1.6-0.5,2.2-0.5c1.9-0.1,3.9,0.7,5.9,2.4c-0.4-1.2-1-2.1-1.8-2.8
				c-0.7-0.6-1.5-1-2.5-1.3c-1.1-0.2-2.2,0-3.2,0.5c-0.9,0.6-1.6,1.4-2.2,2.3L60.5,39.1z"/>
		</g>
	</g>
</g>
</svg>
