<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Games;

use App\Campus\Games\DoubleOrNothing;
use App\Campus\Games\GameStrategyInterface;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use Doctrine\ORM\EntityManagerInterface;

class GameDoubleOrNothingStrategyTest extends TestGameStrategyInterface
{
    public function getStrategy(): GameStrategyInterface
    {
        return new DoubleOrNothing($this->createMock(EntityManagerInterface::class));
    }

    public static function getValidData(): \Generator
    {
        $chapter = new Chapter();
        $chapterType = new ChapterType();
        $chapterType->setCode('999');
        $chapterType->setPercentageCompleted('0.75');
        $chapter->setType($chapterType);

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 2,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 3,
                'correct' => true,
            ],
        ];

        $attempts = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 2,
                'correct' => true,
            ],
        ];

        yield 'result ko, no chapter passed' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => null,
            'expectedPoints' => null,
        ];

        yield 'result ko, no total time passed' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => null,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no answers passed' => [
            'data' => ['answers' => null,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ok, no attempts passed' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => null,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.53,
        ];

        yield 'result ok at level 2. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 2,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.47,
        ];

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 2,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 3,
                'correct' => true,
            ], [
                'id' => 3,
                'questionId' => 3,
                'time' => 3,
                'correct' => true,
            ], [
                'id' => 4,
                'questionId' => 4,
                'time' => 3,
                'correct' => true,
            ],
        ];

        yield 'result ok at level 4. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.76,
        ];

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 2,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 3,
                'correct' => true,
            ], [
                'id' => 3,
                'questionId' => 3,
                'time' => 3,
                'correct' => true,
            ], [
                'id' => 4,
                'questionId' => 4,
                'time' => 3,
                'correct' => true,
            ],
            [
                'id' => 5,
                'questionId' => 5,
                'time' => 3,
                'correct' => true,
            ],
        ];

        $attempts = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
            ],
        ];

        yield 'result ok at level 5. With 1 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 1,
        ];
    }
}
