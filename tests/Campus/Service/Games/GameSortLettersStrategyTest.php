<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Games;

use App\Campus\Games\GameStrategyInterface;
use App\Campus\Games\SortLetters;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use Doctrine\ORM\EntityManagerInterface;

class GameSortLettersStrategyTest extends TestGameStrategyInterface
{
    public function getStrategy(): GameStrategyInterface
    {
        return new SortLetters($this->createMock(EntityManagerInterface::class));
    }

    public static function getValidData(): \Generator
    {
        $chapter = new Chapter();
        $chapterType = new ChapterType();
        $chapterType->setCode('999');
        $chapterType->setPercentageCompleted('0.75');
        $chapter->setType($chapterType);

        $answers = [
            [
                'questionId' => 1,
                'attempts' => [
                    [
                        'word' => 'gata',
                        'time' => 10,
                        'correct' => false,
                    ],
                    [
                        'word' => 'gato',
                        'time' => 5,
                        'correct' => false,
                    ],
                ],
            ],
        ];

        $attempts = [
            [
                'questionId' => 1,
                'attempts' => [
                    [
                        'word' => 'gata',
                        'time' => 10,
                        'correct' => false,
                    ],
                    [
                        'word' => 'gato',
                        'time' => 5,
                        'correct' => false,
                    ],
                ],
            ],
        ];

        yield 'result ko, no chapter passed' => [
            'data' => ['answers' => $answers,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => null,
            'expectedPoints' => null,
        ];

        yield 'result ko, no total time passed' => [
            'data' => ['answers' => $answers,
                'timeTotal' => null,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no attempts passed' => [
            'data' => ['answers' => $answers,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => null,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no answers passed' => [
            'data' => ['answers' => null,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko No correct answers.' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        $answers = [
            [
                'questionId' => 1,
                'attempts' => [
                    [
                        'word' => 'gata',
                        'time' => 10,
                        'correct' => false,
                    ],
                    [
                        'word' => 'gato',
                        'time' => 5,
                        'correct' => true,
                    ],
                ],
            ],
        ];

        yield 'result ok with 1 fail. With 1 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 30,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.86,
        ];

        yield 'result ok with 1 fail and less time. With 1 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 16,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.80,
        ];
    }
}
