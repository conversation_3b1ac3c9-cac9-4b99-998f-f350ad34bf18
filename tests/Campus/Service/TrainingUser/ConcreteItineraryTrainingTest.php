<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\TrainingUser;

use App\Campus\Service\TrainingUser\ConcreteItineraryTraining;
use App\Entity\CourseSection;
use App\Entity\User;
use App\Service\Api\ApiCourseService;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\ItineraryCourseMother;
use App\Tests\Mother\Entity\ItineraryMother;
use App\Tests\Mother\Entity\ItineraryUserMother;
use App\Tests\Mother\Entity\UserMother;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Security\Core\Security;

class ConcreteItineraryTrainingTest extends TestCase
{
    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->settingsService = $this->createMock(SettingsService::class);
        $this->apiCourseService = $this->createMock(ApiCourseService::class);
        $this->security = $this->createMock(Security::class);

        // $this->user = $this->getDefaultUser();
        $this->user = UserMother::create();
        $this->security->method('getUser')->willReturn($this->user);

        $this->concreteItineraryTraining = new ConcreteItineraryTraining(
            $this->entityManager,
            $this->settingsService,
            $this->apiCourseService,
            $this->security
        );
    }

    /**
     * @throws Exception
     */
    public function testGetTrainingWithNoItineraries(): void
    {
        $courseSection = $this->createMock(CourseSection::class);

        $result = $this->concreteItineraryTraining->getTraining($courseSection);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * @throws Exception
     */
    #[DataProvider('getTrainingDataProvider')]
    public function testGetTraining(
        bool $itineraryActive,
        bool $hasCourse,
        bool $hasCourseActive,
        array $courseToSend,
        array $expectedArrayResult
    ): void {
        $itinerary = ItineraryMother::create(
            id: 1,
            name: 'Test Itinerary',
            active: $itineraryActive
        );

        if ($hasCourse) {
            $course = CourseMother::create(
                id: 1,
                name: 'Test Course',
                active: $hasCourseActive
            );
            $itineraryCourse = ItineraryCourseMother::create(
                itinerary: $itinerary,
                course: $course,
                position: 1
            );
            $itinerary->addItineraryCourse($itineraryCourse);
        }

        $itineraryUser = ItineraryUserMother::create(
            itinerary: $itinerary,
            user: $this->user,
        );

        $this->user->addItineraryUser($itineraryUser);

        // Configure API course service
        $this->apiCourseService->method('courseToSend')->willReturn($courseToSend);

        $courseSection = $this->createMock(CourseSection::class);

        $result = $this->concreteItineraryTraining->getTraining($courseSection);

        $this->assertIsArray($result);
        $this->assertEquals($result, $expectedArrayResult);

        if (!empty($expectedArrayResult)) {
            $this->assertCount(1, $result);
            $this->assertNotEmpty($result[0]['courses']);
            $this->assertEquals('Test Itinerary', $result[0]['courses'][0]['typeItinerary']['name']);
        }
    }

    public static function getTrainingDataProvider(): \Generator
    {
        yield 'Itinerary not active' => [
            'itineraryActive' => false,
            'hasCourse' => true,
            'hasCourseActive' => true,
            'courseToSend' => [],
            'expectedArrayResult' => [],
        ];
        yield 'Itinerary with no course' => [
            'itineraryActive' => true,
            'hasCourse' => false,
            'hasCourseActive' => false,
            'courseToSend' => [],
            'expectedArrayResult' => [],
        ];
        yield 'Itinerary with course not active' => [
            'itineraryActive' => true,
            'hasCourse' => true,
            'hasCourseActive' => false,
            'courseToSend' => [],
            'expectedArrayResult' => [],
        ];
        yield 'Itinerary with course active and no course to send' => [
            'itineraryActive' => true,
            'hasCourse' => true,
            'hasCourseActive' => true,
            'courseToSend' => [],
            'expectedArrayResult' => [],
        ];
        yield 'Itinerary with course active' => [
            'itineraryActive' => true,
            'hasCourse' => true,
            'hasCourseActive' => true,
            'courseToSend' => [
                [
                    'id' => 1,
                    'name' => 'Test Course',
                    'new' => true,
                ],
            ],
            'expectedArrayResult' => [
                [
                    'id' => 1,
                    'name' => 'Test Itinerary',
                    'type' => 'ITINERARY',
                    'courses' => [
                        [
                            [
                                'id' => 1,
                                'name' => 'Test Course',
                                'new' => true,
                            ],
                            'typeItinerary' => [
                                'id' => 1,
                                'name' => 'Test Itinerary',
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }
}
