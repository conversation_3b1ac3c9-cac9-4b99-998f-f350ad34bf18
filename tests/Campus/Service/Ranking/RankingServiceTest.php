<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Ranking;

use App\Campus\Service\Ranking\RankingService;
use App\Entity\User;
use App\Repository\UserCourseRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManager;
use Monolog\Test\TestCase;
use Symfony\Component\Security\Core\Security;

class RankingServiceTest extends TestCase
{
    private $mockUserRepo;
    private $mockUserCourseRepo;   
    private $mockEm;
    private $mockSecurity;
    private $mockSettings;
    private $mockUser;

    public function setUp(): void
    {
        parent::setUp();

        $this->mockUserRepo = $this->createMock(UserRepository::class);
        $this->mockUserCourseRepo = $this->createMock(UserCourseRepository::class);       
        $this->mockEm = $this->createMock(EntityManager::class);
        $this->mockSecurity = $this->createMock(Security::class);
        $this->mockSettings = $this->createMock(SettingsService::class);

        $this->mockUser = $this->createMock(User::class);
        $this->mockSecurity->method('getUser')->willReturn($this->mockUser);   
    }

    private function createRankingService(): RankingService
    {
        return new RankingService(
            $this->mockEm,
            $this->mockSettings,
            $this->mockSecurity,
            $this->mockUserRepo,
            $this->mockUserCourseRepo
        );
    }

    private function getPodiumData()
    {
        return [
            ['id' => 1, 'name' => 'John Doe', 'points' => 150],
            ['id' => 2, 'name' => 'Jane Smith', 'points' => 140],
            ['id' => 3, 'name' => 'Carlos Silva', 'points' => 130],
        ];
    }

    public function testGetRankingStructure()
    {
        $service = $this->createRankingService();

        $result = $service->getRanking();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('offsets', $result);
        $this->assertArrayHasKey('podium', $result);
        $this->assertArrayHasKey('users', $result);
        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('userFinished', $result);
        $this->assertArrayHasKey('filters', $result);
    }

    public function testGetRankingData()
    {
        $podium = $this->getPodiumData();

        $users = [
            ['id' => 4, 'name' => 'Alice Brown', 'points' => 120],
            ['id' => 5, 'name' => 'Bob Johnson', 'points' => 110],
        ];

        $this->mockUserRepo->method('getRankingPodium')->willReturn($podium);
        $this->mockUserRepo->method('getRankingPoints')->willReturn($users);

        $service = $this->createRankingService();

        $result = $service->getRanking();

        $this->assertCount(3, $result['podium']);
        $this->assertEquals($podium, $result['podium']);

        $this->assertCount(2, $result['users']);
        $this->assertEquals($users, $result['users']);
    }

    public function testGetUserRankingPositionAndFinishedCourses()
    {
        $userRankingPosition = 5;
        $userFinishedCourses = 10;

        $this->mockUserRepo->method('getRankingPositionByUser')->willReturn($userRankingPosition);
        $this->mockUserCourseRepo->method('finishedCourseByUser')->willReturn($userFinishedCourses);

        $service = $this->createRankingService();

        $result = $service->getRanking();

        $this->assertEquals($userFinishedCourses, $result['userFinished']);
        $this->assertEquals($userRankingPosition, $result['offsets']['self']);
    }

    public function testGetRankingWithSettingsLimit()
    {
        $limit = 3;
        $podium = $this->getPodiumData();

        $this->mockUserRepo->method('getRankingPodium')->willReturn($podium);
        $this->mockUserRepo->method('getRankingPoints')->willReturn($podium);
        $this->mockUserRepo->method('getRankingPositionByUser')->willReturn(2);

        $service = new RankingService(
            $this->mockEm,
            $this->mockSettings,
            $this->mockSecurity,
            $this->mockUserRepo,
            $this->mockUserCourseRepo
        );

        $result = $service->getRanking();

        $this->assertIsArray($result, 'The result must be an array');
        $this->assertArrayHasKey('users', $result, 'should contain the key "users"');
        $this->assertCount($limit, $result['users'], 'The users list should respect the limit defined in the settings');
        $this->assertEquals(
            \array_slice($podium, 0, $limit),
            $result['users'],
            'The ranking data must be limited to the configured amount'
        );
    }

    public function testGetRankingWithoutUsers()
    {
        $podium = [];

        $this->mockUserRepo->method('getRankingPodium')->willReturn($podium);
        $this->mockUserRepo->method('getRankingPoints')->willReturn($podium);
        $this->mockUserRepo->method('getRankingPositionByUser')->willReturn(null);

        $service = new RankingService(
            $this->mockEm,
            $this->mockSettings,
            $this->mockSecurity,
            $this->mockUserRepo,
            $this->mockUserCourseRepo
        );

        $result = $service->getRanking();

        $this->assertIsArray($result, 'The result must be an array');
        $this->assertArrayHasKey('users', $result, 'should contain the key "users"');
        $this->assertEmpty($result['users'], 'The users list should be empty when there are no users in the ranking');
        $this->assertArrayHasKey('podium', $result, 'should contain the key "podium"');
        $this->assertEmpty($result['podium'], 'The podium list should be empty when there are no users in the ranking');
        $this->assertNull($result['offsets']['self'], 'The user position should be null when there are no users in the ranking');
    }  

    public function testGetRankingWhenFilterInRankingIsDisabled()
    {
        $podium = $this->getPodiumData();

        $this->mockSettings
            ->method('get')
            ->willReturnMap([
                ['app.use.filter_in_ranking', false],
            ]);

        $this->mockUserRepo->method('getRankingPodium')->with([])->willReturn($podium);
        $this->mockUserRepo->method('getRankingPoints')->with([])->willReturn($podium);

        $this->mockUserCourseRepo
            ->method('finishedCourseByUser')
            ->with($this->mockUser)
            ->willReturn(5);

        $this->mockSecurity
            ->method('getUser')
            ->willReturn($this->mockUser);

        $service = $this->createRankingService();

        $result = $service->getRanking();

        $this->assertIsArray($result, 'The result must be an array');
        $this->assertArrayHasKey('users', $result, 'The result should contain the key "users"');
        $this->assertArrayHasKey('podium', $result, 'The result should contain the key "podium"');
        $this->assertArrayHasKey('offsets', $result, 'The result should contain the key "offsets"');

        $this->assertEmpty(
            $result['filters'],
            'The filters should be empty when app.use.filter_in_ranking is disabled'
        );

        $this->assertEquals(
            $podium,
            $result['users'],
            'The users list should include all users without applying filters'
        );
        $this->assertEquals(
            $podium,
            $result['podium'],
            'The podium should include all users without applying filters'
        );

        $this->assertEmpty(
            $result['filters'],
            'The filters should be empty when app.use.filter_in_ranking is disabled'
        );
    }

    public function testGetRankingHandlesEmptyPodium()
    {
        $this->mockUserRepo->method('getRankingPodium')->willReturn([]);
        $this->mockUserRepo->method('getRankingPoints')->willReturn([]);

        $service = $this->createRankingService();

        $result = $service->getRanking();

        $this->assertEmpty($result['podium']);
        $this->assertEmpty($result['users']);
    }

    public function testGetRankingHandlesException()
    {
        $this->mockUserRepo->method('getRankingPodium')->willThrowException(new \Exception('Error in obtaining the podium'));

        $service = $this->createRankingService();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Error in obtaining the podium');

        $service->getRanking();
    }
}
