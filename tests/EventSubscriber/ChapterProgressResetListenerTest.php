<?php

declare(strict_types=1);

namespace App\Tests\EventSubscriber;

use App\Entity\Chapter;
use App\Event\ChapterContentChangedEvent;
use App\EventSubscriber\ChapterProgressResetListener;
use App\Service\Chapter\ChapterProgressResetService;
use PHPUnit\Framework\TestCase;

/**
 * Test class for ChapterProgressResetListener
 * Tests the functionality of the listener when chapter content is changed.
 */
class ChapterProgressResetListenerTest extends TestCase
{
    private $progressResetService;
    private $listener;

    protected function setUp(): void
    {
        $this->progressResetService = $this->createMock(ChapterProgressResetService::class);
        $this->listener = new ChapterProgressResetListener($this->progressResetService);
    }

    public function testGetSubscribedEvents(): void
    {
        $events = ChapterProgressResetListener::getSubscribedEvents();

        $this->assertArrayHasKey('chapter.content_changed', $events);
        $this->assertEquals(['onChapterContentChanged', 0], $events['chapter.content_changed']);
    }

    public function testOnChapterContentChanged(): void
    {
        // Create a mock chapter
        $chapter = $this->createMock(Chapter::class);

        // Create the event
        $event = new ChapterContentChangedEvent($chapter);

        // Expect the service to be called with the chapter
        $this->progressResetService
            ->expects($this->once())
            ->method('resetCurrentProgress')
            ->with($chapter);

        // Call the event handler
        $this->listener->onChapterContentChanged($event);
    }
}
