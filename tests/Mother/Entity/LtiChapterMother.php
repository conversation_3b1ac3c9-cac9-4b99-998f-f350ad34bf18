<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Chapter;
use App\Entity\LtiChapter;

class LtiChapterMother
{
    public const string DEFAULT_IDENTIFIER = 'identifier';

    public static function create(
        ?int $id = null,
        ?string $identifier = null,
        ?Chapter $chapter = null,
    ): LtiChapter {
        $lti = new LtiChapter();
        if (null !== $id) {
            $lti->setId($id);
        }

        $lti->setIdentifier($identifier ?? self::DEFAULT_IDENTIFIER);
        $lti->setChapter($chapter);

        return $lti;
    }
}
