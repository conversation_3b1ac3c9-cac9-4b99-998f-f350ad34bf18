<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Announcement;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;

class UserCourseMother
{
    public static function create(
        ?int $id = null,
        ?User $user = null,
        ?Course $course = null,
        ?Announcement $announcement = null,
        ?\DateTimeImmutable $startedAt = null,
        ?\DateTimeImmutable $finishedAt = null,
        ?\DateTimeImmutable $valuedAt = null,
        int $timeSpent = 0,
        float $points = 0,
    ): UserCourse {
        $userCourse = new UserCourse();
        if (null !== $id) {
            $userCourse->setId($id);
        }
        $userCourse->setUser($user)
            ->setCourse($course)
            ->setAnnouncement($announcement)
            ->setStartedAt($startedAt ?? new \DateTimeImmutable())
            ->setFinishedAt($finishedAt)
            ->setValuedAt($valuedAt)
            ->setTimeSpent($timeSpent)
            ->setPoints($points);

        return $userCourse;
    }

    public static function createUserCourseChapter(
        ?int $id = null,
        ?UserCourse $userCourse = null,
        ?Chapter $chapter = null,
        ?\DateTimeImmutable $startedAt = null,
        ?\DateTimeImmutable $finishedAt = null,
        ?string $data = null,
        ?int $timeSpent = 0,
        ?float $points = 0,
        ?\DateTimeImmutable $deletedAt = null,
    ): UserCourseChapter {
        $userCourseChapter = new UserCourseChapter();
        if (null !== $id) {
            $userCourseChapter->setId($id);
        }

        $userCourseChapter->setUserCourse($userCourse);
        $userCourseChapter->setChapter($chapter);
        $userCourseChapter->setStartedAt($startedAt ?? new \DateTimeImmutable());
        $userCourseChapter->setFinishedAt($finishedAt);
        $userCourseChapter->setData($data);
        $userCourseChapter->setTimeSpent($timeSpent);
        $userCourseChapter->setPoints($points);
        $userCourseChapter->setDeletedAt($deletedAt);

        return $userCourseChapter;
    }
}
