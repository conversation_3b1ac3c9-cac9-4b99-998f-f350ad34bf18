<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\CourseCategory;

class CourseCategoryMother
{
    public const string DEFAULT_NAME = 'Category 1';
    public const string DEFAULT_DESCRIPTION = 'Category 1 description';

    public static function create(
        ?int $id = null,
        ?CourseCategory $parent = null,
        ?string $name = null,
        ?string $description = null,
        bool $active = true,
        int $sort = 1,
        ?string $orderType = null,
        ?array $orderProperties = null,
    ): CourseCategory {
        $courseCategory = new CourseCategory();
        if (null !== $id) {
            $courseCategory->setId($id);
        }

        $courseCategory->setParent($parent);
        $courseCategory->setName($name ?? self::DEFAULT_NAME);
        $courseCategory->setDescription($description ?? self::DEFAULT_DESCRIPTION);
        $courseCategory->setActive($active);
        $courseCategory->setSort($sort);
        $courseCategory->setOrderType($orderType);
        $courseCategory->setOrderProperties($orderProperties);

        return $courseCategory;
    }
}
