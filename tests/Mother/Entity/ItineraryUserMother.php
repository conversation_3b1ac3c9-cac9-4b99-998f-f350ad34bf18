<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Itinerary;
use App\Entity\ItineraryUser;
use App\Entity\User;

class ItineraryUserMother
{
    public static function create(
        ?int $id = null,
        ?Itinerary $itinerary = null,
        ?User $user = null,
    ): ItineraryUser {
        $itineraryUser = new ItineraryUser();

        if (null !== $id) {
            $itineraryUser->setId($id);
        }

        $itineraryUser->setItinerary($itinerary ?? ItineraryMother::create())
            ->setUser($user ?? UserMother::create());

        return $itineraryUser;
    }
}
