<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Course;
use App\Entity\Itinerary;
use App\Entity\ItineraryCourse;

class ItineraryCourseMother
{
    public static function create(
        ?int $id = null,
        ?Itinerary $itinerary = null,
        ?Course $course = null,
        ?int $position = null,
    ): ItineraryCourse {
        $itineraryCourse = new ItineraryCourse();

        if (null !== $id) {
            $itineraryCourse->setId($id);
        }

        $itineraryCourse->setItinerary($itinerary ?? ItineraryMother::create())
            ->setCourse($course ?? CourseMother::create())
            ->setPosition($position);

        return $itineraryCourse;
    }
}
