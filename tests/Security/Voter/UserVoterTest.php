<?php

declare(strict_types=1);

namespace App\Tests\Security\Voter;

use App\Entity\User;
use App\Security\Voter\UserVoter;
use App\Tests\Mother\Entity\UserMother;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\VoterInterface;
use Symfony\Component\Security\Core\Security;

class UserVoterTest extends TestCase
{
    private Security $security;
    private UserVoter $voter;
    private TokenInterface $token;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->security = $this->createMock(Security::class);
        $this->voter = new UserVoter($this->security);
        $this->token = $this->createMock(TokenInterface::class);
    }

    /**
     * @throws Exception
     *
     * @dataProvider userOwnProfileProvider
     */
    public function testUserCanEditOwnProfile(array $userRoles): void
    {
        $user = UserMother::create(id: 1, roles: $userRoles);

        $this->security->method('getUser')->willReturn($user);
        $this->assertEquals(
            VoterInterface::ACCESS_GRANTED,
            $this->voter->vote($this->token, $user, [UserVoter::EDIT])
        );
    }

    public static function userOwnProfileProvider(): \Generator
    {
        yield 'User' => [
            'userRoles' => ['ROLE_USER'],
        ];

        yield 'Tutor' => [
            'userRoles' => ['ROLE_TUTOR'],
        ];

        yield 'Manager' => [
            'userRoles' => ['ROLE_MANAGER'],
        ];

        yield 'Admin' => [
            'userRoles' => ['ROLE_ADMIN'],
        ];
    }

    /**
     * @dataProvider userAccessProvider
     */
    public function testUserAccess(array $userRoles, array $targetUserRoles, string $action, int $access): void
    {
        $user = UserMother::create(id: 1, roles: $userRoles);
        $targetUser = UserMother::create(id: 2, roles: $targetUserRoles);

        // Configure security mock to return the user who edits.
        $this->security->method('getUser')->willReturn($user);
        $this->assertEquals($access, $this->voter->vote($this->token, $targetUser, [$action]));
    }

    public static function userAccessProvider(): \Generator
    {
        yield 'User edits User' => [
            'userRoles' => ['ROLE_USER'],
            'targetUserRoles' => ['ROLE_USER'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'User edits Tutor' => [
            'userRoles' => ['ROLE_USER'],
            'targetUserRoles' => ['ROLE_TUTOR'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'User edits Admin' => [
            'userRoles' => ['ROLE_USER'],
            'targetUserRoles' => ['ROLE_TUTOR'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'Tutor edits User' => [
            'userRoles' => ['ROLE_TUTOR'],
            'targetUserRoles' => ['ROLE_USER'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'Tutor edits tutor' => [
            'userRoles' => ['ROLE_TUTOR'],
            'targetUserRoles' => ['ROLE_TUTOR'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'Tutor edits admin' => [
            'userRoles' => ['ROLE_TUTOR'],
            'targetUserRoles' => ['ROLE_ADMIN'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'Manager edits User' => [
            'userRoles' => ['ROLE_MANAGER'],
            'targetUserRoles' => ['ROLE_USER'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_GRANTED,
        ];

        yield 'Manager edits tutor' => [
            'userRoles' => ['ROLE_MANAGER'],
            'targetUserRoles' => ['ROLE_TUTOR'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_GRANTED,
        ];

        yield 'Manager edits admin' => [
            'userRoles' => ['ROLE_MANAGER'],
            'targetUserRoles' => ['ROLE_ADMIN'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'Admin edits user' => [
            'userRoles' => ['ROLE_ADMIN'],
            'targetUserRoles' => ['ROLE_USER'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_GRANTED,
        ];

        yield 'Admin edits tutor' => [
            'userRoles' => ['ROLE_ADMIN'],
            'targetUserRoles' => ['ROLE_TUTOR'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_GRANTED,
        ];

        yield 'Admin edits admin' => [
            'userRoles' => ['ROLE_ADMIN'],
            'targetUserRoles' => ['ROLE_ADMIN'],
            'action' => UserVoter::EDIT,
            'access' => VoterInterface::ACCESS_GRANTED,
        ];
    }

    #[DataProvider('userImpersonateProvider')]
    public function testUserImpersonate(array $userRoles, array $targetUserRoles, int $access): void
    {
        $user = UserMother::create(id: 1, roles: $userRoles);
        $targetUser = UserMother::create(id: 2, roles: $targetUserRoles);

        $this->security->method('getUser')->willReturn($user);
        $this->assertEquals($access, $this->voter->vote($this->token, $targetUser, [UserVoter::IMPERSONATE]));
    }

    public static function userImpersonateProvider(): \Generator
    {
        yield 'User cannot impersonate self' => [
            'userRoles' => ['ROLE_USER'],
            'targetUserRoles' => ['ROLE_USER'],
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'SuperAdmin can impersonate User' => [
            'userRoles' => ['ROLE_SUPER_ADMIN'],
            'targetUserRoles' => ['ROLE_USER'],
            'access' => VoterInterface::ACCESS_GRANTED,
        ];

        yield 'Admin can impersonate User' => [
            'userRoles' => ['ROLE_ADMIN'],
            'targetUserRoles' => ['ROLE_USER'],
            'access' => VoterInterface::ACCESS_GRANTED,
        ];

        yield 'Admin can impersonate Tutor' => [
            'userRoles' => ['ROLE_ADMIN'],
            'targetUserRoles' => ['ROLE_TUTOR'],
            'access' => VoterInterface::ACCESS_GRANTED,
        ];

        yield 'Admin can impersonate Admin' => [
            'userRoles' => ['ROLE_ADMIN'],
            'targetUserRoles' => ['ROLE_ADMIN'],
            'access' => VoterInterface::ACCESS_GRANTED,
        ];

        yield 'Support can impersonate User' => [
            'userRoles' => ['ROLE_SUPPORT'],
            'targetUserRoles' => ['ROLE_USER'],
            'access' => VoterInterface::ACCESS_GRANTED,
        ];

        yield 'Manager cannot impersonate User' => [
            'userRoles' => ['ROLE_MANAGER'],
            'targetUserRoles' => ['ROLE_USER', 'ROLE_ADMIN'],
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'Manager editor cannot impersonate User' => [
            'userRoles' => ['ROLE_MANAGER_EDITOR'],
            'targetUserRoles' => ['ROLE_USER', 'ROLE_ADMIN'],
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'Creator cannot impersonate User' => [
            'userRoles' => ['ROLE_CREATOR'],
            'targetUserRoles' => ['ROLE_USER'],
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'Tutor cannot impersonate User' => [
            'userRoles' => ['ROLE_TUTOR'],
            'targetUserRoles' => ['ROLE_USER'],
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'User cannot impersonate User' => [
            'userRoles' => ['ROLE_USER'],
            'targetUserRoles' => ['ROLE_TUTOR'],
            'access' => VoterInterface::ACCESS_DENIED,
        ];

        yield 'SuperAdmin can impersonate SuperAdmin' => [
            'userRoles' => ['ROLE_SUPER_ADMIN'],
            'targetUserRoles' => ['ROLE_SUPER_ADMIN', 'ROLE_ADMIN'],
            'access' => VoterInterface::ACCESS_GRANTED,
        ];

        yield 'Admin impersonates user with multiple roles including ROLE_ADMIN' => [
            'userRoles' => ['ROLE_ADMIN'],
            'targetUserRoles' => ['ROLE_USER', 'ROLE_ADMIN'],
            'access' => VoterInterface::ACCESS_GRANTED,
        ];

        yield 'Support can impersonate user with multiple roles including ROLE_ADMIN' => [
            'userRoles' => ['ROLE_SUPPORT'],
            'targetUserRoles' => ['ROLE_USER', 'ROLE_ADMIN'],
            'access' => VoterInterface::ACCESS_GRANTED,
        ];
    }
}
