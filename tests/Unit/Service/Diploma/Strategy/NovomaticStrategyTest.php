<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service\Diploma\Strategy;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\UserCourse;
use App\Repository\AnnouncementUserRepository;
use App\Repository\CourseRepository;
use App\Repository\UserCourseRepository;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\DateFormatter\DateFormatterService;
use App\Service\Diploma\Strategy\NovomaticStrategy;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\UserMother;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class NovomaticStrategyTest extends TestCase
{
    private const int TEST_USER_ID = 456;
    private const int TEST_COURSE_ID = 123;
    private const int TEST_ANNOUNCEMENT_COURSE_ID = 789;

    private EntityManagerInterface|MockObject $entityManager;
    private SettingsService|MockObject $settingsService;
    private AnnouncementConfigurationsService|MockObject $announcementConfigurationsService;
    private DateFormatterService|MockObject $dateFormatterService;
    private NovomaticStrategy $strategy;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->settingsService = $this->createMock(SettingsService::class);
        $this->announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $this->dateFormatterService = $this->createMock(DateFormatterService::class);

        $this->strategy = new NovomaticStrategy(
            $this->entityManager,
            $this->settingsService,
            $this->announcementConfigurationsService,
            $this->dateFormatterService
        );
    }

    /**
     * Test getContentCourseDiploma with different scenarios.
     *
     * @dataProvider courseDataProvider
     */
    public function testGetContentCourseDiploma(
        string $firstName,
        string $lastName,
        string $courseName,
        ?\DateTimeImmutable $valuedAt,
        ?string $expectedFormattedDate,
        string $expectedUserName
    ): void {
        // Arrange
        $user = UserMother::create(
            id: self::TEST_USER_ID,
            firstName: $firstName,
            lastName: $lastName
        );

        $course = CourseMother::create(
            id: self::TEST_COURSE_ID,
            name: $courseName
        );

        $userCourse = null;
        if ($valuedAt) {
            $userCourse = $this->createMock(UserCourse::class);
            $userCourse->method('getValuedAt')->willReturn($valuedAt);
        }

        $courseRepository = $this->createMock(CourseRepository::class);
        $courseRepository->method('find')->with(self::TEST_COURSE_ID)->willReturn($course);

        $userCourseRepository = $this->createMock(UserCourseRepository::class);
        $userCourseRepository->method('findOneBy')
            ->with([
                'user' => self::TEST_USER_ID,
                'course' => self::TEST_COURSE_ID,
            ])
            ->willReturn($userCourse);

        $this->entityManager->method('getRepository')
            ->willReturnMap([
                [Course::class, $courseRepository],
                [UserCourse::class, $userCourseRepository],
            ]);

        if ($expectedFormattedDate) {
            $this->dateFormatterService->method('formatDate')
                ->with($valuedAt, 'medium', $this->anything())
                ->willReturn($expectedFormattedDate);
        }

        $request = [
            'idCourse' => self::TEST_COURSE_ID,
            'date' => '2024-01-15',
        ];

        // Act
        $result = $this->strategy->getContentCourseDiploma($request, $user);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('course', $result);
        $this->assertArrayHasKey('userName', $result);
        $this->assertArrayHasKey('courseName', $result);
        $this->assertArrayHasKey('diplomaDate', $result);

        $this->assertEquals($user, $result['user']);
        $this->assertEquals($course, $result['course']);
        $this->assertEquals($expectedUserName, $result['userName']);
        $this->assertEquals($courseName, $result['courseName']);
        $this->assertEquals($expectedFormattedDate, $result['diplomaDate']);
    }

    public static function courseDataProvider(): \Generator
    {
        yield 'Basic course data without UserCourse' => [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'courseName' => 'Test Course',
            'valuedAt' => null,
            'expectedFormattedDate' => null,
            'expectedUserName' => 'John Doe',
        ];

        yield 'Course with UserCourse valued_at date' => [
            'firstName' => 'Jane',
            'lastName' => 'Smith',
            'courseName' => 'Advanced Course',
            'valuedAt' => new \DateTimeImmutable('2024-01-15'),
            'expectedFormattedDate' => '15/01/2024',
            'expectedUserName' => 'Jane Smith',
        ];
    }

    /**
     * Test getContentAnnouncementDiploma with different scenarios.
     *
     * @dataProvider announcementDataProvider
     */
    public function testGetContentAnnouncementDiploma(
        string $firstName,
        string $lastName,
        string $courseName,
        ?\DateTimeImmutable $dateApproved,
        ?string $announcementUserScenario,
        string $expectedFormattedDate,
        string $expectedUserName
    ): void {
        // Arrange
        $user = UserMother::create(
            id: self::TEST_USER_ID,
            firstName: $firstName,
            lastName: $lastName
        );

        $course = CourseMother::create(
            id: self::TEST_ANNOUNCEMENT_COURSE_ID,
            name: $courseName
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getCourse')->willReturn($course);

        // Create AnnouncementUser mock based on the test scenario
        $announcementUserMock = null;
        if ('with_date' === $announcementUserScenario) {
            $announcementUserMock = $this->createMock(AnnouncementUser::class);
            $announcementUserMock->method('getDateApproved')->willReturn($dateApproved);
        } elseif ('without_date' === $announcementUserScenario) {
            $announcementUserMock = $this->createMock(AnnouncementUser::class);
            $announcementUserMock->method('getDateApproved')->willReturn(null);
        }

        $announcementUserRepository = $this->createMock(AnnouncementUserRepository::class);
        $announcementUserRepository->method('findOneBy')
            ->with([
                'user' => self::TEST_USER_ID,
                'announcement' => $announcement,
            ])
            ->willReturn($announcementUserMock);

        $courseRepository = $this->createMock(CourseRepository::class);
        $courseRepository->method('find')->with(self::TEST_ANNOUNCEMENT_COURSE_ID)->willReturn($course);

        $userCourseRepository = $this->createMock(UserCourseRepository::class);
        $userCourseRepository->method('findOneBy')->willReturn(null);

        $this->entityManager->method('getRepository')
            ->willReturnMap([
                [AnnouncementUser::class, $announcementUserRepository],
                [Course::class, $courseRepository],
                [UserCourse::class, $userCourseRepository],
            ]);

        if ($dateApproved) {
            $this->dateFormatterService->method('formatDate')
                ->with($dateApproved, 'medium', $this->anything())
                ->willReturn($expectedFormattedDate);
        } else {
            $this->dateFormatterService->method('formatDate')
                ->with($this->isInstanceOf(\DateTime::class), 'medium', $this->anything())
                ->willReturn($expectedFormattedDate);
        }

        // Act
        $result = $this->strategy->getContentAnnouncementDiploma($announcement, $user);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('diplomaDate', $result);
        $this->assertEquals($expectedFormattedDate, $result['diplomaDate']);
        $this->assertEquals($expectedUserName, $result['userName']);
        $this->assertEquals($courseName, $result['courseName']);
    }

    public static function announcementDataProvider(): \Generator
    {
        yield 'Announcement with date_approved' => [
            'firstName' => 'Alice',
            'lastName' => 'Johnson',
            'courseName' => 'Announcement Course',
            'dateApproved' => new \DateTimeImmutable('2024-01-20'),
            'announcementUserScenario' => 'with_date',
            'expectedFormattedDate' => '20/01/2024',
            'expectedUserName' => 'Alice Johnson',
        ];

        yield 'Announcement without date_approved (fallback to current date)' => [
            'firstName' => 'Bob',
            'lastName' => 'Wilson',
            'courseName' => 'Fallback Course',
            'dateApproved' => null,
            'announcementUserScenario' => 'without_date',
            'expectedFormattedDate' => '15/01/2024',
            'expectedUserName' => 'Bob Wilson',
        ];

        yield 'Announcement without AnnouncementUser record' => [
            'firstName' => 'Charlie',
            'lastName' => 'Brown',
            'courseName' => 'No User Course',
            'dateApproved' => null,
            'announcementUserScenario' => null,
            'expectedFormattedDate' => '15/01/2024',
            'expectedUserName' => 'Charlie Brown',
        ];
    }
}
