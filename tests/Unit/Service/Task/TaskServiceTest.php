<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service\Task;

use App\Entity\Export;
use App\Entity\Task;
use App\Entity\User;
use App\Exception\TaskLimitExceededException;
use App\Exception\TaskNotFoundException;
use App\Repository\ExportRepository;
use App\Repository\TaskRepository;
use App\Service\SettingsService;
use App\Service\SlotManagerService;
use App\Service\Task\TaskService;
use App\Service\TaskLimitService;
use App\Service\TemplatedEmail\TemplatedEmailService;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\Expr;
use Doctrine\ORM\QueryBuilder;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class TaskServiceTest extends TestCase
{
    private $em;
    private $logger;
    private $translator;
    private $settings;
    private $taskLimitService;
    private $security;
    private $templatedEmailService;
    private $slotManagerService;
    private $taskService;
    private $taskRepository;
    private $exportRepository;
    private $queryBuilder;
    private $query;

    protected function setUp(): void
    {
        $this->em = $this->createMock(EntityManagerInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->translator = $this->createMock(TranslatorInterface::class);
        $this->settings = $this->createMock(SettingsService::class);
        $this->taskLimitService = $this->createMock(TaskLimitService::class);
        $this->security = $this->createMock(Security::class);
        $this->templatedEmailService = $this->createMock(TemplatedEmailService::class);
        $this->slotManagerService = $this->createMock(SlotManagerService::class);

        $this->taskRepository = $this->createMock(TaskRepository::class);
        $this->exportRepository = $this->createMock(ExportRepository::class);

        $this->em->method('getRepository')
            ->willReturnCallback(function ($entityClass) {
                if (Task::class === $entityClass) {
                    return $this->taskRepository;
                }
                if (Export::class === $entityClass) {
                    return $this->exportRepository;
                }

                return null;
            });

        $this->queryBuilder = $this->createMock(QueryBuilder::class);
        $this->query = $this->createMock(AbstractQuery::class);

        $this->taskService = new TaskService(
            $this->em,
            $this->logger,
            $this->translator,
            $this->settings,
            $this->taskLimitService,
            $this->slotManagerService
        );
    }

    public function testEnqueueTaskSuccess(): void
    {
        $user = $this->createMock(User::class);
        $user->method('getFirstName')->willReturn('Test User');

        $limit = 10;
        $this->taskLimitService->method('getLimit')->willReturn($limit);

        // Set up the TaskRepository to return a count less than the limit
        $this->taskRepository
            ->expects($this->once())
            ->method('countPendingTasksByUser')
            ->with($user)
            ->willReturn(5); // Less than the limit

        $mockResult = ['status' => 'pending', 'id' => 1];

        // Set up the ExportRepository to return a new Export
        $this->exportRepository
            ->expects($this->once())
            ->method('newExportTask')
            ->willReturn($mockResult);

        $result = $this->taskService->enqueueTask(
            $user,
            'export-file',
            ['test' => 'data'],
            'test-type',
            'test-filename'
        );

        $this->assertSame($mockResult, $result);
    }

    public function testEnqueueTaskLimitExceeded(): void
    {
        $user = $this->createMock(User::class);
        $user->method('getFirstName')->willReturn('Test User');

        $limit = 10;
        $this->taskLimitService->method('getLimit')->willReturn($limit);

        // Set up the TaskRepository to return a count equal to the limit
        $this->taskRepository
            ->expects($this->once())
            ->method('countPendingTasksByUser')
            ->with($user)
            ->willReturn($limit + 1); // exceeded limit

        $this->translator
            ->expects($this->once())
            ->method('trans')
            ->with(
                'task_limit_exceeded',
                [
                    '%id%' => 'Test User',
                    '%limit%' => $limit,
                    '%total%' => $limit + 1
                ],
                'exceptions'
            )
            ->willReturn('User Test User has reached the limit of 10 pending tasks');

        $this->expectException(TaskLimitExceededException::class);

        $this->taskService->enqueueTask(
            $user,
            'export-file',
            ['test' => 'data'],
            'test-type',
            'test-filename'
        );
    }

    public function testEnqueueTaskWithNullUser(): void
    {
        // Null user should not trigger any validation
        $this->taskRepository
            ->expects($this->never())
            ->method('countPendingTasksByUser');

        $mockResult = ['status' => 'pending', 'id' => 1];

        $this->exportRepository
            ->expects($this->once())
            ->method('newExportTask')
            ->willReturn($mockResult);

        $result = $this->taskService->enqueueTask(
            null,
            'export-file',
            ['test' => 'data'],
            'test-type',
            'test-filename'
        );

        $this->assertSame($mockResult, $result);
    }

    public function testGetExpiredTaskWithExportSuccess(): void
    {
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(AbstractQuery::class);
        $expr = $this->createMock(Expr::class);

        // Setup mock exports to be returned
        $export1 = $this->createMock(Export::class);
        $export2 = $this->createMock(Export::class);
        $mockResult = [$export1, $export2];

        // Configure the expression mock
        $orExpr = $this->createMock(Expr\Orx::class);
        $expr->expects($this->once())
            ->method('orX')
            ->willReturn($orExpr);

        // Configure the query builder chain
        $this->exportRepository
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->with('e')
            ->willReturn($queryBuilder);

        $queryBuilder->expects($this->once())
            ->method('expr')
            ->willReturn($expr);

        $queryBuilder->expects($this->once())
            ->method('select')
            ->with('e')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('join')
            ->with('e.task', 't')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('where')
            ->willReturnSelf();

        $queryBuilder->expects($this->exactly(2))
            ->method('andWhere')
            ->willReturnSelf();

        $queryBuilder->expects($this->exactly(2))
            ->method('setParameter')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('orderBy')
            ->with('t.id', 'DESC')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('getQuery')
            ->willReturn($query);

        // Mock the query result
        $query->expects($this->once())
            ->method('getResult')
            ->willReturn($mockResult);

        // Mock the settings service to return a timeout value
        $this->settings->expects($this->once())
            ->method('get')
            ->with('app.export.task.timeout', 1200)
            ->willReturn(600);

        // Execute and assert
        $result = $this->taskService->getExpiredTaskWithExport();
        $this->assertSame($mockResult, $result);
    }

    public function testGetExpiredTaskWithExportHandlesException(): void
    {
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(AbstractQuery::class);
        $expr = $this->createMock(Expr::class);

        // Configure the expression mock
        $orExpr = $this->createMock(Expr\Orx::class);
        $expr->expects($this->once())
            ->method('orX')
            ->willReturn($orExpr);

        // Configure the query builder chain
        $this->exportRepository
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->with('e')
            ->willReturn($queryBuilder);

        $queryBuilder->expects($this->once())
            ->method('expr')
            ->willReturn($expr);

        $queryBuilder->expects($this->once())
            ->method('select')
            ->with('e')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('join')
            ->with('e.task', 't')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('where')
            ->willReturnSelf();

        $queryBuilder->expects($this->exactly(2))
            ->method('andWhere')
            ->willReturnSelf();

        $queryBuilder->expects($this->exactly(2))
            ->method('setParameter')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('orderBy')
            ->with('t.id', 'DESC')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('getQuery')
            ->willReturn($query);

        // Mock the query to throw an exception
        $query->expects($this->once())
            ->method('getResult')
            ->willThrowException(new \Exception('Database error'));

        // Mock the settings service to return a timeout value
        $this->settings->expects($this->once())
            ->method('get')
            ->with('app.export.task.timeout', 1200)
            ->willReturn(600);

        // Execute and assert
        $result = $this->taskService->getExpiredTaskWithExport();
        $this->assertEmpty($result);
    }

    public function testGetExpiredTaskWithoutExportSuccess(): void
    {
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(AbstractQuery::class);
        $expr = $this->createMock(Expr::class);
        $mockResult = [new Task()];

        // Configure the query builder chain
        $this->taskRepository
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->with('t')
            ->willReturn($queryBuilder);

        $queryBuilder->expects($this->once())
            ->method('leftJoin')
            ->with('t.export', 'e')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('where')
            ->with('e.id IS NULL')
            ->willReturnSelf();

        $queryBuilder->expects($this->exactly(3))
            ->method('andWhere')
            ->willReturnSelf();

        $queryBuilder->expects($this->exactly(2))
            ->method('setParameter')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('orderBy')
            ->with('t.id', 'DESC')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('getQuery')
            ->willReturn($query);

        $queryBuilder->expects($this->once())
            ->method('expr')
            ->willReturn($expr);

        $expr->expects($this->once())
            ->method('orX')
            ->with('t.startedAt IS NULL', 't.startedAt < :startedBefore')
            ->willReturn('t.startedAt IS NULL OR t.startedAt < :startedBefore');

        $query->expects($this->once())
            ->method('getResult')
            ->willReturn($mockResult);

        // Mock the settings service to return a timeout value
        $this->settings->expects($this->once())
            ->method('get')
            ->with('app.export.task.timeout', 1200)
            ->willReturn(600);

        // Execute and assert
        $result = $this->taskService->getExpiredTaskWithoutExport();
        $this->assertSame($mockResult, $result);
    }

    public function testGetExpiredTaskWithoutExportHandlesException(): void
    {
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $query = $this->createMock(AbstractQuery::class);
        $expr = $this->createMock(Expr::class);

        // Configure the query builder chain
        $this->taskRepository
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->with('t')
            ->willReturn($queryBuilder);

        $queryBuilder->expects($this->once())
            ->method('leftJoin')
            ->with('t.export', 'e')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('where')
            ->with('e.id IS NULL')
            ->willReturnSelf();

        $queryBuilder->expects($this->exactly(3))
            ->method('andWhere')
            ->willReturnSelf();

        $queryBuilder->expects($this->exactly(2))
            ->method('setParameter')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('orderBy')
            ->with('t.id', 'DESC')
            ->willReturnSelf();

        $queryBuilder->expects($this->once())
            ->method('getQuery')
            ->willReturn($query);

        $queryBuilder->expects($this->once())
            ->method('expr')
            ->willReturn($expr);

        $expr->expects($this->once())
            ->method('orX')
            ->with('t.startedAt IS NULL', 't.startedAt < :startedBefore')
            ->willReturn('t.startedAt IS NULL OR t.startedAt < :startedBefore');

        // Mock the query to throw an exception
        $query->expects($this->once())
            ->method('getResult')
            ->willThrowException(new \Exception('Database error'));

        // Mock the settings service to return a timeout value
        $this->settings->expects($this->once())
            ->method('get')
            ->with('app.export.task.timeout', 1200)
            ->willReturn(600);

        // Execute and assert
        $result = $this->taskService->getExpiredTaskWithoutExport();
        $this->assertEmpty($result);
    }

    public function testGetNextTaskWithLongRunningTasks(): void
    {
        $task = new Task();
        $task->setTask('small-task');
        $task->setStatus(Task::TASK_PENDING);

        // Configurar el mock de SlotManagerService para devolver un ExecutionSlot
        $executionSlot = new \App\Service\TaskCron\ExecutionSlot(true);
        $this->slotManagerService->method('getAvailableTaskExecutionSlot')->willReturn($executionSlot);

        // Configure main query builder
        $this->taskRepository
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($this->queryBuilder);

        $this->queryBuilder
            ->expects($this->once())
            ->method('where')
            ->with('t.status = :status')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('andWhere')
            ->with('t.deletedAt IS NULL')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('setParameter')
            ->with('status', Task::TASK_PENDING)
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('orderBy')
            ->with('t.createdAt', 'ASC')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('setMaxResults')
            ->with(1)
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('getQuery')
            ->willReturn($this->query);

        $this->query
            ->expects($this->once())
            ->method('getOneOrNullResult')
            ->willReturn($task);

        $result = $this->taskService->getNextTask(true);
        $this->assertSame($task, $result);
    }

    public function testGetNextTaskWithoutLongRunningTasks(): void
    {
        $task = new Task();
        $task->setTask('small-task');
        $task->setStatus(Task::TASK_PENDING);

        // Configurar el mock de SlotManagerService para devolver un ExecutionSlot
        $executionSlot = new \App\Service\TaskCron\ExecutionSlot(false);
        $this->slotManagerService->method('getAvailableTaskExecutionSlot')->willReturn($executionSlot);
        $this->slotManagerService->method('getTaskLongRunningTypes')->willReturn(['export-file']);

        // Configure main query builder
        $this->taskRepository
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($this->queryBuilder);

        $this->queryBuilder
            ->expects($this->once())
            ->method('where')
            ->with('t.status = :status')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->exactly(2))
            ->method('andWhere')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->exactly(2))
            ->method('setParameter')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('orderBy')
            ->with('t.createdAt', 'ASC')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('setMaxResults')
            ->with(1)
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('getQuery')
            ->willReturn($this->query);

        $this->query
            ->expects($this->once())
            ->method('getOneOrNullResult')
            ->willReturn($task);

        $result = $this->taskService->getNextTask(false);
        $this->assertSame($task, $result);
    }

    public function testGetNextTaskThrowsExceptionWhenNoTasksAvailable(): void
    {
        // Configurar el mock de SlotManagerService para devolver un ExecutionSlot
        $executionSlot = new \App\Service\TaskCron\ExecutionSlot(true);
        $this->slotManagerService->method('getAvailableTaskExecutionSlot')->willReturn($executionSlot);

        // Configure main query builder
        $this->taskRepository
            ->expects($this->once())
            ->method('createQueryBuilder')
            ->willReturn($this->queryBuilder);

        $this->queryBuilder
            ->expects($this->once())
            ->method('where')
            ->with('t.status = :status')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('andWhere')
            ->with('t.deletedAt IS NULL')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('setParameter')
            ->with('status', Task::TASK_PENDING)
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('orderBy')
            ->with('t.createdAt', 'ASC')
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('setMaxResults')
            ->with(1)
            ->willReturnSelf();

        $this->queryBuilder
            ->expects($this->once())
            ->method('getQuery')
            ->willReturn($this->query);

        $this->query
            ->expects($this->once())
            ->method('getOneOrNullResult')
            ->willReturn(null);

        $this->expectException(TaskNotFoundException::class);
        $this->taskService->getNextTask(true);
    }
}
