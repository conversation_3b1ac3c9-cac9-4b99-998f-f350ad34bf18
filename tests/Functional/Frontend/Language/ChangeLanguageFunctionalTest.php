<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\Language;

use App\Entity\UserLogin;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendLanguagesEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class ChangeLanguageFunctionalTest extends FunctionalTestCase
{
    private string $defaultCampusLanguage = '';

    /**
     * @dataProvider languagesProvider
     */
    public function testLanguagesBySettings(
        string $language,
        $expectedTranslatedMessage,
    ): void {
        $user = $this->getDefaultUser();
        $this->defaultCampusLanguage = $user->getLocaleCampus();

        $userToken = $this->loginAndGetToken();
        $response = $this->makeFrontendApiRequest(
            'POST',
            FrontendLanguagesEndpoints::changeLanguagesEndpoint(),
            [
                'language' => $language,
            ],
            [],
            [],
            $userToken
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);
        $this->assertEquals($language, $responseData['user']['locale']);

        $this->assertEquals($expectedTranslatedMessage, $responseData['message']);

        // Check if the user profile has the same language.
        $response = $this->makeFrontendApiRequest(
            'GET',
            FrontendLanguagesEndpoints::userProfileEndpoint(),
            [],
            [],
            [],
            $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);
        $locale = $responseData['locale'];

        $this->assertEquals($locale, $language);
    }

    public static function languagesProvider(): \Generator
    {
        yield 'Spanish language ' => [
            'language' => 'es',
            'expectedTranslatedMessage' => 'Tus datos han sido modificados',
        ];

        yield 'English language ' => [
            'language' => 'en',
            'expectedTranslatedMessage' => 'Your data has been modified',
        ];
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([UserLogin::class]);

        if ('' !== $this->defaultCampusLanguage) {
            $user = $this->getDefaultUser();
            $user->setLocaleCampus($this->defaultCampusLanguage);
            try {
                $this->getEntityManager()->flush();
            } catch (OptimisticLockException|ORMException $e) {
                $this->fail($e->getMessage());
            }
        }
        parent::tearDown();
    }
}
