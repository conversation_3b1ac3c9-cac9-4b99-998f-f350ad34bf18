<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\User;

use App\Entity\User;
use App\Entity\UserLogin;
use App\Repository\UserRepository;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendUserEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class NormalLoginFunctionalTest extends FunctionalTestCase
{
    /**
     * @dataProvider noEmailOrTokenProvidedProvider
     */
    public function testNoEmailOrTokenProvided(string $password): void
    {
        $response = $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'email' => '',
            'password' => $password,
        ]);

        $this->assertManagedErrorResponse(
            $response,
            Response::HTTP_UNAUTHORIZED,
            'No email or token has been provided'
        );
    }

    public static function noEmailOrTokenProvidedProvider(): \Generator
    {
        yield 'No password' => [
            'password' => '',
        ];

        yield 'With password' => [
            'password' => 'password',
        ];
    }

    /**
     * @dataProvider noPasswordProvidedProvider
     */
    public function testNoPasswordProvided(string $email): void
    {
        $response = $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'email' => $email,
            'password' => '',
        ]);

        $this->assertManagedErrorResponse(
            $response,
            Response::HTTP_UNAUTHORIZED,
            'No password has been provided'
        );
    }

    public static function noPasswordProvidedProvider(): \Generator
    {
        yield 'existing email' => [
            'email' => self::DEFAULT_USER_EMAIL,
        ];

        yield 'non-existing email' => [
            'email' => '<EMAIL>',
        ];
    }

    /**
     * @dataProvider invalidCredentialsProvider
     *
     * @throws NotSupported
     */
    public function testInvalidCredentials(
        string $email,
        string $password,
        bool $isActivated,
        bool $validated
    ): void {
        /** @var UserRepository $userRepository */
        $userRepository = $this->getRepository(User::class);

        $user = $userRepository->find(self::DEFAULT_USER_ID);

        $previousStatus = $user->getIsActive();
        $user->setIsActive($isActivated);

        $previousValidated = $user->getValidated();
        $user->setValidated($validated);

        $response = $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'email' => $email,
            'password' => $password,
        ]);

        $this->assertManagedErrorResponse(
            $response,
            Response::HTTP_UNAUTHORIZED,
            'Las credenciales introducidas no son correctas.',
        );

        $user->setIsActive($previousStatus);
        $user->setValidated($previousValidated);
    }

    public static function invalidCredentialsProvider(): \Generator
    {
        yield 'existing email and invalid password' => [
            'email' => self::DEFAULT_USER_EMAIL,
            'password' => 'invalid_password',
            'isActivated' => true,
            'validated' => true,
        ];

        yield 'non-existing email and password' => [
            'email' => '<EMAIL>',
            'password' => 'password',
            'isActivated' => true,
            'validated' => true,
        ];

        yield 'existing email and valid password when user is not activated' => [
            'email' => self::DEFAULT_USER_EMAIL,
            'password' => self::DEFAULT_USER_PASSWORD,
            'isActivated' => false,
            'validated' => true,
        ];

        yield 'existing email and valid password when user is not validated' => [
            'email' => self::DEFAULT_USER_EMAIL,
            'password' => self::DEFAULT_USER_PASSWORD,
            'isActivated' => true,
            'validated' => false,
        ];
    }

    public function testSuccessfulLogin(): void
    {
        $response = $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'email' => self::DEFAULT_USER_EMAIL,
            'password' => self::DEFAULT_USER_PASSWORD,
        ]);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('token', $responseData);
        $this->assertArrayHasKey('refreshToken', $responseData);
        $this->assertArrayHasKey('level', $responseData);

        $this->assertArrayHasKey('user', $responseData);
        $this->assertArrayHasKey('id', $responseData['user']);
        $this->assertEquals(self::DEFAULT_USER_ID, $responseData['user']['id']);
        $this->assertArrayHasKey('email', $responseData['user']);
        $this->assertEquals(self::DEFAULT_USER_EMAIL, $responseData['user']['email']);
        $this->assertArrayHasKey('firstName', $responseData['user']);
        $this->assertArrayHasKey('lastName', $responseData['user']);
        $this->assertArrayHasKey('extra', $responseData['user']);
        $this->assertArrayHasKey('points', $responseData['user']);
        $this->assertArrayHasKey('avatar', $responseData['user']);
        $this->assertArrayHasKey('locale', $responseData['user']);
        $this->assertArrayHasKey('dataAvatar', $responseData['user']);
        $this->assertArrayHasKey('fullName', $responseData['user']);
        $this->assertArrayHasKey('policies', $responseData['user']);
        $this->assertArrayHasKey('finishedCourses', $responseData['user']);
        $this->assertArrayHasKey('timeSpent', $responseData['user']);
        $this->assertArrayHasKey('itineraries', $responseData['user']);
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([UserLogin::class]);

        parent::tearDown();
    }
}
