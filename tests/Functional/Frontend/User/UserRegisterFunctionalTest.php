<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\User;

use App\Entity\RecoveryCode;
use App\Entity\Setting;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendUserEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\MailerInterface;

class UserRegisterFunctionalTest extends FunctionalTestCase
{
    protected const DEFAULT_USER_FIRST_NAME = 'soporte';
    protected const DEFAULT_USER_LAST_NAME = 'gestionet';
    protected const TEST_USER_EMAIL = '<EMAIL>';

    private string $defaultExtraFields = '';

    private bool $defaultUserFreeRegistration = false;

    /**
     * @throws NotSupported
     */
    public function setUp(): void
    {
        parent::setUp();
        $settingsRepository = $this->getRepository(Setting::class);

        $userExtraFieldsSetting = $settingsRepository->findOneBy(['code' => 'app.user.extra_fields']);
        $userFreeRegistrationSetting = $settingsRepository->findOneBy(['code' => 'app.free.user.registration']);
        $this->defaultExtraFields = $userExtraFieldsSetting->getValue();
        $this->defaultUserFreeRegistration = filter_var($userFreeRegistrationSetting->getValue(), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false;
    }

    /**
     * @dataProvider userRegisterErrorsProvider
     *
     * @throws NotSupported
     */
    public function testUserRegisterErrors($email, $firstName, $lastName, $password, $repeatPassword, $expectedMessage, $extraFields, $extraFieldsInSettings = true): void
    {
        $this->setAutoRegister(false);
        if ($extraFieldsInSettings) {
            $this->setSettingsExtraFields();
        }

        $response = $this->getUserRegisterResponse($email, $firstName, $lastName, $password, $repeatPassword, $extraFields);
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertManagedErrorResponse(
            $response,
            Response::HTTP_OK,
            $expectedMessage
        );
    }

    public static function userRegisterErrorsProvider(): \Generator
    {
        yield 'User register when auto register is not activated (app.free.user.registration = false)' => [
            'email' => '<EMAIL>',
            'firstName' => self::DEFAULT_USER_FIRST_NAME,
            'lastName' => self::DEFAULT_USER_LAST_NAME,
            'password' => self::DEFAULT_USER_PASSWORD,
            'repeatPassword' => self::DEFAULT_USER_PASSWORD,
            'expectedMessage' => 'El email no existe en base de datos',
            'extraFields' => [],
            'extraFieldsInSettings' => false,
        ];

        yield 'User already activated' => [
            'email' => self::DEFAULT_USER_EMAIL,
            'firstName' => self::DEFAULT_USER_FIRST_NAME,
            'lastName' => self::DEFAULT_USER_LAST_NAME,
            'password' => self::DEFAULT_USER_PASSWORD,
            'repeatPassword' => self::DEFAULT_USER_PASSWORD,
            'expectedMessage' => 'Este usuario ya esta activado',
            'extraFields' => [],
            'extraFieldsInSettings' => false,
        ];
    }

    /**
     * @throws NotSupported
     * @throws ORMException
     *
     * @dataProvider userRegisterProvider
     */
    public function testUserRegister(
        $email,
        $firstName,
        $lastName,
        $password,
        $repeatPassword,
        $expectedMessageArray,
        $extraFields,
        $extraFieldsHttpCode,
        $userActivated,
        $autoRegister,
        $extraFieldsInSettings = true
    ): void {
        $this->setAutoRegister($autoRegister);
        if ($extraFieldsInSettings) {
            $this->setSettingsExtraFields();
        }

        $mailer = $this->getService(MailerInterface::class);
        $messages = $mailer->getMessages();
        $this->assertEmpty($messages);

        $response = $this->getUserRegisterResponse($email, $firstName, $lastName, $password, $repeatPassword, $extraFields);
        $this->assertEqualsCanonicalizing($extraFieldsHttpCode, $response->getStatusCode());

        if (Response::HTTP_OK == $response->getStatusCode()) {
            $responseData = $this->extractResponseData($response);
            $this->assertContains($responseData, $expectedMessageArray);
            $messages = $mailer->getMessages();
            $this->assertNotEmpty($messages);
        }

        $this->deleteTestUser();
    }

    public static function userRegisterProvider(): \Generator
    {
        yield 'User register when auto register is activated. User Activated. 1 String required extra field of two in settings. 400 expected' => [
            'email' => self::TEST_USER_EMAIL,
            'firstName' => self::DEFAULT_USER_FIRST_NAME,
            'lastName' => self::DEFAULT_USER_LAST_NAME,
            'password' => self::DEFAULT_USER_PASSWORD,
            'repeatPassword' => self::DEFAULT_USER_PASSWORD,
            'expectedMessageArray' => [
                0 => '¡Enhorabuena!',
                1 => '<p>¡Enhorabuena!</p><p>Tu solicitud de registro ha sido enviada, revisa tu correo para ver las instrucciones de activación.</p>',
            ],
            'extraFields' => [
                [
                    'name' => 'input_text',
                    'value' => 'Input text',
                ],
            ],
            'extraFieldsHttpCode' => Response::HTTP_BAD_REQUEST,
            'userActivated' => true,
            'autoRegister' => true,
        ];

        yield 'User register when auto register is activated. User activated. 2 String required extra field of two in settings. 200 expected' => [
            'email' => self::TEST_USER_EMAIL,
            'firstName' => self::DEFAULT_USER_FIRST_NAME,
            'lastName' => self::DEFAULT_USER_LAST_NAME,
            'password' => self::DEFAULT_USER_PASSWORD,
            'repeatPassword' => self::DEFAULT_USER_PASSWORD,
            'expectedMessageArray' => [
                0 => '¡Enhorabuena!',
                1 => '<p>¡Enhorabuena!</p><p>Tu solicitud de registro ha sido enviada, revisa tu correo para ver las instrucciones de activación.</p>',
            ],
            'extraFields' => [
                [
                    'name' => 'input_select',
                    'value' => '1',
                ],
                [
                    'name' => 'input_text',
                    'value' => 'Input text',
                ],
            ],
            'extraFieldsHttpCode' => Response::HTTP_OK,
            'userActivated' => true,
            'autoRegister' => true,
        ];

        yield 'User register when auto register is not activated. User not activated. No extra fields passed with extra fields in settings. 400 expected' => [
            'email' => self::TEST_USER_EMAIL,
            'firstName' => self::DEFAULT_USER_FIRST_NAME,
            'lastName' => self::DEFAULT_USER_LAST_NAME,
            'password' => self::DEFAULT_USER_PASSWORD,
            'repeatPassword' => self::DEFAULT_USER_PASSWORD,
            'expectedMessageArray' => [
                1 => 'El email no existe en base de datos',
            ],
            'extraFields' => null,
            'extraFieldsHttpCode' => Response::HTTP_BAD_REQUEST,
            'userActivated' => false,
            'autoRegister' => false,
        ];

        yield 'User register when auto register is not activated. User activated. No extra fields with no extra fields in settings. 200 expected' => [
            'email' => self::TEST_USER_EMAIL,
            'firstName' => self::DEFAULT_USER_FIRST_NAME,
            'lastName' => self::DEFAULT_USER_LAST_NAME,
            'password' => self::DEFAULT_USER_PASSWORD,
            'repeatPassword' => self::DEFAULT_USER_PASSWORD,
            'expectedMessageArray' => [
                0 => '¡Enhorabuena!',
                1 => '<p>¡Enhorabuena!</p><p>Tu solicitud de registro ha sido enviada, revisa tu correo para ver las instrucciones de activación.</p>',
            ],
            'extraFields' => null,
            'extraFieldsHttpCode' => Response::HTTP_OK,
            'userActivated' => false,
            'autoRegister' => true,
            'extraFieldsInSettings' => false,
        ];
    }

    private function getUserRegisterResponse($email, $firstName, $lastName, $password, $repeatPassword, $extraFields): Response
    {
        return $this->makeFrontendApiRequest(
            'POST',
            FrontendUserEndpoints::userRegisterEndpoint(),
            [
                'email' => $email,
                'firstName' => $firstName,
                'lastName' => $lastName,
                'password' => $password,
                'repeatPassword' => $repeatPassword,
                'extraFields' => $extraFields,
            ]
        );
    }

    /**
     * @throws NotSupported
     */
    private function setAutoRegister($status)
    {
        $settingsRepository = $this->getRepository(Setting::class);
        $userAutoRegister = $settingsRepository->findOneBy(['code' => 'app.free.user.registration']);

        $userAutoRegister->setValue($status ? 'true' : 'false');
        $this->entityManagerSave();
    }

    /**
     * @throws NotSupported
     */
    private function setSettingsExtraFields()
    {
        $settingsRepository = $this->getRepository(Setting::class);
        $userExtraFieldsSetting = $settingsRepository->findOneBy(['code' => 'app.user.extra_fields']);
        $userExtraFieldsSetting->setValue(json_encode($this->getExtraField()));
        $this->entityManagerSave();
    }

    private function setUser($email, $firstName, $lastName, $password, $userActivated)
    {
        $user = new User();
        $user->setEmail($email);
        $user->setFirstName($firstName);
        $user->setLastName($lastName);
        $user->setPassword($password);
        $user->setOpen(true);
        $user->setIsActive($userActivated);

        $this->entityManagerSave($user);
    }

    private function entityManagerSave($newObject = null): void
    {
        try {
            $em = $this->getEntityManager();
            if ($newObject) {
                $em->persist($newObject);
            }
            $em->flush();
        } catch (OptimisticLockException|ORMException $e) {
            $this->fail($e->getMessage());
        }
    }

    /**
     * @throws NotSupported
     * @throws ORMException
     */
    private function deleteTestUser()
    {
        $recoveryCodeRepository = $this->getRepository(RecoveryCode::class);
        $userRepository = $this->getRepository(User::class);
        $user = $userRepository->findOneBy(['email' => self::TEST_USER_EMAIL]);
        $recoveryCode = $recoveryCodeRepository->findOneBy(['user' => $user]);

        if (!$user) {
            return;
        }

        try {
            $em = $this->getEntityManager();
            if ($recoveryCode) {
                $em->remove($recoveryCode);
            }

            $em->flush();

            $connection = $em->getConnection();
            $connection->executeStatement('DELETE FROM user WHERE id = :id', ['id' => $user->getId()]);
        } catch (OptimisticLockException|ORMException|Exception $e) {
            $this->fail($e->getMessage());
        }
    }

    private static function getExtraField(): array
    {
        return [
            [
                'name' => 'input_select',
                'required' => true,
                'label' => [
                    'default' => 'Input select',
                    'translations' => [
                        [
                            'language' => 'es',
                            'value' => 'Selector de entrada',
                        ],
                        [
                            'language' => 'en',
                            'value' => 'Input select',
                        ],
                    ],
                ],
                'type' => 'select',
                'options' => [
                    [
                        'value' => '1',
                        'name' => [
                            'default' => 'Option 1',
                            'translations' => [
                                [
                                    'language' => 'es',
                                    'value' => "Opci\u00f3n 1",
                                ],
                                [
                                    'language' => 'en',
                                    'value' => 'Option 1',
                                ],
                            ],
                        ],
                    ],
                    [
                        'value' => '2',
                        'name' => [
                            'default' => 'Option 2',
                            'translations' => [
                                [
                                    'language' => 'es',
                                    'value' => "Opci\u00f3n 2",
                                ],
                                [
                                    'language' => 'en',
                                    'value' => 'Option 2',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            [
                'name' => 'input_text',
                'required' => true,
                'label' => [
                    'default' => 'Input text',
                    'translations' => [
                        [
                            'language' => 'es',
                            'value' => 'Texto de entrada',
                        ],
                        [
                            'language' => 'en',
                            'value' => 'Input text',
                        ]],
                ],
                'type' => 'text',
            ],
        ];
    }

    /**
     * @throws NotSupported
     * @throws ORMException
     * @throws MappingException
     */
    public function tearDown(): void
    {
        $settingsRepository = $this->getRepository(Setting::class);
        $userExtraFieldsSetting = $settingsRepository->findOneBy(['code' => 'app.user.extra_fields']);
        $userFreeRegistrationSetting = $settingsRepository->findOneBy(['code' => 'app.free.user.registration']);

        $userFreeRegistrationSetting->setValue($this->defaultUserFreeRegistration ? 'true' : 'false');
        $userExtraFieldsSetting->setValue($this->defaultExtraFields);

        $em = $this->getEntityManager();
        $em->flush();

        parent::tearDown();
    }
}
