<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\Sections;

use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseSection;
use App\Entity\UserLogin;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendSectionEndpoints;
use Symfony\Component\HttpFoundation\Response;

class GetSectionsFunctionalTest extends FunctionalTestCase
{
    private string $userToken;

    protected function setUp(): void
    {
        parent::setUp();
        $this->userToken = $this->loginAndGetToken();
        $this->createSampleData();
    }

    private function createSampleData(): void
    {
        $this->createAndGetCourse(
            'Test Course', // name
            'TEST-COURSE', // code
            null, // typeCourse
            'Test course description', // description
            'es', // locale
            true, // active
            true, // open
            true, // isNew
            true, // openVisible
            null // CourseCategory
        );

        $this->createAndGetCourseSection();
    }

    public function testEmptySectionWhenNoData(): void
    {
        $this->truncateEntities([CourseSection::class]);

        $response = $this->makeFrontendApiRequest(
            'GET',
            FrontendSectionEndpoints::sectionsEndpoint(),
            [],
            [],
            [],
            $this->userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('sections', $responseData);
        $this->assertEmpty($responseData['sections']);
        $this->assertArrayHasKey('training', $responseData);
        $this->assertEmpty($responseData['training']);
    }

    private function getSectionsResponse(): array
    {
        $response = $this->makeFrontendApiRequest(
            'GET',
            FrontendSectionEndpoints::sectionsEndpoint(),
            [],
            [],
            [],
            $this->userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        return $this->extractResponseData($response);
    }

    public function testContainCorrectStructure(): void
    {
        // This is done because otherwise, the current user's language remains cached in another language. This might need to be reviewed.
        $this->setLocaleForUser(self::DEFAULT_USER_LOCALE);

        $responseData = $this->getSectionsResponse();

        $this->assertArrayHasKey('sections', $responseData);
        $this->assertArrayHasKey('training', $responseData);
        $this->assertIsArray($responseData['sections']);
        $this->assertIsArray($responseData['training']);

        $baseSection = $responseData['sections'][0];
        $this->assertArrayHasKey('name', $baseSection);
        $this->assertEquals('Mi Formación', $baseSection['name']);
        $this->assertArrayHasKey('slug', $baseSection);
        $this->assertEquals('mi-formacion', $baseSection['slug']);
        $this->assertArrayHasKey('sort', $baseSection);
        $this->assertEquals(1, $baseSection['sort']);
        $this->assertArrayHasKey('hideCategoryName', $baseSection);
        $this->assertArrayHasKey('isMain', $baseSection);
        $this->assertArrayHasKey('openCampus', $baseSection);
        $this->assertArrayHasKey('isItineraries', $baseSection);
        $this->assertArrayHasKey('isAnnouncements', $baseSection);
        $this->assertArrayHasKey('categories', $baseSection);
        $this->assertIsArray($baseSection['categories']);

        $this->assertIsBool($baseSection['isMain']);
        $this->assertIsBool($baseSection['openCampus']);
        $this->assertIsBool($baseSection['isItineraries']);
        $this->assertIsBool($baseSection['isAnnouncements']);
        $this->assertIsBool($baseSection['hideCategoryName']);
    }

    public function testCheckStructureOfSectionCategories(): void
    {
        $responseData = $this->getSectionsResponse();

        $this->assertArrayHasKey('sections', $responseData);
        $this->assertNotEmpty($responseData['sections'], 'No sections found in response.');

        foreach ($responseData['sections'] as $section) {
            $categories = $section['categories'];

            $this->assertIsArray($categories);

            if (empty($categories)) {
                continue;
            }

            foreach ($categories as $category) {
                $this->validateCategoryStructure($category);
            }
        }
    }

    private function validateCategoryStructure(array $category): void
    {
        $this->assertArrayHasKey('id', $category);
        $this->assertArrayHasKey('name', $category);
        $this->assertArrayHasKey('slug', $category);
        $this->assertArrayHasKey('sort', $category);
    }

    public function testHasAtMostOneMainSection(): void
    {
        $responseData = $this->getSectionsResponse();
        $sections = $responseData['sections'] ?? [];

        $mainSectionCount = 0;

        foreach ($sections as $section) {
            if (isset($section['isMain']) && true === $section['isMain']) {
                ++$mainSectionCount;
            }
        }

        $this->assertLessThanOrEqual(
            1,
            $mainSectionCount,
            \sprintf('Expected at most one main section, but found %d.', $mainSectionCount)
        );
    }

    public function tearDown(): void
    {
        $this->truncateEntities([UserLogin::class, Course::class, 'course_section_course_category', CourseCategory::class, CourseSection::class]);
        parent::tearDown();
    }
}
