<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseSection;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Tests\Mother\Entity\TypeCourseMother;
use App\V2\Domain\Course\Diploma\DiplomaConfig;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

/**
 * Trait with methods to create, update, and delete Course.
 * Assumes that if your Course requires a TypeCourse, it is injected as a parameter
 * or created separately using the TypeCourseHelperTrait.
 */
trait CourseHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetCourse(
        string $name = 'Test Course Name',
        string $code = 'TestCourseCode-1',
        ?TypeCourse $typeCourse = null,
        ?string $description = 'Test Course Description',
        string $locale = 'es',
        bool $active = true,
        bool $open = true,
        bool $isNew = true,
        bool $openVisible = true,
        ?CourseCategory $courseCategory = null,
        ?\DateTimeInterface $newAt = null,
        ?int $duration = null,
        ?User $createdBy = null,
        ?bool $showDuration = null
    ): Course {
        $em = $this->getEntityManager();

        // Crear y persistir el TypeCourse primero si no se proporciona
        if (null === $typeCourse) {
            $typeCourse = $this->createAndGetTypeCourse(
                id: TypeCourseMother::ONLINE_ID,
            );
            $em->refresh($typeCourse);
        }

        // Crear y persistir la CourseCategory si no se proporciona
        if (null === $courseCategory) {
            $courseCategory = $this->createAndGetCourseCategory();
            $em->refresh($courseCategory);
        }

        $course = new Course();
        $course->setName($name)
            ->setCode($code)
            ->setTypeCourse($typeCourse)
            ->setCategory($courseCategory)
            ->setLocale($locale)
            ->setDescription($description)
            ->setActive($active)
            ->setOpen($open)
            ->setIsNew($isNew)
            ->setOpenVisible($openVisible)
            ->setNewAt($newAt)
            ->setDuration($duration);

        if ($createdBy) {
            $course->setCreatedBy($createdBy);
        }

        if (null !== $showDuration) {
            $diplomaConfig = new DiplomaConfig($showDuration);
            $course->setDiplomaConfig($diplomaConfig);
        }

        $em->persist($course);
        $em->flush();
        $em->refresh($course);

        return $course;
    }

    protected function createAndGetCourseCategory(?string $name = null): CourseCategory
    {
        $em = $this->getEntityManager();

        $courseCategory = new CourseCategory();

        $courseCategory->setName($name ?? 'Test Category');

        $em->persist($courseCategory);
        $em->flush();

        return $courseCategory;
    }

    protected function createAndGetCourseSection(
        string $name = 'Mi Formación',
        ?CourseCategory $courseCategory = null
    ): CourseSection {
        $em = $this->getEntityManager();

        $courseSection = new CourseSection();

        $courseSection->setName($name);
        $courseSection->setActive(true);
        $courseSection->setSort(1);

        $em->persist($courseSection);
        $em->flush();

        return $courseSection;
    }

    /**
     * Creates and persists a TypeCourse and returns it.
     *
     * @throws ORMException
     */
    protected function createAndGetTypeCourse(
        ?int $id = null,
        ?string $name = null,
        ?string $description = null,
        bool $active = true,
        ?string $code = null,
        ?string $denomination = null,
        ?\DateTimeInterface $deletedAt = null,
    ): TypeCourse {
        $em = $this->getEntityManager();
        if (null !== $id) {
            $typeCourse = $em->getRepository(TypeCourse::class)->find($id);
            if (null !== $typeCourse) {
                return $typeCourse;
            }
        }

        $typeCourse = TypeCourseMother::create(
            id: $id,
            name: $name,
            description: $description,
            active: $active,
            code: $code,
            denomination: $denomination,
            deletedAt: $deletedAt,
        );

        $originalMetadata = $this->setCustomIdToEntity($typeCourse);
        $em->persist($typeCourse);
        $em->flush();
        $this->restoreEntityMetadata($typeCourse, $originalMetadata);

        return $typeCourse;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetUserCourse(
        User $user,
        Course $course,
        ?\DateTimeImmutable $startAt = null,
        ?\DateTimeImmutable $finishedAt = null
    ): UserCourse {
        $em = $this->getEntityManager();

        $userCourse = new UserCourse();
        $userCourse->setUser($user);
        $userCourse->setCourse($course);
        $userCourse->setStartedAt($startAt ?? new \DateTimeImmutable('today'));
        $userCourse->setFinishedAt($finishedAt);

        $em->persist($userCourse);
        $em->flush();

        return $userCourse;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetUserCourseChapter(
        UserCourse $userCourse,
        Chapter $chapter,
        ?\DateTimeImmutable $startAt = null,
        ?\DateTimeImmutable $finishedAt = null,
        $timeSpent = 0
    ): UserCourseChapter {
        $em = $this->getEntityManager();

        $userCourseChapter = new UserCourseChapter();
        $userCourseChapter->setUserCourse($userCourse);
        $userCourseChapter->setChapter($chapter);
        $userCourseChapter->setStartedAt($startAt ?? new \DateTimeImmutable('today'));
        $userCourseChapter->setFinishedAt($finishedAt);
        $userCourseChapter->setTimeSpent($timeSpent);

        $em->persist($userCourseChapter);
        $em->flush();

        return $userCourseChapter;
    }

    /**
     * Deletes a Course from the database.
     *
     * @throws ORMException
     */
    protected function removeCourse(Course $course): void
    {
        $em = $this->getEntityManager();
        $em->remove($course);
        $em->flush();
    }

    /**
     * Deletes a TypeCourse from the database.
     *
     * @throws ORMException
     */
    protected function removeTypeCourse(TypeCourse $typeCourse): void
    {
        $em = $this->getEntityManager();
        $em->remove($typeCourse);
        $em->flush();
    }

    /**
     * Deletes a TypeCourse from the database.
     *
     * @throws ORMException
     */
    protected function removeUserCourse(UserCourse $userCourse): void
    {
        $em = $this->getEntityManager();
        $em->remove($userCourse);
        $em->flush();
    }

    /**
     * Deletes a TypeCourse from the database.
     *
     * @throws ORMException
     */
    protected function removeUserCourseChapter(UserCourseChapter $userCourseChapter): void
    {
        $em = $this->getEntityManager();
        $em->remove($userCourseChapter);
        $em->flush();
    }
}
