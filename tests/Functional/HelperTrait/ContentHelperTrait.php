<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Chapter;
use App\Entity\Content;
use App\Tests\Mother\Entity\ContentMother;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait ContentHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetContent(
        ?int $id = null,
        ?string $title = null,
        ?string $content = null,
        ?int $position = 1,
        ?Chapter $chapter = null,
    ): Content {
        $entity = ContentMother::create(
            id: $id,
            title: $title,
            content: $content,
            position: $position,
            chapter: $chapter
        );

        $em = $this->getEntityManager();
        $em->persist($entity);
        $em->flush();

        return $entity;
    }
}
