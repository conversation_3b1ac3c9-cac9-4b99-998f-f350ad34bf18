<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Course;
use App\Entity\Season;
use App\Entity\TypeCourse;
use App\Tests\Mother\Entity\SeasonMother;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

/**
 * Trait with methods to create, update, and delete Course.
 * Assumes that if your Course requires a TypeCourse, it is injected as a parameter
 * or created separately using the TypeCourseHelperTrait.
 */
trait SeasonHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetSeason(
        Course $course,
        string $name = 'Test Season',
        int $sort = 1,
        string $type = Season::TYPE_FREE
    ): Season {
        $em = $this->getEntityManager();

        $season = SeasonMother::create(
            name: $name,
            sort: $sort,
            type: $type,
            course: $em->getRepository(Course::class)->find($course->getId()),
        );

        $em->persist($season);
        $em->flush();

        return $season;
    }

    /**
     * Deletes a TypeCourse from the database.
     *
     * @throws ORMException
     */
    protected function removeSeason(Season $season): void
    {
        $em = $this->getEntityManager();
        $em->remove($season);
        $em->flush();
    }
}
