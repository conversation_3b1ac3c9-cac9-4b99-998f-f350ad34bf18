<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\TypeCourse;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class AdminCourseFunctionalTest extends FunctionalTestCase
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testUrlPreDataCourse(): void
    {
        $response = $this->getCoursePreDataResponse();

        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('categories', $responseData);
        $this->assertArrayHasKey('multilingual', $responseData);
        $this->assertArrayHasKey('setCoursePoints', $responseData);
        $this->assertArrayHasKey('points', $responseData);
        $this->assertArrayHasKey('useSegment', $responseData);
        $this->assertArrayHasKey('setCourseLevel', $responseData);
        $this->assertArrayHasKey('courseDocumentation', $responseData);
        $this->assertArrayHasKey('typesCourse', $responseData);
        $this->assertArrayHasKey('surveysCourse', $responseData);
        $this->assertArrayHasKey('typeDiplomas', $responseData);
        $this->assertArrayHasKey('typeIndexDiploma', $responseData);

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function testCoursesListStructure(): void
    {
        $this->createAndGetCourse();
        $responseData = $this->extractResponseData($this->getCoursesListResponse());

        $this->assertArrayHasKey('courses', $responseData);
        $this->assertArrayHasKey('totalCourses', $responseData);
        $this->assertArrayHasKey('typeCourses', $responseData);
        $this->assertArrayHasKey('courseCategories', $responseData);
        $this->assertArrayHasKey('creatorsCourses', $responseData);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSingleCourseStructure(): void
    {
        $this->createAndGetCourse();
        $responseData = $this->extractResponseData($this->getCoursesListResponse());

        $this->assertNotEmpty($responseData['courses']);
        $course = $responseData['courses'][0];

        $expectedKeys = [
            'id',
            'name',
            'typeCourse',
            'category',
            'active',
            'open',
            'totalChapters',
            'thumbnailUrl',
            'locale',
            'completed',
            'languages',
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $course);
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testDetailCourse(): void
    {
        $course = $this->createAndGetCourse();
        $this->assertNotNull($course, 'Course should not be null after creation.');
        $this->assertInstanceOf(Course::class, $course, 'Course should be an instance of Course.');
        if (!$course instanceof Course) {
            $this->fail('Expected $course to be an instance of Course.');
        }

        $response = $this->getCoursesDetailResponse($course);
        $responseData = $this->extractResponseData($response);

        $this->assertIsArray($responseData);
        $this->assertArrayHasKey('id', $responseData);
        $this->assertArrayHasKey('name', $responseData);
        $this->assertArrayHasKey('category', $responseData);
        $this->assertArrayHasKey('updatedBy', $responseData);
        $this->assertArrayHasKey('createdBy', $responseData);
        $this->assertArrayHasKey('createdAt', $responseData);
        $this->assertArrayHasKey('updatedAt', $responseData);
        $this->assertArrayHasKey('description', $responseData);
        $this->assertArrayHasKey('image', $responseData);
        $this->assertArrayHasKey('typeCourseId', $responseData);
        $this->assertArrayHasKey('typeCourse', $responseData);
        $this->assertArrayHasKey('icon', $responseData);
        $this->assertArrayHasKey('totalChapters', $responseData);
        $this->assertArrayHasKey('locale', $responseData);
        $this->assertArrayHasKey('languages', $responseData);
        $this->assertArrayHasKey('active', $responseData);
        $this->assertArrayHasKey('open', $responseData);
        $this->assertArrayHasKey('open_visible', $responseData);
        $this->assertArrayHasKey('isNew', $responseData);
        $this->assertArrayHasKey('translation', $responseData);
        $this->assertArrayHasKey('averageRating', $responseData);
        $this->assertArrayHasKey('usersStartCourse', $responseData);
        $this->assertArrayHasKey('usersFinishCourse', $responseData);
        $this->assertArrayHasKey('totalTimeCourse', $responseData);

        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     */
    public function testCreatorAndUpdaterAreRetrieveEvenWhenAreDeleted(): void
    {
        $creator = $this->createAndGetUser(
            email: '<EMAIL>',
            password: self::DEFAULT_USER_PASSWORD,
        );

        $this->loginAndGetToken(
            email: $creator->getEmail(),
            password: self::DEFAULT_USER_PASSWORD
        );

        $course = $this->createAndGetCourse(
            name: 'Test Course',
            code: 'TEST-CODE-123',
        );

        $this->getEntityManager()->remove($creator);
        $this->getEntityManager()->flush();

        $userToken = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            'GET',
            AdminCourseEndpoints::coursesDetailEndpoint($course->getId()),
            [],
            [],
            [],
            $userToken
        );

        $this->assertEquals(200, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('createdBy', $responseData);
        $this->assertArrayHasKey('updatedBy', $responseData);
        $this->assertEquals($creator->getEmail(), $responseData['createdBy']);
        $this->assertEquals($creator->getEmail(), $responseData['updatedBy']);

        $this->hardDeleteUsersByIds([$creator->getId()]);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function getCoursesListResponse(): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'GET',
            AdminCourseEndpoints::coursesEndpoint(),
            [],
            [],
            [],
            $userToken
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function getCoursesDetailResponse(Course $course): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'GET',
            AdminCourseEndpoints::coursesDetailEndpoint($course->getId()),
            [],
            [],
            [],
            $userToken
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function getCoursePreDataResponse(): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'GET',
            AdminCourseEndpoints::preDataEndpoint(),
            [],
            [],
            [],
            $userToken
        );
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([Course::class]);
        $this->truncateEntities([TypeCourse::class]);
        $this->truncateEntities([CourseCategory::class]);

        parent::tearDown();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSingleTypeCourseStructure(): void
    {
        $this->createAndGetCourse();
        $responseData = $this->extractResponseData($this->getCoursesListResponse());

        $this->assertNotEmpty($responseData['typeCourses']);
        $typeCourse = $responseData['typeCourses'][0];

        $expectedKeys = [
            'id',
            'name',
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $typeCourse);
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSingleValidationTotalCourses(): void
    {
        $this->createAndGetCourse();
        $responseData = $this->extractResponseData($this->getCoursesListResponse());

        $this->assertNotEmpty($responseData['totalCourses']);
        $totalCourses = $responseData['totalCourses'];
        $this->assertIsInt($totalCourses, 'The data entered must be an integer.');
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSingleCourseCategoriesStructure(): void
    {
        $this->createAndGetCourse();
        $responseData = $this->extractResponseData($this->getCoursesListResponse());

        $this->assertNotEmpty($responseData['courseCategories']);
        $courseCategories = $responseData['courseCategories'][0];

        $expectedKeys = [
            'id',
            'name',
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $courseCategories);
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function getCoursesDeleteResponse(Course $course): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'DELETE',
            AdminCourseEndpoints::coursesDeleteEndPoint($course->getId()),
            [],
            [],
            [],
            $userToken
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testDeleteCourse(): void
    {
        $course = $this->createAndGetCourse();
        $this->assertNotNull($course, 'Course should not be null after creation.');
        $this->assertInstanceOf(Course::class, $course, 'Course should be an instance of Course.');
        if (!$course instanceof Course) {
            $this->fail('Expected $course to be an instance of Course.');
        }

        $response = $this->getCoursesDeleteResponse($course);

        $this->assertEquals(200, $response->getStatusCode());
    }
}
