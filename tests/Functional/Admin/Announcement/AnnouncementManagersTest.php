<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementManager;
use App\Entity\Course;
use App\Entity\User;
use App\Entity\UserManage;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendAnnouncementEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Symfony\Component\HttpFoundation\Response;

class AnnouncementManagersTest extends FunctionalTestCase
{
    private ?Course $course = null;
    private ?Announcement $announcement = null;
    private ?User $user = null;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->user = $this->getDefaultUser();
        $this->course = $this->createAndGetCourse();
        $this->announcement = $this->createAndGetAnnouncement(course: $this->course, createdBy: $this->user);
        $this->em = $this->getEntityManager();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     *
     * @dataProvider announcementManagersDataProvider
     */
    public function testSetAnnouncementManagers($userRoles): void
    {
        $this->user->setRoles($userRoles);
        $this->announcement->setCreatedBy($this->user);
        $this->getEntityManager()->flush();

        $manager1 = $this->getOrCreateUser('<EMAIL>', 'Manager', 'One', $userRoles);
        $manager2 = $this->getOrCreateUser('<EMAIL>', 'Manager', 'Two', $userRoles);

        $managers = ['managers' => [$manager1->getId(), $manager2->getId()]];
        $response = $this->getApiRequestResponse($this->announcement->getId(), $managers);

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        // Verify managers were set correctly
        $announcementManagers = $this->getEntityManager()
            ->getRepository(AnnouncementManager::class)
            ->findBy(['announcement' => $this->announcement]);

        $this->assertCount(2, $announcementManagers);

        $managerIds = array_map(function ($am) {
            return $am->getManager()->getId();
        }, $announcementManagers);

        $this->assertContains($manager1->getId(), $managerIds);
        $this->assertContains($manager2->getId(), $managerIds);

        // Reset Data.
        $this->announcement->setCourse(null);
        $this->announcement->setCreatedBy(null);
        $this->getEntityManager()->flush();

        $this->announcement->setCreatedBy(null);
        $this->truncateEntities([
            AnnouncementManager::class,
            Announcement::class,
            Course::class,
            UserManage::class,
        ]);

        $this->em->createQuery('DELETE FROM App\Entity\User u WHERE u.email = :email1 OR u.email = :email2')
            ->setParameter('email1', '<EMAIL>')
            ->setParameter('email2', '<EMAIL>')
            ->execute();
        $this->em->flush();
    }

    public static function announcementManagersDataProvider(): \Generator
    {
        yield 'User as admin' => [
            'userRoles' => ['ROLE_ADMIN'],
        ];

        yield 'User as manager' => [
            'userRoles' => ['ROLE_MANAGER'],
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     */
    public function testSetAnnouncementManagersAsUnauthorizedUser(): void
    {
        $this->user->setRoles(['ROLE_MANAGER']);
        $this->course = $this->createAndGetCourse();
        $this->announcement = $this->createAndGetAnnouncement(course: $this->course, createdBy: $this->user);
        $manager1 = $this->getOrCreateUser('<EMAIL>', 'Manager', 'One', ['ROLE_MANAGER']);

        // Set a different creator
        $creator = $this->getOrCreateUser('<EMAIL>', 'Creator', 'User', ['ROLE_MANAGER']);
        $this->announcement->setCreatedBy($creator);
        $this->getEntityManager()->flush();

        $managers = ['managers' => [$manager1->getId()]];
        $response = $this->getApiRequestResponse($this->announcement->getId(), $managers);

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());

        // Reset Data.
        $this->announcement->setCourse(null);
        $this->announcement->setCreatedBy(null);
        $this->getEntityManager()->flush();

        $this->announcement->setCreatedBy(null);
        $this->truncateEntities([
            AnnouncementManager::class,
            Announcement::class,
            Course::class,
            UserManage::class,
        ]);

        $this->em->createQuery('DELETE FROM App\Entity\User u WHERE u.email = :email1 OR u.email = :email2')
            ->setParameter('email1', '<EMAIL>')
            ->setParameter('email2', '<EMAIL>')
            ->execute();
        $this->em->flush();
    }

    public function testSetAnnouncementManagersWithInvalidPayload(): void
    {
        // Test with missing managers field
        $response = $this->getApiRequestResponse($this->announcement->getId(), ['wrong_field' => []]);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        // Test with non-array managers field
        $response = $this->getApiRequestResponse($this->announcement->getId(), ['managers' => 'not_an_array']);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        // Test with non-integer values in managers array
        $response = $this->getApiRequestResponse($this->announcement->getId(), ['managers' => ['not_an_integer']]);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     */
    public function testSetAnnouncementManagersWithInvalidManagerIds(): void
    {
        // Test with non-existent manager ID
        $nonExistentId = 99999;
        $response = $this->getApiRequestResponse($this->announcement->getId(), ['managers' => [$nonExistentId]]);
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        // Test with user who is not a manager
        $regularUser = $this->getOrCreateUser('<EMAIL>', 'Regular', 'User', ['ROLE_USER']);
        $managers = ['managers' => [$regularUser->getId()]];
        $response = $this->getApiRequestResponse($this->announcement->getId(), $managers);

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        // Reset Data.
        $this->announcement->setCourse(null);
        $this->announcement->setCreatedBy(null);
        $this->getEntityManager()->flush();

        $this->announcement->setCreatedBy(null);
        $this->truncateEntities([
            AnnouncementManager::class,
            Announcement::class,
            Course::class,
            UserManage::class,
        ]);
        $this->em->createQuery('DELETE FROM App\Entity\User u WHERE u.email = :email1')
            ->setParameter('email1', '<EMAIL>')
            ->execute();
        $this->em->flush();
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     */
    public function testReplaceExistingManagers(): void
    {
        // Create a new manager
        $manager3 = $this->getOrCreateUser('<EMAIL>', 'Manager', 'Three', ['ROLE_MANAGER']);
        $managers = ['managers' => [$manager3->getId()]];

        // Replace with new set of managers
        $response = $this->getApiRequestResponse($this->announcement->getId(), $managers);
        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        // Verify only the new manager is associated
        $announcementManagers = $this->getEntityManager()
            ->getRepository(AnnouncementManager::class)
            ->findBy(['announcement' => $this->announcement]);

        $this->assertCount(1, $announcementManagers);
        $this->assertEquals($manager3->getId(), $announcementManagers[0]->getManager()->getId());

        // Reset Data.
        $this->announcement->setCourse(null);
        $this->announcement->setCreatedBy(null);
        $this->getEntityManager()->flush();

        $this->announcement->setCreatedBy(null);
        $this->truncateEntities([
            AnnouncementManager::class,
            Announcement::class,
            Course::class,
            UserManage::class,
        ]);

        $this->em->createQuery('DELETE FROM App\Entity\User u WHERE u.email = :email1')
            ->setParameter('email1', '<EMAIL>')
            ->execute();
        $this->em->flush();
    }

    private function getApiRequestResponse($announcementId, $managers): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeFrontendApiRequest(
            'POST',
            FrontendAnnouncementEndpoints::announcementManagersEndpoint($announcementId),
            $managers,
            [],
            [],
            $userToken
        );
    }
}
