<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Security;

use App\Tests\Functional\V2\SQLiteFunctionalTestCase;
use App\Tests\Mother\Entity\RefreshTokenMother;
use App\V2\Domain\Security\Exceptions\RefreshTokenNotFoundException;
use App\V2\Domain\Security\RefreshTokenCriteria;
use App\V2\Infrastructure\Persistence\Security\DoctrineRefreshTokenRepository;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\Persistence\Mapping\MappingException;

class DoctrineRefreshTokenRepositoryTest extends SQLiteFunctionalTestCase
{
    private const string TABLE_NAME = 'refresh_tokens';

    /**
     * @throws DBALException
     * @throws SchemaException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $schema = $this->getSchema();
        if ($schema->hasTable(self::TABLE_NAME)) {
            $this->dropTable();
        }

        $this->createTable($schema);
    }

    /**
     * @throws SchemaException
     * @throws DBALException
     */
    private function createTable(Schema $schema): void
    {
        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'integer');
        $table->addColumn('refresh_token', 'string');
        $table->addColumn('username', 'string');
        $table->addColumn('valid', 'datetime');
        $table->addColumn('extra', 'string', ['notnull' => false]);

        $table->setPrimaryKey(['id']);

        foreach ($this->getConnection()->getDatabasePlatform()->getCreateTableSQL($table) as $sql) {
            $this->getConnection()->executeStatement($sql);
        }
    }

    public function testPut(): void
    {
        $refreshTokenRepository = new DoctrineRefreshTokenRepository($this->em);

        $refreshToken1 = RefreshTokenMother::create(
            refreshToken: 'token_test_1'
        );

        $refreshToken2 = RefreshTokenMother::create(
            refreshToken: 'token_test_2'
        );

        $refreshTokenRepository->put(
            $refreshToken1
        );

        $refreshTokenRepository->put(
            $refreshToken2
        );

        $result = $refreshTokenRepository->findOneBy(
            RefreshTokenCriteria::createEmpty()
        );

        $this->assertEquals($refreshToken1, $result);

        $result = $refreshTokenRepository->findOneBy(
            RefreshTokenCriteria::createEmpty()
                ->filterByToken('token_test_2')
        );

        $this->assertEquals($refreshToken2, $result);
    }

    public function testDelete(): void
    {
        $refreshTokenRepository = new DoctrineRefreshTokenRepository($this->em);

        $refreshToken = RefreshTokenMother::create(
            refreshToken: 'token_test_1'
        );

        $refreshTokenRepository->put(
            $refreshToken
        );

        $result = $refreshTokenRepository->findOneBy(
            RefreshTokenCriteria::createEmpty()
                ->filterByToken('token_test_1')
        );
        $this->assertEquals($refreshToken, $result);

        $refreshTokenRepository->delete($refreshToken);

        $this->expectException(RefreshTokenNotFoundException::class);
        $refreshTokenRepository->findOneBy(
            RefreshTokenCriteria::createEmpty()
                ->filterByToken('token_test_1')
        );
    }

    /**
     * @throws DBALException
     */
    private function dropTable(): void
    {
        $this->em->getConnection()
            ->executeStatement('DROP TABLE IF EXISTS `' . self::TABLE_NAME . '`');
    }

    /**
     * @throws MappingException
     * @throws DBALException
     */
    protected function tearDown(): void
    {
        $this->dropTable();
        parent::tearDown();
    }
}
