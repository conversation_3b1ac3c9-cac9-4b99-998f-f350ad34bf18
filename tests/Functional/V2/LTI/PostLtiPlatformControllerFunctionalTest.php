<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\LTI;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\LtiEndpoints;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\LtiPlatformFixtureTrait;
use App\Tests\Functional\V2\Fixtures\LtiRegistrationFixtureTrait;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\LTI\LtiPlatformCriteria;
use App\V2\Domain\LTI\LtiPlatformRepository;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class PostLtiPlatformControllerFunctionalTest extends FunctionalTestCase
{
    use LtiRegistrationFixtureTrait;
    use LtiPlatformFixtureTrait;
    use UserHelperTrait;

    private array $userIds = [];

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $superAdminUser = $this->createAndGetUser(
            roles: ['ROLE_SUPER_ADMIN'],
            email: '<EMAIL>',
        );

        $this->userIds[] = $superAdminUser->getId();
    }

    private function getLtiPlatformRepository(): object
    {
        return $this->client->getContainer()
            ->get('App\V2\Domain\LTI\LtiPlatformRepository');
    }

    public function testOk(): void
    {
        /** @var LtiPlatformRepository $repository */
        $repository = $this->getLtiPlatformRepository();

        $registration = $this->setAndGetLtiRegistrationInRepository(
            name: 'Registration 1',
            clientId: 'registration-1',
        );

        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiPlatformEndpoint($registration->getId()->value()),
            body: [
                'name' => 'Platform 1',
                'audience' => 'Platform audience',
                'oidc_authentication_url' => 'https://example.com/oidc-authentication',
                'oauth2_access_token_url' => 'https://example.com/oauth2-token',
                'jwks_url' => 'https://example.com/jwks',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());

        $platform = $repository->findOneBy(
            LtiPlatformCriteria::createEmpty()
                ->filterByRegistrationId($registration->getId())
        );

        $this->assertEquals('Platform 1', $platform->getName());
        $this->assertEquals('Platform audience', $platform->getAudience());
        $this->assertEquals(
            'https://example.com/oidc-authentication',
            $platform->getOidcAuthenticationUrl()->value()
        );
        $this->assertEquals(
            'https://example.com/oauth2-token',
            $platform->getOauth2AccessTokenUrl()->value()
        );
        $this->assertEquals(
            'https://example.com/jwks',
            $platform->getJwksUrl()->value()
        );

        // Update
        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiPlatformEndpoint($registration->getId()->value()),
            body: [
                'name' => 'Platform 1 Updated',
                'audience' => 'Platform audience',
                'oidc_authentication_url' => 'https://example.com/oidc-authentication',
                'oauth2_access_token_url' => 'https://example.com/oauth2-token',
                'jwks_url' => 'https://example.com/jwks',
            ],
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());
        $platformUpdated = $repository->findOneBy(
            LtiPlatformCriteria::createEmpty()
                ->filterByRegistrationId($registration->getId())
        );
        $this->assertNotEquals($platform, $platformUpdated);
        $this->assertEquals($platform->getId(), $platformUpdated->getId());
        $this->assertEquals('Platform 1 Updated', $platformUpdated->getName());
        $this->assertEquals('Platform audience', $platformUpdated->getAudience());
    }

    public function testForbidden(): void
    {
        $user = $this->createAndGetUser(
            roles: ['ROLE_ADMIN'],
            email: '<EMAIL>',
        );
        $this->userIds[] = $user->getId();

        $token = $this->loginAndGetToken(email: $user->getEmail());

        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiPlatformEndpoint(UuidMother::create()->value()),
            body: [
                'name' => 'Platform 1',
                'audience' => 'Platform audience',
                'oidc_authentication_url' => 'https://example.com/oidc-authentication',
                'oauth2_access_token_url' => 'https://example.com/oauth2-token',
                'jwks_url' => 'https://example.com/jwks',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    #[DataProvider('provideBadRequest')]
    public function testBadRequest(array $payload, array $violations): void
    {
        $registration = $this->setAndGetLtiRegistrationInRepository(
            name: 'Registration 1',
            clientId: 'registration-1',
        );

        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiPlatformEndpoint($registration->getId()->value()),
            body: $payload,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
        $this->assertArrayHasKey('metadata', $content);
        $metadata = $content['metadata'];
        $this->assertArrayHasKey('violations', $metadata);
        $this->assertEquals($violations, $metadata['violations']);
    }

    public static function provideBadRequest(): \Generator
    {
        yield 'empty body' => [
            'payload' => [],
            'violations' => [
                '' => 'Body cannot be empty',
                '[name]' => 'This field is missing.',
                '[audience]' => 'This field is missing.',
                '[oidc_authentication_url]' => 'This field is missing.',
                '[oauth2_access_token_url]' => 'This field is missing.',
                '[jwks_url]' => 'This field is missing.',
            ],
        ];

        yield 'wrong values' => [
            'payload' => [
                'name' => '',
                'audience' => 'Platform audience',
                'oidc_authentication_url' => 'ht://example.com/oidc-authentication',
                'oauth2_access_token_url' => '',
                'jwks_url' => 'https://example.com/jwks',
            ],
            'violations' => [
                '[name]' => 'This value should not be blank.',
                '[oidc_authentication_url]' => 'This value is not a valid URL.',
                '[oauth2_access_token_url]' => 'This value should not be blank.',
            ],
        ];
    }

    public function testLtiRegistrationNotFound(): void
    {
        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiPlatformEndpoint(UuidMother::create()->value()),
            body: [
                'name' => 'Platform 1 Updated',
                'audience' => 'Platform audience',
                'oidc_authentication_url' => 'https://example.com/oidc-authentication',
                'oauth2_access_token_url' => 'https://example.com/oauth2-token',
                'jwks_url' => 'https://example.com/jwks',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('LTI registration could not be found.', $content['message']);
    }

    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds($this->userIds);
        parent::tearDown();
    }
}
