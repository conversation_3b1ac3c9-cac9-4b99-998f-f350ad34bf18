<?php

declare(strict_types=1);

namespace App\Tests\Functional;

use App\Entity\Chapter;
use App\Entity\Scorm;
use App\Entity\Season;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Tests\Functional\HelperTrait\ChapterHelperTrait;
use App\Tests\Functional\HelperTrait\ChapterTypeHelperTrait;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\SeasonHelperTrait;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;

class ChapterProgressResetFlowTest extends FunctionalTestCase
{
    use CourseHelperTrait;
    use ChapterHelperTrait;
    use ChapterTypeHelperTrait;
    use UserHelperTrait;
    use SeasonHelperTrait;

    protected $em;

    protected function setUp(): void
    {
        parent::setUp();
        $this->em = static::getContainer()->get(EntityManagerInterface::class);
    }

    public function testCompleteProgressResetFlow(): void
    {
        // 1. Create test data
        $course = $this->createAndGetCourse();
        $chapterType = $this->createAndGetChapterType('scorm');
        $user = $this->getDefaultUser();

        // Create a season
        $season = new Season();
        $season->setName('Test Season');
        $season->setCourse($course);
        $season->setSort(1);
        $season->setType(Season::TYPE_SEQUENTIAL);
        $this->em->persist($season);
        $this->em->flush();

        // Create a chapter
        $chapter = $this->createAndGetChapter(course: $course, chapterType: $chapterType, season: $season);
        $this->em->refresh($chapter);

        // Create UserCourse
        $userCourse = new UserCourse();
        $userCourse->setUser($user);
        $userCourse->setCourse($course);
        $this->em->persist($userCourse);
        $this->em->flush();

        // Create user progress in the chapter
        $userCourseChapter = new UserCourseChapter();
        $userCourseChapter->setUserCourse($userCourse);
        $userCourseChapter->setChapter($chapter);
        $userCourseChapter->setData([
            'scorm' => [
                'cmi.core.student_id' => $user->getId(),
                'cmi.core.student_name' => $user->getEmail(),
                'cmi.core.lesson_status' => 'incomplete',
                'cmi.suspend_data' => 'test progress data'
            ]
        ]);
        $userCourseChapter->setFinishedAt(new \DateTime());

        $this->em->persist($userCourseChapter);
        $this->em->flush();

        // Check for existing SCORMs
        $existingScorms = $this->em->getRepository(Scorm::class)->findBy(['chapter' => $chapter]);
        foreach ($existingScorms as $scorm) {
            $this->em->remove($scorm);
        }
        $this->em->flush();
        $this->em->clear(); // Clear EntityManager cache

        // Reload entities after clearing cache
        $chapter = $this->em->getRepository(Chapter::class)->find($chapter->getId());
        $userCourseChapter = $this->em->getRepository(UserCourseChapter::class)->find($userCourseChapter->getId());

        // 2. Create a temporary SCORM file
        $tempFile = tempnam(sys_get_temp_dir(), 'scorm_test_');
        file_put_contents($tempFile, 'test content');

        $file = new UploadedFile(
            $tempFile,
            'test.zip',
            'application/zip',
            null,
            true
        );

        // Authenticate user
        $this->client->loginUser($user);

        // 3. Make request to update SCORM
        $this->client->request(
            'POST',
            '/create-scorm',
            ['chapter' => $chapter->getId()],
            ['file' => $file]
        );

        // 4. Verify response
        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode(), $response->getContent());
        $responseContent = json_decode($response->getContent(), true);
        $this->assertFalse($responseContent['error']);

        // 5. Verify progress has been reset
        $this->em->refresh($userCourseChapter);
        $data = $userCourseChapter->getData();

        $this->assertNull($userCourseChapter->getFinishedAt());
        $this->assertArrayHasKey('scorm', $data);
        $this->assertCount(2, $data['scorm']); // Should only have student_id and student_name
        $this->assertArrayHasKey('cmi.core.student_id', $data['scorm']);
        $this->assertArrayHasKey('cmi.core.student_name', $data['scorm']);
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Scorm::class,
            UserCourseChapter::class,
            UserCourse::class,
            Chapter::class,
            Season::class
        ]);

        parent::tearDown();
    }
}
