<?php

declare(strict_types=1);

namespace App\Tests\Functional\Service\DateFormatter;

use App\Service\DateFormatter\DateFormatterService;
use App\Tests\Functional\FunctionalTestCase;

class DateFormatterTest extends FunctionalTestCase
{
    private const TEST_DEBUG_MODE = false;

    protected function setUp(): void
    {
        parent::setUp();
        self::$debugMode = self::TEST_DEBUG_MODE;
    }

    /**
     * Test that DateFormatterService is properly registered.
     */
    public function testDateFormatterServiceIsRegistered(): void
    {
        $this->log('Testing DateFormatterService registration');

        $dateFormatter = $this->getContainer()->get(DateFormatterService::class);

        $this->assertInstanceOf(
            DateFormatterService::class,
            $dateFormatter,
            'DateFormatterService should be properly registered'
        );
    }

    /**
     * Test date formatting with different locales.
     *
     * @dataProvider localeProvider
     */
    public function testFormatDateWithDifferentLocales(string $locale): void
    {
        $this->log("Testing date formatting with locale: $locale");

        $this->setLocaleForUser($locale);

        $dateFormatter = $this->getContainer()->get(DateFormatterService::class);
        $testDate = new \DateTimeImmutable('2024-01-15 14:30:00');

        $formattedDate = $dateFormatter->formatDate($testDate, 'short', $locale);

        $this->assertIsString($formattedDate, 'Formatted date should be a string');
        $this->assertNotEmpty($formattedDate, 'Formatted date should not be empty');

        // Verify that the date contains expected elements based on locale
        if ('es' === $locale) {
            $this->assertMatchesRegularExpression('/\d{1,2}\/\d{1,2}\/\d{2,4}/', $formattedDate, 'Spanish date should match DD/MM/YY format');
        } elseif ('en' === $locale) {
            $this->assertMatchesRegularExpression('/\d{1,2}\/\d{1,2}\/\d{2,4}/', $formattedDate, 'English date should contain date elements');
        }
    }

    /**
     * Test different date format styles.
     *
     * @dataProvider formatStyleProvider
     */
    public function testFormatDateWithDifferentStyles(string $format): void
    {
        $this->log("Testing date formatting with style: $format");

        $dateFormatter = $this->getContainer()->get(DateFormatterService::class);
        $testDate = new \DateTimeImmutable('2024-01-15 14:30:00');

        $formattedDate = $dateFormatter->formatDate($testDate, $format, 'es');

        $this->assertIsString($formattedDate, 'Formatted date should be a string');
        $this->assertNotEmpty($formattedDate, 'Formatted date should not be empty');

        // Verify that different formats produce different outputs
        $shortFormat = $dateFormatter->formatDate($testDate, 'short', 'es');
        $longFormat = $dateFormatter->formatDate($testDate, 'long', 'es');

        if ('short' === $format) {
            $this->assertEquals($shortFormat, $formattedDate, 'Short format should match');
        } elseif ('long' === $format) {
            $this->assertEquals($longFormat, $formattedDate, 'Long format should match');
            $this->assertNotEquals($shortFormat, $longFormat, 'Long format should differ from short format');
        }
    }

    public static function formatStyleProvider(): \Generator
    {
        yield 'Short format' => ['short'];
        yield 'Medium format' => ['medium'];
        yield 'Long format' => ['long'];
        yield 'Full format' => ['full'];
    }

    /**
     * Test date and time formatting.
     */
    public function testFormatDateTime(): void
    {
        $this->log('Testing date and time formatting');

        $dateFormatter = $this->getContainer()->get(DateFormatterService::class);
        $testDateTime = new \DateTimeImmutable('2024-01-15 14:30:00');

        $formattedDateTime = $dateFormatter->formatDateTime($testDateTime, 'short', 'short', 'es');

        $this->assertIsString($formattedDateTime, 'Formatted datetime should be a string');
        $this->assertNotEmpty($formattedDateTime, 'Formatted datetime should not be empty');

        // Should contain both date and time elements
        $this->assertMatchesRegularExpression('/\d/', $formattedDateTime, 'Should contain numeric elements');
    }

    /**
     * Test custom pattern formatting.
     */
    public function testFormatWithPattern(): void
    {
        $this->log('Testing custom pattern formatting');

        $dateFormatter = $this->getContainer()->get(DateFormatterService::class);
        $testDate = new \DateTimeImmutable('2024-01-15 14:30:00');

        $customPattern = 'dd/MM/yyyy';
        $formattedDate = $dateFormatter->formatWithPattern($testDate, $customPattern, 'es');

        $this->assertIsString($formattedDate, 'Formatted date should be a string');
        $this->assertEquals('15/01/2024', $formattedDate, 'Custom pattern should produce expected format');
    }

    /**
     * Test relative date formatting.
     */
    public function testFormatRelativeDate(): void
    {
        $this->log('Testing relative date formatting');

        $dateFormatter = $this->getContainer()->get(DateFormatterService::class);
        $testDate = new \DateTimeImmutable('2024-01-15');

        $formattedDate = $dateFormatter->formatRelativeDate($testDate, 'es');

        $this->assertIsString($formattedDate, 'Formatted relative date should be a string');
        $this->assertNotEmpty($formattedDate, 'Formatted relative date should not be empty');
    }

    /**
     * Test locale mapping functionality.
     */
    public function testLocaleMapping(): void
    {
        $this->log('Testing locale mapping');

        $dateFormatter = $this->getContainer()->get(DateFormatterService::class);
        $testDate = new \DateTimeImmutable('2024-01-15');

        // Test with different locales to ensure mapping works
        $spanishDate = $dateFormatter->formatDate($testDate, 'medium', 'es');
        $englishDate = $dateFormatter->formatDate($testDate, 'medium', 'en');

        $this->assertIsString($spanishDate, 'Spanish formatted date should be a string');
        $this->assertIsString($englishDate, 'English formatted date should be a string');

        // They should be different due to locale differences
        $this->assertNotEquals($spanishDate, $englishDate, 'Different locales should produce different formats');
    }

    /**
     * Test error handling with invalid locale (should throw exception).
     */
    public function testErrorHandlingWithInvalidLocale(): void
    {
        $this->log('Testing error handling with invalid locale');

        $dateFormatter = $this->getContainer()->get(DateFormatterService::class);
        $testDate = new \DateTimeImmutable('2024-01-15');

        // Test with invalid locale - should throw exception
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("Locale 'invalid_locale_xyz' is not supported");

        $dateFormatter->formatDate($testDate, 'short', 'invalid_locale_xyz');
    }

    /**
     * Test integration with user locale from security context.
     */
    public function testIntegrationWithUserLocale(): void
    {
        $this->log('Testing integration with user locale from security context');

        // Set user locale
        $this->setLocaleForUser('fr');

        $dateFormatter = $this->getContainer()->get(DateFormatterService::class);
        $testDate = new \DateTimeImmutable('2024-01-15');

        // Format without explicit locale - should use user's locale
        $formattedDate = $dateFormatter->formatDate($testDate, 'short');

        $this->assertIsString($formattedDate, 'Should format with user locale');
        $this->assertNotEmpty($formattedDate, 'Should return formatted date');
    }

    /**
     * Test date formatting consistency for diploma generation.
     */
    public function testDateFormattingForDiplomas(): void
    {
        $this->log('Testing date formatting consistency for diploma generation');

        $dateFormatter = $this->getContainer()->get(DateFormatterService::class);
        $diplomaDate = new \DateTimeImmutable('2024-01-15');

        // Test the specific format used in diploma strategies
        $spanishDiplomaDate = $dateFormatter->formatDate($diplomaDate, 'short', 'es');
        $englishDiplomaDate = $dateFormatter->formatDate($diplomaDate, 'short', 'en');

        $this->assertIsString($spanishDiplomaDate, 'Spanish diploma date should be string');
        $this->assertIsString($englishDiplomaDate, 'English diploma date should be string');

        // Verify consistency - same date should always format the same way
        $secondSpanishFormat = $dateFormatter->formatDate($diplomaDate, 'short', 'es');
        $this->assertEquals($spanishDiplomaDate, $secondSpanishFormat, 'Date formatting should be consistent');
    }
}
