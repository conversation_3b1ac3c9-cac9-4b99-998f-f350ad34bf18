<?php

declare(strict_types=1);

namespace App\Tests\Service\Api;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementTemporalization;
use App\Entity\AnnouncementUser;
use App\Entity\AnnouncementUserDigitalSignature;
use App\Entity\Chapter;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Enum\ChapterContent;
use App\Repository\AnnouncementGroupSessionRepository;
use App\Repository\AnnouncementUserDigitalSignatureRepository;
use App\Repository\AnnouncementUserRepository;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementGroupAssistanceService;
use App\Service\Api\ApiAnnouncementService;
use App\Service\Geolocation\GeolocationService;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\AnnouncementMother;
use App\Tests\Mother\Entity\ChapterMother;
use App\Tests\Mother\Entity\ChapterTypeMother;
use App\Tests\Mother\Entity\ContentMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\SeasonMother;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\Rule\InvokedCount;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;

class ApiAnnouncementServiceTest extends TestCase
{
    private function getService(
        ?EntityManagerInterface $em = null,
        ?SettingsService $settingsService = null,
        ?AnnouncementConfigurationsService $announcementConfigurationsService = null,
        ?AnnouncementGroupAssistanceService $announcementGroupAssistanceService = null,
        ?Security $security = null,
        ?GeolocationService $geolocationService = null,
        ?RequestStack $requestStack = null
    ): ApiAnnouncementService {
        return new ApiAnnouncementService(
            $em ?? $this->createMock(EntityManagerInterface::class),
            $settingsService ?? $this->createMock(SettingsService::class),
            $announcementConfigurationsService ?? $this->createMock(AnnouncementConfigurationsService::class),
            $announcementGroupAssistanceService ?? $this->createMock(AnnouncementGroupAssistanceService::class),
            $security ?? $this->createMock(Security::class),
            $geolocationService ?? $this->createMock(GeolocationService::class),
            $requestStack ?? $this->createMock(RequestStack::class)
        );
    }

    private function getSecurityMock(User $user, ?InvokedCount $invokedCount = null): Security
    {
        $security = $this->createMock(Security::class);
        $security->expects($invokedCount ?: $this->once())
            ->method('getUser')
            ->willReturn($user);

        return $security;
    }

    private function getAnnouncementUserRepositoryMock(
        ?AnnouncementUser $announcementUser,
        ?InvokedCount $invokedCount = null
    ): AnnouncementUserRepository {
        $announcementUserRepository = $this->createMock(AnnouncementUserRepository::class);
        $announcementUserRepository->expects($invokedCount ?: $this->once())
            ->method('findOneBy')
            ->willReturn($announcementUser);

        return $announcementUserRepository;
    }

    private function getEntityManagerMock(
        ?AnnouncementUserRepository $announcementUserRepository = null,
        ?AnnouncementGroupSessionRepository $announcementGroupSessionRepository = null,
        ?AnnouncementUserDigitalSignatureRepository $announcementUserDigitalSignatureRepository = null
    ): EntityManagerInterface {
        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')
            ->willReturnCallback(function ($entity) use (
                $announcementUserRepository,
                $announcementGroupSessionRepository,
                $announcementUserDigitalSignatureRepository
            ) {
                if (AnnouncementUser::class === $entity) {
                    return $announcementUserRepository;
                }

                if (AnnouncementGroupSession::class === $entity) {
                    return $announcementGroupSessionRepository;
                }

                if (AnnouncementUserDigitalSignature::class === $entity) {
                    return $announcementUserDigitalSignatureRepository;
                }

                return null;
            });

        return $em;
    }

    /**
     * @dataProvider courseHasSignatureDigitalPendingNoCorrectAnnouncementUserDataProvider
     */
    public function testCourseHasSignatureDigitalPendingNoCorrectAnnouncementUser(
        ?AnnouncementUser $announcementUser
    ): void {
        $user = new User();
        $security = $this->getSecurityMock($user);
        $announcementUserRepository = $this->getAnnouncementUserRepositoryMock($announcementUser);
        $em = $this->getEntityManagerMock($announcementUserRepository);
        $service = $this->getService(
            $em,
            null,
            null,
            null,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());
        $this->assertFalse($service->theCourseHasSignatureDigitalPending($announcement));
    }

    public static function courseHasSignatureDigitalPendingNoCorrectAnnouncementUserDataProvider(): \Generator
    {
        yield 'No announcement user' => [
            null,
        ];

        yield 'Announcement user without announcement group' => [
            new AnnouncementUser(),
        ];
    }

    public function testCourseHasSignatureDigitalPendingWithoutSessions(): void
    {
        $user = new User();
        $security = $this->getSecurityMock($user);

        $announcementGroup = $this->createMock(AnnouncementGroup::class);
        $announcementGroup->method('getId')->willReturn(1);

        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncementGroup($announcementGroup);

        $em = $this->getEntityManagerMock($this->getAnnouncementUserRepositoryMock($announcementUser));

        $announcementGroupAssistanceService = $this->createMock(AnnouncementGroupAssistanceService::class);
        $announcementGroupAssistanceService->expects($this->once())
            ->method('getSessions')
            ->willReturn([]);

        $service = $this->getService(
            $em,
            null,
            null,
            $announcementGroupAssistanceService,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());
        $this->assertFalse($service->theCourseHasSignatureDigitalPending($announcement));
    }

    public function testCourseHasSignatureDigitalPendingWithoutDigitalSignature(): void
    {
        $user = new User();
        $security = $this->getSecurityMock($user);

        $announcementGroup = $this->createMock(AnnouncementGroup::class);
        $announcementGroup->method('getId')->willReturn(1);

        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncementGroup($announcementGroup);

        $announcementGroupAssistanceService = $this->createMock(AnnouncementGroupAssistanceService::class);
        $announcementGroupAssistanceService->expects($this->once())
            ->method('getSessions')
            ->willReturn([
                [
                    'id' => 1,
                ],
                [
                    'id' => 2,
                ],
            ]);

        $announcementGroupSessionRepository = $this->createMock(AnnouncementGroupSessionRepository::class);
        $announcementGroupSessionRepository->expects($this->once())
            ->method('findBy')
            ->with(['id' => [1, 2]])
            ->willReturn([
                new AnnouncementGroupSession(),
                new AnnouncementGroupSession(),
            ]);

        $em = $this->getEntityManagerMock(
            $this->getAnnouncementUserRepositoryMock($announcementUser),
            $announcementGroupSessionRepository
        );

        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->expects($this->once())
            ->method('hasDigitalSignature')
            ->willReturn(false);

        $service = $this->getService(
            $em,
            null,
            $announcementConfigurationsService,
            $announcementGroupAssistanceService,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());
        $this->assertFalse($service->theCourseHasSignatureDigitalPending($announcement));
    }

    public function testCourseHasSignatureDigitalPendingWithSessionsNotInProcess(): void
    {
        $user = new User();
        $security = $this->getSecurityMock($user);

        $announcementGroup = $this->createMock(AnnouncementGroup::class);
        $announcementGroup->method('getId')->willReturn(1);

        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncementGroup($announcementGroup);

        $announcementGroupAssistanceService = $this->createMock(AnnouncementGroupAssistanceService::class);
        $announcementGroupAssistanceService->expects($this->once())
            ->method('getSessions')
            ->willReturn([
                [
                    'id' => 1,
                    'state' => 'OTHER',
                ],
                [
                    'id' => 2,
                    'state' => 'OTHER',
                ],
            ]);

        $announcementGroupSessionRepository = $this->createMock(AnnouncementGroupSessionRepository::class);
        $announcementGroupSessionRepository->expects($this->once())
            ->method('findBy')
            ->willReturnCallback(
                function ($criteria) {
                    if (isset($criteria['id'])) {
                        $sessions = [];
                        foreach ($criteria['id'] as $id) {
                            $sessions[] = new AnnouncementGroupSession();
                        }

                        return $sessions;
                    }

                    return null;
                }
            );

        $em = $this->getEntityManagerMock(
            $this->getAnnouncementUserRepositoryMock($announcementUser),
            $announcementGroupSessionRepository
        );

        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->expects($this->once())
            ->method('hasDigitalSignature')
            ->willReturn(true);

        $service = $this->getService(
            $em,
            null,
            $announcementConfigurationsService,
            $announcementGroupAssistanceService,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());

        $this->assertFalse($service->theCourseHasSignatureDigitalPending($announcement));
    }

    public function testCourseHasSignatureDigitalPendingWithSessionInProcess(): void
    {
        $user = new User();
        $security = $this->getSecurityMock($user, $this->exactly(2));

        $announcementGroup = $this->createMock(AnnouncementGroup::class);
        $announcementGroup->method('getId')->willReturn(1);

        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncementGroup($announcementGroup);

        $announcementGroupAssistanceService = $this->createMock(AnnouncementGroupAssistanceService::class);
        $announcementGroupAssistanceService->expects($this->once())
            ->method('getSessions')
            ->willReturn([
                [
                    'id' => 1,
                    'state' => 'OTHER',
                ],
                [
                    'id' => 2,
                    'state' => 'IN_PROCESS',
                ],
            ]);

        $announcementGroupSessionRepository = $this->createMock(AnnouncementGroupSessionRepository::class);
        $announcementGroupSessionRepository
            ->expects($this->once())
            ->method('findBy')
            ->with(['id' => [1, 2]])
            ->willReturn([
                new AnnouncementGroupSession(),
                new AnnouncementGroupSession(),
            ]);
        $announcementGroupSessionRepository
            ->expects($this->exactly(2))
            ->method('findOneBy')
            ->willReturn(new AnnouncementGroupSession());

        $announcementUserDigitalSignatureRepository = $this->createMock(
            AnnouncementUserDigitalSignatureRepository::class
        );
        $announcementUserDigitalSignatureRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(new AnnouncementUserDigitalSignature());

        $em = $this->getEntityManagerMock(
            $this->getAnnouncementUserRepositoryMock($announcementUser, $this->exactly(2)),
            $announcementGroupSessionRepository,
            $announcementUserDigitalSignatureRepository
        );

        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->expects($this->once())
            ->method('hasDigitalSignature')
            ->willReturn(true);

        $service = $this->getService(
            $em,
            null,
            $announcementConfigurationsService,
            $announcementGroupAssistanceService,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());
        $this->assertFalse($service->theCourseHasSignatureDigitalPending($announcement));
    }

    public function testCourseHasSignatureDigitalPendingWithSessionInProcessAndSignatureNotFound(): void
    {
        $user = new User();
        $security = $this->getSecurityMock($user, $this->exactly(2));

        $announcementGroup = $this->createMock(AnnouncementGroup::class);
        $announcementGroup->method('getId')->willReturn(1);

        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncementGroup($announcementGroup);

        $announcementGroupAssistanceService = $this->createMock(AnnouncementGroupAssistanceService::class);
        $announcementGroupAssistanceService->expects($this->once())
            ->method('getSessions')
            ->willReturn([
                [
                    'id' => 1,
                    'state' => 'OTHER',
                ],
                [
                    'id' => 2,
                    'state' => 'IN_PROCESS',
                ],
            ]);

        $announcementGroupSessionRepository = $this->createMock(AnnouncementGroupSessionRepository::class);
        $announcementGroupSessionRepository
            ->expects($this->once())
            ->method('findBy')
            ->with(['id' => [1, 2]])
            ->willReturn([
                new AnnouncementGroupSession(),
                new AnnouncementGroupSession(),
            ]);
        $announcementGroupSessionRepository
            ->expects($this->exactly(2))
            ->method('findOneBy')
            ->willReturn(new AnnouncementGroupSession());

        $announcementUserDigitalSignatureRepository = $this->createMock(
            AnnouncementUserDigitalSignatureRepository::class
        );
        $announcementUserDigitalSignatureRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(null);

        $em = $this->getEntityManagerMock(
            $this->getAnnouncementUserRepositoryMock($announcementUser, $this->exactly(2)),
            $announcementGroupSessionRepository,
            $announcementUserDigitalSignatureRepository
        );

        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->expects($this->once())
            ->method('hasDigitalSignature')
            ->willReturn(true);

        $service = $this->getService(
            $em,
            null,
            $announcementConfigurationsService,
            $announcementGroupAssistanceService,
            $security
        );

        $announcement = $this->createMock(Announcement::class);
        $announcement->method('getNotifiedAt')->willReturn(new \DateTimeImmutable());
        $this->assertTrue($service->theCourseHasSignatureDigitalPending($announcement));
    }

    /**
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     * @throws Exception
     */
    public function testGetTemporalizationAnnouncement(): void
    {
        $course = CourseMother::create();
        $announcement = AnnouncementMother::create(
            course: $course,
            startAt: new \DateTimeImmutable('-1 day'),
            finishAt: new \DateTimeImmutable('+1 day'),
        );
        $season1 = SeasonMother::create(
            id: 1,
            name: 'Season 1',
            sort: 1,
            course: $course,
        );
        $season2 = SeasonMother::create(
            id: 2,
            name: 'Season 2',
            sort: 2,
            course: $course,
        );

        $course
            ->addSeason($season1)
            ->addSeason($season2);

        $chapterType = ChapterTypeMother::create(
            id: ChapterContent::CONTENT_TYPE,
            name: 'Content',
        );

        $chapter1 = ChapterMother::create(
            title: 'Chapter 1',
            position: 1,
            type: $chapterType,
            season: $season1,
        );
        $chapter2 = ChapterMother::create(
            title: 'Chapter 2',
            position: 2,
            type: $chapterType,
            season: $season1,
        );
        $chapter3 = ChapterMother::create(
            title: 'Chapter 3',
            position: 1,
            type: $chapterType,
            season: $season2,
        );
        $chapter4 = ChapterMother::create(
            title: 'Chapter 4',
            position: 2,
            type: $chapterType,
            season: $season2,
        );
        $chapter5 = ChapterMother::create(
            title: 'Chapter 5',
            position: 3,
            type: $chapterType,
            season: $season1,
        );

        $chapter1->addContent(ContentMother::create(id: 1, chapter: $chapter1));
        $chapter2->addContent(ContentMother::create(id: 2, chapter: $chapter2));
        $chapter3->addContent(ContentMother::create(id: 3, chapter: $chapter3));
        $chapter4->addContent(ContentMother::create(id: 4, chapter: $chapter4));
        $chapter5->addContent(ContentMother::create(id: 5, chapter: $chapter5));

        $course
            ->addChapter($chapter1)
            ->addChapter($chapter2)
            ->addChapter($chapter3)
            ->addChapter($chapter4)
            ->addChapter($chapter5);

        $userCourse = $this->createMock(UserCourse::class);
        $announcementTemporalization = $this->createMock(AnnouncementTemporalization::class);
        $userCourseChapter = $this->createMock(UserCourseChapter::class);

        // Setup request mock
        $request = $this->createMock(Request::class);
        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->method('getCurrentRequest')->willReturn($request);
        $request->method('get')->with('new', false)->willReturn(false);
        $request->method('getSchemeAndHttpHost')->willReturn('https://example.com');

        // Setup repositories
        $announcementTemporalizationRepo = $this->createMock(EntityRepository::class);
        $announcementTemporalizationRepo->method('findOneBy')->willReturn($announcementTemporalization);

        $userCourseChapterRepo = $this->createMock(EntityRepository::class);
        $userCourseChapterRepo->method('findOneBy')->willReturn($userCourseChapter);

        $userCourseRepo = $this->createMock(EntityRepository::class);
        $userCourseRepo->method('findOneBy')->willReturn($userCourse);

        $chapterRepository = $this->createMock(EntityRepository::class);
        $chapterRepository->method('findBy')->willReturn($course->getChapters()->toArray());

        // Setup entity manager
        $repositories = [
            AnnouncementTemporalization::class => $announcementTemporalizationRepo,
            UserCourseChapter::class => $userCourseChapterRepo,
            UserCourse::class => $userCourseRepo,
            Chapter::class => $chapterRepository,
        ];
        $em = $this->createMock(EntityManagerInterface::class);
        $em->method('getRepository')->willReturnCallback(function ($entityClass) use ($repositories) {
            return $repositories[$entityClass] ?? $this->createMock(EntityRepository::class);
        });

        // Setup settings service
        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->method('get')->willReturn('UTC');

        // Setup announcement configurations service
        $announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $announcementConfigurationsService->method('hasTemporalization')->willReturn(true);

        $service = $this->getService(
            em: $em,
            settingsService: $settingsService,
            announcementConfigurationsService: $announcementConfigurationsService,
            geolocationService: $this->createMock(GeolocationService::class),
            requestStack: $requestStack,
        );

        // Call method
        $result = $service->getTemporalizationAnnouncement($announcement);

        // Assertions
        $this->assertIsArray($result);

        $this->assertCount(5, $result);
        $expectedOrder = [
            'Chapter 1',
            'Chapter 2',
            'Chapter 5',
            'Chapter 3',
            'Chapter 4',
        ];

        $resultOrder = array_map(fn ($chapter) => $chapter['title'], $result);

        $this->assertSame($expectedOrder, $resultOrder);
    }
}
