<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Announcement\Manager;

use App\V2\Domain\Announcement\Manager\AnnouncementManager;
use App\V2\Domain\Shared\Id\Id;

class AnnouncementManagerMother
{
    private const int DEFAULT_USER_ID = 1;
    private const int DEFAULT_ANNOUNCEMENT_ID = 1;

    public static function create(
        ?Id $userId = null,
        ?Id $announcementId = null,
    ): AnnouncementManager {
        return new AnnouncementManager(
            userId: $userId ?? new Id(self::DEFAULT_USER_ID),
            announcementId: $announcementId ?? new Id(self::DEFAULT_ANNOUNCEMENT_ID),
        );
    }
}
