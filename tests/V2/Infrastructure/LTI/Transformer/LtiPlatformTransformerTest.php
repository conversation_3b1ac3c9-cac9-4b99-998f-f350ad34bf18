<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\LTI\Transformer;

use App\Tests\V2\Mother\LTI\LtiPlatformMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\LTI\Transformer\LtiPlatformTransformer;
use OAT\Library\Lti1p3Core\Platform\Platform as CorePlatform;
use PHPUnit\Framework\TestCase;

class LtiPlatformTransformerTest extends TestCase
{
    /**
     * @throws InvalidUuidException
     */
    public function testFromLtiPlatformToCorePlatform(): void
    {
        $uuid1 = UuidMother::create();
        $ltiPlatform = LtiPlatformMother::create(
            id: $uuid1,
            registrationId: UuidMother::create(),
            name: 'Platform 1',
            audience: 'Audience platform 1'
        );
        $this->assertEquals(
            new CorePlatform(
                identifier: $uuid1->value(),
                name: 'Platform 1',
                audience: 'Audience platform 1',
                oidcAuthenticationUrl: 'https://example.com/lti1p3/oidc/authentication',
                oAuth2AccessTokenUrl: 'https://example.com/lti1p3/auth/platformKey/token'
            ),
            LtiPlatformTransformer::fromLtiPlatformToCorePlatform($ltiPlatform)
        );
    }
}
