<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Announcement\Manager;

use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerCollectionMother;
use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\Tests\V2\Mother\Announcement\Manager\ManagerMother;
use App\Tests\V2\Mother\Shared\Email\EmailMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Infrastructure\Announcement\Manager\AnnouncementManagerTransformer;
use PHPUnit\Framework\TestCase;

class AnnouncementManagerTransformerTest extends TestCase
{
    public function testFromCollectionToArrayWithManagers(): void
    {
        // Arrange
        $manager1 = ManagerMother::create(
            id: IdMother::create(1),
            email: EmailMother::create('<EMAIL>'),
            firstName: 'John',
            lastName: 'Doe'
        );

        $manager2 = ManagerMother::create(
            id: IdMother::create(2),
            email: EmailMother::create('<EMAIL>'),
            firstName: 'Jane',
            lastName: 'Smith'
        );

        $announcementManager1 = AnnouncementManagerMother::create();
        $announcementManager1->setManager($manager1);

        $announcementManager2 = AnnouncementManagerMother::create();
        $announcementManager2->setManager($manager2);

        $collection = new AnnouncementManagerCollection([
            $announcementManager1,
            $announcementManager2,
        ]);

        // Act
        $result = AnnouncementManagerTransformer::fromCollectionToArray($collection);

        // Assert
        $this->assertCount(2, $result);

        $this->assertEquals([
            'id' => 1,
            'email' => '<EMAIL>',
            'name' => 'John',
            'lastName' => 'Doe',
        ], $result[0]);

        $this->assertEquals([
            'id' => 2,
            'email' => '<EMAIL>',
            'name' => 'Jane',
            'lastName' => 'Smith',
        ], $result[1]);
    }

    public function testFromCollectionToArrayWithoutManagers(): void
    {
        // Arrange
        $announcementManager = AnnouncementManagerMother::create(
            userId: IdMother::create(1),
            announcementId: IdMother::create(1)
        );
        // Don't set manager (simulating case where manager is not hydrated)

        $collection = new AnnouncementManagerCollection([$announcementManager]);

        // Act
        $result = AnnouncementManagerTransformer::fromCollectionToArray($collection);

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals([
            'id' => 1,
        ], $result[0]);
    }

    public function testFromManagerToArray(): void
    {
        // Arrange
        $manager = ManagerMother::create(
            id: IdMother::create(1),
            email: EmailMother::create('<EMAIL>'),
            firstName: 'Test',
            lastName: 'User'
        );

        // Act
        $result = AnnouncementManagerTransformer::fromManagerToArray($manager);

        // Assert
        $this->assertEquals([
            'id' => 1,
            'email' => '<EMAIL>',
            'name' => 'Test',
            'lastName' => 'User',
        ], $result);
    }

    public function testFromCollectionToArrayWithEmptyCollection(): void
    {
        // Arrange
        $collection = AnnouncementManagerCollectionMother::empty();

        // Act
        $result = AnnouncementManagerTransformer::fromCollectionToArray($collection);

        // Assert
        $this->assertEmpty($result);
    }
}
