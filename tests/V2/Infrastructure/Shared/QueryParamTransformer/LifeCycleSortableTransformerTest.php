<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Shared\QueryParamTransformer;

use App\V2\Domain\Shared\Criteria\InvalidSortException;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Infrastructure\Shared\QueryParamTransformer\LifeCycleSortableTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class LifeCycleSortableTransformerTest extends TestCase
{
    /**
     * Test that getSortableFields returns the expected array of sortable fields.
     */
    public function testGetSortableFields(): void
    {
        // Act
        $result = LifeCycleSortableTransformer::getSortableFields();

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('created_at', $result);
        $this->assertArrayHasKey('updated_at', $result);
        $this->assertSame('createdAt', $result['created_at']);
        $this->assertSame('updatedAt', $result['updated_at']);
    }

    /**
     * Test that toSortable<PERSON>ield correctly transforms valid sort fields.
     */
    #[DataProvider('validSortFieldProvider')]
    public function testToSortableFieldWithValidField(string $sortBy, string $expected): void
    {
        // Act
        $result = LifeCycleSortableTransformer::toSortableField($sortBy);

        // Assert
        $this->assertInstanceOf(SortableField::class, $result);
        $this->assertSame($expected, $result->value());
    }

    /**
     * Test that toSortableField throws an exception for invalid sort fields.
     */
    public function testToSortableFieldWithInvalidField(): void
    {
        // Arrange
        $invalidField = 'invalid_field';

        // Assert
        $this->expectException(InvalidSortException::class);
        $this->expectExceptionMessage(\sprintf('Field %s is not sortable.', $invalidField));

        // Act
        LifeCycleSortableTransformer::toSortableField($invalidField);
    }

    /**
     * Data provider for testToSortableFieldWithValidField.
     */
    public static function validSortFieldProvider(): \Generator
    {
        yield 'created_at' => [
            'sortBy' => 'created_at',
            'expected' => 'createdAt',
        ];

        yield 'updated_at' => [
            'sortBy' => 'updated_at',
            'expected' => 'updatedAt',
        ];
    }
}
