<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin\LTI;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\LTI\LtiToolValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class LtiToolValidatorTest extends ValidatorTestCase
{
    #[DataProvider('provideValidatePostLtiTool')]
    public function testValidatePostLtiTool(array $payload): void
    {
        $this->expectNotToPerformAssertions();
        LtiToolValidator::validatePostLtiTool($payload);
    }

    public static function provideValidatePostLtiTool(): \Generator
    {
        yield 'valid data 1' => [
            'payload' => [
                'name' => 'Tool 1',
                'audience' => 'http://audience.com',
                'oidc_initiation_url' => 'https://example.com/authentication',
                'launch_url' => 'https://example.com/launch',
                'deep_linking_url' => 'https://example.com/deep_linking',
                'jwks_url' => 'https://example.com/jwks',
            ],
        ];
    }

    #[DataProvider('provideInvalidPostLtiTool')]
    public function testFailedValidatePostLtiTool(array $payload, array $violations): void
    {
        try {
            LtiToolValidator::validatePostLtiTool($payload);
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function provideInvalidPostLtiTool(): \Generator
    {
        yield 'empty body' => [
            'payload' => [],
            'violations' => [
                '' => 'Body cannot be empty',
                '[name]' => 'This field is missing.',
                '[audience]' => 'This field is missing.',
                '[oidc_initiation_url]' => 'This field is missing.',
                '[launch_url]' => 'This field is missing.',
                '[deep_linking_url]' => 'This field is missing.',
                '[jwks_url]' => 'This field is missing.',
            ],
        ];

        yield 'empty values' => [
            'payload' => [
                'name' => '',
                'audience' => '',
                'oidc_initiation_url' => '',
                'launch_url' => '',
                'deep_linking_url' => '',
                'jwks_url' => '',
            ],
            'violations' => [
                '[name]' => 'This value should not be blank.',
                '[audience]' => 'This value should not be blank.',
                '[oidc_initiation_url]' => 'This value should not be blank.',
                '[launch_url]' => 'This value should not be blank.',
                '[deep_linking_url]' => 'This value should not be blank.',
                '[jwks_url]' => 'This value should not be blank.',
            ],
        ];

        yield 'invalid urls' => [
            'payload' => [
                'name' => 'Name 1',
                'audience' => 'Audience',
                'oidc_initiation_url' => 'http://',
                'launch_url' => 'example.com',
                'deep_linking_url' => 'http//example.com',
                'jwks_url' => '/jwks',
            ],
            'violations' => [
                '[oidc_initiation_url]' => 'This value is not a valid URL.',
                '[launch_url]' => 'This value is not a valid URL.',
                '[deep_linking_url]' => 'This value is not a valid URL.',
                '[jwks_url]' => 'This value is not a valid URL.',
            ],
        ];

        yield 'invalid data type' => [
            'payload' => [
                'name' => 1,
                'audience' => 2,
                'oidc_initiation_url' => 3,
                'launch_url' => 4,
                'deep_linking_url' => 5,
                'jwks_url' => 6,
            ],
            'violations' => [
                '[name]' => 'This value should be of type string.',
                '[audience]' => 'This value should be of type string.',
                '[oidc_initiation_url]' => ['This value should be of type string.', 'This value is not a valid URL.'],
                '[launch_url]' => ['This value should be of type string.', 'This value is not a valid URL.'],
                '[deep_linking_url]' => ['This value should be of type string.', 'This value is not a valid URL.'],
                '[jwks_url]' => ['This value should be of type string.', 'This value is not a valid URL.'],
            ],
        ];
    }
}
