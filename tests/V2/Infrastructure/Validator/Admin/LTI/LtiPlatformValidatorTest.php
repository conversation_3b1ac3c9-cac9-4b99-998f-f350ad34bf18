<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin\LTI;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\LTI\LtiPlatformValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class LtiPlatformValidatorTest extends ValidatorTestCase
{
    #[DataProvider('providePostValidate')]
    public function testPostValidate(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        LtiPlatformValidator::validatePostLtiPlatform($payload);
    }

    public static function providePostValidate(): \Generator
    {
        yield 'valid data' => [
            'payload' => [
                'name' => 'Platform 1',
                'audience' => 'http://audience.com',
                'oidc_authentication_url' => 'https://example.com/authentication',
                'oauth2_access_token_url' => 'https://example.com/token',
                'jwks_url' => 'https://example.com/jwks',
            ],
        ];
    }

    #[DataProvider('providePostInvalidData')]
    public function testPostInvalidData(array $payload, array $violations): void
    {
        try {
            LtiPlatformValidator::validatePostLtiPlatform($payload);
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function providePostInvalidData(): \Generator
    {
        yield 'empty body' => [
            'payload' => [],
            'violations' => [
                '' => 'Body cannot be empty',
                '[name]' => 'This field is missing.',
                '[audience]' => 'This field is missing.',
                '[oidc_authentication_url]' => 'This field is missing.',
                '[oauth2_access_token_url]' => 'This field is missing.',
                '[jwks_url]' => 'This field is missing.',
            ],
        ];

        yield 'empty values' => [
            'payload' => [
                'name' => '',
                'audience' => '',
                'oidc_authentication_url' => '',
                'oauth2_access_token_url' => '',
                'jwks_url' => '',
            ],
            'violations' => [
                '[name]' => 'This value should not be blank.',
                '[audience]' => 'This value should not be blank.',
                '[oidc_authentication_url]' => 'This value should not be blank.',
                '[oauth2_access_token_url]' => 'This value should not be blank.',
                '[jwks_url]' => 'This value should not be blank.',
            ],
        ];

        yield 'invalid urls' => [
            'payload' => [
                'name' => 'Name 1',
                'audience' => 'Audience',
                'oidc_authentication_url' => 'http://',
                'oauth2_access_token_url' => 'example.com',
                'jwks_url' => '/jwks',
            ],
            'violations' => [
                '[oidc_authentication_url]' => 'This value is not a valid URL.',
                '[oauth2_access_token_url]' => 'This value is not a valid URL.',
                '[jwks_url]' => 'This value is not a valid URL.',
            ],
        ];

        yield 'invalid data type' => [
            'payload' => [
                'name' => 1,
                'audience' => 2,
                'oidc_authentication_url' => 3,
                'oauth2_access_token_url' => 4,
                'jwks_url' => 6,
            ],
            'violations' => [
                '[name]' => 'This value should be of type string.',
                '[audience]' => 'This value should be of type string.',
                '[oidc_authentication_url]' => [
                    'This value should be of type string.',
                    'This value is not a valid URL.',
                ],
                '[oauth2_access_token_url]' => [
                    'This value should be of type string.',
                    'This value is not a valid URL.',
                ],
                '[jwks_url]' => ['This value should be of type string.', 'This value is not a valid URL.'],
            ],
        ];
    }
}
