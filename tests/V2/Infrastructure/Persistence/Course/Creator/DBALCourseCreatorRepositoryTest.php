<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Course\Creator;

use App\Tests\V2\Domain\Course\Creator\CourseCreatorRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Infrastructure\Persistence\Course\Creator\DBALCourseCreatorRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\ORM\EntityManager;

class DBALCourseCreatorRepositoryTest extends CourseCreatorRepositoryTestCase
{
    private const string TABLE_NAME = 'course_creator';

    private Connection $connection;

    /**
     * @throws \PHPUnit\Framework\MockObject\Exception
     * @throws SchemaException
     * @throws Exception
     */
    protected function getRepository(): CourseCreatorRepository
    {
        /**
         * @var Schema $schema
         */
        [$this->connection, $schema] = DBALConnectionFactory::create();

        if ($schema->hasTable(self::TABLE_NAME)) {
            $this->dropTable();
        }

        $courseCreatorTable = $schema->createTable(self::TABLE_NAME);
        $courseCreatorTable->addColumn('user_id', 'integer');
        $courseCreatorTable->addColumn('course_id', 'integer');

        $courseCreatorTable->setPrimaryKey(['user_id', 'course_id']);

        foreach ($this->connection->getDatabasePlatform()->getCreateTableSQL($courseCreatorTable) as $sql) {
            $this->connection->executeStatement($sql);
        }

        $em = $this->createMock(EntityManager::class);
        $em->method('getConnection')
            ->willReturn($this->connection);

        return new DBALCourseCreatorRepository(
            em: $em,
            courseCreatorTableName: self::TABLE_NAME,
        );
    }

    /**
     * @throws Exception
     */
    private function dropTable(): void
    {
        $this->connection->executeStatement('DROP TABLE IF EXISTS `' . self::TABLE_NAME . '`');
    }

    /**
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->dropTable();

        parent::tearDown();
    }
}
