<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence;

use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Infrastructure\Persistence\DBALSortTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class DBALSortTransformerTest extends TestCase
{
    #[DataProvider('sortDirectionProvider')]
    public function testFromSortDirection(SortDirection $direction, string $expected): void
    {
        $result = DBALSortTransformer::fromSortDirection($direction);

        $this->assertSame($expected, $result);
    }

    public static function sortDirectionProvider(): array
    {
        return [
            'ascending' => [
                'direction' => SortDirection::ASC,
                'expected' => 'ASC',
            ],
            'descending' => [
                'direction' => SortDirection::DESC,
                'expected' => 'DESC',
            ],
        ];
    }
}
