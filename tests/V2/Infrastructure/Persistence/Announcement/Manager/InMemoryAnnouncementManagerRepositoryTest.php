<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Announcement\Manager;

use App\Tests\V2\Domain\Announcement\Manager\AnnouncementManagerRepositoryTestCase;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Infrastructure\Persistence\Announcement\Manager\InMemoryAnnouncementManagerRepository;

class InMemoryAnnouncementManagerRepositoryTest extends AnnouncementManagerRepositoryTestCase
{
    protected function getRepository(): AnnouncementManagerRepository
    {
        return new InMemoryAnnouncementManagerRepository();
    }
}
