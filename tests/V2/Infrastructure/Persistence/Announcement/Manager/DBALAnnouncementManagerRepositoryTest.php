<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Announcement\Manager;

use App\Tests\V2\Domain\Announcement\Manager\AnnouncementManagerRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Infrastructure\Persistence\Announcement\Manager\DBALAnnouncementManagerRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\ORM\EntityManager;
class DBALAnnouncementManagerRepositoryTest extends AnnouncementManagerRepositoryTestCase
{
    private const string TABLE_NAME = 'announcement_manager';

    private Connection $connection;

    /**
     * @throws \PHPUnit\Framework\MockObject\Exception
     * @throws SchemaException
     * @throws Exception
     */
    protected function getRepository(): DBALAnnouncementManagerRepository
    {
        /**
         * @var Schema $schema
         */
        [$this->connection, $schema] = DBALConnectionFactory::create();

        if ($schema->hasTable(self::TABLE_NAME)) {
            $this->dropTable();
        }

        $announcementManagerTable = $schema->createTable(self::TABLE_NAME);
        $announcementManagerTable->addColumn('id', 'integer', ['autoincrement' => true]);
        $announcementManagerTable->addColumn('manager_id', 'integer');
        $announcementManagerTable->addColumn('announcement_id', 'integer');
        $announcementManagerTable->setPrimaryKey(['id']);

        $announcementManagerTable->addUniqueIndex(['manager_id', 'announcement_id']);

        foreach ($this->connection->getDatabasePlatform()->getCreateTableSQL($announcementManagerTable) as $sql) {
            $this->connection->executeStatement($sql);
        }

        $em = $this->createMock(EntityManager::class);
        $em->method('getConnection')
            ->willReturn($this->connection);

        return new DBALAnnouncementManagerRepository(
            em: $em,
            announcementManagerTableName: self::TABLE_NAME,
        );
    }

    /**
     * @throws Exception
     */
    private function dropTable(): void
    {
        $this->connection->executeStatement('DROP TABLE IF EXISTS `' . self::TABLE_NAME . '`');
    }

    /**
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->dropTable();

        parent::tearDown();
    }
}
