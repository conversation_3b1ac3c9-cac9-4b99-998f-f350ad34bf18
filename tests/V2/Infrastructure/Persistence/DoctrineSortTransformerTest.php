<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence;

use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Infrastructure\Persistence\DoctrineSortTransformer;
use PHPUnit\Framework\TestCase;

class DoctrineSortTransformerTest extends TestCase
{
    /**
     * Test that fromSortDirection correctly transforms SortDirection enum values to strings.
     *
     * @dataProvider sortDirectionProvider
     */
    public function testFromSortDirection(SortDirection $direction, string $expected): void
    {
        // Act
        $result = DoctrineSortTransformer::fromSortDirection($direction);

        // Assert
        $this->assertSame($expected, $result);
    }

    /**
     * Data provider for testFromSortDirectionWithAllValues.
     *
     * @return array<string, array{direction: SortDirection, expected: string}>
     */
    public static function sortDirectionProvider(): array
    {
        return [
            'ascending' => [
                'direction' => SortDirection::ASC,
                'expected' => 'ASC',
            ],
            'descending' => [
                'direction' => SortDirection::DESC,
                'expected' => 'DESC',
            ],
        ];
    }
}
