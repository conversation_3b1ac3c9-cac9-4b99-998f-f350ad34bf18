<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\LTI;

use App\Tests\V2\Domain\LTI\LtiDeploymentRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\LTI\LtiDeploymentRepository;
use App\V2\Infrastructure\Persistence\LTI\DBALLtiDeploymentRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\ORM\EntityManager;

class DBALLtiDeploymentRepositoryTest extends LtiDeploymentRepositoryTestCase
{
    private const string TABLE_NAME = 'lti_deployment';
    private Connection $connection;

    /**
     * @throws \PHPUnit\Framework\MockObject\Exception
     * @throws SchemaException
     * @throws Exception
     */
    protected function getRepository(): LtiDeploymentRepository
    {
        /**
         * @var Schema $schema
         */
        [$this->connection, $schema] = DBALConnectionFactory::create();

        if ($schema->hasTable(self::TABLE_NAME)) {
            $this->dropTable();
        }

        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'string');
        $table->addColumn('registration_id', 'string');
        $table->addColumn('name', 'string');
        $table->addColumn('deployment_id', 'string');

        $table->setPrimaryKey(['id']);
        $table->addUniqueConstraint(
            columnNames: ['registration_id', 'deployment_id'],
            indexName: 'INX_LTI_DEPLOYMENT_REG_ID_DEPLOYMENT_ID_UNIQUE',
        );

        foreach ($this->connection->getDatabasePlatform()->getCreateTableSQL($table) as $sql) {
            $this->connection->executeStatement($sql);
        }

        $em = $this->createMock(EntityManager::class);
        $em->method('getConnection')
            ->willReturn($this->connection);

        return new DBALLtiDeploymentRepository(
            em: $em,
            ltiDeploymentTableName: self::TABLE_NAME,
        );
    }

    /**
     * @throws Exception
     */
    private function dropTable(): void
    {
        $this->connection->executeStatement('DROP TABLE IF EXISTS `' . self::TABLE_NAME . '`');
    }

    protected function tearDown(): void
    {
        $this->dropTable();
        parent::tearDown();
    }
}
