<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\LTI;

use App\Tests\V2\Domain\LTI\LtiPlatformRepositoryTestCase;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Infrastructure\Persistence\LTI\InMemoryLtiPlatformRepository;

class InMemoryLtiPlatformRepositoryTest extends LtiPlatformRepositoryTestCase
{
    protected function getRepository(): LtiPlatformRepository
    {
        return new InMemoryLtiPlatformRepository();
    }
}
