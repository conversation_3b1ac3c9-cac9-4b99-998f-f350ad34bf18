<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\LTI;

use App\Tests\V2\Domain\LTI\LtiToolRepositoryTestCase;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Infrastructure\Persistence\LTI\InMemoryLtiToolRepository;

class InMemoryLtiToolRepositoryTest extends LtiToolRepositoryTestCase
{
    protected function getRepository(): LtiToolRepository
    {
        return new InMemoryLtiToolRepository();
    }
}
