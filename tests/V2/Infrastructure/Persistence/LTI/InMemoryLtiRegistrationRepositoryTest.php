<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\LTI;

use App\Tests\V2\Domain\LTI\LtiRegistrationRepositoryTestCase;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Infrastructure\Persistence\LTI\InMemoryLtiRegistrationRepository;

class InMemoryLtiRegistrationRepositoryTest extends LtiRegistrationRepositoryTestCase
{
    protected function getRepository(): LtiRegistrationRepository
    {
        return new InMemoryLtiRegistrationRepository();
    }
}
