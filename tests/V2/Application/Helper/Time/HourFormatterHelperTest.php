<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Helper\Time;

use App\V2\Application\Helper\Time\HourFormatterHelper;
use PHPUnit\Framework\TestCase;

class HourFormatterHelperTest extends TestCase
{
    public function testMinutesToHours(): void
    {
        $this->assertSame(2.0, HourFormatterHelper::minutesToHours(120));
        $this->assertSame(1.5, HourFormatterHelper::minutesToHours(90));
        $this->assertNull(HourFormatterHelper::minutesToHours(null));
        $this->assertNull(HourFormatterHelper::minutesToHours(0));
        $this->assertNull(HourFormatterHelper::minutesToHours(-10));
    }
}
