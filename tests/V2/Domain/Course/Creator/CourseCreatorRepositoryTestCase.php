<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Course\Creator;

use App\Tests\V2\Mother\Course\Creator\CourseCreatorMother;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Creator\CourseCreatorRepositoryException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class CourseCreatorRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): CourseCreatorRepository;

    /**
     * @throws InfrastructureException
     * @throws CourseCreatorNotFoundException
     */
    public function testInsert(): void
    {
        $repository = $this->getRepository();

        $creator1 = CourseCreatorMother::create(
            userId: 2,
            courseId: 5
        );

        $creator2 = CourseCreatorMother::create(
            userId: 3,
            courseId: 5
        );

        $repository->insert($creator1);
        $repository->insert($creator2);

        $found = $repository->findOneBy(
            CourseCreatorCriteria::createEmpty()->filterByUserId(2)
        );
        $this->assertNotSame($creator1, $found);
        $this->assertEquals($creator1, $found);

        $duplicateCreator = CourseCreatorMother::create(userId: 2, courseId: 5);
        try {
            $repository->insert($duplicateCreator);
            $this->fail('Expected exception was not thrown');
        } catch (CourseCreatorRepositoryException $e) {
            $this->assertEquals(
                CourseCreatorRepositoryException::duplicateCourseCreator($duplicateCreator),
                $e
            );
        }
    }

    /**
     * @throws InfrastructureException
     * @throws CourseCreatorNotFoundException
     */
    public function testFindOneBy(): void
    {
        $creator1 = CourseCreatorMother::create(userId: 1, courseId: 1);
        $creator2 = CourseCreatorMother::create(userId: 2, courseId: 2);
        $creator3 = CourseCreatorMother::create(userId: 2, courseId: 3);
        $creator4 = CourseCreatorMother::create(userId: 1, courseId: 4);

        $repository = $this->getRepository();
        $repository->insert($creator1);
        $repository->insert($creator2);
        $repository->insert($creator3);
        $repository->insert($creator4);

        $found = $repository->findOneBy(
            CourseCreatorCriteria::createEmpty()->filterByUserId(2)
        );
        $this->assertEquals($creator2, $found);

        $found = $repository->findOneBy(
            CourseCreatorCriteria::createEmpty()
                ->filterByCourseId(2)
        );
        $this->assertEquals($creator2, $found);

        $found = $repository->findOneBy(
            CourseCreatorCriteria::createEmpty()
                ->filterByCourseId(4)
        );
        $this->assertEquals($creator4, $found);

        $found = $repository->findOneBy(
            CourseCreatorCriteria::createEmpty()
        );
        $this->assertEquals($creator1, $found);
    }

    #[DataProvider('provideFindBy')]
    public function testFindBy(
        array $elements,
        CourseCreatorCriteria $criteria,
        int $expectedCount,
        array $expectedResult,
    ): void {
        $repository = $this->getRepository();
        foreach ($elements as $element) {
            $repository->insert($element);
        }

        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);
        $difference = array_diff(
            $expectedResult,
            $result->all()
        );
        $this->assertCount(0, $difference);
    }

    public static function provideFindBy(): \Generator
    {
        $creator1 = CourseCreatorMother::create(userId: 1, courseId: 1);
        $creator2 = CourseCreatorMother::create(userId: 2, courseId: 2);
        $creator3 = CourseCreatorMother::create(userId: 2, courseId: 3);
        $creator4 = CourseCreatorMother::create(userId: 1, courseId: 4);

        yield '4 creators find all' => [
            'elements' => [$creator1, $creator2, $creator3, $creator4],
            'criteria' => CourseCreatorCriteria::createEmpty(),
            'expectedCount' => 4,
            'expectedResult' => [$creator1, $creator2, $creator3, $creator4],
        ];

        yield '4 creators find two user number 2' => [
            'elements' => [$creator1, $creator2, $creator3, $creator4],
            'criteria' => CourseCreatorCriteria::createEmpty()->filterByUserId(2),
            'expectedCount' => 2,
            'expectedResult' => [$creator2, $creator3],
        ];

        yield '4 creators find two user number 1' => [
            'elements' => [$creator1, $creator2, $creator3, $creator4],
            'criteria' => CourseCreatorCriteria::createEmpty()->filterByUserId(1),
            'expectedCount' => 2,
            'expectedResult' => [$creator1, $creator4],
        ];

        yield '4 creators find one course number 1' => [
            'elements' => [$creator1, $creator2, $creator3, $creator4],
            'criteria' => CourseCreatorCriteria::createEmpty()
                ->filterByCourseId(1),
            'expectedCount' => 1,
            'expectedResult' => [$creator1],
        ];

        yield '4 creators find one course number 3' => [
            'elements' => [$creator1, $creator2, $creator3, $creator4],
            'criteria' => CourseCreatorCriteria::createEmpty()
                ->filterByCourseId(3),
            'expectedCount' => 1,
            'expectedResult' => [$creator3],
        ];
    }

    /**
     * @throws InfrastructureException
     */
    #[DataProvider('provideDelete')]
    public function testDelete(
        array $elements,
        array $toDelete,
        array $expectedResult,
    ): void {
        $repository = $this->getRepository();
        foreach ($elements as $element) {
            $repository->insert($element);
        }

        $result = $repository->findBy(CourseCreatorCriteria::createEmpty());
        $this->assertCount(
            0,
            array_diff(
                $elements,
                $result->all(),
            )
        );

        foreach ($toDelete as $element) {
            $repository->delete($element);
        }

        $result = $repository->findBy(CourseCreatorCriteria::createEmpty());
        $difference = array_diff(
            $expectedResult,
            $result->all()
        );
        $this->assertCount(0, $difference);
    }

    public static function provideDelete(): \Generator
    {
        $creator1 = CourseCreatorMother::create(userId: 1, courseId: 1);
        $creator2 = CourseCreatorMother::create(userId: 2, courseId: 2);
        $creator3 = CourseCreatorMother::create(userId: 2, courseId: 3);
        $creator4 = CourseCreatorMother::create(userId: 1, courseId: 4);
        yield '4 creators delete 3' => [
            'elements' => [$creator1, $creator2, $creator3, $creator4],
            'toDelete' => [$creator3],
            'expectedResult' => [$creator1, $creator2, $creator4],
        ];

        yield '4 creators delete 2' => [
            'elements' => [$creator1, $creator2, $creator3, $creator4],
            'toDelete' => [$creator2, CourseCreatorMother::create(userId: 999, courseId: 999)],
            'expectedResult' => [$creator1, $creator3, $creator4],
        ];
    }
}
