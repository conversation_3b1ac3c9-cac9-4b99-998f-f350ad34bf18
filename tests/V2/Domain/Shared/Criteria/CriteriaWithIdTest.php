<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Shared\Criteria;

use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaId;
use App\V2\Domain\Shared\Criteria\CriteriaWithId;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\Shared\Identifier;

class CriteriaWithIdTest extends CriteriaIdTestCase
{
    protected function getCriteriaClass(): CriteriaId
    {
        return new class extends CriteriaWithId {
        };
    }

    protected function createId(): Identifier
    {
        return IdMother::create();
    }

    /**
     * @throws CollectionException
     */
    protected function getIds(array $ids): Collection
    {
        return new IdCollection($ids);
    }
}
