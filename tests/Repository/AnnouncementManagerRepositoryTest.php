<?php

declare(strict_types=1);

namespace App\Tests\Repository;

use App\Entity\Announcement;
use App\Entity\AnnouncementManager;
use App\Entity\Course;
use App\Entity\User;
use App\Entity\UserManage;
use App\Repository\AnnouncementManagerRepository;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class AnnouncementManagerRepositoryTest extends KernelTestCase
{
    private ?EntityManagerInterface $entityManager = null;
    private ?AnnouncementManagerRepository $repository = null;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()->get('doctrine')->getManager();
        $this->repository = $this->entityManager->getRepository(AnnouncementManager::class);
        $this->managerRepository = $this->entityManager->getRepository(User::class);
    }

    /**
     * @throws Exception
     */
    public function testFindManagersByAnnouncement(): void
    {
        $course = $this->createCourse();
        $announcement = $this->createAnnouncement($course);
        $manager1 = $this->getOrCreateManager('<EMAIL>', 'Manager', 'One');
        $manager2 = $this->getOrCreateManager('<EMAIL>', 'Manager', 'Two');

        $this->entityManager->flush();

        // Create announcement managers
        $announcementManager1 = new AnnouncementManager();
        $announcementManager1->setAnnouncement($announcement);
        $announcementManager1->setManager($manager1);
        $this->entityManager->persist($announcementManager1);

        $announcementManager2 = new AnnouncementManager();
        $announcementManager2->setAnnouncement($announcement);
        $announcementManager2->setManager($manager2);
        $this->entityManager->persist($announcementManager2);

        $this->entityManager->flush();

        // Test findManagersByAnnouncement
        $result = $this->repository->findManagersByAnnouncement($announcement);
        $this->assertCount(2, $result);

        // Reset Data.
        $this->truncateEntities([
            AnnouncementManager::class,
            UserManage::class,
        ]);

        $this->entityManager->createQuery('DELETE FROM App\Entity\User u WHERE u.email = :email1 OR u.email = :email2')
            ->setParameter('email1', '<EMAIL>')
            ->setParameter('email2', '<EMAIL>')
            ->execute();
        $this->entityManager->flush();
    }

    /**
     * @throws Exception
     */
    public function testRemoveAllByAnnouncement(): void
    {
        $course = $this->createCourse();
        $announcement = $this->createAnnouncement($course);
        $manager = $this->getOrCreateManager('<EMAIL>', 'Manager', 'Test');

        $this->entityManager->flush();

        // Create announcement manager
        $announcementManager = new AnnouncementManager();
        $announcementManager->setAnnouncement($announcement);
        $announcementManager->setManager($manager);
        $this->entityManager->persist($announcementManager);

        $this->entityManager->flush();

        // Verify it exists
        $result = $this->repository->findBy(['announcement' => $announcement]);
        $this->assertCount(1, $result);

        // Test removeAllByAnnouncement
        $this->repository->removeAllByAnnouncement($announcement);

        // Verify it was removed
        $result = $this->repository->findBy(['announcement' => $announcement]);
        $this->assertCount(0, $result);

        // Reset Data.
        $this->truncateEntities([
            AnnouncementManager::class,
            UserManage::class,
        ]);

        $this->entityManager->createQuery('DELETE FROM App\Entity\User u WHERE u.email = :email1')
            ->setParameter('email1', '<EMAIL>')
            ->execute();
        $this->entityManager->flush();
    }

    /**
     * @throws Exception
     */
    protected function truncateEntities(array $entities): void
    {
        $connection = $this->entityManager->getConnection();
        $databasePlatform = $connection->getDatabasePlatform();
        $connection->executeQuery('SET FOREIGN_KEY_CHECKS=0');

        foreach ($entities as $entity) {
            $query = $databasePlatform->getTruncateTableSQL(
                $this->entityManager->getClassMetadata($entity)->getTableName()
            );
            $connection->executeQuery($query);
        }

        $connection->executeQuery('SET FOREIGN_KEY_CHECKS=1');
    }

    protected function createCourse(): Course
    {
        $course = new Course();
        $course->setCode('TEST-COURSE');
        $course->setName('Test Course');
        $course->setLocale('es');
        $course->setOpen(true);
        $course->setActive(true);
        $course->setIsNew(false);
        $this->entityManager->persist($course);

        return $course;
    }

    protected function createAnnouncement(Course $course): Announcement
    {
        $announcement = new Announcement();
        $announcement->setCourse($course);
        $announcement->setStartAt(new \DateTime('today'));
        $announcement->setFinishAt(new \DateTime('tomorrow'));
        $announcement->setSubsidized(false);
        $this->entityManager->persist($announcement);

        return $announcement;
    }

    protected function getOrCreateManager(string $email, string $firstName, string $lastName): User
    {
        $manager = $this->managerRepository->findOneBy(['email' => $email]);
        if ($manager) {
            return $manager;
        }

        $manager = new User();
        $manager->setEmail($email);
        $manager->setPassword('password');
        $manager->setRoles(['ROLE_MANAGER']);
        $manager->setFirstName($firstName);
        $manager->setLastName($lastName);
        $manager->setIsActive(true);
        $manager->setValidated(true);
        $manager->setOpen(true);
        $manager->setLocale('es');
        $this->entityManager->persist($manager);

        return $manager;
    }

    /**
     * @throws Exception
     */
    protected function tearDown(): void
    {
        if ($this->entityManager) {
            $this->truncateEntities([
                AnnouncementManager::class,
                Announcement::class,
                Course::class,
                UserManage::class,
            ]);
        }

        parent::tearDown();

        // Avoid memory leaks
        if ($this->entityManager) {
            $this->entityManager->close();
            $this->entityManager = null;
        }
        $this->repository = null;
    }
}
