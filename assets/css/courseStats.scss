.course-filters, .progressContainer {
  background-color: #E1F5FE;
}
.icon-merge {
  position: relative;
  display: inline-grid;
  width: 1.2rem;
  height: 1.2rem;
  color: inherit;
  place-items: center;

  i {
    position: absolute;

    &:first-child {
      color: inherit;
      font-size: 1rem;
    }

    &:last-child {
      color: white;
      font-size: 50%;
    }
  }
}
.subtitle {
  font-size: 0.8rem;
  color: var(--color-neutral-mid-darker);
}
.UserCourseDetails, .CourseStatsDetails {
  td img {
    height: 2rem; width: 2rem; object-fit: cover; object-position: center;
  }
}
.modal-header {
  background-color: var(--color-neutral-darker);
  align-items: center;
  h5 { color: var(--color-neutral-lightest); }
}
.bg-details {
  background-color: #CBD5E1;
}
.bg-gray {
  background-color: #ECEFF1;
}
.bg-white-alt {
  background-color: rgba(255, 255, 255, 0.67);
}
.bg-success-alt {
  background-color: #C8E6C9;
}
.bg-danger-alt {
  background-color: #FFCDD2;
}
.dateContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(3rem, 1fr));
  gap: 1rem;
  font-size: 0.7rem;
  text-align: center;
  justify-content: center;
}

.totalTime {
  display: block;
  font-weight: bold;
  font-size: 0.7rem;
}

.answer-icon {
  position: absolute;
  left: -2.5rem;
}

.Chart {
  border: none !important;
  box-shadow: none !important;
}

.fa-spinner:not(.no-spin) {
  animation: rotate 1s linear infinite;
}

.scrollable {
  overflow-y: auto;
  max-height: 255pt;
}

@keyframes rotate {
  0% { transform: rotate(0); }
  100% { transform: rotate(360deg); }
}

.tableLoadingInfo {
  position: absolute;
  display: grid;
  width: 100%;
  height: 100%;
  place-content: center;
  inset: 0;
  background-color: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(7px);
  td {
    border: 0 !important;
  }
}

.multiselect__placeholder {
  margin-bottom: 0 !important;
  color: var(--color-neutral-dark) !important;
}

.multiselect__tags {
  border: 1px solid var(--color-neutral-mid) !important;
}

.multiselect__tag {
  background-color: var(--color-primary) !important;
}