import axios from 'axios';

export const getters = {
    isLoadingLibraries(state) { return state.isLoading },
    isLoadingComments(state) { return state.isLoadingComments },
    isLoadingLibrary(state) { return state.isLoadingLibrary; },
};

const mutations = {
    SET_IS_LOADING(state, loading) {
        state.isLoading = loading;
    },
    SET_IS_LOADING_COMMENTS(state, loading) {
        state.isLoadingComments = loading;
    },
    SET_IS_LOADING_LIBRARY(state, loading) {
        state.isLoadingLibrary = loading;
    }
};

export const actions = {
    async getLibraries({ commit }, {categoryId, currentPage, query}) {
        commit('SET_IS_LOADING', true);
        try {
            let url = `/admin/library/category/${categoryId}/libraries/${currentPage}`;
            if (query.length > 0) {
                url += `?query=${ query }`;
            }
            const result = await axios.get(url);
            const { error, data } = result.data;
            if (!error) return data;
            return [];
        } catch (e) {
            console.log(e);
            return [];
        } finally {
            commit('SET_IS_LOADING', false);
        }
    },

    async getLibrary({commit}, libraryId) {
        commit('SET_IS_LOADING_LIBRARY', true);
        try {
            const url = `/admin/library/${libraryId}`;
            const result = await axios.get(url);
            return result.data;
        } finally {
            commit('SET_IS_LOADING_LIBRARY', false);
        }
    },

    async newLibrary({commit}, {categoryId, formData}) {
        try {
            const headers = {
                'Content-Type': 'multipart/form-data'
            };

            const url = `/admin/library/category/${categoryId}/library`;
            const result = await axios.post(url, formData, {
                headers
            });
            console.log(result);
            return result.data;
        } catch (e) {
            return Promise.reject(e);
        }
    },

    async updateLibrary({ commit }, { id, formData}) {
        try {
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const url = `/admin/library/${id}/update`;
            const result = await axios.post(url, formData, { headers });
            return result.data;
        } catch (e) {
            console.log(e);
        }
    },

    loadFilters({commit}, id) {
        try {
            const url = `/admin/library/${id}/filters`;
            return axios.get(url);
        } finally {

        }
    },

    addFilter({commit}, {libraryId, filterId}) {
        try {
            const url = `/admin/library/${libraryId}/filter/${filterId}`;
            return axios.post(url);
        } finally {

        }
    },
    removeFilter({ commit }, {libraryId, filterId}) {
        try {
            const url = `/admin/library/${libraryId}/filter/${filterId}`;
            return axios.delete(url);
        } finally {

        }
    },

    async uploadLibraryFile({ commit }, { id, formData }) {
        try {
            const url = `/admin/library/${id}/file`;
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const result = await axios.post(url, formData, {
                headers
            }).catch(e => {
                console.log(e);
            });
            return result.data;
        } finally {

        }
    },

    async getComments({ commit }, { id , page }) {
        commit('SET_IS_LOADING_COMMENTS', true);
        try {
            const url = `/admin/library/${id}/comments/${page}`;
            const result = await axios.get(url);
            return result.data;
        } finally {
            commit('SET_IS_LOADING_COMMENTS', false);
        }
    },

    async commentVisibility({ commit }, {libraryId, commentId, visible}) {
        try {
            const url = `/admin/library/${libraryId}/comment/${commentId}/visible`;
            const result = await axios.put(url, { visible })
            return result.data;
        } finally {

        }
    },

    // allowComment({ commit }, {libraryId, commentId}) {
    //     const url = `/admin/library/${libraryId}/comment/${commentId}/allow`;
    //     return axios.put(url);
    // },
    //
    // banComment({ commit }, {libraryId, commentId}) {
    //     const url = `/admin/library/${libraryId}/comment/${commentId}/ban`;
    //     return axios.put(url);
    // },

    async getRating({ commit }, id) {
        const url = `/admin/library/${id}/rating`;
        const result = await axios.get(url);
        const { data } = result.data;
        return data;
    },

    async deleteLibrary({ commit }, id) {
        const url = `/admin/library/${id}`;
        const result = await axios.delete(url).catch(e => {
            console.log(e);
            return { error: true, data: 'Exception raised' };
        });
        return result.data;
    }
};

export default {
    namespaced: true,
    state: {
        isLoading: true,
        isLoadingComments: false,
        isLoadingLibrary: true
    },
    getters,
    mutations,
    actions
}
