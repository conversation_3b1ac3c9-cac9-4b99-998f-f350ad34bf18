<script>
import {sync, get} from "vuex-pathify";
import Translation from "../../../common/components/Translation.vue";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import translationWarningsMixin from '../../../mixins/translationWarnings'


export default {
  name: "Information",
  components: {BaseSwitch, Translation},
  mixins: [translationWarningsMixin],
  data() {
    return {
      locale: 'es',
      activeIndex: 0,
    };
  },
  computed: {
    defaultLocale: get('localeModule/defaultLocale'),
    userLocale: get("localeModule/userLocale"),
    name: sync('courseCategoryModule/form@name'),
    description: sync('courseCategoryModule/form@description'),
    translations: sync('courseCategoryModule/form@translations'),
    typeCourses: sync('courseCategoryModule/form@typeCourses'),
    internTypeCourses: get("configModule/config@intern"),
    externTypeCourses: get("configModule/config@extern"),

    translationFroalaConfiguration() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        pluginsEnabled: ["align"],
        toolbarButtons: {
          'moreText': {
            buttons: ['bold', 'italic', 'underline']
          },
        },
        events: {
          'html.get': (e) => {
            this.translations[this.activeIndex].description = e;
          }
        }
      };
    },
  },
  created() {
    this.locale = this.userLocale;
    const actions = [];
    actions.push({
      name: this.$t("SAVE"),
      event: "onSave",
      class: "btn btn-primary",
    });

    if (this.$isGranted("ROLE_ADMIN") && this.$route.params.id != -1)
      actions.push({
        name: this.$t("DELETE"),
        event: "onDelete",
        class: "btn btn-danger",
      });

    this.$store.dispatch("contentTitleModule/setActions", {
      route: this.$route.name,
      actions,
    });
  }
}
</script>

<template>
<div class="Information">
  <div class="col-12">
    <h4 class="w-100 Title BorderBottom mb-3"><i class="fa fa-clipboard-check"></i> {{ $t('COMMON.BASIC_INFO') }}</h4>
    <translation v-model="locale" direction="horizontal" :warning="warningLocales">
      <template v-slot:content>
        <div class="col-12" v-for="t in translations" :key="t.locale" v-if="locale === t.locale">
          <div class="form-group col-12 required mb-0">
            <label>{{ $t('NAME') }}</label>
            <input type="text" class="form-control" v-model="t.name">
          </div>
          <div class="form-group col-12">
            <label>{{ $t("DESCRIPTION") }}</label>
            <textarea  name="statement" id="statement" rows="3" class="form-control" v-model="t.description"></textarea>
          </div>
        </div>
      </template>
    </translation>
  </div>
  <div class="TypeCourses col-12 d-flex flex-row flex-wrap mt-3" v-if="$isAdmin() || $isSuperAdmin()">
    <h4 class="w-100 Title BorderBottom mb-3">{{ $t('COURSE_CATEGORY.COURSE_TYPES') }}</h4>
    <div class="Intern col-xs-12 col-md-6 mt-3" v-if="internTypeCourses.length > 0">
      <h4>{{ $t('ANNOUNCEMENT.FORMATIVE_ACTION_TYPE.INTERN') }}</h4>
      <div v-for="iTypeCourse in internTypeCourses"
           :key="iTypeCourse.id"
           class="d-flex flex-row flex-nowrap align-items-center mb-2">
        <BaseSwitch :tag="`intern-type-course-${iTypeCourse.id}`"
                     v-model="typeCourses[iTypeCourse.id]"
        />
        <span class="ml-3">{{ iTypeCourse.name }}</span>
      </div>
    </div>
    <div class="Extern col-xs-12 col-md-6 mt-3" v-if="externTypeCourses.length > 0">
      <h4>{{ $t('ANNOUNCEMENT.FORMATIVE_ACTION_TYPE.EXTERN') }}</h4>
      <div v-for="iTypeCourse in externTypeCourses"
           :key="iTypeCourse.id"
           class="d-flex flex-row flex-nowrap align-items-center mb-1">
        <BaseSwitch :tag="`extern-type-course-${iTypeCourse.id}`"
                     v-model="typeCourses[iTypeCourse.id]"
        />
        <span class="ml-3">{{ iTypeCourse.name }}</span>
      </div>
    </div>
  </div>
</div>
</template>

<style scoped lang="scss">
.Information {
  h4 {
    font-size: 16px;
    color: var(--color-neutral-darkest);
  }
  .Title {
    font-size: 18px;
    color: var(--color-neutral-darkest);
    &.BorderBottom {
      border-bottom: 1px solid var(--color-neutral-mid);
    }
  }
}
</style>
