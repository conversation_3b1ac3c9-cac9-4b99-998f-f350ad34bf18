export class UserDetails {
  constructor({
    id = 0,
    name = '',
    lastName = '',
    email = '',
    avatar = '',
    language = '',
    code = '',
    company = '',
    zone = '',
    dni = '',
    openCampus = true,
    extra = [],
    roles = [],
    filters = [],
    management = []
  } = {}) {
    this.id = id || 0
    this.firstName = name || ''
    this.lastName = lastName || ''
    this.email = email || ''
    this.avatar = avatar || ''
    this.language = language || ''
    this.code = code || ''
    this.company = company || ''
    this.zone = zone || ''
    this.dni = dni || ''
    this.openCampus = openCampus || false
    this.extra = (extra || []).map((field) => new ExtraField(field))
    this.roles = (roles || []).map((role) => role.id)
    this.password = ''
    this.filters = UserFilterSelected(filters)
    this.management = UserFilterSelected(management)
  }

  getErrorList() {
    return []
  }

  getPayload(isAdmin = false) {
    return {
      ... (this.id ? { id: this.id } : {}),
      name: this.firstName,
      lastName: this.lastName,
      email: this.email,
      language: this.language,
      code: this.code,
      company: this.company,
      zone: this.zone,
      dni: this.dni,
      openCampus: this.openCampus,
      extra: this.extra.map((field) => ({ key: field.key, value: field.value })),
      roles: GetUserFilterSelected(this.roles),
      ... (isAdmin ? { management: GetUserFilterSelected(this.management) } : {})
    };
  }
}

class ExtraField {
  constructor({ key = '', label = '', value = ''} = {}) {
    this.key = key || ''
    this.label = label || ''
    this.value = value || ''
  }
}

function UserFilterSelected(filters = []) {
  return (filters || []).reduce((acc, item) => ({
    ...acc,
    [`category_${item.id}`]: (item.selected || []).map(
      (selected) => ({ id: selected.id || 0, name: selected.name || '' }))
  }), {})
}

function GetUserFilterSelected(filters = []) {
  return (filters || []).map((filter) => ({ id: filter.id, selected: filter.selected.map((selected) => selected.id) }))
}