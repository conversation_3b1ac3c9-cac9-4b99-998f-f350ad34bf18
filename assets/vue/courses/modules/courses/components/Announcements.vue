<template>
  <div
    class="d-flex align-items-center justify-content-center"
    v-if="isLoading"
  >
    <Spinner />
  </div>
  <div v-else>
    <div class="d-flex justify-content-end mb-4">
      <button
        class="btn btn-primary"
        @click="addAnnouncement"
      >
        {{ $t('COURSE.ADD_ANNOUNCEMENT') }}
      </button>
    </div>
    <div class="table-responsive">
      <table class="table">
        <thead>
          <tr>
            <th>{{ $t("ANNOUNCEMENT.FORM.ENTITY.ANNOUNCEMENT_NAME") }}</th>
            <th>{{ $t("ANNOUNCEMENT_START_AT") }}</th>
            <th>{{ $t("ANNOUNCEMENT_FINISH_AT") }}</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="announcement in announcements" :key="announcement.id">
            <td>
              {{ announcement.code }}
            </td>
            <td>
              {{ announcement.startAt }}
            </td>
            <td>
              {{ announcement.finishAt }}
            </td>
            <td class="text-end">
              <router-link class="btn btn-success" :to="{
                name: ROUTE_NAMES_ANNOUNCEMENT.VIEW_ANNOUNCEMENT,
                params: {
                  id: announcement.id
                }
              }">
                <i class="fa fa-eye text-white"></i>
              </router-link>
              <router-link
                v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.ANNOUNCEMENT)"
                class="btn btn-primary"
                :to="{
                  name: ROUTE_NAMES_ANNOUNCEMENT.UPDATE_ANNOUNCEMENT,
                  params: {
                    id: announcement.id
                  }
                }"
              >
                <i class="fa fa-pencil"></i>
              </router-link>
              <button v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.ANNOUNCEMENT)" class="btn btn-danger" @click="deleteAnnouncement(announcement.id)">
                <i class="fa fa-trash"></i>
              </button>
            </td>
          </tr>
          <tr v-if="announcements.length === 0">
            <td colspan="12">
              <BaseNotResult />
            </td>
          </tr>
        </tbody>
      </table>
      <p class="mt-4">
        <b>{{ announcements.length }}</b> {{ $t("RESULTS") }}
      </p>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import { format } from "date-fns";
import { enUS, es } from "date-fns/locale";
import Spinner from "../../../../admin/components/base/Spinner.vue";
import BaseNotResult from "../../../../base/BaseNotResult.vue";
import ROUTE_NAMES_ANNOUNCEMENT from "../../announcement/router/routeNames";
import { COURSE_PERMISSIONS } from '../../../../common/utils/auth/permissions/course.permissions'

export default {
  name: "Announcements",
  components: { BaseNotResult, Spinner },
  data() {
    return {
      ROUTE_NAMES_ANNOUNCEMENT,
      isLoading: true,
    };
  },
  computed: {
    COURSE_PERMISSIONS() {
      return COURSE_PERMISSIONS
    },
    ...get("coursesModule", {
      announcementsRaw: "getAnnouncements",
      isEveryChapterContentCompleted: "getIsEveryChapterContentCompleted",
    }),
    locale: get("localeModule/userLocale"),
    announcements() {
      return this.announcementsRaw.map((announcement) => ({
        ...announcement,
        startAt: this.formatDate(announcement.startAt),
        finishAt: this.formatDate(announcement.finishAt),
      }));
    },
    courseId() {
      return this.$route.params.id
    }
  },
  async created() {
    this.isLoading = true;
    await this.fetchAnnouncements();
    this.isLoading = false;
  },
  methods: {
    async fetchAnnouncements() {
      await this.$store.dispatch(
        "coursesModule/fetchAnnouncements",
        this.$route.params.id
      );
    },
    getLocaleObject() {
      const locales = {
        en: enUS,
        es: es,
      };
      return locales[this.locale] || enUS;
    },
    formatDate(date) {
      try {
        return format(
          date,
          `EEEE, d '${this.$t("OF")}' MMMM '${this.$t("OF")}' yyyy`,
          {
            locale: this.getLocaleObject(),
          }
        );
      } catch (error) {
        return date;
      }
    },
    async deleteAnnouncement(id) {
      try {
        await this.$alertify.confirmWithTitle(
          this.$t("DELETE"),
          this.$t("COMMON_AREAS.QUESTION_DELETE"),
          async () => {
            await this.$store.dispatch("coursesModule/deleteAnnouncement", id);
            await this.fetchAnnouncements();
            this.$toast.success(this.$t("DELETE_SUCCESS"));
          },
          () => {}
        );
      } catch (e) {
        this.$toast.error(this.$t("DELETE_FAILED"));
      }
    },
    addAnnouncement() {
      if (!this.isEveryChapterContentCompleted) {
        this.$toast.warning(this.$t('COURSE.ALERT_COURSE_CONTENT_INCOMPLETE'));
        return
      }
      this.$router.push({
        name: this.ROUTE_NAMES_ANNOUNCEMENT.CREATE_ANNOUNCEMENT,
        params: {
          id: this.courseId,
          courseName: this.$route.params.name,
          origin: "course",
          isFromCourseDetails: true,
        }
      })
    }
  },
};
</script>

<style scoped lang="scss">
.table-responsive {
  min-height: 400px;
}
</style>
