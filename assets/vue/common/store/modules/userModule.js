import axios from "axios";
import {make} from "vuex-pathify";

const state = {
    roles: [],
    user: null
};

export default {
    namespaced: true,
        state,
        getters: {
            ...make.getters(state),
            getRoles(state) { return state.roles; },
            isAdmin(state) {
                return state.roles.findIndex((item) => item === 'ROLE_ADMIN') >= 0;
            },
            isManager(state) {
                return state.roles.findIndex((item) => item === 'ROLE_MANAGER') >= 0;
            },
            isManagerEditor(state) {
                return state.roles.findIndex((item) => item === 'ROLE_MANAGER_EDITOR') >= 0;
            }
    },
    mutations: {
        ...make.mutations(state),
    },
    actions: {
        setUserRoles({ commit }, roles) {
            commit('SET_ROLES', roles)
        },

        setUser({ commit }, user) {
            commit('SET_USER', user);
        },

        async uploadUserCV({ commit }, { id, formData }) {
            try {
                const url = ` / admin / user / ${ id } / user - cv`;
                const headers = {
                    'Content-Type': 'multipart/form-data'
                };
                const result = await axios.post(url, formData, {
                    headers
                });
                return result.data;
            } finally {

            }
        },

        async deleteUserCV({ commit }, id) {
            const url = ` / admin / user / ${ id } / user - cv`;
            const result = await axios.delete(url);
            return result.data;
        },

        hasRole({ getters }, role) {
            return getters.getRoles.findIndex(item => item === role) >= 0;
        },

        async loadAllManagers(){
            const result = await axios.get('/admin/managers/list');
            const { data } = result.data;
            return data.managers
        }
    }
}
