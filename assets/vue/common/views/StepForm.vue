<template>
  <div class="StepForm">
    <form :id="id" action="" class="StepForm--form" @submit.prevent>
      <div
        v-if="showProgress"
        class="col-12 StepForm--form--header">
        <form-progress
            :prefix="headerPrefix"
            :number-of-steps="numberOfSteps"
            :current-step="currentStep"
            :custom-titles="stepsCustomTitles"
            :showBarProgressPercentage="showBarProgressPercentage"
            @on-step-change="$emit('on-step-change', $event)"
            :warning="warning"
            :i18n="i18n"
        />
      </div>
      <div class="col-12 StepForm--form--content">
        <h1 class="CurrentStepSectionTitle">
          <i :class="currentStepIcon"></i>  {{ getStepTitle(currentStep) }} 
          <i v-if="currentStepDescriptionTooltip?.length" class="fas fa-question-circle text-primary ml-2 fs-4" data-toggle="tooltip" :title="currentStepDescriptionTooltip" data-placement="right" />
        </h1>
        <slot name="form-content"></slot>
      </div>
      <div class="col-12 StepForm--form--footer">
        <button
            :id="`${id}-btn-prev`"
            type="button" class="btn btn-primary"
            @click="$emit('prev')"
            v-if="currentStep > 1 && availableSteps > 1"
        >{{ $t('PREV') }}</button>
        <button
            :id="`${id}-btn-next`"
            type="button" class="btn btn-primary"
            @click="$emit('next')"
            v-if="currentStep >= 1 && currentStep < availableSteps && availableSteps > 1">{{ $t('NEXT') }}</button>
        <button
            :id="`${id}-btn-submit`"
            type="button" class="btn btn-primary"
            @click="$emit('submit')"
            v-if="currentStep === availableSteps || availableSteps === 1">{{ $t(btnSaveText) }}</button>
      </div>
    </form>
  </div>
</template>

<script>
import FormProgress from "../components/FormProgress.vue";

export default {
  name: "StepForm",
  components: {FormProgress},
  props: {
    id: {
      type: String,
      default: 'form-progress'
    },
    headerPrefix: {
      type: String,
      default: 'FORM'
    },
    numberOfSteps: {
      type: Number,
      default: 1
    },
    currentStep: {
      type: Number,
      default: 1
    },
    showProgress: {
      type: Boolean,
      default: true,
    },
    stepsCustomTitles: {
      type: Map,
      default: () => (new Map())
    },
    currentStepIcon: {
      type: String,
      default: 'fa fa-book'
    },

    /**
     * @param warning Pass the step number who has a warning
     */
    warning: {
      type: Object,
      default: () => ({})
    },

    btnSaveText: {
      type: String,
      default: 'SUBMIT'
    },
    i18n: {
      type: Boolean,
      default: true
    },
    showBarProgressPercentage: {
      type: Boolean,
      default: true
    },
    currentStepDescriptionTooltip: {
      type: String,
      default: ''
    },
    disabledSteps: {
      type: Array,
      default: () => []
    }
  },
  
  computed: {
    availableSteps() {
      return this.numberOfSteps - this.disabledSteps.length
    }
  },

  methods: {
    getStepTitle(step) {
      let title = '';
      if (this.stepsCustomTitles.has(step))
        title = this.stepsCustomTitles.get(step);
      else
        title = `${this.headerPrefix}.STEP-${step}`;
      return this.i18n ? this.$t(title) : title;
    },
  }
}
</script>

 <style scoped lang="scss"> 
.StepForm {
  &--form {
    display: flex;
    flex-flow: column nowrap;
    &--header {
      //padding: 1rem;
      min-height: 5rem;
      align-items: center;
      display: flex;
      justify-content: center;
      padding: 1rem 0 0.15rem 0 !important;
    }

    &--content {
      width: 100%;
      background-color: #FFFFFF;
      padding: 1rem;
      height: calc(100% - 10rem);
      min-height: calc(100% - 10rem);

      .CurrentStepSectionTitle {
        font-weight: bold;
        border-bottom: 1px solid $base-border-color;
        width: 100%;
        font-size: 16px;
        padding-bottom: 0.3rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
      }
    }

    &--footer {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: flex-end;
      padding: 2rem 3rem;
      min-height: 5rem;

      button {
        margin: 0.25rem;
      }
    }
  }
}
</style>
