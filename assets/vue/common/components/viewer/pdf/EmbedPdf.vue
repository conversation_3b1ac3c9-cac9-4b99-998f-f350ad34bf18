<template>
  <div class="EmbedPdf">
    <embed :src="src" width="100%" height="100%" :type="contentType"/>
  </div>
</template>

<script>
export default {
  name: "EmbedPdf",
  props: {
    src: {
      type: String,
      required: true
    },
    contentType: {
      type: String,
      default: 'application/pdf'
    }
  }
}
</script>

 <style scoped lang="scss"> 
.EmbedPdf {
  width: 100%;
  height: calc(100% - 10px);
}
</style>
