<template>
  <div class="Viewer">
    <viewer-modal v-if="modal"
                  :id="file.id"
                  :open="open" @close="$emit('close')"
                  :dialog-style="dialogStyle ?? dDialogStyle" :body-style="bodyStyle ?? style">
      <template v-slot:content>
        <component :is="type"
                   :src="`${basePath}/${file.filename}`"
                   :url="file.url || file.urlMaterial"
                   :custom="custom"
        />
      </template>
    </viewer-modal>
    <div v-else>
      <!-- Default view -->
      <component :is="type"
                 :src="`${basePath}/${file.filename}`"
                 :url="file.url || file.urlMaterial"
                 :custom="custom"
      />
    </div>
  </div>
</template>

<script>
import ImageViewer from "./ImageViewer.vue";
import OfficeViewer from "./OfficeViewer.vue";
import PdfViewer from "./PdfViewer.vue";
import TxtViewer from "./TxtViewer.vue";
import ViewerModal from "./ViewerModal.vue";
import VideoViewer from "./VideoViewer.vue";
export default {
  name: "Viewer",
  components: {ImageViewer, OfficeViewer, PdfViewer, TxtViewer, ViewerModal, VideoViewer},
  props: {
    /**
     * @param file Required element with at least a filename
     */
    file: {
      type: Object,
      required: true
    },

    basePath: {
      type: String,
      required: true
    },

    /**
     * @param modal Decide if the document display is in a modal or as base view
     */
    modal: {
      type: Boolean,
      default: true
    },

    /**
     * @param open Valid only if modal = true. Open modal when value is true
     */
    open: {
      type: Boolean,
      default: false
    },

    /**
     * @param custom Property to decide if show default component or a custom component
     */
    custom: {
      type: Boolean,
      default: true
    },

    bodyStyle: {
      type: Object,
      default: null
    },

    dialogStyle: {
      type: Object,
      default: null
    },
  },
  data() {
    return {
      style: {},
      dDialogStyle: {}
    };
  },
  computed: {
    // type() {
    //   if ('type' in this.file || 'typeMaterial' in this.file) {
    //     let type;
    //     if (isNaN(this.file.type)) {
    //       type = this.file.type.toUpperCase();
    //     } else {
    //       type = parseInt(this.file.type);
    //     }

    //     switch (type) {
    //       case 'PDF': case 1: case "1":
    //         return 'PdfViewer';
    //       case 'VIDEO': case 2: case "2":
    //         return 'VideoViewer';
    //       case 'IMAGE': case 4: case "4":
    //         return 'ImageViewer';
    //       case 'OFFICE': case 5: case "5":
    //         return 'OfficeViewer';
    //       case 'TXT': case 6: case "6":
    //         this.style = { 'background-color': '#ffffff' };
    //         return 'TxtViewer';
    //       case 'AUDIO': case 7: case '7':
    //         return '';
    //       default:
    //         return null;
    //     }
    //   }
    //   return null;
    // }
    type() {
      const fileType = this.file.type ?? this.file.typeMaterial;

      if (!fileType) {
        return null;
      }

      const type = isNaN(fileType) ? fileType.toUpperCase() : parseInt(fileType);

      switch (type) {
        case 'PDF': case 1: case "1":
          return 'PdfViewer';
        case 'VIDEO': case 2: case "2":
          return 'VideoViewer';
        case 'IMAGE': case 4: case "4":
          return 'ImageViewer';
        case 'OFFICE': case 5: case "5":
          return 'OfficeViewer';
        case 'TXT': case 6: case "6":
          this.style = { 'background-color': '#ffffff' };
          return 'TxtViewer';
        case 'AUDIO': case 7: case "7":
          return '';
        default:
          return null;
      }
    }
  },
  methods: {

  }
}
</script>

 <style scoped lang="scss"> 
.Viewer {
  width: 100%;
}
</style>
