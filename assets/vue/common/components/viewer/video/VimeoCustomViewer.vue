<template>
  <div class="VimeoCustomViewer">
    <div class="d-flex align-items-center justify-content-center w-100" v-if="loading">
      <spinner />
    </div>
    <vimeo-player ref="vimeo-player"
                  :video-url="url"
                  :player-width="width"
                  :player-height="height"
                  :controls="false"
                  @loaded="loading = false"
    ></vimeo-player>
  </div>
</template>

<script>
import Spinner from "../../../../admin/components/base/Spinner.vue";

/**
 * Pending design
 */
export default {
  name: "VimeoCustomViewer",
  components: {Spinner},
  props: {
    url: {
      type: String,
      required: true
    },
    width: {
      type: Number,
      default: 1280
    },
    height: {
      type: Number,
      default: 720
    }
  },
  data() {
    return {
      loading: true
    };
  }
}
</script>

 <style scoped lang="scss"> 
.VimeoCustomViewer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;

  & > div {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
