<template>
  <div class="PdfViewer">
    <custom-pdf :src="src" v-if="custom"></custom-pdf>
    <embed-pdf :src="src" v-else/>
  </div>
</template>

<script>
import CustomPdf from "./pdf/CustomPdf.vue";
import EmbedPdf from "./pdf/EmbedPdf.vue";
import ViewerModal from "./ViewerModal.vue";

export default {
  name: "PdfViewer",
  components: {EmbedPdf, CustomPdf, ViewerModal},
  props: {
    id: {
      type: String | Number,
      default: 'pdf'
    },

    src: {
      type: String,
      required: true
    },

    custom: {
      type: Boolean,
      default: true
    },
  }
}
</script>

 <style scoped lang="scss"> 
.PdfViewer {
  width: 100%;
  height: calc(100%);
}
</style>
