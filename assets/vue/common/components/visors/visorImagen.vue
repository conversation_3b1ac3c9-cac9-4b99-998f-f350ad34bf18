<template>
  <div class="visor-imagen">
    <img :src="`${routeBase}${name}`" />
  </div>
</template>

<script>
export default {
  props: {
    name: {
      type: String,
      default: "",
    },

    routeBase:{
      type: String,
      required: true
    }
  },
};
</script>

 <style scoped lang="scss"> 
.visor-imagen {
  height: 90vh;
  img {
    width: 100%;
    height: 100%;
    aspect-ratio: 1/1;
  }
}
</style>