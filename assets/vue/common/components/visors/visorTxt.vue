<template>
  <div class="visorTxt"> 
   <VueDocPreview :url="urlOfficce" type="text" />
  </div>
</template>

<script>
import VueDocPreview from "vue-doc-preview";
export default {
  components: {
    VueDocPreview,
  },
  props: {
    name: {
      type: String,
      default: "",
    },

     routeBase: {
      type: String,
      required: true,
    },
  },

  computed: {
    urlOfficce() {
      const domain = window.location.origin;
      return `${domain}${this.routeBase}${this.name}`;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.visorTxt{
  padding: 1rem;
  height: 90vh;
  scroll-behavior: smooth;
  overflow: scroll;  
}
</style>