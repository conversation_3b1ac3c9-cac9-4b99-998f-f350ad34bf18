<template>
  <div class="ObservationStep1">
    <input type="hidden" :value="value" name="observations">
    <froala :tag="`textarea`" v-model="value" :config="froalaObservationConfig"></froala>
  </div>
</template>

<script>
export default {
  name: "ObservationStep3",
  props: {
    observation: null
  },
  data() {
    return {
      value: this.observation?.observations ?? ''
    };
  },
  watch: {
    observation(newValue) {
      this.value = newValue?.observations ?? '';
    }
  },
  computed: {
    froalaObservationConfig() {
      return {
        ...this.$store.getters['froalaEditorModule/getDefaultConfiguration'],
        height:500,
        pluginsEnabled: ['align', 'link', 'url', 'image', 'lists', 'file', 'paragraphStyle', 'paragraphFormat']
      };
    }
  }
}
</script>

 <style scoped lang="scss"> 

</style>
