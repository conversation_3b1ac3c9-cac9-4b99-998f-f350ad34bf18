<script>

import GridFilePreview from "./GridFilePreview.vue";
import axios from "axios";
import DataNotFound from "../../../announcement/components/details/DataNotFound.vue";

export default {
  name: "FileViewGrid",
  components: {DataNotFound, GridFilePreview},
  props: {
    tag: {
      type: String,
      default: 'no-tag'
    },
    files: {
      type: Object|Array,
      default: []
    },
    downloadButtons: {
      type: Boolean,
      default: false
    },
    deleteBtn: {
      type: Boolean,
      default: false
    },
    downloadAllUrl: {
      type: String,
      default: null
    }
  },
  methods: {
    download() {
      axios.get(this.downloadAllUrl, {
        responseType: 'blob'
      }).then(r => {
        const objectUrl = window.URL.createObjectURL(
            new Blob([r.data], { type: 'application/zip' }),
        );
        let link = document.createElement('a');
        link.href = objectUrl;
        link.click();

        window.URL.revokeObjectURL(objectUrl);
      })
    }
  }
}
</script>

<template>
  <div class="FileViewGrid d-flex flex-column">
    <button class="btn btn-sm btn-primary ml-auto" v-if="downloadButtons && files.length > 0" type="button" @click="download()"><i class="fa fa-download"></i></button>
    <div v-if="files.length === 0">
      <DataNotFound :hide-on="files.length === 0"
                    :text="$t('NO_DATA') || ''"
                    icon="fa-folder"
                    :banner="true" />
    </div>
    <div class="FileViewGrid--container">
      <grid-file-preview v-for="(file, index) in files"
                    :id="`${tag}-${index}-FileViewGrid-item`"
                    :file="file"
                         :width="250"
                         :height="250"
                         :download-btn="downloadButtons"
                         :delete-btn="deleteBtn"
                         @delete="$emit('delete', $event)"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.FileViewGrid {
  width: 100%;
  &--container {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, 250px);
    gap: 1rem;
  }
}
</style>
