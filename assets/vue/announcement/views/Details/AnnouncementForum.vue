<template>
  <div class="AnnouncementForum">
    <div class="w-100">
      <button type="button" class="mr-1 btn btn-sm"
              v-for="(g, index) in groupStudents"
              :key="index"
              :class="g.groupInfo.id === selectedGroup?.groupInfo?.id ? 'btn-primary' : 'btn-default'"
              @click="selectedGroup = g">{{ g.name }}</button>
    </div>
    <forum :threads="list"
           :loading-messages="loadingThreadChildren"
           :loading-threads="isLoading"
           :active="activeThread"
           @active="activeThread = $event"
           @create="onNewThread"
           @update-thread="onUpdateThread($event)"
           @delete-thread="deleteAnnouncementForum($event)"
           @reply="sendMessage"
           v-model="activeThreadChildren"
    >
      <template v-slot:top-content>
        <forum-post v-if="threadForm.newThread || threadForm.updateThread"
                    :id="threadForm.updateThread?.id ?? announcement.id"
                    :update="threadForm.updateThread != null"
                    :emit-events="true"
                    :parent-id="forumChannelId"
                    :element-prop="threadForm.updateThread"
                    @success="onFormSuccess($event)"
                    @cancel="threadForm.newThread = false; threadForm.updateThread = null"/>
      </template>
    </forum>
  </div>
</template>

<script>
import 'bootstrap';

import Forum         from "../../../common/components/forum/Forum.vue";
import ForumPost     from "../../components/forum/ForumPost.vue";
import Loader        from "../../../admin/components/Loader.vue";
import Spinner       from "../../../admin/components/base/Spinner.vue";
import ThreadMessage from "../../components/forum/ThreadMessage.vue";
import {get}         from "vuex-pathify";

export default {
  name: "AnnouncementForum",
  components: {Forum, ThreadMessage, Spinner, Loader, ForumPost},
  data() {
    return {
      selectedGroup: null,
      list: [],
      activeThread: undefined,
      activeThreadChildren: [],
      activeThreadTotalItems: 0,
      loadingThreadChildren: false,

      //Form
      threadForm: {
        newThread: false,
        updateThread: null
      },
      intervalId: null,
      loadingMessagesTemp: false,
      forumChannelId: null
    };
  },
  computed: {
    announcement: get('announcementModule/announcement'),
    channels: get('announcementModule/announcement@channels'),
    groupStudents: get('announcementModule/calledUsers'),
    typeCourse() {
      // [1] Teleformación [2] Presencial [3] Mixto [4] Aula Virtual
      return (this.announcement?.course) ? this.announcement.course.typeID : 1;
    },
    isTeleformationOrMixte() {
      return this.typeCourse === 1 || this.typeCourse === 3
    },
    showForum() {
      return this.isTeleformationOrMixte && (this.announcement?.comunications?.FORUM)
    },
    channelsInGroup() {
      if (!this.showForum) return {};
      const group = this.channels.find(g => g.group === this.selectedGroup?.groupInfo?.id);
      return group?.channels ?? {};
    },
    isLoading() {
      return this.$store.getters['forumModule/isLoading'];
    },
  },
  watch: {
    activeThread(newValue) {
      this.loadThreadData();
    },
    groupStudents: {
      immediate: true,
      handler: function () {
        if (this.selectedGroup == null && this.groupStudents.length > 0)
          this.selectedGroup = this.groupStudents[0];
      }
    },
    channelsInGroup: {
      immediate: true,
      deep: true,
      handler: function () {
        this.forumChannelId = this.channelsInGroup.forum?.id;
      }
    },
    forumChannelId: {
      immediate: true,
      handler: function () {
        this.loadAnnouncementForum();
      }
    },
  },
  created() {
    this.loadAnnouncementForum();
    this.intervalId = setInterval(() => {
      if (this.activeThread?.id != null && !this.loadingMessagesTemp) this.loadThreadData(false);
    }, 10000);
  },
  beforeDestroy() {
    if (this.intervalId != null) clearInterval(this.intervalId);
  },
  methods: {
    onNewThread() {
      this.threadForm.updateThread = null;
      this.threadForm.newThread = true;
    },

    onUpdateThread(thread) {
      this.threadForm.updateThread = thread;
      this.threadForm.newThread = false;
    },

    async loadAnnouncementForum() {
      if (this.forumChannelId == null) return;
      const result = await this.$store.dispatch('forumModule/getAnnouncementForum', this.forumChannelId)
      const { data } = result;
      this.list = data;
      if (this.list.length > 0) this.activeThread = this.list[0];
      else {
        this.activeThread = undefined
        this.activeThreadChildren = []
        this.activeThreadTotalItems = 0
      }
    },

    loadThreadData(loader = true) {
      if (!this.activeThread?.id) return null;
      if (loader) this.loadingThreadChildren = true;
      this.loadingMessagesTemp = true;
      this.$store.dispatch('forumModule/loadForumPostFullData', this.activeThread.id).then(res => {
        const { data } = res;
        this.activeThreadChildren = data;
        this.activeThreadTotalItems = data.length;
      }).finally(() => {
        this.loadingMessagesTemp = false;
        if (loader) this.loadingThreadChildren = false;
      });
    },

    deleteAnnouncementForum(forum) {
      this.$alertify.confirmWithTitle(
          this.$t('ANNOUNCEMENT.CONFIRM_THREAD_DELETE.TITLE'),
          this.$t('ANNOUNCEMENT.CONFIRM_THREAD_DELETE.THREAD_DESC'),
          () => {
            this.$store.dispatch('forumModule/removeAnnouncementForum', {
              channelId: forum.id,
              parentId: this.forumChannelId
            }).then(res => {
              const { error } = res;
              if (error) this.$toast.error(this.$t('DELETE_FAILED') + '');
              else {
                this.$toast.success(this.$t('DELETE_SUCCESS') + '');
                if (`${forum.id}` === `${this.activeThread.id}`) this.loadAnnouncementForum();
                const index = this.list.findIndex(item => item.id === forum.id);
                if (index > -1) this.list.splice(index, 1);
              }
            })
          },
          () => {}
      )
    },

    sendMessage(data) {
      if (data.message.length < 1) return;
      if (this.activeThread == null) return;

      this.$toast.clear();
      this.$toast.info('Publicando comentario');

      this.$store.dispatch('forumModule/sendReply', { channelId: this.activeThread.id, ...data }).then(res => {
        const { error, data } = res;
        this.$toast.clear();
        if (error) this.$toast.error(this.$t('MESSAGE_REPLY_FAILED') + '');
        else {
          if (data.replyTo) {
            const foundIndex = this.activeThreadChildren.findIndex(thread => `${thread.id}` === `${data.replyTo}`);
            if (foundIndex > -1) this.activeThreadChildren[foundIndex].replies.push(data);
          } else
            this.activeThreadChildren.push(data);

          this.$toast.info('Comentario publicado correctamente');
        }
      })
    },

    onFormSuccess(content) {
      if (this.threadForm.newThread) {
        this.list.push(content);
      } else if (this.threadForm.updateThread != null) {
        if (content.id === this.activeThread.id) {
          this.activeThread.name = content.name;
        }

        const index = this.list.findIndex(item => parseInt(item.id) === parseInt(content.id));
        if (index >= 0) this.list[index].name = content.name;

      }
      this.threadForm.newThread = false;
      this.threadForm.updateThread = null;
    }
  }
}
</script>

 <style scoped lang="scss"> 
.AnnouncementForum {
  ::v-deep(.Forum) {
    padding: 0;
  }
}
</style>
