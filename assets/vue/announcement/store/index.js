import initStore                     from "../../common/store";
import announcementFormModule from './module/announcementForm/module';
import announcementModule            from "./module/announcementModule";
import announcementObservationModule from "./module/announcementObservationModule";
import announcementTaskModule        from "./module/announcementTaskModule";
import forumModule                   from "./module/forumModule";
import chatModule                    from "./module/chatModule";
import notificationModule            from "./module/notificationModule";
import materialCourseModule          from "../../common/store/modules/materialCourseModule";
import taskCourseModule              from "../../common/store/modules/taskCourseModule";
import timeCounterModule					 from "../../common/store/modules/timeCounterModule";
import baseChatModule from "../../common/store/modules/baseChatModule";
import announcementReportModule from "./module/announcementReportModule";

export default initStore(
	{
		modules: {
			announcementModule,
			announcementFormModule,
			announcementObservationModule,
			announcementReportModule,
			announcementTaskModule,
			forumModule,
			chatModule,
			materialCourseModule,
			taskCourseModule,
			notificationModule,
			timeCounterModule,
			baseChatModule
		}
	}
)
