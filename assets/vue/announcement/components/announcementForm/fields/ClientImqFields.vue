<template>
  <div class="ClientImqFields d-flex flex-row flex-wrap">
    <div class="form-group col-md-4">
      <label for="afType">{{ $t("ANNOUNCEMENT.IMQ.AF_TYPE") }}</label>
      <input
        name="afType"
        id="afType"
        type="text"
        class="form-control"
        v-model="data.afType"
        :placeholder="$t('INPUT.PLACEHOLDER.AF_TYPE')"
      />
    </div>
    <div class="form-group col-md-4">
      <label for="formationLevel">{{
        $t("ANNOUNCEMENT.IMQ.FORMATION_LEVEL")
      }}</label>
      <input
        name="formationLevel"
        id="formationLevel"
        type="text"
        class="form-control"
        v-model="data.formationLevel"
        :placeholder="$t('INPUT.PLACEHOLDER.FORMATION_LEVEL')"
      />
    </div>
    <div class="form-group col-md-4">
      <label for="numberOfParticipantsAf">{{
        $t("ANNOUNCEMENT.IMQ.NUMBER_OF_PARTICIPANTS_AF")
      }}</label>
      <input
        name="numberOfParticipantsAf"
        id="numberOfParticipantsAf"
        type="number"
        min="0"
        class="form-control"
        v-model.number="data.numberOfParticipantsAf"
      />
    </div>
    <div class="form-group col-md-4">
      <label for="numberOfGroups">{{
        $t("ANNOUNCEMENT.IMQ.NUMBER_OF_GROUPS")
      }}</label>
      <input
        name="numberOfGroups"
        id="numberOfGroups"
        type="number"
        class="form-control"
        min="0"
        v-model.number="data.numberOfGroups"
      />
    </div>
    <div class="form-group col-md-4">
      <label for="directedTo">{{ $t("ANNOUNCEMENT.IMQ.DIRECTED_TO") }}</label>
      <input
        name="directedTo"
        id="directedTo"
        type="text"
        class="form-control"
        v-model="data.directedTo"
        :placeholder="$t('INPUT.PLACEHOLDER.DIRECTED_TO')"
      />
    </div>
    <div class="form-group col-md-4">
      <label for="selectionCriteria">{{
        $t("ANNOUNCEMENT.IMQ.SELECTION_CRITERIA")
      }}</label>
      <input
        name="selectionCriteria"
        id="selectionCriteria"
        type="text"
        class="form-control"
        v-model="data.selectionCriteria"
        :placeholder="$t('INPUT.PLACEHOLDER.SELECTION_CRITERIA')"
      />
    </div>
    <div class="form-group col-md-4">
      <label for="contactPerson">{{ $t("CONTACT_PERSON") }}</label>
      <input
        name="contactPerson"
        id="contactPerson"
        type="text"
        class="form-control"
        v-model="data.contactPerson"
        :placeholder="$t('INPUT.PLACEHOLDER.CONTACT_PERSON')"
      />
    </div>
    <div class="form-group col-md-4">
      <label for="telephone">{{ $t("TELEPHONE") }}</label>
      <input
        name="telephone"
        id="telephone"
        type="text"
        class="form-control"
        v-model="data.telephone"
        :placeholder="$t('INPUT.PLACEHOLDER.TELEPHONE')"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "ClientImqFields",
  props: {
    announcement: null,
  },
  data() {
    return {
      data: {
        afType: this.announcement?.afType ?? "",
        formationLevel: this.announcement?.formationLevel ?? "",
        numberOfParticipantsAf: this.announcement?.numberOfParticipantsAf ?? "",
        numberOfGroups: this.announcement?.numberOfGroups ?? "",
        directedTo: this.announcement?.directedTo ?? "",
        selectionCriteria: this.announcement?.selectionCriteria ?? "",
        contactPerson: this.announcement?.contactPerson ?? "",
        telephone: this.announcement?.telephone ?? "",
      },
    };
  },
  watch: {
    data: {
      handler: function (old, newVal) {
        this.$emit("updated", this.data);
      },
      deep: true,
    },
  },
};
</script>

 <style scoped lang="scss"> 
</style>
