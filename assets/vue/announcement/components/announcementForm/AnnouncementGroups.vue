<script>

import {get, sync} from "vuex-pathify";
import UserGroup from "../UserGroup.vue";
import DataNotFound from "../details/DataNotFound.vue";

export default {
  name: "AnnouncementGroups",
  components: {DataNotFound, UserGroup},
  computed: {
    groups: sync('announcementFormModule/announcement@students'),
    tutors: get('announcementFormModule/tutors'),
    type: get("announcementFormModule/announcement@type"),
    notifiedAt: get("announcementFormModule/announcement@notifiedAt"),
    isNotified: get("announcementFormModule/isNotified"),
  },
}
</script>

<template>
  <div class="AnnouncementGroups">
    <DataNotFound
        :text="$t('ANNOUNCEMENT.FORM.GROUP.GROUPS_NOT_AVAILABLE') || ''"
        icon="fa-users"
        :hide-on="!groups.length"
        :banner="true"/>
    <user-group v-for="(group, i) in groups"
                :title="`Grupo ${i + 1} ${group.code ? `(${group.code})` : ''}`"
                :key="group.id"
                :opened="!i"
                :tag="`AnnouncementGroups-${group.id}`"
                v-model="groups[i]" :tutors="tutors"
                :announcement-type="type"
                :allow-delete="notifiedAt != null && notifiedAt.length > 0"
    />
  </div>
</template>

<style scoped lang="scss">
.AnnouncementGroups {
  .UserGroup {
    margin-bottom: 2rem;
  }
}
</style>
