<script>
import {get, sync} from "vuex-pathify";
import {configurationClientAnnouncement} from "../../mixins/configurationClientAnnouncement";

import ButtonWithDescription from "../../../common/components/ButtonWithDescription.vue";
import ChapterTiming from "../chapterTiming/ChapterTiming.vue";
import Spinner from "../../../base/BaseSpinner.vue";
import FileAsText from "../../../common/components/file/FileAsText.vue";
import LabelWithInfo from "../../../common/components/ui/LabelWithInfo.vue";
import Multiselect from "vue-multiselect";

function defaultExternStudentsData() {
  return [
    {
      data: [],
      tutor: {
        name: "",
        email: "",
      },
      place: "",
      cost: 0,
      typeMoney: null
    }
  ];;
}

export default {
  name: "AnnouncementGeneralInfoExtern",
  components: {
    LabelWithInfo,   
    FileAsText,
    Spinner,
    ChapterTiming,
    ButtonWithDescription,
    Multiselect,
  },

  mixins: [configurationClientAnnouncement],

  data() {
    return {
      loadingChapters: false,
      showChapters: false,
      test: {},
      modalities: [{ type: 'online', name: 'online'}],
      modality: '',
      testType: null,
    };
  },

  computed: {
    announcement: sync("announcementFormModule/announcement"), // Main announcement object
    typesCourse: sync("announcementFormModule/types"), // Main announcement object
    courseCategories: get("announcementFormModule/courseCategories"), // Main announcement object
    typeMoneys: get("announcementFormModule/typeMoneys"), // Main announcement object
    extra() {
      return this.$store.state.announcementFormModule.extra || [];
    },

    locales() {
      const l = [];
      const locales = this.$store.getters["localeModule/getLocales"];
      const keys = Object.keys(locales);
      keys.forEach(k => {
        l.push({id: k, name: locales[k]})
      });
      return l;
    },
    timezones: get("announcementFormModule/timezones"),
    isNotified: get("announcementFormModule/isNotified"),

    configAnnouncement: sync(
        "announcementFormModule/announcement@configAnnouncement"
    ),

    code: sync("announcementFormModule/announcement@code"),
    course: sync("announcementFormModule/announcement@course"),
    type: sync("announcementFormModule/announcement@type"),
    courseTypeObject: {
      get() {
        return this.typesCourse.find(t => t.type === this.type);
      },
      set(newValue) {
        this.type = newValue.type;
        this.course.typeCourseId = newValue.id;
      }
    },
    courseCategory: {
      get() {
        return this.course.category ?? {};
      },
      set(newValue) {
        this.$set(this.course, 'category', newValue);
      }
    },
    locale: {
      get() {
        return this.locales.find(l => l.id === this.course.locale);
      },
      set(newValue) {
        this.$set(this.course, 'locale', newValue.id);
      }
    },
    // locale: sync("announcementFormModule/announcement@course"),
    timezone: sync("announcementFormModule/announcement@timezone"),
    startAt: sync("announcementFormModule/announcement@startAt"),
    finishAt: sync("announcementFormModule/announcement@finishAt"),
    totalHours: sync("announcementFormModule/announcement@totalHours"),

    // place: sync("announcementFormModule/announcement@totalHours"),
    place: {
      get() {
        const students = this.announcement.students;
        if (students.length === 0) return "";
        return students[0].place;
      },
      set(newValue) {
        const students = this.announcement.students;
        if (students.length === 0) {
          this.announcement.students = [
            {
              data: [],
              tutor: {
                name: "",
                email: "",
              },
              place: ""
            }
          ];
        }
        this.announcement.students[0].place = newValue;
      }
    },
    cost: {
      get() {
        const students = this.announcement.students;
        if (students.length === 0) return "";
        return students[0].cost;
      },
      set(newValue) {
        const students = this.announcement.students;
        if (students.length === 0) {
          this.announcement.students = [
            {
              data: [],
              tutor: {
                name: "",
                email: "",
              },
              place: "",
              cost: 0
            }
          ];
        }
        this.announcement.students[0].cost = newValue;
      }
    },
    typeMoney: {
      get() {
        const students = this.announcement.students;
        if (students.length === 0) return {};
        return students[0].typeMoney ?? {};
      },
      set(newValue) {
        const students = this.announcement.students;
        if (students.length === 0) {
          this.announcement.students = defaultExternStudentsData();
        }
        this.announcement.students[0].typeMoney = newValue;
      }
    },
    tutorName: {
      get() {
        const students = this.announcement.students;
        if (students.length === 0) return "";
        return students[0].tutor.name;
      },
      set(newValue) {
        const students = this.announcement.students;
        if (students.length === 0) {
          this.announcement.students = [
              {
                data: [],
                tutor: {
                  name: "",
                  email: "",
                }
              }
          ];
        }
        this.announcement.students[0].tutor.name = newValue;
      }
    },
    tutorEmail: {
      get() {
        const students = this.announcement.students;
        if (students.length === 0) return "";
        return students[0].tutor.email;
      },
      set(newValue) {
        const students = this.announcement.students;
        if (students.length === 0) {
          this.announcement.students = [
            {
              data: [],
              tutor: {
                name: "",
                email: "",
              }
            }
          ];
        }
        this.announcement.students[0].tutor.email = newValue;
      }
    },

    froalaConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 400,
        pluginsEnabled: ["align", "lists", "paragraphStyle", "paragraphFormat"],
      };
    },
  }
};
</script>

<template>
  <div class="AnnouncementGeneralInfoExtern">
    <div class="AnnouncementGeneralInfoExtern--info d-flex flex-row flex-wrap">
      <div
          id="AnnouncementGeneralInfoExtern-code"
          class="form-group col-xs-12 col-md-4 info-icon"
      >
        <label-with-info
            :info="$t('ANNOUNCEMENT.FORM.ENTITY.CODE_INFO') + ''"
        >
          {{ $t("ANNOUNCEMENT.FORM.ENTITY.ANNOUNCEMENT_NAME") }}
        </label-with-info>
        <input
            type="text"
            class="form-control"
            v-model="code"
            :disabled="isNotified"
        />
      </div>

      <div id="AnnouncementGeneralInfoExtern-courseName" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("COURSES.COURSE_NAME") }}</label>
        <input
            type="text"
            class="form-control"
            v-model="course.name"
            :disabled="isNotified"
        />
      </div>

      <div id="AnnouncementGeneralInfoExtern-courseType" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("ANNOUNCEMENT.DEFAULT.MODALITY") }}</label>
        <Multiselect
            v-model="courseTypeObject"
            :options="typesCourse"
            :allow-empty="false"
            :searchable="true"
            track-by="type"
            label="name"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
            :disabled="isNotified"
        ></Multiselect>
      </div>

      <div id="AnnouncementGeneralInfoExtern-courseCategory" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("CATEGORY") }}</label>

        <Multiselect
            v-model="courseCategory"
            :options="courseCategories"
            :searchable="true"
            :allow-empty="false"
            track-by="id"
            label="name"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
            :disabled="isNotified"
        ></Multiselect>
      </div>

      <div id="AnnouncementGeneralInfoExtern-courseLocale" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("LOCALE") }}</label>
        <Multiselect
            v-model="locale"
            :options="locales"
            track-by="id"
            label="name"
            :searchable="true"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
            :disabled="isNotified"
        ></Multiselect>
      </div>
      <div class="form-group col-xs-12 col-md-4" id="AnnouncementGeneralInfoExtern-timezone">
        <label>{{ $t("TIMEZONE") }}</label>
        <Multiselect
            v-model="timezone"
            :options="timezones"
            :searchable="true"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
            :disabled="isNotified"
        ></Multiselect>
      </div>

      <div id="AnnouncementGeneralInfoExtern-startAt" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("ANNOUNCEMENT.FORM.ENTITY.START_AT") }}</label>
        <input
            type="datetime-local"
            class="form-control"
            v-model="startAt"
            :disabled="isNotified"
        />
      </div>

      <div id="AnnouncementGeneralInfoExtern-finishAt" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("ANNOUNCEMENT.FORM.ENTITY.FINISH_AT") }}</label>
        <input
            type="datetime-local"
            class="form-control"
            v-model="finishAt"
            :disabled="isNotified"
        />
      </div>

      <div id="AnnouncementGeneralInfoExtern-totalHours" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("ANNOUNCEMENT.FORM.ENTITY.FORMATION_TIME") }}</label>
        <input
            type="number"
            class="form-control"
            v-model.number="totalHours"
            min="0"
            :disabled="isNotified"
        />
      </div>

      <div id="AnnouncementGeneralInfoExtern-place" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("ANNOUNCEMENT.FORM.GROUP.PLACE") }}</label>
        <input
            type="text"
            class="form-control"
            v-model="place"
            :disabled="isNotified"
        />
      </div>

      <div id="AnnouncementGeneralInfoExtern-tutorName" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("USER.TUTOR_NAME") }}</label>
        <input
            type="text"
            class="form-control"
            v-model="tutorName"
            :disabled="isNotified"
        />
      </div>

      <div id="AnnouncementGeneralInfoExtern-tutorEmail" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("USER.TUTOR_EMAIL") }}</label>
        <input
            type="email"
            class="form-control"
            v-model="tutorEmail"
            :disabled="isNotified"
        />
      </div>

      <div id="AnnouncementGeneralInfoExtern-typeMoney" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("ANNOUNCEMENT.GROUP_SESSION_INFO.TYPE_MONEY") }}</label>
        <Multiselect
            v-model="typeMoney"
            :options="typeMoneys"
            :searchable="true"
            track-by="id"
            label="name"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            selectLabel=""
            selectedLabel=""
            deselectLabel=""
            :disabled="isNotified"
        ></Multiselect>
      </div>

      <div id="AnnouncementGeneralInfoExtern-cost" class="form-group col-xs-12 col-md-4">
        <label>{{ $t("ANNOUNCEMENT.GROUP_SESSION_INFO.COST") }}</label>
        <input
            type="number"
            class="form-control"
            v-model.number="cost"
            min="0"
            :disabled="isNotified"
        />
      </div>
      <div
        v-for="(item, index) in extra" 
        :key="item.id" 
        :id="`AnnouncementGeneralInfoExtern-extra-${item.id}`" 
        class="form-group col-xs-12 col-md-4"
      >
        <label>{{ item.name }}</label>
        <input
          type="text"
          class="form-control"
          v-model="extra[index].value"
          :disabled="isNotified"
        />
      </div>
    
    </div>
  </div>
</template>

<style scoped lang="scss">
.custom-file-with-button {
  label {
    width: 100%;
  }

  input {
    display: none;
  }
}

.AnnouncementGeneralInfoExtern {
  &--info {
    padding: 0 0 1rem 0;

    .objetive-and-content {
      border: 1px solid var(--color-neutral-mid-darker);
      padding: 0.5rem;
      border-radius: 5px;
      min-height: 239px;
      max-height: 240px;
      overflow-y: auto;
    }
  }

  &--criteria {
    background-color: #d8eef8;
    margin: 0 -1rem;

    h1 {
      font-weight: bold;
      font-size: 18px;
      color: var(--color-neutral-darkest);
    }

    .form-group {
      label {
        font-size: 16px;
      }

      &.with-switch {
        .form-check.form-switch {
          display: flex;
          align-items: center;
          padding-left: 0;

          & > label {
            flex-grow: 1;
            margin-left: 0.25rem;
          }
        }
      }

      .criteria-enabled {
        opacity: 1 !important;
      }
    }
  }

  &--chapter--timing {
    padding-top: 1rem;

    .actions {
      display: flex;
      flex-flow: column;

      @media #{min-small-screen()} {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
      }
    }

    .ChapterTimingContainer {
      display: flex;
      flex-flow: column;
      width: 100%;
      margin-top: 2rem;
      gap: 0.5rem;
      justify-content: center;

      @media #{min-small-screen()} {
        display: grid;
        grid-template-columns: repeat(auto-fit, 271px);
        align-items: flex-start;
      }
    }
  }
}
</style>
