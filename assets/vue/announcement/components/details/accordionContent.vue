<template>
<div class="accordionContent col-12">
  <div class="accordionHeader d-flex justify-content-between align-items-center">
    <div @click="isOpened=!isOpened" class="cursor-pointer">
      <span><i class="fa" :class="icon"></i> {{title}}</span>
      <span v-show="badge.length" class="badge bg-light text-dark ml-3 user-select-none">{{badge}}</span>
    </div>
    <div v-if="search" class="col-4 mr-auto">
      <label class="content-search-label w-100 mb-0">
        <input type="search" class="form-control is-blank" @input="onSearch" spellcheck="false">
      </label>
    </div>
    <div class="d-flex flex-row flex-nowrap">
      <slot name="extra-actions" />
       <button class="btn btn-sm btn-primary mr-3" v-show="showReportButton && hasReportZip"
              @click="setGroup"
              data-bs-toggle="modal"
              data-bs-target="#modalInspectorReports">
        <i class="fa fa-file mr-2"></i>{{ $t('GENERATE_REPORT') }}
      </button> 
      <i v-show="isOpened" class="fa fa-chevron-up cursor-pointer" @click="isOpened=false"></i>
      <i v-show="!isOpened" class="fa fa-chevron-down cursor-pointer" @click="isOpened=true"></i>
    </div>
  </div>
  <div class="accordionBody row overflow-auto w-100" v-show="isOpened">
    <slot></slot>
  </div>
</div>
</template>

<script>
export default {
  name: "accordionContent",
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    badge: {
      type: String,
      default: ''
    },
    opened: {
      type: Boolean,
      default: false
    },
    showReportButton: {
      type: Boolean,
      default: true
    },
    groupId: {
      type: Number|String,
      default: 0
    },
    search: {
      type: Boolean,
      default: false
    },
    hasReportZip: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isOpened: false
    }
  },
  mounted() {
    this.isOpened = this.opened;
  },
  methods: {
    setGroup() {
      this.$store.dispatch("announcementModule/setGroupSelected", { id: this.groupId });
    },
    onSearch(e) {
      this.$emit('on-search', e.target.value);
    }
  }
}
</script>

 <style scoped lang="scss"> 
.accordionContent {
  .accordionHeader {
    padding: 1rem 1.5rem;
    color: white;
    background-color: var(--color-neutral-mid-darker);
  }

  &.warning {
    .accordionHeader {
      background-color: $color-secondary-light;
      & > * {
        color: #ffffff;
      }
    }
  }

  @media (max-width: 576px) {
    padding: 0;
  }
}
</style>
