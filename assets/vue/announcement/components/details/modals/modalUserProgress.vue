<template>
  <div class="modalUserProgress">
    <BaseModal
      :identifier="tag + 'userProgress'"
      :title="$t('PROGRESS') || ''"
      padding="0"
      size="modal-xl"
      @close="chapterSelected = undefined"
    >
      <div class="row px-0 mx-0">
        <div class="col-lg-5 col-md-12 p-3 p-sm-5 resumen">
          <div
            class="d-flex flex-column justify-content-between align-items-start h-100"
          >
            <div class="w-100">
              <div class="d-flex justify-content-start align-items-center mb-4">
                <img class="userAvatar mr-2" :src="user.avatar || ''" alt="" />
                <h5 class="font-weight-bold title">{{ user.name }}</h5>
              </div>
              <div class="cursor-pointer" @click="currentPage = 0">
                <TaskProgressBar
                  :title="$t('ANNOUNCEMENT.MODALS.PROGRESS_TASK1') + ':'"
                  :selected="currentPage === 0"
                  :avg-value="user.progressCourse"
                />
              </div>
              <div class="mt-2 cursor-pointer" @click="currentPage = 1">
                <TaskProgressBar
                  :title="$t('ANNOUNCEMENT.MODALS.PROGRESS_TASK2') + ':'"
                  :selected="currentPage === 1"
                  :avg-value="user.progressTask"
                />
              </div>
              <div class="mt-2 cursor-pointer">
                <TaskProgressBar
                  :title="$t('ANNOUNCEMENT.MODALS.PROGRESS_HOURS') + ':'"
                  :avg-value="user.progressHours"
                />
              </div>
            </div>
            <div class="footer w-100 mt-5">
              <p
                class="d-flex justify-content-between align-items-center w-100 pr-4"
              >
                <span class="font-weight-bold"
                  >{{
                    $t("ANNOUNCEMENT.MODALS.PROGRESS_GUIDE_SUBTITLE")
                  }}:</span
                >
                <span v-if="user.dateReadDidacticGuide">
                  {{ getDate(user.dateReadDidacticGuide) }}
                  {{ getTime(user.dateReadDidacticGuide) }}</span
                >
              </p>
              <div
                class="d-flex justify-content-between align-items-center w-100 bg-white bg-white p-4 borderRadius"
              >
                <div>
                  <span class="font-weight-bold d-block mb-2"
                    >{{ $t("ANNOUNCEMENT.FORM.STEPS.CERTIFICATE") }}:</span
                  >
                  <button
                    class="btn btn-sm btn-primary"
                    type="button"
                    :disabled="downloading"
                    @click="downloadDiploma"
                  >
                    <i
                      class="fa fa-download mr-2"
                      :class="{
                        'fa-circle-o-notch fa-spin fa-fw': downloading,
                      }"
                    ></i>
                    {{
                      $t("ANNOUNCEMENT.MODALS.PROGRESS_DOWNLOAD_CERTIFICATE")
                    }}
                  </button>
                </div>
                <span class="text-danger pl-3" v-show="!user.isDownloadDiploma">
                  {{ $t("ANNOUNCEMENT.MODALS.PROGRESS_STATUS1") }}
                </span>
                <span class="text-success pl-3" v-show="user.isDownloadDiploma">
                  {{ $t("ANNOUNCEMENT.MODALS.PROGRESS_STATUS2") }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-7 m-auto col-md-12 contentPreview">
          <div class="row pr-2 pl-2">
            <TaskDetails
              :tasks="tasks"
              class="w-100 py-3"
              v-if="currentPage"
              :allow-open="false"
              :change-background="false"
            />
            <div v-else>
              <UserCourseDetailsChapterList
                v-if="!chapterSelected"
                class="col-lg-12 col-md-11 col-sm-12 py-3 details"
                :user-data="details"
                :loading-data="sendingExcelData"
                @select="setChapterSelected"
                @download="downloadExcel"
              />
              <UserCourseDetailsChapter
                v-else
                class="col-lg-12 col-md-11 col-sm-12 bg-details py-3 details"
                :chapter-data="chapterSelected"
                @return="setChapterSelected(undefined)"
              />
            </div>
          </div>
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script>
import UserCourseDetailsChapterList from "../../../../admin/course/modal/UserCourseDetailsChapterList.vue";
import UserCourseDetailsChapter from "../../../../admin/course/modal/UserCourseDetailsChapter.vue";
import UserStatsModel from "../../../../admin/course/models/UserStatsModel";
import TaskDetails from "../taskDetails";
import TaskContentDetails from "../taskContentDetails";
import TaskProgressBar from "../taskProgressBar";
import { UtilsMixin } from "../../../mixins/utilsMixin";
import { get } from "vuex-pathify";
import axios from "axios";
import BaseModal from "../../../../base/BaseModal.vue";

export default {
  name: "modalUserProgress",
  mixins: [UtilsMixin],
  components: {
    TaskProgressBar,
    TaskContentDetails,
    TaskDetails,
    UserCourseDetailsChapter,
    UserCourseDetailsChapterList,
    BaseModal
  },
  props: {
    tag: { type: String, default: "" },
  },
  computed: {
    announcement: get("announcementModule/announcement"),
    user: get("announcementModule/userSelected"),
    tasks() {
      const { comunications } = this.user;
      return comunications ? comunications.tasks : [];
    },
    chapters() {
      const { comunications } = this.user;
      return comunications ? comunications.chapters : [];
    },
    details() {
      const { details } = this.user;
      return details ? new UserStatsModel(details) : new UserStatsModel();
    },
  },
  data() {
    return {
      currentPage: 0,
      downloading: false,
      chapterSelected: undefined,
      sendingExcelData: false,
    };
  },
  methods: {
    downloadDiploma() {
      if (this.downloading) return;
      this.downloading = true;
      this.$store
        .dispatch("announcementModule/downloadDiploma", {
          idUser: this.user.id,
          idAnnouncement: this.announcement.id,
        })
        .then((fileData) => {
          if (fileData) {
            const downloadLink = document.createElement("a");
            downloadLink.href = fileData.data;
            downloadLink.download = fileData.nombre;
            downloadLink.click();
          }
        })
        .finally(() => (this.downloading = false));
    },
    setChapterSelected(selected) {
      this.chapterSelected = selected;
    },
    async downloadExcel() {
      if (this.sendingExcelData) return null;
      this.sendingExcelData = true;
      try {
        let data = {
          userId: this.details.id
        };
        const {
          data: { error },
        } = await axios.post(
          `/admin/api/v1/course-stats/${this.announcement.course.id}/xlsx?announcementId=${ this.announcement.id}`,
          data
        );
        if (error) this.$toast.error(`${courseTranslations.export_error}`);
        else
          this.$toast.success(
            `${courseTranslations.export_success}<br/>(${this.$t(
              "COURSE_STATS.EXPORT.ZIP_DIR"
            )})`
          );
      } catch (e) {
        console.log(e);
      } finally {
        this.sendingExcelData = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.modalUserProgress {
  .userAvatar {
    width: 3rem;
    height: 3rem;
  }

  .contentPreview {
    overflow-y: auto;
    max-height: 80vh;

    @media (max-width: 576px) {
      max-height: initial;
    }
  }

  .footer {
    font-size: 0.8rem;
  }

  .borderRadius {
    border-radius: 5px;
  }

  &::v-deep {
    .modal-xl {
      max-width: 1400px;
    }

    .resumen {
      background-color: var(--color-neutral-mid-light);
    }

    .pl-5 {
      padding-left: 1rem !important;
    }
    .pr-5 {
      padding-right: 1rem !important;
    }
  }
}
</style>
