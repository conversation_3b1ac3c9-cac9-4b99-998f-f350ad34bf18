<template>
<div class="modalUserDetails">
  <BaseModal
      :identifier="tag + 'userDetails'"
      :title="$t('ANNOUNCEMENT.MODALS.STUDENT_PROFILE') || ''"
      padding="3rem"
      size="modal-lg"
  >
    <div class="d-flex flex-column align-items-center">
      <img class="userAvatar" :src="user.avatar || ''" alt=" ">
      <h3 class="font-weight-bold title font-weight-bold title my-3">{{ user.name }}</h3>
    </div>
    <div class="container">
      <div class="row">
        <div class="col-md-6 col-xs-12 font-weight-bold">
          <i class="fa fa-id-card"></i> DNI:
        </div>
        <div class="col-md-6 col-xs-12 line-break-anywhere">{{ user.dni }}</div>
      </div>
      <div class="row mt-2 mb-2">
        <div class="col-md-6 col-xs-12 font-weight-bold">
          <i class="fa fa-envelope"></i> {{ $t('ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_EMAIL') }}:
        </div>
        <div class="col-md-6 col-xs-12"><a :href="'mailto:'+user.email" class="link line-break-anywhere">{{ user.email }}</a></div>
      </div>
    </div>
  </BaseModal>
</div>
</template>

<script>

import {get}     from "vuex-pathify";
import BaseModal from "../../../../base/BaseModal.vue";

export default {
  name: "modalUserDetails",
  components: { BaseModal},
  props: {
    tag: { type: String, default: '' },
  },
  computed: {
    user: get('announcementModule/userSelected'),
  },
}
</script>

 <style scoped lang="scss"> 
.modalUserDetails {
  .userAvatar {
    width: 5rem;
    height: 5rem;
  }

  .container {
    padding: 1rem;
    border-radius: 7px;
    background-color: white;
  }

  ::v-deep {
    .modal-body {
      background-color: var(--color-neutral-lighter);
    }
  }
}
</style>
