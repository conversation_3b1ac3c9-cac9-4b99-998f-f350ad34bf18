<script>

import {sync} from "vuex-pathify";
import FileViewGrid from "../../../common/components/fileGrid/FileViewGrid.vue";

export default {
  name: "ModalSessionAssistanceFiles",
  components: {FileViewGrid},
  computed: {
    session: sync("announcementModule/sessionSelected"),
  },
  methods: {
    deleteFile(file) {
      this.$alertify.confirmWithTitle(
          this.$t('FILE_UPLOAD.CONFIRM_DELETE.TITLE'),
          this.$t('FILE_UPLOAD.CONFIRM_DELETE.DESCRIPTION'),
          () => {
            this.$store.dispatch('announcementModule/deleteSessionAssistanceFile', { sessionId: this.session.id, id: file.id }).then(r => {
              const { error } = r;
              if (error) this.$toast.error(this.$t('DELETE_FAILED') + '');
              else {
                const files = this.session.assistanceFiles ?? [];
                const index = files.findIndex(item => item.id === file.id);
                if (index >= 0) files.splice(index, 1);
                this.session.assistanceFiles = files;
                this.$toast.success(this.$t('DELETE_SUCCESS') + '');
              }
            })
          },
          () => {},
      );
    }
  }
}
</script>

<template>
  <div class="ModalSessionAssistanceFiles">
    <BaseModal identifier="ModalSessionAssistanceFiles-modal"
                padding="2rem"
                size="modal-lg"
                :title="$t('ANNOUNCEMENT.ASSISTANCE_FILES') + ''"
    >
      <file-view-grid
          :files="session?.assistanceFiles ?? []"
          :download-all-url="`/admin/announcement/group-session/${session.id}/download-assistance-files`"
          :download-buttons="true"
          :delete-btn="true"
          @delete="deleteFile"
      />
    </BaseModal>
  </div>
</template>

<style scoped lang="scss">

</style>
