<script>
import { SOURCE_ANNOUNCEMENT_INTERN, SOURCE_ANNOUNCEMENT_EXTERN } from "../store/module/announcementForm/common";

export default {
  name: "AnnouncementTypeSelection",
  data() {
    return {
      selected: {
        type: SOURCE_ANNOUNCEMENT_INTERN
      },
      types: [
        {
          type: SOURCE_ANNOUNCEMENT_INTERN
        },
        {
          type: SOURCE_ANNOUNCEMENT_EXTERN
        },
      ]
    };
  },

  methods: {
    getName(type) {
      return this.$t('ANNOUNCEMENT.FORMATION_TYPE.REGISTER_' + type.toUpperCase());
    },
    getDescription(type) {
      return this.$t('ANNOUNCEMENT.FORMATION_TYPE.REGISTER_' + type.toUpperCase() + '_DESC');
    },
    go() {
      this.closeModal();
      this.$emit('go', this.selected.type);
    },
    closeModal() {
      const closeBtn = document.getElementById('announcement-type-selection_close');
      if (closeBtn) closeBtn.click();
    }
  },
}
</script>

<template>
  <div class="AnnouncementTypeSelection">
    <BaseModal
        identifier="announcement-type-selection"
        :title="$t('ANNOUNCEMENT.FORMATION_TYPE.REGISTER_MODAL_TITLE')"
        padding="2rem"
        size="modal-lg"
    >
      <div class="w-100 AnnouncementTypeSelection--selection">
        <div class="AnnouncementTypeSelection--selection--type" :class="t.type === selected.type ? 'selected' : ''" v-for="t in types" :key="t.type" @click="selected = t">
          <div class="text-center">
            <img class="w-75 mx-auto my-2" :src="`/assets/imgs/${t.type}_announcement.svg`" alt="">
          </div>
          <h4 class="text-primary mt-3 text-center">{{ getName(t.type) }}</h4>
          <p class="text-center">{{ getDescription(t.type) }}</p>
        </div>
      </div>
      <div class="w-100 d-flex flex-row flex-nowrap justify-content-end gap-1 pt-3">
        <button type="button" class="btn btn-danger" @click="closeModal()">{{ $t('CANCEL') }}</button>
        <button type="button" class="btn btn-primary" @click="go()">{{ $t('CONFIRM') }}</button>
      </div>
    </BaseModal>
  </div>
</template>

<style scoped lang="scss">
.AnnouncementTypeSelection {
  :deep(.modal-body) {
    background-color: #f4f5f6;
  }
  &--selection {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;

    &--type {
      width: 100%;
      filter: grayscale(100%);
      display: flex;
      flex-flow: row wrap;
      align-items: flex-start;
      justify-content: center;
      background-color: #ffffff;
      padding: 1rem;

      & > * { width: 100%;}

      &.selected {
        filter: unset;
      }

      img {
        width: 100%;
        height: 260px !important;
      }
    }
  }
}
</style>
