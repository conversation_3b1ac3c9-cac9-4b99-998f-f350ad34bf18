<template>
  <div class="base-input-file-imagen" :class="[`size--${size}`]">
    <label> {{ title }}</label>
    <div
      :style="{ backgroundImage: 'url(' + currentImage + ')' }"
      :class="
        image == null && urlImage == null
          ? 'preview-image preview-image-default'
          : 'preview-image'
      "
      @click="$refs.inputFile.click()"
    ></div>

    <div class="actions">
      <input
        type="file"
        @change="loadImage($event)"
        :accept="accept"
        ref="inputFile"
      />
      <a class="btn-sm btn btn-primary" @click="$refs.inputFile.click()">
        <i class="fas fa-upload"></i>
        {{ translationsVue.games_text_common_select_image }}
      </a>

      <a class="btn-sm btn btn-danger" @click="removeImage()" v-if="trash">
        <i class="fas fa-trash-alt"></i>
      </a>
    </div>
  </div>
</template>

<script>
const sizes = ["s", "m", "l", "xl"];

export default {
  name: "BaseInputFileImagen",
  props: {
    urlImage: {
      type: String,
      default: null,
    },

    size: {
      type: String,
      default: sizes[0],
      validator: (size) => sizes.includes(size),
    },

    title: {
      type: String,
      default: "",
    },

    deleteImage: {
      type: Boolean,
      default: false,
    },

    accept: {
      type: String,
      default: "image/png",
    },

    trash: {
      type: Boolean,
      default: true,
    },
  },

  watch: {
    deleteImage() {
      this.removeImage();
    },
  },

  data() {
    return {
      image: null,
      preview: "/assets/common/add_image_file.svg",
      translationsVue,
    };
  },

  computed: {
    currentImage() {
      if (this.image) {
        return this.preview;
      }

      return this.urlImage ?? this.preview;
    },
  },

  methods: {
    loadImage(event) {
      const input = event.target;

      if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = (e) => {
          this.preview = e.target.result;
        };

        this.image = input.files[0];

        reader.readAsDataURL(input.files[0]);

        this.$emit("file-selected", this.image);
      }
    },

    removeImage() {    
      this.image = null;
      this.preview = "/assets/common/add_image_file.svg";

      this.$refs.inputFile.value = null;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.base-input-file-imagen {
  .preview-image {
    width: 100%;
    height: 100%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    cursor: pointer;
  }

  .preview-image-default {
    background-size: 30%;
  }

  input[type="file"] {
    display: none;
  }

  .actions {
    margin-top: 0.5rem;
  }

  &.size {
    &--s {
      width: 280px;
      height: 220px;
    }

    &--m {
      width: 250px;
      height: 250px;
    }

    &--l {
      width: 300px;
      height: 300px;
    }

    &--xl {
      width: 400px;
      height: 400px;
    }
  }
}
</style>