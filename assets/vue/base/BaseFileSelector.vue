<template>
  <div class="BaseFileSelector"
  :class="[`size--${size}`]"
  >
    <label>{{ $t(title) }}</label>
    <div class="BaseFileSelector__preview" @click="openBaseFileSelector()" :style="{
            'background-image': `url(${ image ?? defaultImage })`
          }" :class="image ? 'has-image' : ''"></div>
    <div class="BaseFileSelector__selector">
      <input :name="name" :id="name" type="file" :accept="accept" @change="onFileChange($event.target)">
      <button @click="openBaseFileSelector()" type="button" id="btn-select-image" class="btn btn-secondary BaseFileSelector__selector--select-file"><i class="fa fa-upload"></i> {{ $t(btnSelectorValue) }}</button>
      <button type="button" class="btn btn-danger BaseFileSelector__selector--reset" @click="resetBaseFileSelector()"><i class="fa fa-trash"></i></button>
    </div>
  </div>
</template>

<script>
import $ from 'jquery';
const sizes = ['s', 'm', 'l', 'xl'];
export default {
  name: "BaseFileSelector",
  $,
  props: {
    name: {
      type: String,
      default: 'file'
    },
    accept: {
      type: String,
      default: 'image/png, image/jpeg, image/jpg'
    },
    image: {
      type: String,
      default: null
    },
    defaultImage: {
      type: String,
      default: '/assets/common/add_image_file.svg'
    },
    title: {
      type: String,
      default: 'THUMBNAIL'
    },
    btnSelectorValue: {
      type: String,
      default: 'SELECT_IMAGE'
    },

    size: {
      type: String,
      default: sizes[3],
      validator: (size) => sizes.includes(size),
    },
  },
  methods: {
    onFileChange(input) {
      if (this.accept.includes('image') && input.files && input.files[0]) {
        let reader = new FileReader();
        reader.onload = function (e) {
          $('.BaseFileSelector__preview').css('background-image', 'url(' + e.target.result + ')').addClass('has-image')
        }
        reader.readAsDataURL(input.files[0]);
      }
    },
    openBaseFileSelector() {
      $('#' + this.name).click();
    },
    resetBaseFileSelector() {
      $('#' +  this.name).val('');
      if (this.accept.includes('image')) {
        $('.BaseFileSelector__preview').css('background-image', 'url(' + this.defaultImage + ')').removeClass('has-image')
      }
    }
  },
}
</script>

 <style scoped lang="scss"> 
.BaseFileSelector {
    display: flex;
    flex-flow: column;

  &__preview {
    width: 100%;
    height: 100%;
    border: 1px solid $base-border-color;
    cursor: pointer;
    background-size: 50%;
    background-repeat: no-repeat;
    background-position: center center;

    &.has-image {
      background-size: cover;
    }
  }

  &__selector {
    margin-top: 1rem;
    display: flex;
    input {
      display: none;
    }
    &--select-file {
      flex-grow: 1;
      margin-right: 1rem;
    }
    &--reset {

    }
  }

  &.size {
    &--s {    
      width: 100px;
      height: 100px;     
    }

    &--m {
      width: 150px;
      height: 150px;     
    }

    &--l {
      width: 300px;
      height: 300px; 
    }

    &--xl {
      width: 400px;
    height: 400px; 
    }

  
  }
}
</style>
