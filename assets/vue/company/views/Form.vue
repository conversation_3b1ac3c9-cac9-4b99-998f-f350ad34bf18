<template>
  <div class="d-flex w-100 align-items-center justify-content-center" v-if="loading">
    <spinner/>
  </div>
  <div class="FormView" v-else>
    <div class="form-group col-12">
      <label>{{ $t('NAME') }}*</label>
      <input type="text" class="form-control" v-model="userCompany.name">
    </div>

    <div class="form-group col-12">
      <label>{{ $t('COMPANY.PROFYLE') }}</label>
      <input type="text" class="form-control" v-model="userCompany.profile">
    </div>

    <div class="form-group col-12">
      <label>{{ $t('COMPANY.CIF') }}</label>
      <input type="text" class="form-control" v-model="userCompany.cif">
    </div>

    <div class="form-group col-12">
      <label>{{ $t('COMPANY.CODE') }}</label>
      <input type="text" class="form-control" v-model="userCompany.code">
    </div>


    <div class="form-group col-12">
      <label>{{ $t('COMPANY.STATE') }}</label>
      <BaseSwitch v-model="userCompany.state" />
    </div>
    <div class="form-group col-12">
      <label>{{ $t('DESCRIPTION') }}</label>      
      <textarea
          type="text"
          class="form-control"
          name="description"
          v-model="userCompany.description"
          rows="5"
        />
    </div>

  </div>
  
</template>

<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../base/BaseSwitch.vue";
import Spinner from "../../admin/components/base/Spinner.vue";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm, Spinner},
  data() {
    return {
      locale: 'es',
      userCompany: {
        id: -1,
        name: '',
        description: '',
        state: true,
        cif: '',
        profile: '',  
        code: '',  
      },
    };
  },
  computed: {
    ...get("companyModule", ["loading", "getCompanies"]),

    companies() {
      return this.getCompanies();
    },
  },
  async created() {
    let userCompany = {
        id: -1,
        name: '',
        description: '',
        state: true,
        cif: '',
        profile: '',  
        code: '',  
      };
      let isUpdate = this.$route.name === 'CompanyUpdate';

      await this.$store.dispatch('contentTitleModule/addRoute', {
        routeName: this.$route.name,
        params: {
          linkName: isUpdate ? this.$t('COMPANY.UPDATE_COMPANY') : this.$t('COMPANY.CREATE_COMPANY'),
          params: {}
        }
      });

      await this.$store.dispatch('contentTitleModule/setActions', {
      route: this.$route.name,
      actions: [
        {
          name: this.$t('SAVE'),
          event: 'onSubmit',
          class: 'btn btn-primary'
        }
      ]
    });

    if (isUpdate) {
      userCompany = this.companies.find(c => c.id === this.$route.params.id);

      if (userCompany === undefined) {
        this.returnToList();
        return;
      }
    }

    this.userCompany = userCompany;
  },
  mounted() {
    this.$eventBus.$on('onSubmit', () => {
      this.submit();
    })
  },
  beforeDestroy() {
    this.$eventBus.$off('onSubmit');
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'Home', params: this.$route.params});
    },
    async submit() {
      if(this.userCompany.name===""){
        this.$toast.error(this.$t('COMPANY.ERROR1.DESCRIPTION') + '');
        return;
      }else{
        const update = this.$route.name === 'CompanyUpdate';
        const endpoint = update ? '/admin/company/update' : '/admin/company/create';

        await this.$store.dispatch("companyModule/save", {
          endpoint: endpoint,
          requestData: this.userCompany,
        });

        this.returnToList();
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
