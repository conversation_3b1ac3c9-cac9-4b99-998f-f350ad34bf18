<script>
import Home from "../../base/Home.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../base/BaseSwitch.vue";
import Spinner from "../../base/BaseSpinner.vue";

export default {
  name: "HomeView",
  components: {Spinner, BaseSwitch, Home},
  data() {
    return {
      orderUpdated: false,
      dragItem: null,
      dropItem: null,
    };
  },
  computed: {
    loading: get("helpCategoryModule/loading"),
    help_categories: get("helpCategoryModule/help_categories"),
  },
  created() {
    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: this.$t("HELP_CATEGORY.LABEL.PLURAL"),
        params: {},
      },
    });
  },
  mounted() {
    this.$store.dispatch('helpCategoryModule/getCategories');
  },
  methods: {
    startDrag(evt, index) {
      this.dragItem = index;
      evt.dataTransfer.dropEffect = 'move'
      evt.dataTransfer.effectAllowed = 'move'
    },

    onDrop(index) {
      let data = this.orderNewSort( this.dragItem,  index+1);

      this.dropItem = this.help_categories.splice(this.dragItem, 1)[0];
      this.help_categories.splice(index,0,this.dropItem);
      this.dropItem=null;

      this.orderUpdated = true;
    },

    orderNewSort(antPosSort, newPosSort ){
      let newSortCategories = [];

      let elmentTras = {
        id: this.help_categories[antPosSort].id,
        newSort: newPosSort
      }
      newSortCategories.push(elmentTras);

      if(this.dragItem > newPosSort){
        this.help_categories.forEach((item) => {
          let element = {
            id:null,
            newSort:null
          }

          if(item.sort >= newPosSort && item.sort <= antPosSort){
            element.id = item.id;
            element.newSort = item.sort + 1;
            newSortCategories.push(element);
          }
        });
      }else{
        this.help_categories.forEach((item) => {
          let element = {
            id:null,
            newSort:null
          }
          if(item.sort > antPosSort+1 && item.sort <= newPosSort){
            element.id = item.id;
            element.newSort = item.sort - 1;
            newSortCategories.push(element);
          }
        });
      }

      return newSortCategories;
    },

    remove(id) {
      this.$alertify.confirmWithTitle(
          this.$t('DELETE'),
          this.$t('COMMON_AREAS.QUESTION_DELETE'),
          () => {

            this.$store.dispatch('helpCategoryModule/deleteCategory', id)
            .then(r => {
              const index = this.help_categories.findIndex((item) => item.id === id);
              this.help_categories.splice(index, 1);
              this.$toast.success(this.$t('DELETE_SUCCESS') + '');
            }).catch(e => {
              if(e && e.status === 422 && e.message) {
                this.$toast.error(e.message);
              } else {
                this.$toast.error(e);
              }
            })
          },
          () => {},
      )
    },

    setOrderUpdated() {
      console.log("Order updated");
      this.orderUpdated = true;
    },

    sendNewOrder() {
      this.$store.dispatch('helpCategoryModule/updateCategoriesOrder').then(r => {
        this.orderUpdated = false;
        this.$toast.success('SUCCESS');
      }).catch(e => {
        this.$toast.error("Failed");
      });
    }
  }
}
</script>

<template>
  <home
      title="HELP_CATEGORY.LABEL.PLURAL"
      description="HELP_CATEGORY.DESCRIPTION"
      src-thumbnail="/assets/imgs/course_category_home.svg">
    <template v-slot:content-actions>
      <button type="button" class="btn btn-primary" v-if="orderUpdated" @click="sendNewOrder"><i class="fa fa-refresh"></i> {{ $t('COURSE_CATEGORY.SORT.UPDATE') }}</button>
      <router-link :to="{name: 'CreateView', params: {id: -1}}" type="button" class="btn btn-primary">{{ $t('HELP_CATEGORY.CREATE') }}</router-link>
    </template>
    <template v-slot:content-main>
      <div v-if="loading" class="d-flex flex-row align-items-center justify-content-center">
        <spinner />
      </div>
      <div v-else>
          <table class="table table-condensed mt-5" >
            <thead>
              <tr>
                <th>{{ $t("COURSE.COURSE_SECTION.SORT") }}</th>
                <th> {{ $t("NAME") }}</th>
                <th class="CategoriesEdit">{{ $t('EDIT') }} </th>
              </tr>
            </thead>
            <tbody>
                <tr
                  v-for="(category, index) in help_categories"
                  :key="category.id"
                  draggable
                  @dragstart="startDrag($event, index)"
                  @drop="onDrop(index)"
                  @dragover.prevent
                  @dragenter.prevent
                >
                  <td><i class="fa fa-bars Categories"></i></td>
                  <td>{{ category.name }}</td>
                  <td>
                    <div class="d-flex  justify-content-end">
                      <router-link class="btn btn-primary" :to="{name: 'CreateView', params: {id: category.id}}"> <i class="fa fa-pen" ></i></router-link>
                      <button v-if="$isAdmin()" type="button" class="btn btn-danger ms-2" @click="remove(category.id)"><i class="fa fa-trash "></i></button>
                    </div>
                  </td>
                </tr>
            </tbody>
          </table>
      </div>
    </template>
  </home>
</template>

<style scoped lang="scss">
.Categories {
  color: var(--color-primary);
}
.CategoriesEdit{
  text-align: end;
}
.CourseCategoryTable {
  display: grid;
  grid-template-columns: 1fr;
  row-gap: .25rem;
}
.CourseCategoryRow {
  width: 100%;
  display: grid;
  grid-template-columns: 100px 1fr 100px 100px;
  column-gap: .15rem;
  border-bottom: 1px solid var(--color-neutral-light);
  row-gap: .25rem;


  &.Header {
    color: var(--color-neutral-darkest);
    font-weight: bold;
    font-size: 16px !important;
  }

  & > div {
    width: 100%;
    //border: 1px solid blue;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    font-size: 15px;
  }
}
</style>
