<script>
import {get, sync} from "vuex-pathify";
import Multiselect from "vue-multiselect";
import Spinner from "../../../base/BaseSpinner.vue";

export default {
  name: "UserExtraFields",
  components: {Spinner, Multiselect},
  data() {
    return {
      selectedProperty: ''
    };
  },
  computed: {
    loading: get('settingsSamlModule/loadingUserConfiguration'),
    saving: get('settingsSamlModule/saving'),
    userExtraAttributes: sync("settingsSamlModule/userConfiguration@userExtraAttributes"),
    sourceUserExtraProperties: get('configModule/config@userExtraProperties'),
    availableProperties() {
      return this.sourceUserExtraProperties.filter(p => this.userExtraAttributes[p] == null);
    },
  },
  methods: {
    saveConfiguration() {
      const data = {
        code: 'saml.user.optional_attributes',
        value: {
          userExtraAttributes: this.userExtraAttributes
        }
      }

      this.$store.dispatch('settingsSamlModule/saveConfiguration', data).then(r => {
        const { error } = r;
        if (error) this.$toast.error('Error al guardar');
        else this.$toast.success('Cambios guardados')
      })
    },
    add()
    {
      this.userExtraAttributes[this.selectedProperty] = '';
      this.selectedProperty = '';
    },

    deleteProperty(property) {
      let attrs = this.userExtraAttributes;
      delete attrs[property + ''];
      this.userExtraAttributes = attrs;
    },
  }
}
</script>

<template>
  <div class="d-flex align-items-center justify-content-center" v-if="loading">
    <spinner />
  </div>
  <form @submit.prevent="saveConfiguration()" class="col-12 UserExtraFields" v-else>
    <h5 class="w-100 text-center">Atributos y propiedades: Datos adicionales del usuario</h5>
    <div class="col-12 d-flex flex-row flex-nowrap gap-1">
      <div class="col-md-4 ml-auto">
        <Multiselect
            v-model="selectedProperty"
            :options="availableProperties"
            :searchable="true"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
        ></Multiselect>
        <button class="btn btn-primary" type="button" @click="add()"><i class="fa fa-plus"></i> Agregar</button>
      </div>

    </div>
    <div class="w-100 d-flex flex-row flex-wrap">
      <div class="col-xs-12 col-md-3 d-flex flex-row flex-nowrap" v-for="(attribute, property) in userExtraAttributes" :key="`user-extra-property-${property}`">
        <div class="form-group w-100 mb-0 p-0">
          <label>{{ property }}</label>
          <input :name="property" required type="text"
                 class="form-control" placeholder="Nombre del atributo"
                 v-model.trim="userExtraAttributes[property]">
        </div>
        <div class="mt-auto">
          <button type="button" class="btn btn-danger" @click="deleteProperty(property)"><i class="fa fa-trash"></i></button>
        </div>
      </div>
    </div>
    <div class="col-12 d-flex flex-row justify-content-end">
      <button :disabled="saving" type="submit"
              class="btn btn-primary"><i class="fa fa-save"></i> Aplicar cambios</button>
    </div>
  </form>
</template>

<style scoped lang="scss">
form {
  border: 1px dashed var(--color-primary);
  padding: 1rem;
  border-radius: 5px;
  margin-bottom: 1rem;
}
</style>
