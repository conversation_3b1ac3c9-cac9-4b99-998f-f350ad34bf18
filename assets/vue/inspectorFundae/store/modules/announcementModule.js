import axios from 'axios';
import { make } from 'vuex-pathify';
import TaskQueueMixin from '../../../mixins/TaskQueueMixin';

const state = {
  loading: true,
  config: {
    subsidizerActive: false,
    clientsFields: [],
  },
  announcement: null,
  loadingCalledUsers: true,
  calledUsers: [],
  loadingAssistanceUsers: true,
  assistanceUsers: [],
  userSelected: {},
  groupSelected: {},
  tutorSelected: {},
  calledUsersPagination: {
    page: 1,
    totalItems: 0,
  },
  refresh: {
    refresh: false,
    action: null, // If null, refresh all elements in page, otherwise only the specified method
  },

  sessionSelected: {},
  uncompletedUserProfile: [],
  selectedUserProfile: null,
};

const getters = {
  ...make.getters(state),
  isLoading(state) {
    return state.loading;
  },
};

const mutations = {
  ...make.mutations(state),
  SET_RESET_VIEW_STATE(state) {
    state.announcement = null;
    state.refresh = {
      refresh: false,
      action: null, // If null, refresh all elements in page, otherwise only the specified method
    };
  },
};

/**
 * Share common state for froala editor
 */
export default {
  namespaced: true,
  state,
  getters,
  mutations,
  mixins: [
    TaskQueueMixin
  ],
  actions: {
    ...make.actions('config'),

    async loadAnnouncement({ commit }) {
      commit('SET_LOADING', true);
      try {
        const result = await axios.post('/inspector/announcement', { });
        const { data, error } = result.data;
        console.log(data)
        commit('SET_ANNOUNCEMENT', data);
      } finally {
        commit('SET_LOADING', false);
      }
    },

    async loadAnnouncementCalledUsers({ commit }) {
      commit('SET_LOADING_CALLED_USERS', true);
      try {
        const result = await axios.get(`/inspector/groups-announcement`);
        const { data } = result.data;
        commit('SET_CALLED_USERS', data);
      } finally {
        commit('SET_LOADING_CALLED_USERS', false);
      }
    },

    async loadAnnouncementOpinions({ commit }, id) {
      const result = await axios.get(`/inspector/announcement/${id}/opinions`);
      return result.data;
    },

    async downloadDiploma({ commit }, data) {
      try {
        const result = await axios.get(
          `/inspector/download-diploma-user/${data.idUser}/announcement/${data.idAnnouncement}`,
        );
        return `data:application/pdf;base64,${result.data?.data}`;
      } finally {
      }
    },

    async checkAnnouncementReportStatus({}, groupId) {
      const result = await axios.post('/inspector/check-group-report', {
        groupId
      });
      return result.data;
    },

    async requestAnnouncementGroupReport({getters}, request) {
      const { announcement } = getters;
      try {
        const result = await this.enqueueTask({
          url: `admin/generate-report/announcement/${announcement.id}`,
          data: request,
          messages: {
            success: `Reporte generado con éxito<br/>(Disponible en directorio de descargas)`,
            error: 'Error al generar el reporte'
          }
        });
        return result.data;
      } catch (error) {
        console.error('Error:', error);
        return null;
      }
    },

    async downloadAnnouncementGroupReport({}, groupId) {
      axios.post('/inspector/download-report', {groupId}, {
        responseType: 'blob'
      }).then(r => {
        const objectUrl = window.URL.createObjectURL(new Blob([r.data], {type: 'application/zip'}));
        let link = document.createElement('a');
        link.href = objectUrl;
        link.click();

        window.URL.revokeObjectURL(objectUrl);
      })
    },

    async downloadAnnouncementUserReport({ commit }, data ) {
      const result = await axios.get(
          `/admin/report-pdf-announcement-user/${data.userId}/announcement/${data.announcementId}`,
      );
      return `data:application/pdf;base64,${result.data?.data}`;
    },

    async loadAnnouncementAssistanceUsers({ commit }, id) {
      commit('SET_LOADING_ASSISTANCE_USERS', true);
      try {
        const result = await axios.get(`/inspector/groups-announcement/assistance/${id}`);
        const { data } = result.data;
        commit('SET_ASSISTANCE_USERS', data);
      } finally {
        commit('SET_LOADING_ASSISTANCE_USERS', false);
      }
    },

    setUserSelected({ commit }, data = {}) {
      commit('SET_USER_SELECTED', data);
    },

    setSessionSelected({ commit }, data = {}) {
      commit('SET_SESSION_SELECTED', data);
    },

    setGroupSelected({ commit }, data = {}) {
      commit('SET_GROUP_SELECTED', data);
    },

    setTutorSelected({ commit }, data = {}) {
      commit('SET_TUTOR_SELECTED', data);
    },
  },
};
