<template>
  <div>
    <BaseAlertWarning
      class="alerts-games"
      :message="messageImagePuzzle"
      v-if="!detailPuzzle.image"
    />

    <div class="UploadPuzzle">
      <div class="image">
        <cropper
          v-if="cutoutImage == null"
          class="upload-example-cropper"
          :src="image"
          ref="cropper"
          :default-size="{
            width: 840,
            height: 840,
          }"
          :stencil-props="{
            aspectRatio: 12 / 12,
          }"
          @change="onChange"
        ></cropper>

        <div v-else class="upload-example-cropper">
          <img
            class="imagePuzzle"
            :src="cutoutImage"
            v-if="detailPuzzle != 'No results'"
          />
        </div>

        <div class="actions">
          <div class="button-wrapper">
            <input
              type="file"
              ref="file"
              @change="loadImage($event)"
              accept="image/*"
            />
            <button class="btn btn-primary btn-sm" @click="$refs.file.click()">
              <i class="fas fa-upload"></i>
              {{ translationsVue.games_text_common_select_image }}
            </button>
          </div>

          <div v-if="image != null">
            <button class="btn btn-primary btn-sm" @click="saveCropped()">
              <i class="fas fa-check"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="description">
        <h5>{{ translationsVue.puzzle_configureFields_recomendation }}</h5>

        <p
          v-html="
            translationsVue.puzzle_configureFields_recomendation_dimentions
          "
        ></p>
        <p
          v-html="
            translationsVue.puzzle_configureFields_recomendation_description
          "
        ></p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "upload-puzzle",
  props: ["id"],
  data() {
    return {
      image: null,
      coordinates: {
        width: 0,
        height: 0,
        left: 0,
        top: 0,
      },
      detailPuzzle: undefined,
      cutoutImage: null,
      cropImage: null,
      error: null,
      translationsVue,
      messageImagePuzzle,
    };
  },

  async mounted() {
    await this.fetchImagePuzzle();
  },

  methods: {
    reset() {
      this.image = null;
    },

    loadImage(event) {
      var input = event.target;

      if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = (e) => {
          this.image = e.target.result;
        };
        this.cutoutImage = null;
        reader.readAsDataURL(input.files[0]);
      }
    },

    b64toBlob(b64Data, contentType, sliceSize) {
      contentType = contentType || "";
      sliceSize = sliceSize || 512;

      var byteCharacters = atob(b64Data);
      var byteArrays = [];

      for (
        var offset = 0;
        offset < byteCharacters.length;
        offset += sliceSize
      ) {
        var slice = byteCharacters.slice(offset, offset + sliceSize);

        var byteNumbers = new Array(slice.length);
        for (var i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }

        var byteArray = new Uint8Array(byteNumbers);

        byteArrays.push(byteArray);
      }

      var blob = new Blob(byteArrays, { type: contentType });
      return blob;
    },

    async saveCropped() {
      const { coordinates, canvas } = await this.$refs.cropper.getResult();
      this.coordinates = coordinates;
      this.image = canvas.toDataURL();

      console.log(this.image);

      await this.uploadImagePuzzle("first");
      await this.uploadImagePuzzle("second");
    },

    async uploadImagePuzzle(state) {
      try {
        if (this.image != null) {
          const newCanva = document.createElement("canvas");
          const context = newCanva.getContext("2d");

          const cropImage = new Image();
          cropImage.src = this.image;

          newCanva.width = 840;
          newCanva.height = 840;

          context.fillRect(0, 0, newCanva.width, newCanva.height);
          context.drawImage(cropImage, 0, 0, 840, 840);

          cropImage.onload = () => {
            context.drawImage(cropImage, 0, 0);
          };

          const resultImage = newCanva.toDataURL();
          this.cropImage = resultImage;

          const block = resultImage.split(";");
          const contentType = block[0].split(":")[1];
          const realData = block[1].split(",")[1];
          const blob = this.b64toBlob(realData, contentType);

          if (
            this.detailPuzzle == "No results" &&
            this.image != null &&
            state == "second"
          ) {
            const data = {
              file: blob,
              chapter: this.id,
            };

            await this.saveImagePuzzle(data, "new");
          } else if (
            this.detailPuzzle != "No results" &&
            this.image != null &&
            state == "second"
          ) {
            const dataUpdate = {
              id: this.detailPuzzle.id,
              chapter: this.id,
              file: blob,
              nameImage: this.detailPuzzle.image,
            };

            await this.saveImagePuzzle(dataUpdate, "update");
          }
        }
      } catch (error) {
        this.error = "error";

        this.$toast.open({
          message: "Ocurrio un error al guardar, Intente nuevamente",
          type: "error",
          duration: 5000,
          position: "top-right",
        });
      }
    },

    async saveImagePuzzle(payload, action) {
      try {
        if (action == "new") {
          let result = await this.$store.dispatch(
            "puzzleModule/uploadImagePuzzle",
            payload
          );
          this.alertSuccess();
        } else if (action == "update") {
          await this.$store.dispatch("puzzleModule/updatePuzzle", payload);

          this.alertSuccess();
        }
      } catch (error) {
        this.error = "error";
        this.$toast.open({
          message: "Ocurrio un error al guardar, Intente nuevamente",
          type: "info",
          duration: 5000,
          position: "top-right",
        });
      } finally {
        await this.fetchImagePuzzle();
        this.image = null;
      }
    },

    alertSuccess() {
      this.$toast.open({
        message: this.translationsVue.puzzle_configureFields_save_image,
        type: "success",
        duration: 5000,
        position: "top-right",
      });
    },

    onChange({ coordinates, canvas }) {
      this.coordinates = coordinates;
      // this.image = canvas.toDataURL();
    },

    async fetchImagePuzzle() {
      const detailPuzzle = await this.$store.dispatch(
        "puzzleModule/detailPuzzle",
        this.id
      );

      this.detailPuzzle = detailPuzzle.data.data;

      this.cutoutImage = `/uploads/puzzle/images/${this.detailPuzzle?.image}`;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.UploadPuzzle {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  //align-items: center;
  justify-items: center;
  gap: 3rem;
  background: #fff;
  padding: 1rem;
  margin-bottom: 2rem;

  // margin-bottom: 1rem;
  //height: auto;
  /* width: 80%; */
  /* height: 100%; */
  /* margin: auto;  */

  .image {
    width: min(400px, 400px);
    height: min(400px, 400px);

    .actions {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 1rem;

      input[type="file"] {
        display: none;
      }
    }
  }
  .description {
    flex: 1;
  }
}

.cropper {
  .cropper {
    height: 280px;
    width: 280px;
    background: #ddd;
  }
}

.imagePuzzle {
  width: 100%;
  justify-content: center;
  object-fit: cover;
  /*  padding: 1rem; */
}

.upload-example-cropper {
  border: solid 1px #eee;
  height: 280px;
  width: 100%;
  //margin-top: 1rem;
  display: flex;
  justify-content: center;
  background-image: url("../assets/images/add_image_file.svg");
  background-size: 50%;
  background-repeat: no-repeat;
  background-position: center;
  box-shadow: $shadow-elevation-1;
  cursor: pointer;
}
.button {
  color: white;
  font-size: 16px;
  padding: 10px 20px;
  background: var(--color-primary);
  cursor: pointer;
  width: auto;
  text-align: center;
  &:hover {
    background: white;
    border: solid 1px var(--color-primary);
    color: var(--color-primary);
  }
}

.button input {
  display: none;
}

.btnSave {
  width: 100%;
  margin-top: 1rem;
  height: 3rem;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 0.3rem;
  margin-bottom: 3rem;
  &:hover {
    background: white;
    border: solid 1px var(--color-primary);
    color: var(--color-primary);
  }
}
</style>
,
