<template>
  <div class="row h-100 w-100 m-0">
    <div v-if="this.isLoading()" class="col-12 text-center">
      <loader :isLoaded="this.isLoading()"></loader>
    </div>

    <div
      v-if="this.isLoaded"
      class="col-3 h-100 overflow-hidden p-0 position-fixed"
    >
      <div id="content-menu" class="h-100 overflow-hidden">
        <div class="card h-100 overflow-hidden">
          <div class="card-header">
            {{ chapter.title }}
          </div>

          <ul class="list-group list-group-flush h-100 overflow-auto">
            <li
              class="list-group-item d-flex align-items-center"
              v-for="content in chapter.contents"
              :key="content.id"
            >
              <div class="status">
                <i class="fa fa-check" v-if="content.position <= opened"></i>
              </div>

              <a
                class="nav-link"
                href="#"
                v-bind:class="{
                  'font-weight-bold': content.position === current,
                  disabled: content.position > opened,
                }"
                v-scroll-to="'#content-' + content.position"
                v-bind:ref="'toContent-' + content.position"
                @click="navigation(content.position)"
              >
                {{ content.title }}
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div
      v-if="this.isLoaded"
      class="col-9 h-100 shadow-sm overflow-auto p-2 ml-auto"
    >
      <div
        class="content"
        v-for="content in chapter.contents"
        :key="content.id"
      >
        <transition name="slide-fade">
          <div
            class="card p-2"
            v-bind:id="'content-' + content.position"
            v-if="content.position <= opened"
            v-view="inView"
            v-bind:position="content.position"
          >
            <div class="card-header p-2">
              {{ content.title }}
            </div>

            <div class="card-body p-2">
              <froalaView v-model="content.content"></froalaView>
            </div>
       
            <div
              class="card-footer text-center"
              v-if="content.position < last && content.position == opened"
            >
              <button
                class="btn btn-primary"
                @click="openContent(content.position)"
              >
                {{ textNext }} <i class="fa fa-chevron-down"></i>
              </button>
            </div>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>
<script>
import { get } from "vuex-pathify";
import Loader from "../components/Loader";

export default {
  name: "content-player",
  components: {
    Loader,
  },

  props: ["id", "text-next"],

  data() {
    return {
      chapter: undefined,
      title: undefined,
      current: undefined,
      percent: undefined,
      last: undefined,
      opened: undefined,
      click: undefined,
    };
  },

  async created() {
    await this.$store
      .dispatch("chapterModule/getState", this.id)
      .then(async (data) => {
        this.chapter = await this.$store.dispatch(
          "contentModule/fetchChapter",
          this.id
        );
        console.log("Data", data);
        if (data !== null && data.length !== 0) {
          this.current = 1;
          this.opened = data.opened;
        } else {
          this.current = this.opened = 1;
        }
        this.last = this.chapter.contents.length;

        await this.$store.dispatch("timeModule/initStart", this.userChapter());

        if (this.last == 1) this.openContent(0);

        if(this.opened == this.last) {
          await this.$store.dispatch("chapterModule/updateState", {
            chapter: this.id,
            opened: this.opened,
            finished: true,
          });
        }
      });
  },

  computed: {
    ...get("contentModule", ["isLoading"]),
    ...get("chapterModule", ["userChapter"]),

    isLoaded() {
      return !this.isLoading() && this.chapter;
    },

    next() {
      return this.opened + 1;
    },
  },

  methods: {
    async navigation(position) {
      this.click = position;
      this.current = position;
      await this.$store.dispatch("timeModule/fetchTime", this.userChapter());
      setTimeout(this.removeNavigation, 500);
    },

    removeNavigation() {
      this.click = undefined;
    },

    async openContent(prev) {
      this.opened = prev + 1;

      let chapterStatus;

      if (this.opened === this.chapter.contents.length) {
        chapterStatus = {
          chapter: this.id,
          opened: this.opened,
          finished: true,
        };
      } else {
        chapterStatus = {
          chapter: this.id,
          opened: this.opened,
          finished: false,
        };
      }
      await this.$store.dispatch("chapterModule/updateState", chapterStatus);

      setTimeout(this.scrollToOpened, 500);
    },

    scrollToOpened() {
      this.$refs["toContent-" + this.opened][0].click();
    },

    inView(e) {
      // console.log(position);
      // console.log(e.type) // 'enter', 'exit', 'progress'
      // console.log(e.percentInView) // 0..1 how much element overlap the viewport
      // console.log(e.percentTop) // 0..1 position of element at viewport 0 - above , 1 - below
      // console.log(e.percentCenter) // 0..1 position the center of element at viewport 0 - center at viewport top, 1 - center at viewport bottom
      // console.log(e.scrollPercent) // 0..1 current scroll position of page
      // console.log(e.scrollValue) // 0..1 last scroll value (change of page scroll offset)
      // console.log(e.target.element) // element.getBoundingClientRect() result
      // console.log(e.target.element.getAttribute('position') + ' - ' + e.percentInView) // element.getBoundingClientRect() result
      if (e.type === "progress" && !this.click) {
        const position = parseInt(e.target.element.getAttribute("position"));
        if (position === this.current) this.percent = e.percentInView;
        else {
          if (this.percent <= e.percentInView) {
            this.current = position;
            this.percent = e.percentInView;
          }
        }
      }
    },
  },
};
</script>
<style scoped lang="scss">
@import "../assets/config/_transitions.scss";
@import "../../../../assets/css/config/_colors.scss";

#content-menu {
  // position: fixed;

  .card {
    border: 0;

    .card-header {
      text-transform: uppercase;
      font-weight: bold;
      background-color: transparent;
      border: 0;
      color: var(--color-secondary-darker);
      padding: 0.5rem;
    }

    ul li {
      border: 0;
      padding: 0.25rem 0.5rem;

      .status {
        width: 1em;
        padding: 0.5rem 0;

        i,
        a {
          color: var(--color-primary-darkest);
        }
      }
    }
  }
}

.content {
  .card {
    border: 0;

    .card-header {
      background-color: transparent;
      border: 0;
      font-size: 2em;
      font-weight: bold;
      color: var(--color-primary-darkest);
    }

    .card-body {
    }

    .card-footer {
      border: 0;
      background-color: transparent;

      .btn-primary {
        background-color: var(--color-primary-darkest);
        border-color: var(--color-primary-darkest);
      }

      .btn-primary:hover {
        background-color: var(--color-secondary-darkest);
      }
    }
  }
}
</style>