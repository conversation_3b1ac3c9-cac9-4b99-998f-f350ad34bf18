<template>
  <div class="parejas">
    <div class="col align-self-end text-right mb-2">
      <button
        type="button"
        class="btn btn-primary"
        data-bs-toggle="modal"
        :data-bs-target="`#modal-chapter-${typeChapter}`"
        v-if="parejas && parejas.length < 18"
      >
        {{ translationsVue.add }}
      </button>
    </div>

    <!-- Modal -->
    <div
      class="modal fade"
      :id="`modal-chapter-${typeChapter}`"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="staticBackdropLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="staticBackdropLabel">
              {{ translationsVue.pairs_configureFields_create_game }}
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              :id="`close-modal-chapter-${typeChapter}`"
            ></button>
          </div>
          <div class="modal-body">
            <NewMemoryMatch />
          </div>
        </div>
      </div>
    </div>

    <BaseAlertWarning
      class="alerts-games"
      :message="messageMinimumQuestion"
      v-if="parejas && parejas.length < minimalQuestion"
    />

    <div class="time">
      <label for="title" class="form-label"
        >{{ translationsVue.games_text_common_time }}
      </label>

      <BaseInputTime
        v-model="time"
        :options="['minutes', 'seconds']"
        :maxMinutes="31"
        :time="time"
        @time-update="timeUpdate"
      />
    </div>

    <div class="mt-4">
      <table class="table" v-if="parejas && parejas.length > 0">
        <thead>
          <tr>
            <th scope="col">
              {{ translationsVue.common_areas_image }}
            </th>
            <th scope="col">
              {{ translationsVue.content_configureFields_title }}
            </th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="pareja in parejas" :key="pareja.id">
            <td>
              <a
                data-bs-toggle="modal"
                :data-bs-target="`#parejas${pareja.id}`"
              >
                <img
                  :src="
                    pareja.image
                      ? `uploads/games/parejas/${pareja.image}`
                      : 'assets/chapters/default-image.svg'
                  "
                  alt=""
                  @error="
                    $event.target.src = 'assets/chapters/default-image.svg'
                  "
                />
              </a>

              <BaseViewImagen
                :identifier="`parejas${pareja.id}`"
                :image="`uploads/games/parejas/${pareja.image}`"
              />
            </td>
            <td v-if="pareja.text" width="50%">{{ pareja.text }}</td>
            <td v-else width="50%">--</td>

            <td class="text-right">
              <button
                type="button"
                class="btn-sm btn btn-danger"
                data-bs-toggle="modal"
                :data-bs-target="`#deleteModal${pareja.id}`"
              >
                <i class="fas fa-trash-alt"></i>
              </button>
              <button
                type="button"
                class="btn btn-primary btn-sm"
                data-bs-toggle="modal"
                :data-bs-target="`#modal-edit-question${pareja.id}`"
              >
                <i class="fas fa-edit"></i>
              </button>
            </td>

            <!-- Modal -->
            <div
              class="modal fade"
              :id="`modal-edit-question${pareja.id}`"
              data-bs-backdrop="static"
              data-bs-keyboard="false"
              tabindex="-1"
              aria-labelledby="staticBackdropLabel"
              aria-hidden="true"
            >
              <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="staticBackdropLabel">
                      {{ translationsVue.pairs_configureFields_create_game }}
                    </h5>
                    <button
                      type="button"
                      class="btn-close"
                      data-bs-dismiss="modal"
                      aria-label="Close"
                    ></button>
                  </div>
                  <div class="modal-body">
                    <EditMemoryMatch :pareja="pareja" />
                  </div>
                </div>
              </div>
            </div>

            <!-- Modal delete-->
            <BaseModalDelete
              :identifier="`deleteModal${pareja.id}`"
              :title="translationsVue.quiz_configureFields_question_delete"
              @delete-element="deletePareja(pareja.id)"
            />
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";

import { formatDateMixin } from "../../mixins/formatDateMixin";

import NewMemoryMatch from "../components/parejas/NewMemoryMatch";
import EditMemoryMatch from "../components/parejas/EditMemoryMatch";

const STORE = "rouletteWordModule";

export default {
  components: {
    NewMemoryMatch,
    EditMemoryMatch,
  },

  props: {
    blocks: {
      type: Array,
      default: () => [],
    },
    parejaimagenes: {
      type: Array,
      default: () => [],
    },
    chapterId: {
      type: Number,
      default: 0,
    },
  },

  mixins: [formatDateMixin],

  async created() {
    await this.fetchParejas();
    await this.fetchTimeChapter();
    this.convertTime();
  },

  data() {
    return {
      typeChapter,
      translationsVue,
      time: "00:00:30",
      messageMinimumQuestion,
      minimalQuestion,
    };
  },

  computed: {
    ...get("parejasModule", ["getParejas"]),
    ...get("rouletteWordModule", ["getTimeChapter"]),

    parejas() {
      return this.getParejas();
    },

    timeFromChapter() {
      return this.getTimeChapter();
    },
  },

  methods: {
    async deletePareja(id) {
      const formData = new FormData();
      formData.append("id", id);
      formData.append("idChapter", this.chapterId);

      await this.$store.dispatch("parejasModule/deleteParejas", formData);
      await this.fetchParejas();
    },

    getTime(time) {
      const hours = Math.floor(time / 3600);
      const minutes = Math.floor((time - hours * 3600) / 60);
      const seconds = Math.floor(time - hours * 3600 - minutes * 60);

      const minutesFormat = minutes < 10 ? "0" + minutes : minutes;
      const secondsFormat = seconds < 10 ? "0" + seconds : seconds;
      const hoursFormat = hours < 10 ? "0" + hours : hours;

      return hoursFormat + ":" + minutesFormat + ":" + secondsFormat;
    },

    async fetchParejas() {
      await this.$store.dispatch("parejasModule/fetchParejas", this.chapterId);
    },

    timeUpdate(time) {
      this.time = time;
      this.saveTimeChapter();
    },

    async fetchTimeChapter() {
      console.log("fetchTimeChapter");
      return await this.$store.dispatch(
        `${STORE}/fetchTimeChapter`,
        this.chapterId
      );
    },

    convertTime() {
      this.time =
        this.convertSecondToHoursMinutesAndSeconds(this.timeFromChapter) ??
        "00:00:30";
    },

    async saveTimeChapter() {
      const formData = new FormData();
      const secondsTime = this.convertDateHoursMinutesAndSeconds(this.time);
      formData.append("idChapter", this.chapterId);
      formData.append("time", secondsTime);
      await this.$store.dispatch(`${STORE}/saveTimeChapter`, formData);
    },
  },
};
</script>
 <style scoped lang="scss"> 
.parejas {
  background: #fff;
  padding-top: 2rem;

  .time {
    flex-wrap: wrap;
    display: flex;
    justify-content: flex-end;
    flex-direction: column;
    margin-left: 50%;
    margin-right: 1rem;
    padding-bottom: 1rem;
  }
}
.cursor {
  display: inline;
  margin-right: 0.5rem;
  cursor: pointer;
}

.campoInput {
  margin: 0.5rem;
  border: 1px solid #5ae8e8;
  border-radius: 25px;
  padding: 0.5rem;
}

.question {
  display: flex;
  flex-direction: column;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
}

img {
  width: 50px;
  cursor: zoom-in;
}
</style>
