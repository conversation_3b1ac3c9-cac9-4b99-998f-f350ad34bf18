<template>
  <div class="Videoquiz">
    <div class="col align-self-end text-right mb-2" v-if="!videoQuiz">
      <button
        type="button"
        class="btn btn-primary btn-sm"
        data-bs-toggle="modal"
        :data-bs-target="`#modal-chapter-${typeChapter}`"
      >
        {{ translationsVue.chapter_type_add_21 }}
      </button>
    </div>

    <div class="col align-self-end text-right mb-2" v-if="videoQuiz">
      <button
        type="button"
        class="btn btn-primary btn-sm"
        data-bs-toggle="modal"
        :data-bs-target="`#modal-edit-${typeChapter}`"
      >
        {{ translationsVue.games_edit_video_quiz }}
      </button>

      <button
        type="button"
        class="btn btn-danger btn-sm"
        data-bs-toggle="modal"
        :data-bs-target="`#deleteModal${typeChapter}`"
      >
        {{ translationsVue.games_delete_video_quiz }}
      </button>
    </div>

    <!-- Modal -->
    <div
      class="modal fade"
      :id="`modal-chapter-${typeChapter}`"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="staticBackdropLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-fullscreen">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="staticBackdropLabel">
              {{ translationsVue.video_configureFields_title }}
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              :id="`close-modal-chapter-${typeChapter}`"
            ></button>
          </div>
          <div class="modal-body">
            <NewVideoQuiz
              :url-chapter="urlChapter"
              :vimeo-upload-subdomain="vimeouploadsubdomain"
            />
          </div>
        </div>
      </div>
    </div>

    <div
      class="modal fade"
      :id="`modal-edit-${typeChapter}`"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="staticBackdropLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-fullscreen">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="staticBackdropLabel">
              {{ translationsVue.video_configureFields_title }}
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              :id="`close-modal-chapter-${typeChapter}`"
            ></button>
          </div>
          <div class="modal-body">
            <EditVideoQuiz
              v-if="videoQuiz"
              :url-chapter="urlChapter"
              :video-quiz="videoQuiz"
              :vimeo-upload-subdomain="vimeouploadsubdomain"
            />
          </div>
        </div>
      </div>
    </div>

    <BaseModalDelete
      :identifier="`deleteModal${typeChapter}`"
      :title="translationsVue.quiz_configureFields_question_delete"
      @delete-element="deleteVideo(videoQuiz.id)"
    />
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Loader from "../components/Loader";
import NewVideoQuiz from "../components/videoquiz/NewVideoQuiz";
import EditVideoQuiz from "../components/videoquiz/EditVideoQuiz";

export default {
  props: {
    blocks: {
      type: Array,
      default: () => [],
    },
    chapterId: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      lines: this.blocks,
      currentLine: {},
      registroQuiz: {},
      called: true,
      verOptions: true,
      llamado: 0,
      typeChapter,
      translationsVue,
      vimeouploadsubdomain,
    };
  },

  components: {
    Loader,
    NewVideoQuiz,
    EditVideoQuiz,
  },

  computed: {
    ...get("callModule", ["isLoading"]),
    ...get("videoquizModule", ["getVideoQuiz", "getUrlChapter"]),

    showCalled() {
      return !this.isLoading() && this.called;
    },

    videoQuiz() {
      return this.getVideoQuiz();
    },

    urlChapter() {
      return this.getUrlChapter();
    },
  },

  async created() {
    await this.fetchVideo();
  },

  methods: {
    async deleteVideo() {
      const formData = new FormData();
      formData.append("id", this.videoQuiz.id);
      await this.$store.dispatch("videoquizModule/deleteVideoQuiz", formData);
      await this.fetchVideo();
    },

    async fetchVideo() {
      await this.$store.dispatch(
        "videoquizModule/fetchVideoQuiz",
        this.chapterId
      );
    },
  },
};
</script>

 <style scoped lang="scss"> 
.options {
  height: 100%;
  flex: 2;

  display: flex;
  flex-direction: column;
  justify-content: space-around;
  margin-top: 1rem;
}
.question {
  display: flex;
  flex-direction: column;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
}

.modal-fullscreen {
  width: 80vw;
  max-width: none;
  height: 100%;
  margin: auto;
}
</style>
