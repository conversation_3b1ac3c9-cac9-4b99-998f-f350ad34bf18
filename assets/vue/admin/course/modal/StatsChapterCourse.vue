<template>
  <div class="StatsChapterCourse">
    <BaseModal
      identifier="generalCourseStatsModal"
      size="modal-xl"
      padding="0"
      :title="chapterData.name">
      <div class="row mx-0 courseStatsDetails">
        <div v-if="!chapterData.questions.length">
          <spinner/>
        </div>
        <div class="col-lg-3 col-md-4 col-sm-12 bg-gray py-3" v-if="chapterData.questions.length">
          <h4 class="font-weight-bold mb-3">{{ courseData.name }}</h4>
          <img class="w-100 h-auto" :src="courseData.image" :alt="courseData.name">
          <p class="courseDesc my-1"><b class="text-capitalize" v-show="courseData.type">{{ courseData.type }}</b></p>
          <p class="courseDesc my-1" v-show="courseData.category">
            <b>{{ $t('LIBRARY.CATEGORY') }}:</b>
            {{ courseData.category }}
          </p>
          <p class="my-2">{{ $t('COURSE.COURSE_SECTION.STATS_GENERAL_TOTALS', [chapterData.totalUserAttempts]) }}
            <b>{{ chapterData.totalAttempts }}</b>
          </p>
        </div>
        <div class="col-lg-9 col-md-8 col-sm-12 bg-details py-3" style="margin-bottom: 10px;" v-if="chapterData.questions.length">
          <div class="d-flex align-items-center justify-content-between" v-if="itinerary">
            <button class="btn btn-sm btn-primary mr-3" @click="goBack" id="backButton">
              <i class="fa fa-caret-left"></i> {{ $t('SUBSCRIPTION.GO_BACK') }}
            </button>
          </div>
          <div class="d-flex justify-content-between align-items-center">
            <b>{{ chapterData.name }}</b>
            <div>
              <b>{{ chapterData.type }}</b>
              <img :src="iconDir" :alt="chapterData.name" style="height: 2rem; width: fit-content; filter: grayscale(1) brightness(0);">
            </div>
          </div>
          <div class="mx-0 my-2 bg-white table-responsive" style="border-radius: 7px">
            <table class="table table-condensed mb-0">
              <thead>
              <tr>
                <th class="text-center"><i class="fa fa-check-circle text-success"></i></th>
                <th class="text-center"><i class="fa fa-times-circle text-danger"></i></th>
                <th class="text-center"><i class="fa fa-percent text-dark"></i></th>
                <th></th>
              </tr>
              </thead>
              <tbody>
              <tr
                  v-for="(question, index) in chapterData.questions"
                  :key="'stats_questions_' + index"
                  :class="{
                  'bg-success-alt': maxValue !== minValue && question.successResultAvg === maxValue,
                  'bg-danger-alt': maxValue !== minValue && question.successResultAvg === minValue
                }"
              >
                <td class="text-success text-center">{{ question.totalSuccess }}</td>
                <td class="text-danger text-center">{{ question.totalErrors }}</td>
                <td class="text-center"><b>{{ question.successResultAvg }}</b></td>
                <td>
                  <component
                      :is="chapterData.detailsType"
                      :index="chapterData.detailsType !== 'StatementType' ? index + 1 : 0"
                      :question="question"
                      :enumerable="chapterData.showAnswerEnum"
                      :total="chapterData.questions?.length" />
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script>
import DefaultType from './chapterDetails/defaultType.vue'
import StatementType from './chapterDetails/statementType.vue'
import CoupleType from './chapterDetails/coupleType.vue'
import { GeneralStatsModel } from '../models/GeneralStatsModel'
import BaseModal from '../../../base/BaseModal.vue'
import Spinner from "../../../base/BaseSpinner.vue";

export default {
  name: "StatsChapterCourse",
  components: {Spinner, BaseModal, CoupleType, StatementType, DefaultType },
  props: {
    chapterData: { type: GeneralStatsModel, default: () => new GeneralStatsModel({}) },
    courseData: { type: Object, default: () => ({})},
    itinerary: { type: Boolean, default: false }
  },
  computed: {
    iconDir() { return this.chapterData?.icon ? `${ assetsDir || '' }${this.chapterData.icon}` : '' },
    minValue() { return Math.min(...this.chapterData.questions.map((question) => question.successResultAvg)) },
    maxValue() { return Math.max(...this.chapterData.questions.map((question) => question.successResultAvg)) }
  },
  methods: {
    goBack() {
      const button = document.getElementById('backButton');
      button.setAttribute('data-bs-toggle', 'modal');
      button.setAttribute('data-bs-target', `#CourseStatsDetailsModal`);
      button.click();
    }
  }
}
</script>
