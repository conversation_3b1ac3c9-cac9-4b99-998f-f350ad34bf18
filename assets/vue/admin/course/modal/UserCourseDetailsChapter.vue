<template>
  <div class="UserCourseDetailsChapter">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <button class="btn btn-sm btn-primary mr-3" @click="goBack">
          <i class="fa fa-caret-left"></i> {{ $t('SUBSCRIPTION.GO_BACK') }}
        </button>
        <b>{{ chapterData.name }}</b>
      </div>
      <div>
        <b>{{ chapterData.type }}</b>
        <img :src="iconDir" :alt="chapterData.name" style="height: 2rem; width: fit-content; filter: grayscale(1) brightness(0);">
      </div>
    </div>
    <div class="row mx-0">
      <div class="col-sm-3 col-xs-12 pl-0 mt-3 scrollable" style="height: clamp(200px, 60svh, 400px);">
        <div class="row mx-0">
          <div
            class="col-sm-12 p-2 mb-3 cursor-pointer"
            v-for="(attempt, index) in chapterData.attempts"
            :key="'attempt_' + index"
            :class="{ 'bg-white-alt': index !== currentChapter, 'bg-white': currentChapter === index}"
            :style="{ order: index * -1, borderRadius: '7px' }"
            @click="setCurrent(index)"
          >
            <p class="font-weight-bold mb-1 text-center">
              <i class="fa fa-check-circle text-success" v-if="attempt.success"/>
              <i class="fa fa-times-circle text-danger" v-else/>
              {{ $t('ATTEMPT', [index + 1]) }}
            </p>
            <div class="dateContainer" v-show="chapterData.showAttemptsCardDetails">
              <p class="my-0">
                <span class="font-weight-bold">{{ attempt.startDateTimeArray[0] }}</span>
                {{ attempt.startDateTimeArray[1] }}</p>
              <p class="my-0">
                <span class="font-weight-bold">{{ attempt.endDateTimeArray[0] }}</span>
                {{ attempt.endDateTimeArray[1] }}</p>
            </div>
            <span class="totalTime text-center" v-show="chapterData.showAttemptsCardDetails">{{ attempt.time }}</span>
          </div>
        </div>
      </div>
      <div class="col-sm-9 col-xs-12 mt-3 scrollable" style="height: clamp(200px, 60svh, 400px);">
        <div class="row bg-white py-2 mb-2" style="border-radius: 7px">
          <div class="col-12"
               v-for="(question, index) in chapterSelected.questions"
               :key="'attempt_question_' + index">
            <component
              :is="chapterData.detailsType"
              :index="index + 1"
              :question="question"
              :show-response="true"
              :enumerable="chapterData.showAnswerEnum"
              :details="true"
              :total="chapterSelected.questions?.length" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DefaultType from './chapterDetails/defaultType.vue'
import StatementType from './chapterDetails/statementType.vue'
import CoupleType from './chapterDetails/coupleType.vue'
import { chapterDetailsModel } from '../models/UserStatsModel'

export default {
  name: "UserCourseDetailsChapter",
  components: { CoupleType, StatementType, DefaultType },
  props: {
    chapterData: { type: chapterDetailsModel, default: () => (new chapterDetailsModel()) },
  },
  data: () => ({
    currentChapter: 0,
  }),
  computed: {
    iconDir() { return this.chapterData?.type ? `${ assetsDir || '' }${this.chapterData.icon}` : '' },
    chapterSelected() { return this.chapterData.attempts[this.currentChapter] || { questions: [] } },
  },
  mounted() {
    this.setCurrent((this.chapterData?.attempts?.length || 1) - 1)
  },
  methods: {
    goBack() {
      this.$emit('return')
    },
    setCurrent(index = 0) {
      if (index !== this.currentChapter) this.currentChapter = index
    }
  }
}
</script>