import { secondsToTime } from './UserStatsModel'
import ChapterDetailsData from './ChapterDetailsData'

export class GeneralStatsModel extends ChapterDetailsData {
  constructor({
    id = 0,
    name = '',
    thumbnail = '',
    type = '',
    icon = '',
    inProgress = 0,
    finished = 0,
    time = 0,
    showView = false,
    allUsersInChapters = false
  } = {}) {
    super(type || '')
    this.id = id || 0
    this.name = name || ''
    this.image = thumbnail || ''
    this.icon = icon || ''
    this.inProcess = inProgress || 0
    this.finished = finished || 0
    this.time = secondsToTime(time || 0)
    this.showView = showView || false
    this.allUsersInChapters = !!allUsersInChapters
    this.detailsLoaded = false
    this.totalAttempts = 0
    this.totalUserAttempts = 0
    this.questions = []
  }

  setDetails({
    totalAttempts = 0,
    totalUserAttempts = 0,
    questions = []
  }) {
    this.detailsLoaded = true
    this.totalAttempts = totalAttempts || 0
    this.totalUserAttempts = totalUserAttempts || 0
    this.questions = (questions || []).map((question) => new GeneralStatsQuestionsModel(question));
  }
}

export class GeneralStatsQuestionsModel {
  constructor({
    question = '',
    totalSuccess = 0,
    totalErrors = 0,
    answers = []
  } = {}) {
    this.text = `${question || ''}`.replace(/\[(.*?)]/g, '______');
    this.totalSuccess = totalSuccess || 0;
    this.totalErrors = totalErrors || 0;
    this.totalResults = this.totalErrors + this.totalSuccess
    this.answers = (answers || []).map(({correct = false, incorrect = false, answer = ''}) => ({
      correct,
      incorrect,
      text: answer,
      answer: answer,
    }));
  }

  get successResultAvg() {
    return +((this.totalResults > 0 ? this.totalSuccess * 100 / this.totalResults : 0).toFixed(2))
  }
}