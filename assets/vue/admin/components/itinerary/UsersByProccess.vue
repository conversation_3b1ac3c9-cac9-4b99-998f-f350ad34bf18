<template>
  <div class="users-by-process">
    <div class="modal-header title">
      {{ userFiltered.length }} {{ titleModal }}
      <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    <div class="modal-body">
      <div class="col-12 p-0">
        <table class="table">
          <thead>
          <tr>
            <th scope="col">{{ translationsVue.user_configureFields_email }}</th>
            <th scope="col">{{ translationsVue.common_areas_name }}</th>
            <th scope="col">
              {{ translationsVue.user_configureFields_last_name }}
            </th>
            <th scope="col" v-if="state === 'IN_PROCESS'">Avance</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="user in pageOfUsers" :key="user.id">
            <td>
              <a :href="'/admin/user/' + user.id + '/redirect'" target="_blank"
              >{{ user.email }}
              </a>
            </td>
            <td class="text-capitalize">{{ user.first_name.toLowerCase() }}</td>
            <td class="text-capitalize">{{ user.last_name.toLowerCase() }}</td>
            <td v-if="state === 'IN_PROCESS'">
              {{ user.completed }} / {{ courses.length }}
            </td>
          </tr>
          </tbody>
        </table>
      </div>
      <div class="col-12 p-0">
        <div
            class="d-flex align-items-center justify-content-between pt-3"
            v-if="users.length > 1"
        >
          <excel-generator :options="excelOptions">
            <i class="fa fa-file-excel"></i> {{ translationsVue.export_download_file_xlsx }}
          </excel-generator>
          <jw-pagination
              :items="userFiltered"
              :maxPages="2"
              v-on:changePage="onUserChangePage"
              :labels="paginationCustomLabels"
          ></jw-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import JwPagination   from "jw-vue-pagination";
import ExcelGenerator from "../../stats/ExcelGenerator";

export default {
  components: { JwPagination, ExcelGenerator },

  props: {
    users: {
      type: Array,
      default: [],
    },

    courses: {
      type: Array,
      default: [],
    },

    state: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      pageOfUsers: [],
      excelOptions: {},
      translationsVue,
    };
  },

  computed: {
    paginationCustomLabels() {
      return {
        first: "<<",
        last: ">>",
        previous: "<",
        next: ">",
      };
    },

    userFiltered() {
      const states = {
        COMPLETED: this.userFilterCompleted,
        IN_PROCESS: this.userFilterInProcess,
        NOT_STARTED: this.userFilterNotStarted,
      };
      return this.state ? states[this.state] : this.users;
    },

    userFilterCompleted() {
      return this.users.filter(
        (user) => ( user.completed === this.courses.length && this.courses.length != 0)
      );
    },

    userFilterInProcess() {
      return this.users.filter(
        (user) =>
          (user.completed > 0 && user.completed < this.courses.length) ||
          user.started > 0
      );
    },

    userFilterNotStarted() {
      return this.users.filter(
        (user) => user.started === 0 && user.completed === 0
      );
    },

    titleModal() {
      const states = {
        COMPLETED: this.translationsVue.itinerary_chart_users,
        IN_PROCESS: this.translationsVue.itinerary_chart_users_process,
        NOT_STARTED: this.translationsVue.itinerary_chart_users_incomplete,
      };

      return this.state ? states[this.state] : "";
    },
  },

  watch: {
    userFiltered() {
      this.updateExcelOptions();
    }
  },

  methods: {
    onUserChangePage(pageOfUsers) {
      this.pageOfUsers = pageOfUsers;
    },

    updateExcelOptions() {
      const data = this.userFiltered.map((user) => ({
        email     : user.email.toLowerCase(),
        first_name: this.capitalize(user.first_name),
        last_name : this.capitalize(user.last_name),
        progress  : `${user.completed} / ${this.courses.length}`
      }));
      const name = this.translationsVue.menu_users_managment_users
      const cells = [
        {name: this.translationsVue.user_configureFields_email, key: 'email'},
        {name: this.translationsVue.common_areas_name, key: 'first_name'},
        {name: this.translationsVue.user_configureFields_last_name, key: 'last_name'},
      ]

      if (this.state === 'IN_PROCESS') {
        cells.push({name: 'Avance', key: 'progress'});
      }

      this.excelOptions = {
        sheets: [{
          name,
          tables: [{
            name: this.translationsVue.stats_title_information_user || '',
            key: 'data',
            cells
          }]
        }],
        sourceData: { data },
        fileName: `${this.translationsVue.itinerary_label_in_plural}-${name}-${this.state}`,
      };
    },

    capitalize(text) {
      return text.replaceAll(/\s\s+/g, ' ').toLowerCase().split(' ')
          .map((word) => word.charAt(0).toUpperCase() + word.substring(1))
          .join(' ');
    }
  },
};
</script>

 <style scoped lang="scss"> 
.users-by-process {
  border-radius: 7px;
  overflow: hidden;

  .title {
    font-size: 1.5rem;
    font-weight: bold;
    text-transform: uppercase;
  }

  thead th {
    border-width: 0 0 2px;
  }
}
</style>
