<template>
  <div class="new-title">
    <form @submit.prevent="submitForm">
      <div class="row">
        <div class="mb-3 col-md-8">
          <BaseTextTarea
            :label="translationsVue.rouletteWord_configureFields_statement"
            :max="150"
            :value.sync="lower.title"
            :placeholder="
              translationsVue.pairs_configureFields_placeholder_title
            "
            :required="true"
            :rows="2"
            :submitted="submitted"
          ></BaseTextTarea>
        </div>

        <div class="mb-3 col-md-4">
          <label for="title" class="form-label"
            >{{ translationsVue.games_text_common_time }}
          </label>

          <BaseInputTime
            v-model="time"
            :options="['minutes', 'seconds']"
            :maxMinutes="31"
            :time="time"
            @time-update="timeUpdate"
          />
        </div>

        <div class="mb-3 col-md-12">
          <div
            class="alert alert-info alert-dismissible fade show"
            role="alert"
          >
            {{ translationsVue.games_text_common_message_higher_lower }}
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="alert"
              aria-label="Close"
            ></button>
          </div>
        </div>
      </div>

      <div class="row">
        <ul class="list-group" @dragover="allowDrop">
          <li
            v-for="(ans, index) in lower.higherLowerWords"
            :key="ans.id"
            class="list-group-item"
            draggable="true"
            @dragstart="dragStart($event, index)"
            @drop="drop($event, index)"
          >
            <div class="words">
              <div class="arrows">
                <span class="arrow-up" @click="moveUp(index)" v-show="index > 0"
                  ><i class="fas fa-arrow-up"></i
                ></span>
                <span
                  class="arrow-down"
                  @click="moveDown(index)"
                  v-show="index < lower.higherLowerWords.length - 1"
                  ><i class="fas fa-arrow-down"></i
                ></span>
              </div>

              <div class="input-word">
                <BaseTextTarea
                  :label="
                    translationsVue.games_text_common_word +
                    ' ' +
                    (index + 1) +
                    ':'
                  "
                  :min="2"
                  :max="20"
                  :value.sync="ans.word"
                  :placeholder="translationsVue.games_help_write_word"
                  :required="true"
                  :rows="1"
                  :submitted="submitted"
                  :preventSpace="true"
                ></BaseTextTarea>
              </div>

              <div>
                <a
                  class="btn btn-sm btn-danger"
                  @click="deleteAnswer(index)"
                  v-if="index > 1"
                  ><i class="fas fa-trash-alt"></i
                ></a>
              </div>
            </div>
          </li>
        </ul>
      </div>

      <a
        class="btn-sm btn btn-primary"
        @click="addAnswer()"
        v-show="lower.higherLowerWords.length < 12"
      >
        <i class="fas fa-plus"></i> {{ translationsVue.games_add_word }}
      </a>

      <div class="mt-xxl-2 text-center">
        <button
          v-show="0"
          type="button"
          class="btn btn-secondary"
          data-bs-dismiss="modal"
          ref="closeChapterContentModal"
        ></button>

        <button
          class="btn-sm btn btn-primary"
          type="submit"
          @click="submitted = true"
        >
          {{ translationsVue.Save }}
        </button>
      </div>
    </form>
  </div>
</template>

  <script>
import { get } from "vuex-pathify";

import { modalMixin } from "../../../mixins/modalMixin";
import { alertToastMixin } from "../../../mixins/alertToastMixin";
import { formatDateMixin } from "../../../mixins/formatDateMixin";

export default {
  props: {
    lower: {
      type: Object,
      required: true,
    },
  },

  mixins: [modalMixin, alertToastMixin, formatDateMixin],

  data() {
    return {
      word: 1,
      correct: false,
      title: "",
      random: false,
      words: [
        {
          id: 1,
          word: "",
        },
      ],
      image: null,
      preview: null,
      typeChapter,     
      initDrag: 0,
      finishDrag: 0,
      chapterId,
      time: "00:00:30",
      lowersDeleted: [],
      translationsVue,
      submitted: false,
    };
  },

  watch: {
    lower: {
      immediate: true,
      handler() {
        this.time =
          this.convertSecondToHoursMinutesAndSeconds(this.lower.time) ??
          "00:00:30";
      },
    },
  },

  computed: {
    ...get("ordenarMenorMayorModule", ["getRouteChapter"]),

    routeChapter() {
      return this.getRouteChapter();
    },
  },

  methods: {
    addAnswer() {
      const maxId =
        this.lower.higherLowerWords.length > 0
          ? Math.max(...this.lower.higherLowerWords.map((b) => b.id)) + 1
          : 1;

      return this.lower.higherLowerWords.push({
        id: maxId,
        word: "",
      });
    },

    deleteAnswer(index) {
      this.lowersDeleted.push(this.lower.higherLowerWords[index]);
      this.lower.higherLowerWords.splice(index, 1);
    },

    async submitForm() {
      const form = document.querySelector("form");
      if (!form.checkValidity()) {
        return;
      }

      try {
        this.lower?.id
          ? await this.updateQuestion()
          : await this.saveQuestion();

        this.$refs["closeChapterContentModal"].click();
        await this.fetchQuestions();
        this.submitted = false;
        this.clearInputs();

        this.alertSuccesSave();
      } catch (error) {
        this.alertErrorSave();
      }
    },

    async saveQuestion() {
      this.processSave = true;
      await this.$store.dispatch(
        "ordenarMenorMayorModule/createHigherLower",
        this.dataForSendForm()
      );
      console.log("nuevo valor");
      this.time = "30";
    },

    async updateQuestion() {
      this.processSave = true;
      await this.$store.dispatch(
        "ordenarMenorMayorModule/editHigherLower",
        this.dataForSendForm()
      );
    },

    dataForSendForm() {
      const secondsTime = this.convertDateHoursMinutesAndSeconds(this.time);

      const words = this.lower.higherLowerWords.filter(
        (word) => word.word != ""
      );

      const wordsDelete = this.lowersDeleted.filter((word) => word.word != "");

      const formData = new FormData();
      formData.append("id", this.lower.id);
      formData.append("title", this.lower.title);
      formData.append("time", secondsTime);
      formData.append("words", JSON.stringify(words));
      if (this.lower.id)
        formData.append("wordsDelete", JSON.stringify(wordsDelete));
      formData.append("idChapter", this.chapterId);
      formData.append("typeCode", this.typeCode);

      return formData;
    },

    dragStart(event, index) {
      this.initDrag = index;
    },

    drop(event, index) {
      this.finishDrag = index;
      this.reorderAnswers();
    },

    allowDrop(event) {
      event.preventDefault();
    },

    reorderAnswers() {
      const answer = this.lower.higherLowerWords.splice(this.initDrag, 1)[0];
      this.lower.higherLowerWords.splice(this.finishDrag, 0, answer);
    },

    moveUp(index) {
      const answer = this.lower.higherLowerWords[index];
      this.lower.higherLowerWords.splice(index, 1);
      this.lower.higherLowerWords.splice(index - 1, 0, answer);
    },

    moveDown(index) {
      const answer = this.lower.higherLowerWords[index];
      this.lower.higherLowerWords.splice(index, 1);
      this.lower.higherLowerWords.splice(index + 1, 0, answer);
    },

    formatTime(time) {
      return time.toString().padStart(2, "0");
    },

    timeUpdate(time) {
      this.time = time;
    },

    async fetchQuestions() {
      await this.$store.dispatch(
        "ordenarMenorMayorModule/fetchHigherLower",
        this.chapterId
      );
    },

    clearInputs() {
      this.$emit("clear-inputs");
    },
  },
};
</script>

  <style scoped lang="scss">
.list-group {
  padding: 1rem;
}
.words {
  display: flex;
  gap: 1rem;
  align-items: center;

  .input-word {
    flex: 1;
  }

  .arrows {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 1rem;
    color: var(--color-primary);
  }
}

.arrow-up,
.arrow-down {
  cursor: pointer;
}
</style>
