<template>
  <CreateAdivina :adivina="adivina" />
</template>

<script>
import Create<PERSON><PERSON><PERSON> from "./CreateAdivina";

export default {
  components: {
    CreateAdivina,
  },

  props: {
    urlChapter: {
      type: String,
      required: true,
    },

    adivina: {
      type: Object,
      required: true,
    },

    time: {
      type: String,
      required: true,
    },
  },

  /*   computed: {
    copyAdivina() {
      return structuredClone(this.adivina);
    },
  }, */
};
</script>

 <style scoped lang="scss"> 
</style>
