<template>
  <tr v-if="!block.categorized">
    <td>
      <a data-bs-toggle="modal" :data-bs-target="`#question${block.id}`">
        <img
          :src="
            block.route
              ? `/uploads/games/trueOrFalse/${block.route}`
              : '/assets/chapters/default-image.svg'
          "
          alt=""
          @error="$event.target.src = 'assets/chapters/default-image.svg'"
          width="40px"
          height="40px"
        />
      </a>

      <BaseViewImagen
        :identifier="`question${block.id}`"
        :image="`/uploads/games/trueOrFalse/${block.route}`"
      />
    </td>
    <td>{{ block.text }}</td>
    <td>
      {{ convertSecondToHoursMinutesAndSeconds(block.time) }}
    </td>
    <td v-if="block.result === true">
      {{ translationsVue.trueorFalse_configureFields_true }}
    </td>
    <td v-else>{{ translationsVue.trueorFalse_configureFields_false }}</td>

    <td class="text-right">
      <button
        type="button"
        class="btn-sm btn btn-danger"
        data-bs-toggle="modal"
        :data-bs-target="`#deleteModal${block.id}`"
      >
        <i class="fas fa-trash-alt"></i>
      </button>

      <button
        class="btn btn-primary btn-sm"
        @click="modifyLine"
        data-bs-toggle="modal"
        :data-bs-target="`#editQuestion`"
      >
        <i class="fas fa-edit"></i>
      </button>
    </td>

    <BaseModalDelete
      :identifier="`deleteModal${block.id}`"
      :title="translationsVue.quiz_configureFields_question_delete"
      @delete-element="deleteLine"
    />
  </tr>
</template>

<script>
import { formatDateMixin } from "../../../mixins/formatDateMixin";
export default {
  props: {
    block: {
      type: Object,
      default: () => ({}),
    },
  },

  mixins: [formatDateMixin],
  data() {
    return {
      translationsVue,
    };
  },

  methods: {
    modifyLine() {
      const data = {
        id: this.block.id,
        route: this.block.route,
        text: this.block.text,
        result: this.block.result,
        time: this.block.time,
      };

      this.$emit("modifyLine", data);
    },

    deleteLine() {
      this.$emit("deleteLine", this.block.id);
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Resume {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.cursor {
  cursor: pointer;
}

img {
  width: 50px;
  cursor: zoom-in;
}
</style>
