<template>
  <div class="GameBlock">
    <div class="options">
      <div class="title">
        <label>Video:</label>
      </div>

      <div class="question">
        <label for="title">Título:</label>
        <input type="text" id="title" v-model="title" placeholder="Título del juego">
      </div> 

      <div class="row">
        <div class="col-md-8 question" >
          <label for="url">URL: {{url}}</label>
          <form>
            <input
              type="file"
              ref="url"
              id="file-input"
              accept="video/*"
              class="form-control-file"
              @change="fileSelected($event.target.files)" 
            />
          </form>     
        </div>
        <div class="col-md-4 question"> 
          <video id="video" width="300" height="300" controls></video>
        </div>  
      </div>
  
      <div class="question">
        <label class="labelMensaje" v-if="mensajeActivo">{{mensaje}}</label>
      </div>  
      <button
          type="button" data-dismiss="modal" class="btn btn-primary ml-1"
          @click="checkIfComplete"
        ><i class="fa fa-check"></i>
        <div v-if="editionActive">
          Guardar
        </div>

        <div v-else>
          Añadir
        </div>
      </button>

    </div>
  </div>
</template>

<script>

import VideoPlayer from "../VideoPlayer";

export default {
  watch: {
    currentLine: function () {
      this.updateLetter();
    },

  },

  props: {
    name: "App",
    chapterId: {
      type: Number,
      default: 0,
    },

    currentLine: {
      type: Object,
      default: {},
    },
  },

  components: {
    VideoPlayer,
  },

  data() {
    return {
      title: undefined,
      url: undefined,
      editionActive: false,
      imageRoute: '',
      pathImage: undefined,
      mensajeActivo:false,
      mensaje:'',
      mostrarVideo: true,
      videoURL:'',
      videoquizId:0,
      videoquizTime:0,  
      videoDuration:0,   
    };
  },

  computed: {

  },

  methods: {
    fileSelected(files) {
      if (!files || files.length === 0) return;

      this.url = files[0];
      const input = document.getElementById('file-input');
      const video = document.getElementById('video');
      const videoSource = document.createElement('source'); 
      const reader = new FileReader();

      reader.onload = function (e) {
        videoSource.setAttribute('src', e.target.result);
        video.appendChild(videoSource);
        video.load();
      };
      video.onloadeddata = function() {    
        this.videoDuration = video.duration;
        console.log('fileSelected', this.videoDuration)
      };

      reader.readAsDataURL(files[0]); 
    },
    async checkIfComplete(){
      if(!this.url || this.title === ''){
        this.mensaje="Debe suministrar la información";
        this.mensajeActivo = true;
      }else{
        console.log('checkIfComplete', this.videoDuration)
        if(this.editionActive){ 
          const data = {
            id: this.currentLine.id,
            title: this.title,
            url: this.url,
          //  duration: this.videoDuration,
          };

          await this.$store.dispatch("videoquizModule/editBlock", data);
        }else{
          const data = {
            chapterId: this.chapterId,
            title: this.title,
            url: this.url,
        //    duration: this.videoDuration,
          };
        


//          await this.$store.dispatch("videoquizModule/setBlock", data);
        }

      //  this.clearCurrent();
        
      //  this.$emit('reloadBlock');
      }
    },

    clearCurrent(){
      this.title = undefined;
      this.url = undefined;
      this.editionActive = false;
      this.$refs.url.value='';
      this.mensajeActivo=false;
      this.mostrarVideo=false;
    },

    updateLetter(){
      this.title = this.currentLine.title;
      this.url = this.currentLine.url;
      this.$refs.url.value = '';
      this.pathImage = this.currentLine.pathImage;
      this.mostrarVideo=true;
      this.editionActive = true;
      this.mensajeActivo=false;
    },
  }
};
</script>

 <style scoped lang="scss"> 
.GameBlock {
  padding: 1.5rem;

  .game-block{
    height: 30rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .title{
    color: black;
    font-size: 1.5rem
  }

  .select-block{
    padding: .5rem;
  }

  .question, .response{
    display: flex;
    flex-direction: column;
    margin-bottom: .5rem;
    padding: .5rem;
  }

  #words, #time, #clue, #title,  #response{
    border: 1px solid #5ae8e8;
    border-radius: 25px;
  }

  #words, #time, #clue, #title{
    height: 2.5rem;
    padding: .5rem;
  }

  label {
    text-transform: uppercase;
    font-weight: 500;
    padding: 0 .5rem;
    margin-bottom: .25rem;
    padding: .5rem;
  }

  select{
    background-color: #fff;
    border: 1px solid #ffffff;
    border-radius: 50px;

    width: 35%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
  }

  .options{
    height: 100%;
    flex:2;

    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
  .labelMensaje{    
    width: 60%;
    padding: .5rem;
    align-content: center;
    color: red;
  }
}
</style>
