<template>
    <highcharts class="hc" :options="chartOptions" ref="chart"></highcharts>
</template>

<script>

export default {
    name: "StackedBarChart",
    props: {
        series: {
            type: Array,
            default: () => ([{name: 'test', data: [1]}]),
        },
        categories: {
            type: Array,
            default: () => (['2020/21']),
        },
        colors: {
            type: Array,
            default: () => ['var(--color-neutral-mid)', 'var(--color-primary)'],
        },
    },
    data() {
        return {
            chartOptions: {
                chart: {
                    plotBackgroundColor: null,
                    plotBorderWidth: null,
                    plotShadow: false,
                    type: 'bar'
                },
                credits: {
                    enabled: false,
                },
                title: {
                    text: '',
                },
                xAxis: {
                    categories: this.categories,
                },
                yAxis: {
                    min: 0,
                    title: null
                },
                legend: {
                    reversed: true
                },
              /*  tooltip: {
                    pointFormat: 'texto',
                },*/
                colors: this.colors,
                plotOptions: {
                    series: {
                        stacking: 'normal',
                        dataLabels: {
                            enabled: false,
                        }
                    }
                },
                series: this.series,
            }
        };
    },

    // watch: {
    //     seriesData: {
    //         immediate: true,
    //         handler(newVal, oldVal) { // watch it
    //             this.chartOptions.series[0].data = newVal;

    //             for (let i = 0; i < newVal.length; i++) {
    //                 this.chartOptions.xAxis.categories.push(newVal[i].name);
    //             }
    //         }
    //     }
    // }
};
</script>

 <style scoped lang="scss"> 

</style>
