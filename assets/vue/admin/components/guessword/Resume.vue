<template>
  <tr>
    <td>{{ block.question }}</td>
    <td>{{ block.word }}</td>
    <td>{{ convertSecondToHoursMinutesAndSeconds(block.time) }}</td>
    <td class="text-right">
      <button
        class="btn btn-danger btn-sm"
        @click="deleteLine"
        data-bs-toggle="modal"
        data-bs-target="#modalDeleteQuestion"
      >
        <i class="fa fa-trash text-white"></i>
      </button>

      <button
        class="btn btn-primary btn-sm"
        @click="modifyLine"
        data-bs-toggle="modal"
        data-bs-target="#modal-chapter-18"
      >
        <i class="fas fa-edit"></i>
      </button>
    </td>
  </tr>
</template>

<script>
import { formatDateMixin } from "../../../mixins/formatDateMixin";
export default {
  props: {
    block: {
      type: Object,
      default: () => ({}),
    },
  },

  mixins: [formatDateMixin],

  methods: {
    modifyLine() {
      const data = {
        id: this.block.id,
        question: this.block.question,
        time: this.block.time,
        word: this.block.word,
      };

      this.$emit("modifyLine", data);
    },

    deleteLine() {
      this.$emit("deleteLine", this.block.id);
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Resume {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.cursor {
  cursor: pointer;
}
</style>
