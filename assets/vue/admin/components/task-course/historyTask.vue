<template>
  <div class="history-task" v-if="historyTask && historyTask.length > 0">
    <h4 class="title">{{translationsVue.taskCourse_configureFields_history}}</h4>
    <table class="table">
      <thead>
        <tr>
          <th scope="col"></th>
          <th scope="col">Avatar</th>
          <th scope="col">
            {{ translationsVue.user_configureFields_fullname }}
          </th>
          <th scope="col">
            {{ translationsVue.taskCourse_configureFields_senTaskUser }}
          </th>
          <th scope="col">
            {{ translationsVue.state }}
          </th>
          <th scope="col" class="text-right">{{ translationsVue.Actions }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(task, index) in historyTask" :key="index">
          <th scope="row">{{ index + 1 }}</th>
          <th>
            <img
              class="avatar"
              :src="`/uploads/users/avatars/${task.avatar}`"
               :onerror="`this.onerror=null; this.src='${avatarDefault}'`"
              alt=""
            />
          </th>
          <td>
            <a :href="task.detailUser" v-if="origin ===''">{{ task.fullName }}</a>
            <span v-else>{{ task.fullName }}</span>
          </td>
          <td>{{ task.updated }}</td>
          <td><span  :style="{background: colorButton(task.state)}" class="badge"> {{ stateTask(task.state) }}</span></td>
          <td class="text-right">
            <button
              type="button"
              class="btn btn-secondary btn-sm"
              data-bs-toggle="modal"
              :data-bs-target="`#modal${index}`"
            >
              <i class="fas fa-eye"></i>
            </button>
          </td>

          <div
            class="modal fade"
            :id="`modal${index}`"
            tabindex="-1"
            aria-labelledby="exampleModalLabel"
            data-bs-backdrop="static"
            data-bs-keyboard="false"
            aria-hidden="true"
          >
            <div class="modal-dialog modal-xl">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="exampleModalLabel">
                    {{ task.fullName }}
                  </h5>
                  <button
                    type="button"
                    class="btn-close "
                    data-bs-dismiss="modal"
                    aria-label="Close"
                  ></button>
                </div>
                <div class="modal-body">
                  <!--     <h5>Archivos enviados</h5> -->

                  <div
                    v-for="history in task.historyDelivery"
                    :key="history.id"
                  >
                    <div class="state-task">
                      <p>
                        {{
                          translationsVue.taskCourse_configureFields_files_attachment
                        }}
                      </p>
                      <div class="btn-group" v-if="origin === ''">
                        <button
                          :style="{background: colorButton(history.state), border:'none'}"
                          type="button"
                          class="btn btn-info dropdown-toggle text-white"
                          data-bs-toggle="dropdown"
                          aria-expanded="false"
                        >
                          {{ stateTask(history.state) }}
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a
                              class="dropdown-item"
                              @click="changeStateTask(history.id, 2)"
                              >{{
                                translationsVue.taskCourse_configureFields_state_2
                              }}</a
                            >
                          </li>
                          <li>
                            <a
                              class="dropdown-item"
                              @click="changeStateTask(history.id, 3)"
                              >{{
                                translationsVue.taskCourse_configureFields_state_3
                              }}</a
                            >
                          </li>
                          <li>
                            <a
                              class="dropdown-item"
                              @click="changeStateTask(history.id, 4)"
                              >{{
                                translationsVue.taskCourse_configureFields_state_4
                              }}</a
                            >
                          </li>
                        </ul>
                      </div>
                    </div>

                    <div class="files">
                      <FileCardHistory
                        v-for="file in history.filesHistoryTasks"
                        :key="file.id"
                        :file="file"
                      />
                    </div>
                  </div>

                  <div
                    v-for="(history, index) in task.historyDelivery"
                    :key="`${history.id}${index}`"
                    class="comment"
                  >
                    <div class="comment-task">
                      <CommentTask
                        v-for="comment in history.commentTasks"
                        :key="comment.id"
                        :comment="comment"
                        :name-avatar="task.avatar"
                      />
                    </div>

                    <div class="send-messages" v-if="origin === ''">
                      <textarea
                        placeholder="Añadir comentario"
                        v-model="comment"
                      />
                      <button
                        class="btn btn-primary"
                        @click="sendCommnetTask(history.id)"
                      >
                        <i class="fas fa-paper-plane"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </tr>
      </tbody>
    </table>
  </div>
</template>


<script>
import { get } from "vuex-pathify";
import FileCardHistory from "./FileCardHistory";
import CommentTask from "./CommentTask";

const colorState = {
  state1: "#31D2F2",
  state2: "#c370d3",
  state3: "#EA4335",
  state4: "#19863A",
};

export default {
  name: "history-task",
  components: {
    FileCardHistory,
    CommentTask,
  },

  props:{
    origin:{
      type: String,
      default: ''
    }
  },

  data() {
    return {
      idTask,
      translationsVue,
      comment: "",
    };
  },

  computed: {
    ...get("materialCourse", ["isLoading", "getHistoryTask"]),

    historyTask() {
      return this.getHistoryTask();
    },

    avatarDefault() {
      return `/uploads/users/avatars/default.svg`;
    },
  },

  created() {
    this.fetchHistoryTask();
  },

  methods: {
    async fetchHistoryTask() {
      const data = {
        idTask: this.idTask,
      };
      await this.$store.dispatch("materialCourse/fetchHistoryTask", data);
    },

    openModalDialog() {
      const dialog = this.$el.querySelector("dialog");
      dialog.showModal();
    },

    closeDialog() {
      const dialog = this.$el.querySelector("dialog");
      dialog.close();
    },

    stateTask(state) {
      if (state === 1) {
        return this.translationsVue.taskCourse_configureFields_state_1;
      } else if (state === 2) {
        return this.translationsVue.taskCourse_configureFields_state_2;
      } else if (state === 3) {
        return this.translationsVue.taskCourse_configureFields_state_3;
      } else if (state === 4) {
        return this.translationsVue.taskCourse_configureFields_state_4;
      }

      return this.translationsVue.taskCourse_configureFields_state_0;
    },

    colorButton(state) {
      return colorState[`state${state}`];
    },

    async sendCommnetTask(idHistory) {
      const request = {
        idHistory: idHistory,
        comment: this.comment,
      };

      await this.$store.dispatch("materialCourse/commentTask", request);
      this.comment = "";
      this.fetchHistoryTask();
    },

    async changeStateTask(idHistory, state) {
      const request = {
        idHistory: idHistory,
        state: state,
      };

      await this.$store.dispatch("materialCourse/commentTask", request);
      this.comment = "";
      this.fetchHistoryTask();
    },
  },
};
</script>

 <style scoped lang="scss"> 
.history-task {
  margin-top: 3rem;
  .title {
    margin-top: 2rem;
  }

  .avatar {
    width: 3rem;
  }
}

.state-task {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.files {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  background: #F2F2F2FF;
  margin: 0;
  padding: 1rem;
}

.comment {
  margin-top: 1rem;
  .send-messages {
    margin-top: 1rem;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    flex: 1;
    gap: 1rem;
    textarea {
      flex: 1;
      border-radius: 8px;
      font-size: 16px;
      width: 80%;
      height: 2.5rem;
      margin: auto;
      border: solid 1px var(--color-neutral-mid-light);

      &:focus {
        outline: 1px solid var(--color-primary);
      }
    }
  }

  .comment-task {
    margin-top: 0.5rem;
    display: flex;
    flex-direction: column;
    /* flex-wrap: wrap; */
    flex: 1;
    gap: 1rem;
  }
}

.modal {
  backdrop-filter: blur(4px) !important;
}
</style>
