<template>
  <div class="history-task">
    <div v-for="historyTask in historyTaskUser" :key="historyTask.id">
      <div
        v-for="historyDelivery in historyTask.historyDeliveryTasks"
        :key="historyDelivery.id"
      >
        <div class="files" v-if="historyDelivery.state > 0">
          <FileCardHistory
            v-for="file in historyDelivery.filesHistoryTasks"
            :key="file.id"
            :file="file"
          />
        </div>
      </div>
    </div>

    <div
      v-for="(history, index) in historyTaskUser"
      :key="`${history.id}${index}`"
      class="comment"
    >
      <div v-for="historyD in history.historyDeliveryTasks" :key="historyD.id">
        <div class="comment-task">
          <CommentTask
            v-for="comment in historyD.commentTasks"
            :key="comment.id"
            :comment="comment"
            :name-avatar="history.avatar"
          />
        </div>
      </div>

      <div class="send-messages" v-if="origin === ''">
        <textarea placeholder="Añadir comentario" v-model="comment" />
        <button
          class="btn btn-primary"
          @click="sendCommnetTask(history.historyDeliveryTasks[0].id)"
        >
          <i class="fas fa-paper-plane"> </i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import FileCardHistory from "../../task-course/FileCardHistory";
import CommentTask from "../../task-course/CommentTask";

export default {
  components: {
    FileCardHistory,
    CommentTask,
  },

  props: {
    historyTaskUser: {
      type: Array,
      default: () => [],
    },

    origin: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      comment: "",
    };
  },

  methods: {
    async sendCommnetTask(idHistory) {
      const request = {
        idHistory: idHistory,
        comment: this.comment,
      };

      await this.$store.dispatch("materialCourse/commentTask", request);
      this.comment = "";
      this.$emit("fetch-task-User");
    },

    async changeStateTask(idHistory, state) {
      const request = {
        idHistory: idHistory,
        state: state,
      };

      await this.$store.dispatch("materialCourse/commentTask", request);
      this.comment = "";
      this.$emit("fetch-task-User");
    },
  },
};
</script>

 <style scoped lang="scss"> 
.history-task {
  .files {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    background: #f2f2f2;
    margin: 0;
    padding: 1rem;
  }

  .comment {
    margin-top: 1rem;
    .send-messages {
      margin-top: 1rem;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      flex: 1;
      gap: 1rem;
      textarea {
        flex: 1;
        border-radius: 8px;
        font-size: 16px;
        width: 80%;
        height: 2.5rem;
        margin: auto;
        border: solid 1px var(--color-neutral-mid-light);

        &:focus {
          outline: 1px solid var(--color-primary);
        }
      }
    }

    .comment-task {
      margin-top: 0.5rem;
      display: flex;
      flex-direction: column;
      /* flex-wrap: wrap; */
      flex: 1;
      gap: 1rem;
    }
  }
}
</style>
