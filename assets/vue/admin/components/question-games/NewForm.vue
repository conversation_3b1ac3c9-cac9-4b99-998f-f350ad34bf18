+<template>
  <div class="new-question">
    <div
      class="modal fade"
      :id="`modal-chapter-${chapteType}`"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      :aria-labelledby="`modal-chapter-${chapteType}Label`"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
          <div class="modal-header">
            <h5
              class="modal-title"
              id="modal-chapter-2Label"
              v-if="chapteType !== 7"
            >
              {{ translationsVue.quiz_configureFields_title_creation }}
            </h5>
            <h5 class="modal-title" id="modal-chapter-2Label" v-else>
              {{ translationsVue.hiddenword_configureFields_title }}
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              :id="`close-modal-chapter-${chapteType}`"
            ></button>
          </div>
          <div class="modal-body">
            <div id="questions-games">
              <CreateGame
                :questions="questions"
                :id-modal="`modal-chapter-${chapteType}`"
                @clear-input="clearFormInput"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// Chapter type 7: Juego palabras ocultas
//Chapter type 5: Juego quiz

import CreateGame from "./CreateGame";

export default {
  name: "ModalQuestion",
  components: {
    CreateGame,
  },

  data() {
    return {
      chapteType,
      translationsVue,
      questions: {
        question: "",
        answers: [
          {
            id: 1,
            answer: "",
            correct: true,
          },
        ],
        image: null,
        random: false,
        feedback: {
          isFeedback: false,
          correct: "",
          incorrect: "",
        },
        time: "30",
      },
    };
  },

  created() {
    this.minimalResponses();
  },

  methods: {
    minimalResponses() {
      if (this.chapteType !== 7) {
        this.questions = {
          question: "",
          answers: [
            {
              id: 1,
              answer: "",
              correct: false,
            },
            {
              id: 2,
              answer: "",
              correct: false,
            },
          ],
          image: null,
          random: false,
          feedback: {
            isFeedback: false,
            correct: "",
            incorrect: "",
          },
          time: "30",
        };
      } else {
        this.questions = {
          question: "",
          answers: [
            {
              id: 1,
              answer: "",
              correct: true,
            },
          ],
          image: null,
          time: "30",
        };
      }
    },

    clearFormInput() {
      this.minimalResponses();
    },
  },
};
</script>

 <style scoped lang="scss"> 
.new-question {
}
</style>
