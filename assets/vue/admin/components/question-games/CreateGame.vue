<template>
  <div class="new-question">
    <form @submit.prevent="updateQuestion" ref="form">
      <div class="forms-and-image">
        <div class="form-inputs">
          <div class="mb-3">
            <BaseTextTarea
              :label="translationsVue.quiz_configureFields_question"
              :max="maxCharactersQuestion"
              :value.sync="questions.question"
              :placeholder="
                translationsVue.quiz_configureFields_question_placeholder
              "
              :required="true"
              :rows="4"
              :submitted="submitted"
            ></BaseTextTarea>
          </div>

          <div class="time-and-order">
            <div class="time">
              <label for="title" class="form-label"
                >{{ translationsVue.games_text_common_time }}
              </label>

              <BaseInputTime
                v-model="time"
                :options="['minutes', 'seconds']"
                :maxMinutes="31"
                :time="time"
                @time-update="timeUpdate"
              />
            </div>

            <div class="form-check form-switch order" v-if="chapteType !== 7">
              <input
                type="checkbox"
                id="Question_random"
                name="random"
                data-ea-align="left"
                class="form-check-input"
                v-model="questionsCopy.random"
              />
              <label
                class="checkbox-switch form-check-label"
                for="Question_random"
              >
                {{ translationsVue.games_text_common_order_ramdom }}</label
              >
            </div>
          </div>

          <div
            class="d-flex align-items-center"
            v-for="(ans, index) in questions.answers"
            :key="ans.id"
            style="gap: 1rem"
          >
            <div style="flex: 1">
              <div class="mb-3">
                <BaseTextTarea
                  :label="
                    chapteType !== 7
                      ? `${translationsVue.games_text_common_answer} : ${
                          index + 1
                        }`
                      : translationsVue.hiddenword_configureFields_answers_title
                  "
                  :max="maxCharactersAnswers"
                  :value.sync="ans.answer"
                  :placeholder="
                    chapteType !== 7
                      ? translationsVue.games_help_write_answer
                      : translationsVue.hiddenword_configureFields_answers_placeholder
                  "
                  :required="true"
                  :rows="1"
                  :submitted="submitted"
                  :validateSpecialCharacters="chapteType !== 7 ? false : true"
                  :acceptSpecialCharactersEnie="true"
                ></BaseTextTarea>
              </div>
            </div>
            <div class="form-check mt-4" v-show="chapteType !== 7">
              <input
                class="form-check-input"
                type="checkbox"
                value="true"
                v-bind:id="ans.id"
                name="correct"
                v-model="ans.correct"
                v-on:input="checkCorrect(index)"
              />
              <label class="form-check-label" for="flexCheckDefault">
                {{ translationsVue.challenges_correct }}
              </label>
            </div>
            <div class="trash-question" v-if="chapteType !== 7 && index > 1">
              <p class="trash" @click="deleteAnswer(index)">
                <i class="fas fa-times text-danger"></i>
              </p>
            </div>

            <div v-else></div>
          </div>

          <a
            class="btn-sm btn btn-primary"
            @click="addAnswer()"
            v-if="chapteType !== 7"
            ><i class="fas fa-plus"></i>
            {{ translationsVue.games_answers }}
          </a>
        </div>
        
        <div class="form-image">
          <label>
            {{ translationsVue.games_text_common_ilustre_question }}</label
          >
          <div
            :style="{ backgroundImage: 'url(' + currentImage + ')' }"
            :class="
              image == null && questionsCopy.image == null
                ? 'preview-image preview-image-default'
                : 'preview-image'
            "
            @click="$refs.inputFile.click()"
          />
          <div class="mb-3 mt-1">
            <input
              type="file"
              @change="loadImage($event)"
              accept="image/*"
              ref="inputFile"
            />
            <a class="btn-sm btn btn-primary" @click="$refs.inputFile.click()"
              ><i class="fas fa-upload"></i>
              {{ translationsVue.games_text_common_select_image }}
            </a>

            <a class="btn-sm btn btn-danger" @click="removeImage()">
              <i class="fas fa-trash-alt"></i>
            </a>
          </div>
        </div>
      </div>

      <Feedback
        :feedback="feedback"
        :submitted="submitted"
        v-if="chapteType === 5"
      />

      <div class="save-button text-center">
        <button
          v-show="0"
          type="button"
          class="btn btn-secondary"
          data-bs-dismiss="modal"
          ref="closeChapterContentModal"
        ></button>

        <button
          class="btn-sm btn btn-primary"
          v-if="!proccesSave"
          aria-label="Close"
          @click="submitted = true"
        >
          {{ translationsVue.component_video_button_save }}
        </button>
        <Spinner v-else />
      </div>
    </form>
  </div>
</template>
  
  <script>
import Spinner from "../base/Spinner";
import Feedback from "./Feedback";

import { modalMixin } from "../../../mixins/modalMixin";
import { alertToastMixin } from "../../../mixins/alertToastMixin";
import { formatDateMixin } from "../../../mixins/formatDateMixin";

export default {
  components: { Spinner, Feedback },
  props: {
    questions: {
      type: Object,
      required: true,
    },

    idModal: {
      type: String,
      required: true,
    },
  },

  mixins: [modalMixin, alertToastMixin, formatDateMixin],

  data() {
    return {
      answers: [
        {
          id: 1,
          answer: "",
          correct: true,
        },
      ],
      answersDeleted: [],
      image: null,
      preview: "/assets/common/add_image_file.svg",
      chapteType,
      translationsVue,
      chapterId,
      proccesSave: false,
      feedback: {
        isFeedback: false,
        correct: "",
        incorrect: "",
      },
      time: "00:00:30",
      submitted: false,
      maxCharactersQuestion: 255,
      maxCharactersAnswers: 255,
    };
  },

  computed: {
    currentImage() {
      if (this.image) {
        return this.preview;
      }

      return this.getQuestionImage ?? this.preview;
    },

    getQuestionImage() {
      return this.questions.image
        ? "uploads/images/question/" + this.questions.image
        : null;
    },

    questionsCopy() {
      return JSON.parse(JSON.stringify(this.questions));
    },
  },

  mounted() {
    this.feedback = this.questions.feedback;
    this.time =
      this.convertSecondToHoursMinutesAndSeconds(this.questions.time) ??
      "00:00:30";

    this.fetchMaxCharacter();
  },

  watch: {
    questions: {
      immediate: true,
      handler() {
        this.feedback = this.questions.feedback;
        this.time =
          this.convertSecondToHoursMinutesAndSeconds(this.questions.time) ??
          "00:00:30";
      },
    },
  },

  methods: {
    fetchMaxCharacter() {
      this.chapteType === 7
        ? (this.maxCharactersQuestion = 170)
        : (this.maxCharactersQuestion = 255);

      this.chapteType === 7
        ? (this.maxCharactersAnswers = 10)
        : (this.maxCharactersAnswers = 255);
    },

    addAnswer() {
      const maxId =
        this.questions.answers.length > 0
          ? Math.max(...this.questions.answers.map((b) => b.id)) + 1
          : 1;

      this.questions.answers.push({
        id: maxId,
        answer: "",
        correct: false,
      });
    },

    deleteAnswer(index) {
      this.answersDeleted.push(this.questions.answers[index]);
      this.questions.answers.splice(index, 1);
    },

    checkCorrect(index) {
      this.questions.answers.forEach((ans, i) => {
        if (i != index) {
          ans.correct = false;
        }
      });
    },

    loadImage(event) {
      const input = event.target;

      if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = (e) => {
          this.preview = e.target.result;
        };

        this.image = input.files[0];

        reader.readAsDataURL(input.files[0]);
      }
    },

    async updateQuestion(event) {
      try {
        if (!this.$refs.form.checkValidity()) {
          return;
        }

        if (this.$refs.form.checkValidity() && !this.isCorrectInquestion()) {
          this.alertWarning(
            this.translationsVue.puzzle_configureFields_select_correct_answer
          );
          return;
        }

        await this.sendQuestionFormDataEdit();

        await this.fetchQuestions();

        this.alertSuccesSave();

        this.$emit("clear-input");

        this.$refs["closeChapterContentModal"].click();

        // this.closeModal(this.idModal);
        this.removeImage();

        this.proccesSave = false;
      } catch (error) {
        this.alertErrorSave();
      }
    },

    async sendQuestionFormDataEdit() {
      const secondsTime = this.convertDateHoursMinutesAndSeconds(this.time);

      const formData = new FormData();
      formData.append("question", this.questions.question);
      formData.append("random", this.questionsCopy.random);
      formData.append("answers", JSON.stringify(this.questions.answers));
      formData.append("image", this.image); // this.image is a File object
      formData.append("chapter", chapterId);
      formData.append("answersDelete", JSON.stringify(this.answersDeleted));
      formData.append("feedback", JSON.stringify(this.feedback));
      formData.append("time", secondsTime);
      this.proccesSave = true;
      this.submitted = false;

      if (this.questionsCopy?.id) {
        formData.append("idQuestion", this.questionsCopy.id);
        await this.$store.dispatch(
          "questionsGamesModule/updateQuestion",
          formData
        );
      } else {
        await this.$store.dispatch(
          "questionsGamesModule/createQuestion",
          formData
        );
      }

      this.$refs.inputFile.value = null;
    },

    async fetchQuestions() {
      await this.$store.dispatch(
        "questionsGamesModule/fetchQuestionsGames",
        this.chapterId
      );
    },

    removeImage() {
      //Remover la imagen anterior del input file
      this.$refs.inputFile.value = null;

      this.image = null;
      this.preview = "/assets/common/add_image_file.svg";
    },

    isCorrectInquestion() {
      return this.questions.answers.some((ans) => ans.correct);
    },

    timeUpdate(time) {
      this.time = time;
    },
  },
};
</script>
  
   <style scoped lang="scss"> 
.new-question {
  .preview-image {
    width: 350px;
    height: 200px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    cursor: pointer;
  }

  .preview-image-default {
    background-size: 30%;
  }

  input[type="file"] {
    display: none;
  }

  label.form-check-label {
    margin-left: 1rem !important;
  }

  .form-check-input:checked {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
  }

  .form-switch {
    padding: 0;
  }

  .trash-question {
    display: flex;
    justify-content: center;
    align-content: flex-end;
    flex-direction: column;
    margin-top: 30px;
    text-align: right;

    .trash {
      cursor: pointer;
    }
  }

  .save-button {
    margin-top: 1rem;
    margin-bottom: 2rem;
    padding-top: 1.5rem;
  }

  padding: 0 !important;

  form {
    .forms-and-image {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 1rem;
      padding: 1rem;

      .form-inputs {
        flex: 1;

        .time-and-order {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          gap: 1rem;
          align-items: flex-end;
          margin-bottom: 1.5rem;

          .time {
            flex: 1;
          }

          .order {
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
  