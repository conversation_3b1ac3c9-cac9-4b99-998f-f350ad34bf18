import Vue from 'vue';
import Vuex from 'vuex';
import callModule from './callModule';
import callUserFilterModule from './callUserFilterModule';
import contentModule from "./contentModule";
import timeModule from "./timeModule";
import chapterModule from "./chapterModule";
import summonModule from "./summonModule";
import uploadModule from "./uploadModule";
import puzzleModule from "./puzzleModule";
import generalStatsModule from "./generalStatsModule";
import userModule from "./userModule";
import accessLevelModule from "./accessLevelModule";
import managerEditModule from "./managerEditModule";
import scormPackageModule from "./scormPackageModule";
import accumulativeStatsModule from "./accumulativeStatsModule";
import userFilterModule from "./userFilterModule";
import questionsGamesModule   from "./questionsGamesModule";
import forumModule from "./forumModule";
import materialCourse from "./MaterialCourse";
import rouletteWordModule from "./rouletteWordModule";
import trueOrFalseModule from "./trueOrFalseModule";
import adivinaImagenModule from "./adivinaImagenModule";
import ordenarMenorMayorModule from "./ordenarMenorMayorModule";
import parejasModule from "./parejasModule";
import categorizeModule from "./categorizeModule";
import fillgapsModule from "./fillgapsModule";
import guesswordModule from "./guesswordModule";
import wordleModule from "./wordleModule";
import lettersoupModule from "./lettersoupModule";
import videoquizModule from "./videoquizModule";
import localeModule from '../../common/store/modules/localeModule'
import materialCourseModule from '../../common/store/modules/materialCourseModule'


Vue.use(Vuex);

export default new Vuex.Store({
  state: {
  },
  mutations: {
  },
  actions: {
  },
  modules: {
    localeModule,
    materialCourseModule,
    callModule,
    callUserFilterModule,
    contentModule,
    timeModule,
    chapterModule,
    uploadModule,
    summonModule,
    puzzleModule,
    generalStatsModule,
    userModule,
    accessLevelModule,
    scormPackageModule,
    managerEditModule,
    accumulativeStatsModule,
    userFilterModule,
    questionsGamesModule,
    forumModule,
    materialCourse,
    rouletteWordModule,
    trueOrFalseModule,
    adivinaImagenModule,
    ordenarMenorMayorModule,
    parejasModule,
    categorizeModule,
    fillgapsModule,
    guesswordModule,
    wordleModule,
    lettersoupModule,
    videoquizModule,
  },
});
