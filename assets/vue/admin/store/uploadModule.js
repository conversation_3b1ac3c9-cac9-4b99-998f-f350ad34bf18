import axios from "axios";
import { make } from "vuex-pathify";

const getDefaultState = () => ({
  uploadVideo: undefined,
});

const state = () => getDefaultState();

export const getters = {
  getUpload: (state) => () => state.uploadVideo,
};

export const mutations = {
  ...make.mutations(state),
};

export const actions = {
  async newPackageVideoByUrl(context, { typeVideo, chapter, urlVideo, namePackage, referer }) {
    const url = "/admin/createPackageVideobyUrl";

    const body = {
      typeVideo,
      chapter,
      urlVideo,
      namePackage,
      referer,
    };

    const  { data }   = await axios.post(url, body);


    return data;
  },

  async newVideoByVimeo(
    context,
    {
      newVideo,
      typeVideo,
      chapter,
      namePackage,
      domain,
      track,
      fileTextTrack,
      language,
    }
  ) {
     const url = `${domain}/createVideoByVimeo`;

    const options = {
      headers: { "Content-Type": "multipart/form-data" },
    };

    const formData = new FormData();
    formData.append("video", newVideo);
    formData.append("typeVideo", typeVideo);
    formData.append("chapter", chapter);
    formData.append("namePackage", namePackage);
    formData.append("track", track);
    formData.append("fileTextTrack", fileTextTrack);
    formData.append("language", language);

    const { data } = await axios.post(url, formData, options);
    return data;
  },

  async detailVideo(context, idVideo) {
    const url = `/admin/Detailvideo/${idVideo}`;

    const data = await axios.get(url);

    return data;
  },

  async updatePackageVideoByUrl(
    context,
    {
      id,
      typeVideo,
      chapter,
      urlVideo,
      namePackage,
      identifier,
      track,
      origen,
      fileTextTrack,
      language,
      uriTextTrack
    }
  ) {
    const url = "/admin/updatePackageVideo";

    const options = {
      headers: { "Content-Type": "multipart/form-data" },
    };

    const formData = new FormData();
    formData.append("id", id);
    formData.append("typeVideo", typeVideo);
    formData.append("chapter", chapter);
    formData.append("urlVideo", urlVideo);
    formData.append("namePackage", namePackage);
    formData.append("track", track);
    formData.append("origen", origen);
    formData.append("identifier", identifier);
    formData.append("fileTextTrack", fileTextTrack);
    formData.append("language", language);
    formData.append("uriTextTrack", uriTextTrack);

    const { result } = await axios.post(url, formData, options);
    return result;
  },

  async updateVideoByVimeo(
    context,
    {
      id,
      newVideo,
      typeVideo,
      chapter,
      namePackage,
      identifier,
      typePackage,
      domain,
      track,
      fileTextTrack,
      language
    }
  ) {

    const url = `${domain}/admin/updatePackageVideoVimeo`;

    const options = {
      headers: { "Content-Type": "multipart/form-data" },
    };

    const formData = new FormData();
    formData.append("id", id);
    formData.append("video", newVideo);
    formData.append("typeVideo", typeVideo);
    formData.append("chapter", chapter);
    formData.append("namePackage", namePackage);
    formData.append("identifier", identifier);
    formData.append("typePackage", typePackage);
    formData.append("track", track);
    formData.append("fileTextTrack", fileTextTrack);
    formData.append("language", language);

    const { result } = await axios.post(url, formData, options);
    return result;
  },

  async translateTextComponent()
  {
    const url = "/translate-component-video";

    const data = await axios.get(url);

    return data.data;
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
