import axios from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({});

const state = () => getDefaultState();

export const getters = {};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {

    async fetchTrainedUsers({commit}) {
        const url = `/admin/stats/accumulative/trained`;
        let stats;

        try {
            const {data} = await axios.post(url);
            stats = data.data;
        } finally {
        }

        return stats;
    },

    async fetchUniqueLogins({commit}) {
        const url = `/admin/stats/accumulative/sessions`;
        let stats;

        try {
            const {data} = await axios.post(url);
            stats = data.data;
        } finally {
        }

        return stats;
    },

    async fetchCourses({commit}) {
        const url = `/admin/stats/accumulative/courses`;
        let stats;

        try {
            const {data} = await axios.post(url);
            stats = data.data;
        } finally {
        }

        return stats;
    },

    async fetchRatings({commit}) {
        const url = `/admin/stats/accumulative/ratings`;
        let stats;

        try {
            const {data} = await axios.post(url);
            stats = data.data;
        } finally {
        }

        return stats;
    },

    async fetchTime({commit}) {
        const url = `/admin/stats/accumulative/time`;
        let stats;

        try {
            const {data} = await axios.post(url);
            stats = data.data;
        } finally {
        }

        return stats;
    },

    async fetchFilterCategories({commit}) {
        const url = `/admin/stats/accumulative/filters`;
        let stats;

        try {
            const {data} = await axios.post(url);
            stats = data.data;
        } finally {
        }

        return stats;
    },

};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
