import Vue from 'vue';
import store from './store';
import axios from "axios";
import $ from 'jquery';
import '../../css/courseStats.scss'
import 'bootstrap'

import Loader from "./components/Loader";
import UserFilter from "./components/UserFilter";
import ItineraryUserFilter from "./components/ItineraryUserFilter.vue";
import ItineraryManagerFilter from "./components/ItineraryManagerFilter.vue";
import ItineraryCategoryFilter from "./components/ItineraryCategoryFilter.vue";
import Itinerary from "./components/Itinerary.vue";
import CategoryFilter from "../common/components/filter/CategoryFilter.vue";


import Highcharts from 'highcharts';
import Stock from 'highcharts/modules/stock';
import noData from 'highcharts/modules/no-data-to-display';
import HighchartsVue from 'highcharts-vue';
import StackedBarChart from "./components/stats/StackedBarChart";
import draggable from "vuedraggable";
import VueToast from "vue-toast-notification";
import 'vue-toast-notification/dist/theme-sugar.css';
import VueAlertify from "vue-alertify";
import {get} from "vuex-pathify";
import {getI18nApi} from "../common/i18n";
import Pagination from "./components/Pagination";
import BoxInfo from "./components/stats/BoxInfo";
import mySelect from './components/html/select';
import myMultiSelect from './components/html/multiselect';
import inputDate from "./components/html/input-date";
import UsersByProccess from "./components/itinerary/UsersByProccess";
import Spinner from "./components/base/Spinner.vue";

import "./../registerBaseComponents";

import UserStatsModel from './course/models/UserStatsModel'
import { secondsToTime } from './course/models/UserStatsModel'
import CourseStatsDetails from './course/CourseStatsDetails.vue'
import IconNotification from "./../announcement/components/details/iconNotification.vue";

import { ref } from 'vue';

import "../../css/itinerary.scss";
import TaskQueueMixin from '../mixins/TaskQueueMixin';

/**
 * Call user locale information and init i18n, if successful, @call startVueApp
 */
getI18nApi().then((data) => {
	startVueApp(data);
})


Vue.use(VueToast);
Stock(Highcharts);
noData(Highcharts);

Vue.use(HighchartsVue);

/**
 * Init vue app instance after a successful i18n initialization
 * @param i18n
 * @param locales
 * @param locale
 * */
function startVueApp({ i18n, locales, locale}) {
	new Vue({
		delimiters: ['${', '}'],
		components: {
			Loader,
			draggable,
			UserFilter,
			ItineraryUserFilter,
			ItineraryManagerFilter,
			ItineraryCategoryFilter,
			Itinerary,
			CourseStatsDetails,			
			CategoryFilter,
			StackedBarChart,
			Pagination,
			BoxInfo,
			mySelect,
			myMultiSelect,
			inputDate,
			UsersByProccess,
			Spinner,
			IconNotification
		},
		store,
		$,
		i18n,
		data() {
			return {
				tab: 'courses',
				filterCoursesQuery: '',
				courses: [],
				pageOfCourses: [],
				itineraryId: undefined,
				isTeamManager: false,
				tabOptions: 'courses',
				/**
				 * Translation Object, data required
				 * deleteUser: {title, subtitle}
				 */
				translations: [],
				imageBasePath: undefined,

				searchQuery: '',
				selectedSearchQuery: '',
				loadingCourses: false,
				selectedCourses: [],
				findingCourses: false,
				// availableCourses   : [],
				savingCourses: false,

				loadingUsers: true,
				loadingUsersRaw: true,
				users: [],
				usersFromFilters: [],
				pageOfUsers: [],
				page: 1,
				maxPages: 1,
				totalItems: 0,
				currentActiveUsers: 0,//0 All, 1 manual, 2 from filers

				managers: [],
				loadingManagers: true,
				pageOfManagers: [],

				//excel file
				filters: {
					filename: ''
				},

				loadingTimeSpent: true,
				totalTimeSpentInSeconds: 0,
				averageTimeInSeconds: 0,
				averageAllTimeInSeconds: 0,
				progressUsers: [],
				bd_filters: {},
				showFilters: false,
				showCoursesFilters: false,
				appliedCoursesFilters: false,
				appliedUsersFilters: false,
				appliedCategoryCourseFilter: undefined,
				appliedCategoryUserFilter: undefined,
				showFiltersSummary: false,
				filtersPeopleSummary: "",
				allUsersRaw: [],
				usersTotalRaw: 0,
				usersTotal: 0,
				usersTotalManual:0,
				usersTotalCompleted:0,
				usersTotalStarted: 0,
				usersStartedCompleted: 0,
				usersTotalsStatusStatistics: undefined,
				countriesStatistics: undefined,
				//used to reload graphs when filters are changed
				countriesKey: 0,
				hotelsKey: 100,
				departmentsKey: 200,
				groupingKey: 300,
				coursesKey: 400,
				countriesGraph: "COUNTRIES",
				hotelsGraph: "HOTELS",
				departmentsGraph: "DEPARTMENTS",
				groupingGraph: "GROUPINGS",
				coursesGraph: "COURSES",

				hotelsStatistics: undefined,
				departmentsStatistics: undefined,
				groupingStatistics: undefined,
				coursesStatistics: undefined,
				categoryFilter: null,
				availableCategoryFilter: null,
				selectedCategoryFilter: null,
				stateProcess: 'COMPLETED',
				loadingAssignedFilters: true,
				assignedFilters: [],
				userCourseDetails: undefined,
				userCourseData: undefined,
				courseId: undefined,
				totalCourseTime: 0
			}
		},
		beforeMount() {
			this.itineraryId = this.$el.attributes['itinerary-id'].value;
			this.imageBasePath = this.$el.attributes['image-base-path'].value;
			this.translations = JSON.parse(this.$el.attributes['translations'].value)
			this.filters.filename = this.translations.export.filename + ' ' + this.dateFormat(new Date());
			this.isTeamManager = this.isTeamManager;
			this.tab = this.tabOptions;
			this.$i18n.locale = this.$el.attributes['locale'].value;

			const alertifyOk = i18n.t('ALERTIFY.OK');
			const alertifyCancel = i18n.t('ALERTIFY.CANCEL');
			Vue.use(VueAlertify, {
				closable: false,
				movable: false,
				glossary: {
					title: 'Itinerary',
					ok: alertifyOk,
					cancel: alertifyCancel
				}
			});

			this.currentActiveUsers = this.isTeamManager ? 1 : 0;

		},
		async mounted() {
			await this.loadSelectedFilters();
			this.bd_filters = this.defaultBdFilters();
			await this.reloadItinerary();
			this.deleteActionModal();
		},
		computed: {
			categoryFilterOptions: get('userFilterModule/categoryFilterOptions'),
			allCourses: get('userFilterModule/allCourses'),

			availableCourses() {
				const availableCourses = this.allCourses.filter(c => !this.selectedCourses.some(sc => sc.id === c.id));
				const coursesByCategory = this.filterCourseByCategory(availableCourses, this.availableCategoryFilter)

				return this.filterCategoryCoursesByText(coursesByCategory, this.searchQuery);
			},

			filteredSelectedCourses() {
				const coursesByCategory = this.filterCourseByCategory(this.selectedCourses, this.selectedCategoryFilter)

				return this.filterCategoryCoursesByText(coursesByCategory, this.selectedSearchQuery);
			},
			

			itineraryFilteredCourses() {
				const coursesByCategory = this.filterCourseByCategory(this.courses, this.categoryFilter)

				return coursesByCategory;

			},

			userCoursesTotal() {
				return this.progressUsers.length;
			},

			userCoursesCompleted() {
				if (!this.progressUsers) return 0;

				const coursesCompleted = this.progressUsers?.filter(c => c.completed);

				return coursesCompleted.length;
			},

			allUsers() {
				return [...new Set([...this.users, ...this.usersFromFilters.filter(item => {
					const index = this.users.findIndex(user => user.id === item.id);
					return index < 0;
				})
				])
				]
			},

			allFilteredUsers() {
				return [...new Set([...this.users, ...this.usersFromFilters.filter(item => {
					const index = this.users.findIndex(user => user.id === item.id);
					return index < 0;
				})
				])
				]
			},
			filterCourses() {
				return this.courses.filter(course => {
					return course.name.toLowerCase().includes(this.filterCoursesQuery) || course.code.toLowerCase().includes(this.filterCoursesQuery);
				})
			},
			filterSelected() {
				if (this.selectedSearchQuery.length < 1) return this.selectedCourses;
				return this.selectedCourses.filter(course => {
					return course.name.toLowerCase().includes(this.selectedSearchQuery) || course.code.toLowerCase().includes(this.selectedSearchQuery);
				})
			},
			selectedIds() {
				return this.selectedCourses.map(course => course.id);
			},
			displayUsers() {

				if (this.currentActiveUsers === 1) return this.users;

				if (this.currentActiveUsers === 2) return this.usersFromFilters;

				const filteredUsers = this.usersFromFilters.filter(item => {
					const index = this.users.findIndex(user => user.id === item.id);
					return index < 0;
				});

				return [...new Set([...this.users, ...filteredUsers])];
			},
			paginationCustomLabels() {
				return {
					first: '<<',
					last: '>>',
					previous: '<',
					next: '>'
				}
			},
			usuariosItinerarioCompletado() {
				return this.allUsers.filter(user => user.completed === this.courses.length).length;
			},
			usuariosItinerarioProceso() {
				return this.allUsers.filter(user => (user.completed > 0 && user.completed < this.courses.length) || user.started > 0).length;
			},

			usuariosRawItinerarioCompletado() {
				return this.allUsersRaw.filter(user => ( (user.completed === this.courses.length) && ( this.courses.length != 0) ) ).length;
			},
			usuariosRawItinerarioProceso() {
				return this.allUsersRaw.filter(user => (user.completed > 0 && user.completed < this.courses.length) || user.started > 0).length;
			},

			totalTimeFormatted() {
				return this.formattedTime(this.totalTimeSpentInSeconds);
			},

			avgTimeFormatted() {
				/*
				let averageTimeInSeconds = 0;				
				if (this.totalTimeSpentInSeconds > 0 && this.displayUsers.length > 0) {
					averageTimeInSeconds = this.totalTimeSpentInSeconds / this.displayUsers.length;
				}
				return this.formattedTime(averageTimeInSeconds);
				*/
				
				return this.formattedTime(this.averageTimeInSeconds);
			},

			avgAllTimeFormatted() {
				/*
				let averageTimeInSeconds = 0;				
				if (this.totalTimeSpentInSeconds > 0 && this.displayUsers.length > 0) {
					averageTimeInSeconds = this.totalTimeSpentInSeconds / this.displayUsers.length;
				}
				return this.formattedTime(averageTimeInSeconds);
				*/
				
				return this.formattedTime(this.averageAllTimeInSeconds);
			},

			bdFilterIds() {
				let bdFiltersIds = [];
				filterCategories?.forEach(element => {
					if (Object.keys(this.bd_filters['category_' + element.id]).length != 0) {
						const filterId = this.bd_filters['category_' + element.id];
						bdFiltersIds.push(filterId);
					}
				});
				return bdFiltersIds;
			}
		},
		mixins: [
			TaskQueueMixin
		],
		methods: {

			forceRerender(graph) {
				switch(graph) {
					case this.countriesGraph:
						this.countriesKey += 1;
						break;
					case this.hotelsGraph:
						this.hotelsKey += 1;
						break;
					case this.departmentsGraph:
						this.departmentsKey += 1;
						break;
					case this.groupingGraph:
						this.groupingKey += 1;
						break;
					case this.coursesGraph:
						this.coursesKey += 1;
					default:
						// code block
				}
			},
		  

			setAssignedFilters(data) {
				this.assignedFilters = structuredClone(data);
			},
			dateFormat(date) {
				return date.getFullYear() + '-' + `${(date.getMonth() + 1)}`.padStart(2, '0') + '-' + `${date.getDate()}`.padStart(2, '0');
			},

			async loadSelectedFilters() {
				this.loadingAssignedFilters = true;
				axios.get(`/admin/itinerary/${ this.itineraryId }/selected-filters`).then(r => {
					const { data } = r.data;
					this.assignedFilters = data;
				}).finally(() => {
					this.loadingAssignedFilters = false;
				})
			},

			saveSelectedFilters()
			{
				this.$alertify.confirmWithTitle(
					this.$t('CATEGORY_FILTER.CONFIRM_SAVE.TITLE'),
					this.$t('CATEGORY_FILTER.CONFIRM_SAVE.DESCRIPTION'),
					() => {
						axios.post(`/admin/itinerary/${this.itineraryId}/save-selected-filters`, {...this.assignedFilters})
							.then(r => {
								const {error} = r.data;
								if (!error)
								{
									this.$toast.success(this.$t('CATEGORY_FILTER.UPDATE_SUCCESS') + '');
									//this.loadUsers(2);
									this.reloadItinerary(false);
								}
								else this.$toast.error(this.$t('CATEGORY_FILTER.UPDATE_FAILED') + '');
							});
					},
					() => {},
				);
			},

			saveSelectedFiltersRaw()
			{
				axios.post(`/admin/itinerary/${this.itineraryId}/save-selected-filters`, {...this.assignedFilters})
					.then(r => {
						const {error} = r.data;
						if (!error)
						{
							this.$toast.success(this.$t('CATEGORY_FILTER.UPDATE_SUCCESS') + '');
							//this.loadUsers(2);
							this.reloadItinerary(false);
						}
						else this.$toast.error(this.$t('CATEGORY_FILTER.UPDATE_FAILED') + '');
					});
			},

			async reloadItinerary(bFullReload = true) {
				if (bFullReload){					
					await this.$store.dispatch('userFilterModule/fetchCategoryFilterOptions');
					this.loadCourses()
					this.getManagersPaginated();					
					await this.loadUsers(0);
					
								
				}else{
					await this.loadUsers(2);
				}	
				this.allUsersRaw = structuredClone(this.allUsers); 
				this.usersTotalRaw = this.allUsersRaw.length;
				await this.loadUsersTotalsStatusStatistics(bFullReload);
				await this.getTotalTimeSpent();
				this.getAVGTimeSpent();
			},

			formattedTime(timeInSeconds) {
				const hours = Math.floor(timeInSeconds / 3600)+this.$t('HOUR_ABBREVIATION');
				const hoursRest = Math.floor(timeInSeconds % 3600);

				const minutes = Math.floor(hoursRest / 60)+this.$t('MINUTE_ABBREVIATION');
				const seconds = Math.floor(hoursRest % 60)+this.$t('SECOND_ABBREVIATION');

				const formattedTime = `${hours} ${minutes} ${seconds}`;
				return formattedTime.replace(/(\d+)(\s*)(\w+)/g, '<b>$1</b><span>$3</span>');
			},

			async exportItineraryUsersFile() {
				const url = `/admin/itinerary/${this.itineraryId}/export-users`;
			
				try {
					await this.enqueueTask({
						url,
						data: this.prepareData(this.filters),
						messages: {
							success: this.translations.export.success,
							error: this.translations.export.failure
						}
					});
				} catch (error) {
					console.error('Error:', error);
				}
			},

			getUserCourseProgress(user) {
				const size = this.courses.length;
				const progress = size > 0 ? user.completed * 100.0 / size : 0;
				return `${progress}%`
			},

			getChaptersProgress(chapterProgress) {
				const size = chapterProgress.totalChapters;
				const progress = size > 0 ? chapterProgress.finished * 100.0 / size : 0;
				return `${progress}%`
			},

			prepareData(object) {
				const params = new URLSearchParams();
				Object.keys(object).forEach(key => params.append(key, object[key]))
				return params;
			},

			async usersUpdated(payload) {
				await this.loadUsers(1);
			},

			async managersUpdated(payload) {
				await this.getManagersPaginated();
			},

			onUsersChangePage(pageOfUsers) {
				this.pageOfUsers = pageOfUsers;
			},
			onChangeManagerPage(pageOfUsers) {
				this.pageOfManagers = pageOfUsers;
			},

			async loadUsers(type = 0, applyFilters = true) {
				this.loadingUsers = true;
				if (applyFilters != true)
					this.loadingUsersRaw = true;
				try {
					if (this.isTeamManager) {
						const result = await axios.post(`/admin/itinerary/${this.itineraryId}/get-users-paginated`, {})
						const users = result.data.data.users;
						if (users != null) this.users = users.map(obj => ({...obj, origen: 0}));
					} else {
						let filters = {};
						if (applyFilters == true)
							filters = {filters: this.bdFilterIds};

						if (type === 0 || type === 1) {
							const result = await axios.post(`/admin/itinerary/${this.itineraryId}/get-users-paginated`, filters)
							const users = result.data.data.users;
							if (users != null) this.users = users.map(obj => ({...obj, origen: 0}));
						}
						if (type === 0 || type === 2) {
							const result = await axios.post(`/admin/itinerary/${this.itineraryId}/user-in-filters`, filters);
							const users = result.data.data;
							if (users != null) this.usersFromFilters = users.map(obj => ({...obj, origen: 1}));
						}
					}
				} finally {
					this.loadingUsers = false;
					this.loadingUsersRaw = false;
				}
			},

			getAVGTimeSpent() {

				//this.usersStartedCompleted = this.usersTotalCompleted + this.usersTotalStarted;
				this.usersRawStartedCompleted = this.usuariosRawItinerarioCompletado + this.usuariosRawItinerarioProceso;				

				if (this.usersTotalRaw != 0)
					this.averageAllTimeInSeconds = this.totalTimeSpentInSeconds / this.usersTotalRaw;
				else
					this.averageAllTimeInSeconds = this.totalTimeSpentInSeconds;

				if (this.usersRawStartedCompleted != 0)
					this.averageTimeInSeconds = this.totalTimeSpentInSeconds / this.usersRawStartedCompleted;
				else
					this.averageTimeInSeconds = this.totalTimeSpentInSeconds;
				
				
				this.loadingTimeSpent = false;
			},

			async getTotalTimeSpent() {
				const url = `/admin/itinerary/${this.itineraryId}/time-spent`;
				try {
					this.loadingTimeSpent = true;
					await axios.post(url, {
						id: this.id,
						page: 1
					}).then(r => {
						if (!r.data.error) {
							this.totalTimeSpentInSeconds = r.data.data.total_time;
							this.usersStartedCompleted = this.usuariosItinerarioCompletado + this.usuariosItinerarioProceso;
							if (this.usersStartedCompleted != 0)
								this.averageTimeInSeconds = this.totalTimeSpentInSeconds / this.usersStartedCompleted;
							else
								this.averageTimeInSeconds = this.totalTimeSpentInSeconds;
						}
					})
				} finally {
					this.loadingTimeSpent = false;
				}
			},

			async getManagersPaginated() {
				const url = `/admin/itinerary/${this.itineraryId}/get-managers`;
				this.loadingManagers = true;
				try {
					await axios.post(url, {
						id: this.id,
						page: 1
					}).then(r => {
						if (!r.data.error) {
							this.managers = r.data.data.users;
						}
					})
				} finally {
					this.loadingManagers = false;
				}
			},

			async deleteManager(user_id, confirmMessage = 'El usuario perdería acceso al itinerario') {
				const url = `/admin/itinerary/${this.itineraryId}/remove-manager`;
				this.$alertify.confirmWithTitle(
					this.translations.deleteUser.title,
					confirmMessage,
					async () => {
						const {data} = await axios.post(url, {
							id: this.id,
							user_id: user_id
						});
						if (!data.error) {
							this.$toast.success(data.message)
							let index = this.users.findIndex(user => {
								return parseInt(user.id) === parseInt(user_id);
							})
							this.managers.splice(index, 1);
						}
					},
					() => {
					}
				)
			},

			async openUserProgressDetails(itineraryId, userId) {
				const data = {
					itineraryId,
					userId
				};

				this.progressUsers = [];
				const progress = await this.$store.dispatch('userFilterModule/userProgressDetails', data);
				this.progressUsers = progress.data.data;
				let segundos = 0;
				if(this.progressUsers.length){
					this.progressUsers.forEach((e) => {
						if(e.chaptersProgress.totalTime)
							segundos += e.chaptersProgress.totalTime;
					});
					this.totalCourseTime = secondsToTime(segundos);
				}
			},
			async deleteUser(user_id) {
				this.$alertify.confirmWithTitle(
					this.$t('ITINERARY.USER.DELETE.CONFIRM.TITLE'),
					this.$t('ITINERARY.USER.DELETE.CONFIRM.DESCRIPTION'),
					async () => {
						const result = await this.$store.dispatch("userFilterModule/removeUser", {
							url: `/admin/itinerary/${this.itineraryId}/remove-user`,
							data: {
								id: this.id,
								user_id: user_id
							}
						})
						const {error} = result;
						if (error) this.$toast.error(this.$t('ITINERARY.USER.DELETE.FAILED') + '')
						else {
							this.$toast.error(this.$t('ITINERARY.USER.DELETE.SUCCESS') + '')
							let index = this.users.findIndex(user => {
								return parseInt(user.id) === parseInt(user_id);
							})
							this.users.splice(index, 1);
							//location.reload()
						}
					},
					() => {
					}
				)
			},

			tabsAsigned(tabValor){
				if ('URLSearchParams' in window) {
					const url = new URL(window.location);
					url.searchParams.set("tab", tabValor);
					history.pushState(null, '', url);
				}
			}
		,

			changedCoursePosition(event) {
				const newIndex = event.newIndex;
				const oldIndex = event.oldIndex;
				const start = newIndex > oldIndex ? oldIndex : newIndex;
				const end = newIndex > oldIndex ? newIndex : oldIndex;
				let coursesToUpdate = [];
				for (let i = start; i <= end; i++) {
					this.courses[i].position = i + 1;
					coursesToUpdate.push({
						itinerary_course_id: this.courses[i].itinerary_course_id,
						position: this.courses[i].position,
					})
				}
				const url = `/admin/itinerary/${this.itineraryId}/update-position`;
				try {
					axios.post(url, {
						itinerary_id: this.itineraryId,
						itinerary_courses: coursesToUpdate
					}).then(r => {
						if (!r.data.error) {
							this.$toast.success(this.$t('ITINERARY.COURSE.POSITION_UPDATED') + '')
						}
					})
				} finally {

				}
			},

			async loadCourses() {
				this.loadingCourses = true;

				const data = await this.$store.dispatch('userFilterModule/fetchItineraryCourses', this.itineraryId);
				this.selectedCourses = data;
				this.courses = data;

				this.loadingCourses = false;
			},

			filterCategoryCoursesByText(categoryCourses, text) {
				return categoryCourses.filter(c => {
					const needle = text.toLowerCase();
					const haystack = `${c.name} ${c.code}`.toLowerCase();

					return haystack.includes(needle)
				});
			},

			filterCourseByCategory(courses, categoryId) {
				if (!categoryId) return courses;

				return courses.filter(c => c.categoryId == categoryId);
			},

			onSearchChangePage(pageOfItems) {
				this.pageOfCourses = pageOfItems;
			},

			startDrag(event, course) {
				event.dataTransfer.dropEffect = 'move';
				event.dataTransfer.effectAllowed = 'move';
				event.dataTransfer.setData('courseId', course.id)
			},


			/**
			 * **********************************
			 */

			async editCourses() {
				$('#modal-add-courses').modal({
					'show': true,
					static: true,
					backdrop: false,
					keyboard: false
				});
				await this.$store.dispatch('userFilterModule/fetchAvailableCourses');
				//await this.$store.dispatch('userFilterModule/fetchCategoryFilterOptions');
			},

			async addCourse(courseId) {
				let course = this.availableCourses.find(item => item.id == courseId);
				if (course !== undefined)
					this.selectedCourses = [...this.selectedCourses, course];
			},

			async addCourseByDrag(event) {
				let courseId = event.dataTransfer.getData('courseId');
				if (courseId !== undefined)
					this.addCourse(courseId);
			},

			async addCourseByClick(courseId) {
				if (courseId !== undefined)
					this.addCourse(courseId);
			},

			async removeCourse(event) {
				const courseId = event.dataTransfer.getData('courseId');
				this.selectedCourses = this.selectedCourses.filter(item => item?.id != courseId);
			},

			async removeCourseByIndex(course) {
				this.selectedCourses = this.selectedCourses.filter(item => item?.id != course.id);
			},

			async deleteSelectedCourse(index) {
				const url = `/admin/itinerary/${this.itineraryId}/delete-course`;
				this.$alertify.confirmWithTitle(
					this.$t('ITINERARY.COURSE.DELETE.CONFIRM.TITLE'),
					this.$t('ITINERARY.COURSE.DELETE.CONFIRM.DESCRIPTION'),
					async () => {
						const course = this.courses[index];
						try {
							const result = await axios.post(url, {id: course.id})
							const {error, message} = result.data;
							if (error) {
								this.$toast.error(message);
							} else {
								const position = course.position;
								this.$toast.success(message);
								this.courses.splice(index, 1);
								this.courses.forEach(item => {
									if (item.position > position) {
										item.position -= 1;
									}
								})
							}
						} finally {
						}
					},
					() => {
					}
				);
			},

			async saveSelectedCourses() {
				const url = `/admin/itinerary/${this.itineraryId}/save-courses`;
				this.$alertify.confirmWithTitle(
					this.$t('ITINERARY.COURSE.SAVE.CONFIRM.TITLE'),
					this.$t('ITINERARY.COURSE.SAVE.CONFIRM.DESCRIPTION'),
					async () => {
						this.savingCourses = true;
						try {
							await axios.post(url, {
								itinerary_courses_id: this.selectedIds
							}).then(r => {
								if (!r.data.error) {
									this.reloadItinerary()
									$('#modal-add-courses').modal('hide')
									this.successMessage(r.data.message)
								}
							})
						} finally {
							this.savingCourses = false;
						}
					},
					() => {
					}
				);
			},

			imagePath(imageName) {
				return `${this.imageBasePath}/${imageName}`
			},

			successMessage(message) {
				this.$toast.success(message);
			},

			errorMessage(message) {
				this.$toast.error(message);
			},

			defaultBdFilters() {
				let bd_filters = {};
				filterCategories.forEach(element => {
					bd_filters['category_' + element.id] = [];
				});

				return bd_filters;
			},

			async applyFilters() {				
				await this.loadUsers();
				this.showfiltersPeopleSummary();
			},

			showfiltersPeopleSummary(){
				this.showFiltersSummary = true;
				this.filtersPeopleSummary = "";
				filterCategories.forEach(element => {
					if (Object.keys(this.bd_filters['category_' + element.id]).length != 0) {
						let id = element.id;
						this.filtersPeopleSummary = this.filtersPeopleSummary  + " " + this.getFilterName(id) + " ";
					}
				});
				this.appliedCategoryUserFilter = this.filtersPeopleSummary;				
				this.appliedUsersFilters = true;
			},

			clearfiltersPeopleSummary(){
				this.showFiltersSummary = false;
				this.filtersPeopleSummary = "";
			},

			getFilterName(id){
				const select = $("#filter_category_"+id);
				const name = select.find('option:selected').text();
				return (name);
			},

			async clearFilters() {
				filterCategories.forEach(element => {
					this.bd_filters['category_' + element.id] = [];
					const select = $(`filter${element.id}`);
					select.find('option').attr("selected", false);
					select.val(null).trigger('change');
				});
				$('.clear-button').trigger('click');

				this.bd_filters = this.defaultBdFilters();
				
				this.clearfiltersPeopleSummary();

				this.appliedUsersFilters = false;
				this.appliedCategoryUserFilter = null;

				await this.loadUsers();
			},

			openFilter() {
				if (this.showFilters) {
					this.showFilters = false;
					return;
				}
				this.showFilters = true;
			},

			async clearCoursesFilters(){
				const select = $('#filter-course-category');
				select.find('option').attr("selected", false);
				select.val(null).trigger('change');
				this.categoryFilter = null;
				
				this.appliedCoursesFilters = false;
				this.appliedCategoryCourseFilter = null;
			},

			async applyCoursesFilters() {	
				const select = $('#filter-course-category');
				this.categoryFilter = select?.val();
				this.appliedCategoryCourseFilter = select?.find('option:selected').text();
				
				this.appliedCoursesFilters = true;
				
			},

			openCoursesFilter() {
				if (this.showCoursesFilters) {
					this.showCoursesFilters = false;
					return;
				}
				this.showCoursesFilters = true;
			},

			

			async loadUsersTotalsStatusStatistics(bFullReload) {
				let paramReload = 0;
				if (bFullReload == true)
					paramReload = 1;
				const url = `/admin/itinerary/${this.itineraryId}/stats-summary/${paramReload}`;
				try {
					
					await axios.post(url, {
						id: this.id,
						page: 1 
					}).then(r => {
						if (!r.data.error) {
							this.usersTotalManual = r.data.data.usersStats.totalUsers;
							this.usersTotalCompleted = r.data.data.usersStats.totalUsersCompleted;
							this.usersTotalStarted = r.data.data.usersStats.totalUsersStarted;
							//this.totalTimeSpentInSeconds = r.data.data.usersStats.totalUsersTime;
						}
					})
				} finally {
				}
			},

			getStateProcess(state) {
				this.stateProcess = state;
			},
			setUserCourseDetails(detail, courseId) {
				this.courseId = courseId;
				this.userCourseData = detail.courseData;
				this.userCourseDetails = new UserStatsModel(detail.details);
			},
			getUserPercentProgress(completed, total) {
				return Math.round((completed / total) * 100);
			},
			async downloadReport(user_id) {
				const data = { userId: user_id };
				try {
					await this.enqueueTask({
						url: `admin/api/v1/itinerary-stats/${this.itineraryId}/xlsx`,
						data,
						messages: {
							success: `${courseTranslations.export_success}<br/>(${this.$t('COURSE_STATS.EXPORT.ZIP_DIR')})`,
							error: courseTranslations.export_error
						}
					});
				} catch (error) {
					console.error('Error:', error);
				}
			},

			changeTab(tab) {
				this.tab = tab;
			},
			deleteActionModal(){
				$('.action-delete').on('click', function(e) {
					e.preventDefault();
					const url = $(this).attr('href');

					if(itineraryActive){
						$('#modal-delete-button').hide();
						let $modal_body = $('#modal-delete .modal-dialog .modal-content .modal-body');
						let html = '<h4>'+courseTranslations.itinerary_title+'</h4>';
						html += '<p>'+courseTranslations.itinerary_validation+'</p>';
						$modal_body.html(html);

						return false;
					}else{
						$('#modal-delete').modal({ backdrop: true, keyboard: true })
							.off('click', '#modal-delete-button')
							.on('click', '#modal-delete-button', function () {
								let deleteForm = $('#delete-form');
								deleteForm.attr('action', url);
								deleteForm.trigger('submit');
							});
					}
				});
			}
		}
	}).$mount('#itinerary-app')
}