import Vue from 'vue';
import store from './store';
import Highcharts from "highcharts";
import Stock from "highcharts/modules/stock";
import HighchartsVue from "highcharts-vue";
import VueToast from 'vue-toast-notification'

import './filters/index';
import BarChart          from "./components/stats/BarChart";
import PieChart           from "./components/stats/PieChart";
import CoursesTable       from "./components/stats/CoursesTable";
import TrainingTable      from "./components/userStats/TrainingTable";
import LineChart          from "./components/stats/LineChart";
import MasterDetailChart  from "./../admin/stats/MasterDetailChart";
import ModalCourseDetails from "./components/userStats/ModalCourseDetails";
import UserDiplomasModal from "./components/userStats/UserDiplomasModal";
import ExcelGenerator     from "./stats/ExcelGenerator";
import BaseUserModalDelete     from "../base/BaseUserModalDelete.vue" 

import '../../css/transitions.scss';
import '../../css/user.scss';
import '../../css/courseStats.scss'
import 'vue-toast-notification/dist/theme-default.css'
import {getI18nApi}       from "../common/i18n";
import $                  from 'jquery';

import "../registerBaseComponents";

Stock(Highcharts);
Vue.use(HighchartsVue);
Vue.use(VueToast, {
    duration: 5000,
    position: 'top-right',
});

getI18nApi().then((data) => {
    startVueApp(data);
})

function startVueApp({ i18n }) {
    new Vue({
        delimiters: ['${', '}'],
        components: {BarChart, PieChart, CoursesTable, TrainingTable, LineChart, MasterDetailChart, ModalCourseDetails, ExcelGenerator, UserDiplomasModal, BaseUserModalDelete},
        store,
        i18n,
        $,
        data() {
            return {
                id: undefined,
                courseSeries: [[{y: 0}, {y: 0}, {y: 0}],{total: 0}],
                chapterSeries: [{y: 0}, {y: 0}],
                messageSeries: [],
                courses: [],
                loadingLogins: true,
                logins: {
                    type: 'column',
                    series: [],
                },
                loadingPage: true,
                timeSpentByType: [],
                timesColors: undefined,
                itineraries: [],
                itinerariesTranslations: {},
                activePane: 'filter1',
                activePaneAssignedTraining: 'itineraries',
                filterAnnouncement: 'all',
                statusFilter: {
                    finished: this.$t('USERS.STATUS_FINISHED'),
                    inProcess: this.$t('USERS.STATUS_IN_PROCESS'),
                    noStarted: this.$t('NO_STARTING'),
                    all: this.$t('USERS.STATUS_ALL'),
                },
                iconsFilter: {
                    finished: 'fa-check-circle',
                    inProcess: 'fa-spinner',
                    noStarted: 'fa-ban',
                    all: 'fa-layer-group',
                },
                filterTable1: 'all',
                filterTable2: 'all',
                tableData: [],
                voluntaryTableData: [],
                itemSelected: {},
                fullName: '',
                userEmail: ''
            }
        },
        beforeMount() {
            this.$i18n.locale = this.$el.attributes['locale']?.value;
        },
        computed: {
            totalTime() {
              return this.getTimeText(this.timeSpentByType.reduce((acc, cur) => acc + cur.y, 0))
            },
            tableFiltered() {
                let data = this.tableData;
                if (this.activePaneAssignedTraining === 'itineraries')
                    data = this.tableData.filter((item) => item.group === 'Itinerary');
                if (this.activePaneAssignedTraining === 'announcement') {
                    data = (this.filterAnnouncement === 'all') ?
                      this.tableData.filter((item) => item.group === 'Announcement') :
                      this.tableData.filter((item) => item.group === 'Announcement'
                        && item.announcementType === this.filterAnnouncement);
                }
                if (this.activePaneAssignedTraining === 'filters')
                    data = this.tableData.filter((item) => item.group === 'Filter');

                return this.filterArrayByStatus(data, this.filterTable1);
            },
            tableFiltered2() {
                return this.filterArrayByStatus(this.voluntaryTableData, this.filterTable2);
            },
            today() {
                const dateObj = new Date()
                return `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(
                  dateObj.getDate()
                ).padStart(2, '0')}`
            },
            excelData() {
                return {
                    sheets: [
                        {
                            name: this.$t('ANNOUNCEMENT_OBSERVATION.STEP-1'),
                            tables: [
                                {
                                    name: '',
                                    key: 'generalInfo',
                                    cells: [
                                      { name: 'UserId', key: 'userId' },
                                      { name: 'Username', key: 'userName' },
                                      { name: 'Email', key: 'email' },
                                      { name: 'Cursos asignados comenzados', key: 'totaStartedlAssigned' },
                                      { name: 'Cursos asignados finalizados', key: 'totalFinishedAssigned' },
                                      { name: 'Cursos voluntarios comenzados', key: 'totalStartedVoluntary' },
                                      { name: 'Cursos voluntarios finalizados', key: 'totalFinishedVoluntary' },
                                      { name: 'Horas totales de formación', key: 'totalFormation' },
                                    ]
                                },
                            ]
                        },
                        {
                            name: this.$t('USERS.TABLE_TITLE1'),
                            tables: [
                                {
                                    name: '',
                                    key: 'assignedFormation',
                                    cells: [
                                      { name: 'Course ID', key: 'id' },
                                      { name: 'Course name', key: 'title' },
                                      { name: 'Tipo', key: 'announcementType' },
                                      { name: 'Fecha de inicio', key: 'dateStart' },
                                      { name: 'Fecha de fin', key: 'dateEnd' },
                                      { name: 'Estado', key: 'statusName' },
                                      { name: 'Tiempo total', key: 'timeSpent' },
                                      { name: 'Titulo itinerario', key: 'typeItinerary' },
                                    ]
                                },
                            ]
                        },
                        {
                            name: this.$t('USERS.TABLE_TITLE2'),
                            tables: [
                                {
                                    name: '',
                                    key: 'voluntaryFormation',
                                    cells: [
                                        { name: 'Course ID', key: 'id' },
                                        { name: 'Course name', key: 'title' },
                                        { name: 'Tipo', key: 'announcementType' },
                                        { name: 'Fecha de inicio', key: 'dateStart' },
                                        { name: 'Fecha de fin', key: 'dateEnd' },
                                        { name: 'Estado', key: 'statusName' },
                                        { name: 'Tiempo total', key: 'timeSpent' },
                                    ]
                                },
                            ]
                        }
                    ],
                    sourceData: {
                        generalInfo: {
                            userId: this.id,
                            userName: this.fullName,
                            email: this.userEmail,
                            totaStartedlAssigned: this.courseSeries[0][1].y,
                            totalFinishedAssigned: this.courseSeries[0][0].y,
                            totalStartedVoluntary: this.chapterSeries[1].y,
                            totalFinishedVoluntary: this.chapterSeries[0].y,
                            totalFormation: this.totalTime,
                        },
                        assignedFormation: this.tableData,
                        voluntaryFormation: this.voluntaryTableData,
                    },
                    fileName: `Stats_${this.today}_${this.fullName}`
                }
            }
        },

        async created() {
            this.id = userId;
            this.userEmail = userEmail;
            this.fullName = userFullName;

            await this.$store.dispatch('userModule/fetchCourses', this.id)
              .then(courses => {
                  this.courseSeries = courses;
              });

            await this.$store.dispatch('userModule/fetchGeneralData', this.id).then((data) => {
                const itineraries = this.getTableData(data.itineraries, 'Itinerary')
                const filters = this.getTableData(data.coursesFilters, 'Filter')
                const announcements = this.getTableData(data.announcements, 'Announcement')
                this.voluntaryTableData = this.getTableData(data.voluntaryTraining, 'Filter')

                this.tableData = [...announcements, ...itineraries, ...filters]
            })

            await this.$store.dispatch('userModule/fetchChapters', this.id)
              .then(chapters => this.chapterSeries = chapters);

            await this.$store.dispatch('userModule/fetchMessages', this.id)
              .then(messages => this.messageSeries = messages);

            await this.$store.dispatch('userModule/fetchCoursesList', this.id)
              .then(courses => this.courses = courses);

            await this.$store.dispatch('userModule/fetchUserLogins', this.id)
              .then(data => {
                  this.logins.series = data.map((item) =>
                    [new Date(`${item.date} 00:00:01`).getTime(), item.count])
              }).finally(() => {
                  this.loadingLogins = false;
              });

            await this.$store.dispatch('userModule/fetchTimeSpentByType', this.id)
              .then(timeSpent => {
                  const maxValue = timeSpent[0].y;
                  this.timeSpentByType = timeSpent.map(time => {
                      const iconName = time.name.toLowerCase();
                      const icon = iconName === 'otros' ? 'noimg.svg' : `${iconName}.svg`;
                      const percentage = parseInt((time.y / maxValue) * 50, 10);
                      return Object.assign(time, { icon, percentage: `${percentage}%`, y: +time.y });
                  });
              });

            await this.$store.dispatch('userModule/fetchUserItinerary', this.id).then(itinerariesData => {
                let {itineraries, tableTitlesTranslations} = itinerariesData;
                this.itineraries = [].concat(itineraries);
                this.itinerariesTranslations = tableTitlesTranslations;
            });

            this.loadingPage = false
        },

        methods:{
            async deleteElement(){
                await this.$store.dispatch('userModule/deleteUser', this.id).then(redirect => {
                    this.$toast.success(this.$t("USER.DELETE_MESSAGE.SUCESS"));
                    window.location.href = redirect;
                });
            },
            setTimesColors(colors) {
                this.timesColors = colors;
            },
            getRandomValue(min, max) {
                return Math.floor(Math.random() * (max - min + 1)) + min;
            },
            filterArrayByStatus(arr, filter) {
                if (filter === 'finished')
                    return arr.filter((item) => item.status === 'FINISHED');
                if (filter === 'inProcess')
                    return arr.filter((item) => item.status === 'IN_PROCESS');
                if (filter === 'noStarted')
                    return arr.filter((item) => item.status === 'NO_STARTED');
                return arr;
            },
            showDetails(data) {
                this.itemSelected = data
            },
            getTableData(list = [], group = '') { 
                const icons = [null, 'teleformation', 'presential', 'mixed', 'virtual'];
                const types = [null, 'online', 'on_site', 'mixed', 'virtual_classroom']
                const statusList = {
                    FINISHED: this.$t('USERS.STATUS_FINISHED'),
                    IN_PROCESS: this.$t('USERS.STATUS_IN_PROCESS'),
                    NO_STARTED: this.$t('NO_STARTING'),
                }
                const statusAnnouncement = {
                    FINISHED: this.$t('ANNOUNCEMENT.STATS_REPORT.STATUS_FINISHED'),
                    IN_PROCESS: this.$t('ANNOUNCEMENT.STATS_REPORT.STATUS_IN_PROCESS'),
                    NO_STARTED: this.$t('ANNOUNCEMENT.STATS_REPORT.STATUS_NO_STARTED'),
                }
                return list.map((item) => ({
                    urlCourse: item.urlCourse,
                    thumbnail: item.thumbnail,
                    title: item.name,
                    category: item.category?.name || '',
                    typeItinerary: item.typeItinerary?.name || '',
                    informationType: item.type?.name || '',
                    // icon: icons[item.type?.id || 1] || 'teleformation',
                    dateStart: item.startAt || '',
                    dateEnd: item.finishAt || '',
                    status: item.state || 'NO_STARTED',
                    statusName: statusList[item.state || 'NO_STARTED'],
                    timeSpent: item.timeSpent || '00:00:00',
                    icon: item.icon || null,
                    details: item.chapter || [],
                    chapterDetails: item.chapterDetails || [],
                    survey: item.survey || undefined,
                    totalChapters: item.totalChapters || 0,
                    chapterStarted: item.chapterStarted || 0,
                    chaptersFinished: item.chaptersFinished || 0,
                    finished: item.finished || '',
                    id: item.id,
                    announcementType: types[item.type?.id || 1] || 'Teleformation',
                    group,
                    announcementStatus: statusAnnouncement[item.extraAnnouncement?.state || 'NO_STARTED'],
                }))
            },
            getTimeText(time) {
                const hours = Math.trunc(time / 3600)
                let seconds = time - (hours * 3600)
                const minutes = Math.trunc(seconds / 60)
                seconds = seconds - (minutes * 60)

                const hourText = `${hours} ${this.$tc('TIME.HOURS', [hours])}`
                const minuteText = `${minutes} ${this.$tc('TIME.MINUTES', [minutes])}`
                const secondText = `${seconds} ${this.$tc('TIME.SECONDS', [seconds])}`

                if (hours) {
                    if (minutes && seconds) return `${hourText}, ${minuteText} ${this.$t('TIME.AND')} ${secondText}`
                    if (minutes) return `${hourText} ${this.$t('TIME.AND')} ${minuteText}`
                    if (seconds) return `${hourText} ${this.$t('TIME.AND')} ${secondText}`
                    return `${hourText}`
                }
                if (minutes) {
                    return (seconds) ? `${minuteText} ${this.$t('TIME.AND')} ${secondText}` : `${minuteText}`
                }
                return (seconds) ? `${secondText}` : '--'
            }
        },
    }).$mount('#user-stats');
}
