import Vue from "vue";
import store from "./store";
import Highcharts, { color } from "highcharts";
import Stock from "highcharts/modules/stock";
import noData from "highcharts/modules/no-data-to-display";
import HighchartsVue from "highcharts-vue";
import "./filters/index";

import LineChart from "./components/stats/LineChart";
import LineChartDouble from "./components/stats/LineChartDouble";
import Pie<PERSON>hart from "./components/stats/PieChart";


import "../../css/transitions.scss";

Stock(Highcharts);
noData(Highcharts);

Vue.use(HighchartsVue);

new Vue({
  delimiters: ["${", "}"],
  components: {
    LineChart,
    LineChartDouble,
    PieChart
  },
  store,
  data() {
    return {
      trainedUsers: undefined,
      uniqueLogins: undefined,
      courses: undefined,
      ratings: undefined,
      time: undefined,
      categories: undefined,
      trainedUsersChart: undefined,
      uniqueLoginsChart: undefined,
      coursesStartedChart: undefined,
      coursesFinishedChart: undefined,
      ratingsChart: undefined,
      timeChart: undefined,
      categoriesChart: undefined,
    };
  },

  async created() {
    await this.loadData();
  },

  methods: {
    async loadData() {
      this.trainedUsers             = await this.$store.dispatch("accumulativeStatsModule/fetchTrainedUsers");
      this.uniqueLogins             = await this.$store.dispatch("accumulativeStatsModule/fetchUniqueLogins");
      this.courses                  = await this.$store.dispatch("accumulativeStatsModule/fetchCourses");
      this.ratings                  = await this.$store.dispatch("accumulativeStatsModule/fetchRatings");
      this.time                     = await this.$store.dispatch("accumulativeStatsModule/fetchTime");
      this.categories               = await this.$store.dispatch("accumulativeStatsModule/fetchFilterCategories");
      this.trainedUsersChart        = this.getDataChartLinear(this.trainedUsers, 'var(--color-primary)','var(--color-primary-darkest)');   
      this.uniqueLoginsChart        = this.getDataChartLinear(this.uniqueLogins, 'var(--color-primary)','var(--color-primary-darkest)');  
      this.coursesStartedChart      = this.getDataChartLinear(this.startedCourses, 'var(--color-primary)','var(--color-primary-darkest)'); 
      this.coursesFinishedChart     = this.getDataChartLinear(this.finishedCourses, 'var(--color-primary)','var(--color-primary-darkest)');  
      this.ratingsChart             = this.getDataChartLinear(this.ratings, 'var(--color-primary)','var(--color-primary-darkest)');  
      this.timeChart                = this.getDataChartLinear(this.time, 'var(--color-primary)','var(--color-primary-darkest)');  
      this.categoriesChart           =this.getDataPieChart();         
    },

    getDataChartLinear(data, color1, color2){
        const keys = Object.keys(data);

        const categories = keys.map((keyData) => {
            const month = keyData.substring(0, 2);
            const year = keyData.substring(3);
            return month + "-" + year;
          });
    
          const seriesNew = keys.map((keyData) => {
            return parseInt(data[keyData].period);
          });
    
          const seriesAccumulated = keys.map((keyData) => {
            return parseInt(data[keyData].total);
          });
    
          return {
            categories: categories,
            series: [
              {
                name: this.$t('NEW_PLURAL'),
                color: color1,
                data: seriesNew,
                dataLabels: {
                    enabled: true,
                    color: color1,
                    style: {
                      textOutline: 'none'
                    }
                  },
              },
              {
                name: this.$t('ACCUMULATED'),
                color: color2,
                data: seriesAccumulated,
                dataLabels: {
                    enabled: true,
                    color: color2,
                    style: {
                      textOutline: 'none'
                    }
                  },
              },
            ],
          };
    },

    getDataPieChart(){
        const categories = this.categories;
        return categories.map(cat => ({
                 "name" : cat.name,
                 "filters": cat.filters,
                 "seriesData" : cat.filters?.map(series => ({
                     "name" : series.name,
                     "y"    : series.count
                 })
            )}                     
        ));
    }    
  },

  computed: {
    startedCourses() {
      return this.courses?.started;
    },

    finishedCourses() {
      return this.courses?.finished;
    },
  },

  filters: {
    formatNumber: (number) => {
      if (!number) return 0;

      return number.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
    },
  },
}).$mount("#accumulative-stats");
