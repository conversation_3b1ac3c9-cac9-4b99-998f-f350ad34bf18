<script>
import Spinner from "../../../admin/components/base/Spinner.vue";
import {get, sync} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import JwPagination from 'jw-vue-pagination';
import Multiselect from "vue-multiselect";

const PAGE_SIZE = 10;
const customLabels = {
    first: '<<',
    last: '>>',
    previous: '<',
    next: '>'
};

export default {
  name: "SettingGroup",
  components: {BaseSwitch, Spinner, JwPagination, Multiselect},    
  data() {
    return {
      customLabels,
      defaultValue: { id: '', title: '' },
      groups: [],
      settings:[],
      allSettings:[],
      btnFilter: true,
      btnClearFilter: false,
    };
  },
  computed: {
    loading: get('catalogModule/loading'),
    catalogs: sync('catalogModule/catalogs'), 
  },
  async created() { 
    const settings = await this.$store.dispatch('catalogModule/load', '/admin/setting/all'); 
    this.groups= settings.data[0].groups;
    this.settings = this.allSettings = settings.data;
  },
  methods: {
    filterActive(){
      this.btnFilter = !this.btnFilter;
    },
    clearfilter(){
      this.groups = this.allSettings[0].groups;
      this.settings = this.allSettings;
      this.btnClearFilter = false;
      this.defaultValue.id= '';
      this.defaultValue.title= '';
    },
    selectFilter(){
      let filterSettings=[];

      this.allSettings.forEach((item) => {
        if(item.settingGroupID=== this.defaultValue.id) filterSettings.push(item);      
      });

      this.settings = filterSettings;     
      this.filterActive();
      this.btnClearFilter = true;
    },
    onChangePage(settings) {
        this.settings = settings;
    },
    deleteSetting(config){
      this.$alertify.confirmWithTitle(
          this.$t('Realmente deseas borrar la configuración?'),
          this.$t(config.name),
          () => {
            let setting= {
              id: config.id,
            }
            const endpoint = '/admin/setting/delete';

            const save = () => {
              return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: setting });
            }

            save().then(r => {
              const { error, data } = r;
              if (error) this.$toast.error(data);
              else {
                this.$toast.success(this.$t('CATALOG.SAVED') + '');
                this.$store.dispatch('catalogModule/load', '/admin/setting/all');
              }
            })          
              
          },
          () => {},
      )      
    }
  }
}
</script>

<template>
  <div>
    <div class="d-flex align-items-center justify-content-end">
      <div class="col-md-4 col-xs-12 mr-auto justify-content-end">
        <div v-if="btnClearFilter">
          <button class="btn btn-danger"  @click="clearfilter()"  style="margin: 0.5rem">
            <i class="fa fa-trash" >{{ $t('CATALOG.SETTING.CLEAR_FILTERS') }}</i>
          </button>
        </div>
        <div v-if="btnFilter">
          <label>{{ defaultValue.title }}</label>
          <button class="btn btn-primary"  @click="filterActive()">
            <i class="fa fa-filter" >{{ $t('FILTERS') }}</i>
          </button>
        </div>
        <div v-else>
          <label>{{ $t('GROUP') }}</label>
          <multiselect v-model="defaultValue" 
            :options="groups" 
            placeholder="Select one" 
            label="title" 
            track-by="title"
            @input="selectFilter"
          >
          </multiselect> 
        </div>
      </div>
      <router-link type="button" class="btn btn-primary"
                   :to="{name: 'SettingCreate', params: this.$route.params}"
      >
        {{ $t('COMMON.CREATE') }}
      </router-link>
    </div>    
    <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>    

    <table class="table table-condensed mt-3" v-else>
      <thead>
      <tr>
        <th></th>
        <th>{{ $t('GROUP') }}</th>
        <th>{{ $t('NAME') }}</th>
        <th>{{ $t('DESCRIPTION') }}</th>
        <th>{{ $t('TYPE') }}</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(c, index) in settings" :key="c.id">
        <td></td>
        <td>{{ c.settingGroupTitle }}</td>
        <td>{{ c.name }}</td>
        <td>{{ c.description }}</td>
        <td>{{ c.type }}</td>
        <td>
          <div class="dropdown">
            <button class="btn btn-default" type="button" :id="`dropdown-menu-${c.id}`" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fa fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${c.id}`">
              <li><router-link class="dropdown-item" :to="{ name: 'SettingUpdate', params: {...$route.params, id: c.id} }">{{ $t('EDIT') }}</router-link></li>
              
              <li><a class="dropdown-item delete" @click="deleteSetting(c)">{{ $t('DELETE') }}</a></li>
            </ul>
          </div>
        </td>
      </tr>      
      <tr v-if="settings.length>9">
        <td colspan="6">
          <div class="d-flex align-items-center justify-content-start"  >
            <jw-pagination 
              :items="catalogs"
              @changePage="onChangePage"
              :labels="customLabels"
            ></jw-pagination>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss">

</style>
