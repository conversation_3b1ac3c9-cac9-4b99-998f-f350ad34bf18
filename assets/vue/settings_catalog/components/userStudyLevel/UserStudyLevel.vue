<script>
import Spinner from "../../../admin/components/base/Spinner.vue";
import {get, sync} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import JwPagination from 'jw-vue-pagination';
import Multiselect from "vue-multiselect";

const PAGE_SIZE = 10;
const customLabels = {
    first: '<<',
    last: '>>',
    previous: '<',
    next: '>'
};

export default {
  name: "UserStudyLevel", 
  components: {BaseSwitch, Spinner, JwPagination, Multiselect},    
  data() {
    return {
      customLabels,
      defaultValue: '',
      userStudyLevel:[],
      allUserStudyLevel:[],
      btnFilter: true,
      btnClearFilter: false,
    };
  },
  computed: {
    loading: get('catalogModule/loading'), 
    catalogs: sync('catalogModule/catalogs'), 
  },
  async created() { 
    const userStudyLevel = await this.$store.dispatch('catalogModule/load', '/admin/userStudyLevel/all'); 
    console.log(userStudyLevel);
    this.userStudyLevel = this.allUserStudyLevel = userStudyLevel.data;
  },
  methods: {    
    changeActiveStatus(index) {
      const value = this.userStudyLevel[index];
      this.$store.dispatch('catalogModule/changeActiveState',
          {endpoint: `/admin/userStudyLevel/${value.id}/state`, data: {id: value.id, state: value.state}}).then(r => {

      });
    },
    filterActive(){
      this.btnFilter = !this.btnFilter;
    },
    clearfilter(){
      this.userStudyLevel = this.allUserStudyLevel;
      this.btnClearFilter = false;
      this.defaultValue= '';
    },
    selectFilter(){
      let filterSettings=[];

      this.allUserStudyLevel.forEach((item) => {
        if(item.name === this.defaultValue) filterSettings.push(item);      
      });

      this.userStudyLevel = filterSettings;     
      this.filterActive();
      this.btnClearFilter = true;
    },
    onChangePage(userStudyLevel) {
        this.userStudyLevel = userStudyLevel;
    },
  }
}
</script>

<template>
  <div>
    <div class="d-flex align-items-center justify-content-end">
      <!-- <div class="col-md-4 col-xs-12 mr-auto justify-content-end">
        <div v-if="btnClearFilter">
          <button class="btn btn-danger"  @click="clearfilter()"  style="margin: 0.5rem">
            <i class="fa fa-trash" >{{ $t('Clear filters') }}</i>
          </button>
        </div>
        <div v-if="btnFilter">
          <label>{{ defaultValue }}</label>
          <button class="btn btn-primary"  @click="filterActive()">
            <i class="fa fa-filter" >{{ $t('filters') }}</i>
          </button>
        </div>
        <div v-else>
          <label>{{ $t('UserStudyLevel') }}</label>
          <input type="text" class="form-control" v-model="defaultValue">
          <button class="btn btn-primary"  @click="selectFilter()">
            <i class="fa fa-filter" >{{ $t('filters') }}</i>
          </button>
        </div>
      </div> -->
      <router-link type="button" class="btn btn-primary"
                   :to="{name: 'UserStudyLevelCreate', params: this.$route.params}"
      >
        {{ $t('COMMON.CREATE') }}
      </router-link>
    </div>    
    <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>    

    <table class="table table-condensed mt-3" v-else>
      <thead>
      <tr>
        <th></th>
        <th>{{ $t('NAME') }}</th>
        <th>{{ $t('DESCRIPTION') }}</th>
        <th>{{ $t('STATE') }}</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(c, index) in userStudyLevel" :key="c.id">
        <td></td>
        <td>{{ c.name }}</td>
        <td>{{ c.description }}</td>
        <td>
          <BaseSwitch :tag="`switcher-UserStudyLevel-${c.id}`" v-model="c.state" @change="changeActiveStatus(index)" />
        </td>
        <td>
          <div class="dropdown">
            <button class="btn btn-default" type="button" :id="`dropdown-menu-${c.id}`" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fa fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${c.id}`">
              <li><router-link class="dropdown-item" :to="{ name: 'UserStudyLevelUpdate', params: {...$route.params, id: c.id} }">{{ $t('EDIT') }}</router-link></li>
            </ul>
          </div>
        </td>
      </tr>      
      <tr v-if="userStudyLevel.length>9">
        <td colspan="6">
          <div class="d-flex align-items-center justify-content-start"  >
            <jw-pagination 
              :items="userStudyLevel"
              @changePage="onChangePage"
              :labels="customLabels"
            ></jw-pagination>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss">

</style>
