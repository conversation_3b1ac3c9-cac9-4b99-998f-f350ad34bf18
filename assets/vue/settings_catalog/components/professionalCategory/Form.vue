<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm},
  data() {
    return {
      locale: 'es',
      professionalCategory: {
        id: -1,
        name: '',
        description: '',
        state: true,
      },
    };
  },
  computed: {
    catalogs: get('catalogModule/catalogs'),
    locales: get('localeModule/locales'),
  },
  created() {
    // if (this.catalogs.length === 0) {
    //   this.returnToList();
    //   return;
    // }

    let professionalCategory = {
        id: -1,
        name: '',
        description: '',
        state: true, 
      };

    if (this.$route.name === 'ProfessionalCategoryUpdate') {
      professionalCategory = this.catalogs.find(c => c.id === this.$route.params.id);

      if (professionalCategory === undefined) {
        this.returnToList();
        return;
      }
    }

    this.professionalCategory = professionalCategory;
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'ProfessionalCategory', params: this.$route.params});
    },
    submit() {
      const update = this.$route.name === 'ProfessionalCategoryUpdate';
      const endpoint = update ? '/admin/professionalCategory/update' : '/admin/professionalCategory/create';
      const save = () => {
        return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.professionalCategory });
      }

      save().then(r => {
        const { error, data } = r;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t('CATALOG.SAVED') + '');
          this.returnToList();
        }
      })
    }
  }
}
</script>

<template>
  <base-form v-model="locale" @cancel="returnToList()" @submit="submit()">
    <template v-slot:form>

      <div class="form-group col-12">
        <label>{{ $t('NAME') }}</label>
        <input type="text" class="form-control" v-model="professionalCategory.name">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('DESCRIPTION') }}</label>      
        <textarea
            type="text"
            class="form-control"
            name="description"
            v-model="professionalCategory.description"
            rows="5"
          />
      </div>

      <div class="form-group col-12">
        <label>{{ $t('STATE') }}</label>
        <BaseSwitch v-model="professionalCategory.state" />
      </div>

    </template>
  </base-form>
</template>

<style scoped lang="scss">

</style>
