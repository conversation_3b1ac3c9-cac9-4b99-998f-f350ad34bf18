<script>
import Spinner from "../../../admin/components/base/Spinner.vue";
import {get, sync} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";

export default {
  name: "SettingGroup",
  components: {BaseSwitch, Spinner},
  computed: {
    loading: get('catalogModule/loading'),
    catalogs: sync('catalogModule/catalogs'),
  }, 

  created() { 
    this.$store.dispatch('catalogModule/load', '/admin/setting-group/all'); 
  },
  methods: {
    deleteGroup(group){
      this.$alertify.confirmWithTitle(
          this.$t('Realmente deseas borrar el grupo?'),
          this.$t(group.title),
          () => {
            let settingGroup= {
              id: group.id,
              title: group.title,
              sort: group.sort
            }
            const endpoint = '/admin/setting-group/delete';

            const save = () => {
              return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: settingGroup });
            }

            save().then(r => {
              const { error, data } = r;
              if (error) this.$toast.error(data);
              else {
                this.$toast.success(this.$t('CATALOG.SAVED') + '');
                this.$store.dispatch('catalogModule/load', '/admin/setting-group/all');
              }
            })          
              
          },
          () => {},
      )      
    }
  }
}
</script>

<template>
  <div>
    <div class="col-12 d-flex align-items-center justify-content-end">
      <router-link type="button" class="btn btn-primary"
                   :to="{name: 'SettingGroupCreate', params: this.$route.params}"
      >
        {{ $t('COMMON.CREATE') }}
      </router-link>
    </div>
    <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>    

    <table class="table table-condensed mt-3" v-else>
      <thead>
      <tr>
        <th></th>
        <th>{{ $t('TITLE') }}</th>
        <th>{{ $t('CATALOG.SETTING.SORT') }}</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(c, index) in catalogs" :key="c.id">
        <td></td>
        <td>{{ c.title }}</td>
        <td>{{ c.sort }}</td>
        <td>
          <div class="dropdown">
            <button class="btn btn-default" type="button" :id="`dropdown-menu-${c.id}`" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fa fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${c.id}`">
              <li><router-link class="dropdown-item" :to="{ name: 'SettingGroupUpdate', params: {...$route.params, id: c.id} }">{{ $t('EDIT') }}</router-link></li>
              
              <li v-if="c.canDelete"><a class="dropdown-item delete" @click="deleteGroup(c)">{{ $t('DELETE') }}</a></li>
            </ul>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss">

</style>
