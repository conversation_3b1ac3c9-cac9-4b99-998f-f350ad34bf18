<script>
import MappingForm from "./MappingForm.vue";

export default {
  name: "MappingModal",
  components: {MappingForm},
  props: {
    tag: {
      type: String,
      default: () => (Date.now() + '')
    },
    value: null
  },
  data() {
    return {
    };
  },
  computed: {
    innerValue: {
      get() {
        return this.value ?? {};
      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    }
  },
  methods: {

  }
}
</script>

<template>
  <div
      class="modal fade"
      :id="tag"
      tabindex="-1"
      aria-labelledby="baseModal"
      aria-hidden="true"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
  >
    <div :class="`modal-dialog modal-dialog-centered modal-lg`">
      <div class="modal-content">
        <div class="modal-body">
          <div class="w-100 d-flex justify-content-end">
            <button
                :id="tag + '_close'"
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
                :data-modal-id="'#' + tag"
                @click="$emit('save', null);"
            >
            </button>
          </div>
          <mapping-form v-model="innerValue"/>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                  :data-modal-id="'#' + tag"
                  @click="$emit('save', innerValue)">Guardar</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
