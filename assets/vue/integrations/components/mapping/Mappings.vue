<script>
import {sync} from "vuex-pathify";
import MappingModal from "./MappingModal.vue";
import MappingForm from "./MappingForm.vue";

export default {
  name: "Mappings",
  components: {MappingForm, MappingModal},
  data() {
    return {
      mapping: null,
      edit: false
    };
  },
  computed: {
    mappings: sync('integrationMappingModule/current@mappings'),
    innerValue: {
      get() {
        return this.mappings ?? [];
      },
      set(newValue) {
        this.mappings = newValue;
      }
    }
  },
  methods: {
    addMapping() {
      this.mapping = {
        entity: null,
        identifier: null,
        type: 'FILTER',// FILTER|ENTITY
        main: false,
        group: null,
        mapping: []
      };
    },

    addNewMapping() {
      const mappings = structuredClone(this.innerValue);
      const index = mappings.findIndex(m => m.type === this.mapping.type && m.id === this.mapping.id);
      if (index >= 0 && this.edit) {
        mappings[index] = this.mapping;
        this.updateInnerValue(mappings);
        this.mapping = null;
        return;
      }

      if (this.mapping.type === 'FILTER') {
        const found = mappings.find(m => m.type === this.mapping.type && m.entity === this.mapping.entity);
        if (found !== undefined) {
          this.$toast.error('Mapeo para filtros ya existe. Editar el existente');
          return;
        }
      } else {
        const entity = mappings.find(m => m.type === 'ENTITY' && m.entity === this.mapping.entity);
        if (entity !== undefined) {
          this.$toast.error('Mapeo para tabla: ' + this.mapping.entity + ' ya existe. Editar el existente');
          return;
        }
      }

      if (this.mapping?.mapping?.length === 0) return;
      mappings.push(this.mapping);
      this.updateInnerValue(mappings);
      this.mapping = null;
    },

    updateInnerValue(mappings) {
      this.innerValue = mappings;
      this.$store.dispatch('integrationMappingModule/setCurrentGroupMapping', this.innerValue);
    },

    selectMapping(m, edit) {
      this.edit = edit;
      this.mapping = structuredClone(m);
    }
  }
}
</script>

<template>
  <div class="Mappings w-100">
    <div class="col-12">
      <button type="button" class="btn btn-primary" @click="addMapping">Add Mapping
      </button>
      <button type="button" style="display: none;" id="btn-modal-add-mapping" data-bs-toggle="modal"
              data-bs-target="#modal-add-mapping"></button>
    </div>
    <div class="col-12" v-if="mapping == null">
      <table class="table">
        <thead>
        <tr>
          <th>Tipo</th>
          <th>Entity</th>
          <th>Identifier</th>
          <th>Mapping</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(m, i) in innerValue" :key="`group-endpoint-${i}`">
          <th><strong>{{ m.type }}</strong></th>
          <td>{{ m.entity ?? 'ENTITY' }}</td>
          <td>{{ m.identifier ?? 'UNDEFINED' }}</td>
          <td>
            <button type="button" class="btn btn-sm btn-warning" @click="selectMapping(m, true)"><i class="fa fa-edit"></i></button>
            <button type="button" class="btn btn-primary btn-sm" @click="selectMapping(m)"><i class="fa fa-eye"></i></button>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div v-else class="col-12">
      <mapping-form v-model="mapping"/>
      <div class="col-12 d-flex align-items-center justify-content-end p-1 gap-1">
        <button type="button" class="btn btn-warning" @click="mapping = null;">Cancelar</button>
        <button type="button" class="btn btn-primary"
                @click="addNewMapping">Guardar</button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
