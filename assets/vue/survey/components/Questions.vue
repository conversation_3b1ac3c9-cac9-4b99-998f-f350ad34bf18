<template>
  <div class="Questions">
    <div class="Questions--header col-12 d-flex align-items-center justify-content-end">
      <h3 class="mr-auto">{{ $t('SURVEY.ADD_QUESTIONS') }}</h3>
      <button @click="addQuestion()" type="button" class="btn btn-primary"><i class="fa fa-plus"></i> {{ $t('SURVEY.ADD_QUESTION') }}</button>
    </div>

    <div class="w-100 ">
      <table class="Questions--table table p-4">
        <thead>
        <tr>
          <th>{{ $t('PAGES.SORT') }}</th>
          <th></th>
          <th>{{ $t('SURVEY.STATEMENT') }}</th>
          <th>{{ $t('TYPE') }}</th>
          <th>{{ $t('ACTIVATE') }}</th>
          <th  class="text-right">{{ $t('ACTIONS') }}</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(question, index) in questionsLocal" :key="index"
            draggable
            @dragstart="startDrag($event, index)"
            @drop="onDrop(index)"
            @dragover.prevent
            @dragenter.prevent
        >
          <td ><i class="fa fa-bars dragBar"></i></td>
          <td>{{ question.position==0? index+1:question.position}}</td>
          <td>{{ getStatement(index) }}</td>
          <td><span :class="getQuestionTypeIcon(question.type)"></span></td>
          <td>
            <div class="custom-control custom-switch">
              <input type="checkbox" class="custom-control-input" :id="'switch_survey_' + index" v-model="question.active" @change="activateQuestion(question)"
              :disabled="question.main"
              >
              <label class="custom-control-label" :for="'switch_survey_' + index"></label>
            </div>
          </td>
          <td class="text-right">
            <button type="button" class="btn btn-sm btn-primary" @click="updateQuestion(question, index)"><i class="fa fa-pencil"></i></button>
            <button type="button" class="btn btn-sm btn-danger" @click="deleteQuestion(question, index)" v-if="!question.main"><i class="fa fa-trash"></i></button>
          </td>
        </tr>
        </tbody>
      </table>
    </div>

    <div class="modal fade" id="new-question-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="new-question-modal" aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="btn-close" @click="closeModal()" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <NewQuestion :realtime="realtime" @question="onNewQuestion" :question-prop="editQuestion"></NewQuestion>
          </div>
        </div>
      </div> 
    </div>
  </div>
</template>

<script>
import NewQuestion from "./NewQuestion.vue";
import $ from "jquery";
import {get, sync} from "vuex-pathify";

// Handle questions with v-model
export default {
  name: "Questions",
  components: {NewQuestion},
  props: {
    value: {
      type: Object|Array,
      default: function () {
        return [];
      }
    },
    realtime: {
      type: Boolean,
      default: true
    },
    surveyId: {
      type: Number|String,
      default: null
    }
  },
  data() {
    return {
      editQuestion: null,
      editQuestionIndex: -1,
      questionsLocal:[],
      dragItem: null,
      dropItem: null,
    };
  },
  computed: {
    ...get("surveyModule", ["getUserLocale"]),
    questionTypes: get('configModule/config@questionTypes'), 
    questions: sync('surveyModule/form@questions'),
    defaultLocale: get("localeModule/defaultLocale"),
    innerValue: {
      get() { 
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    },
    userLocale() {
      return this.getUserLocale();
    },
  },
  created() {
    this.questionsLocal = this.innerValue
  },
  methods: {  
    getStatement(index){
      let translation = this.questionsLocal[index].translations.find(t => t.locale === this.userLocale);
      return translation ? translation.question : this.questionsLocal[index].statement;
    },
    startDrag(evt, index) {
      this.dragItem = index;
      evt.dataTransfer.dropEffect = 'move'
      evt.dataTransfer.effectAllowed = 'move'
    },
    async onDrop(index) { 
      let data = this.orderNewSort( this.dragItem,  index+1);

      this.dropItem = this.questions.splice(this.dragItem, 1)[0];
      this.questions.splice(index,0,this.dropItem);
      this.dropItem=null;
      
      let newPosQuestions = this.setNewPosQuestions();
    },
    setNewPosQuestions(){
      let newPosQuestions = [];
      this.questions.forEach((item, index) => {
        let element = {id:-1, position:0};
          element.id = item.id;
          element.position = item.position = index+1;
          newPosQuestions.push(element);     
      });
      
      return newPosQuestions;
    },
    orderNewSort(antPosSort, newPosSort ){
      let newSortQuestions = [];

      let elmentTras = {
        id: this.questions[antPosSort].id,
        newSort: newPosSort
      }
      newSortQuestions.push(elmentTras);

      if(this.dragItem > newPosSort){
        this.questions.forEach((item) => {
          let element = {
            id:null,
            newSort:null
          }
          
          if(item.sort >= newPosSort && item.sort <= antPosSort){
            element.id = item.id;
            element.newSort = item.sort + 1;
            newSortQuestions.push(element);
          }
        });
        
      }else{
        this.questions.forEach((item) => {
          let element = {
            id:null,
            newSort:null
          }
          if(item.sort > antPosSort+1 && item.sort <= newPosSort){
            element.id = item.id;
            element.newSort = item.sort - 1;
            newSortQuestions.push(element);
          }
        });
      }

      return newSortQuestions;
    },
    getQuestionTypeIcon(type) {
      const index = this.questionTypes.findIndex(item => item.type === type);
      return this.questionTypes[index].icon;
    },
    addQuestion() {
      this.$store.dispatch('surveyModule/resetQuestionForm');
      this.openModal();
    },
    openModal() {
      $('#new-question-modal').modal({
        show  : true,
        static  : true,
        backdrop: false,
        keyboard: false
      });
    },

    closeModal() {
      this.questionsLocal = [];
      this.questionsLocal = this.innerValue;
      this.editQuestion = null;
      $('#new-question-modal').modal('hide');
    },

    async onNewQuestion(question) {    
      this.closeModal();
    },

    activateQuestion(question) {
      if (this.realtime && 'id' in question) {
        this.$store.dispatch('surveyModule/activateQuestion', { id: question.id, active: question.active }).then(res => {
          const { error } = res;
          if (error) this.$toast.error(this.$t('SURVEY.QUESTION.ACTIVE.FAILED') + '')
        })
      }
    },

    setMainQuestion(index) {
      const questions = structuredClone(this.innerValue);
      for (let i = 0; i < questions.length; i++) {
        if (questions[i].type === questions[index].type) {
          questions[i].main = i === index;
         
          document.getElementById(`switch_survey_main_${i}`).checked = questions[i].main;
        }
      }

      this.innerValue = questions;
    },

    updateQuestion(question, index) {
      this.$store.dispatch('surveyModule/editLocalQuestion', index);
      // this.editQuestion = question;
      // this.editQuestionIndex = index;
      this.openModal();
    },

    deleteQuestion(question, index) {
      let isOnDataBase = this.questionsLocal[index].id==-1?false:true;
      
      if (isOnDataBase) {
        this.$alertify.confirmWithTitle(
            this.$t('SURVEY.QUESTIONS.DELETE.CONFIRM.TITLE'),
            this.$t('SURVEY.QUESTIONS.DELETE.CONFIRM.DESCRIPTION'),
            () => {
              this.$store.dispatch('surveyModule/deleteSurveyQuestion', question.id).then(res => {
                const { error } = res;
                if (error) this.$toast.error(this.$t('SURVEY.QUESTIONS.DELETE.FAILED') + '');
                else {
                  this.$toast.success(this.$t('SURVEY.QUESTIONS.DELETE.SUCCESS') + '');
                  this.innerValue.splice(index, 1);
                }
              })
            },
            () => {},
        )
      } else {
        this.innerValue.splice(index, 1);     
        // const result = structuredClone(this.value);
        //     result.splice(index, 1);
        //     this.innerValue = result;
      }
      this.questionsLocal = [];
      this.questionsLocal = this.innerValue
    }
  }
}
</script>

 <style scoped lang="scss"> 
.dragBar {
    color: var(--color-primary);
  }

.Questions {
  padding: 1rem;

  &--table {
    overflow: auto;
  }
  &--header {
    h3 {
      font-size: 22px;
    }
  }

  &--table {
    background-color: #ffffff;
    border: 0px solid $base-border-color;

    thead {
      border: 0 solid  $base-border-color;
      th {
        border: 0 solid  $base-border-color;
      }
    }

    tbody {
      td, th {
        border-top: 0 solid;
        border-left: 0 solid $base-border-color;
        border-right: 0 solid $base-border-color;
        border-bottom: 1px solid $base-border-color;
      }
    }
  }

  #new-question-modal {
    .modal-header {
      border: unset !important;
      color: #212121 !important;
    }
    .modal-body {
      padding: 0 !important;
    }

    .modal-header, .modal-body {
      background-color: #f4f5f6 !important;
    }
  }
}
</style>
