import { make } from "vuex-pathify";
import axios from "axios";

const state = {
  loading: true,
  menses: false,
  categorysfilterdb: [],
  languages: [],
  userlocale: undefined,
  filtercategoryfilterdb:[],
  questions: [],
  indexQuestionForm: -1,
  filters:[],
  filter:{
    filter_category_id: -1,
    id: -1,
    name: null,
    translations: [],
    operation :null,
  },
  ranking:null,
}

export const getters = {
  getCategoryFilterDB: (state) => () => state.categorysfilterdb,
  getLanguages: (state) => () => state.languages,
  getUserlocale: (state) => () => state.userlocale,
  getFilterCategoryFilterDB: (state) => () => (state.filtercategoryfilterdb),
  getRanking: (state) => () => (state.ranking),
};

export const mutations = {
  ...make.mutations(state),

  SET_NEW_VALUE_CATEGORYSFILTERDB(state, newValue) {
    state.categorysfilterdb = state.categorysfilterdb.map((categoryFilter) => {
      if (categoryFilter.id === newValue.id) {
        return newValue;     }

      return categoryFilter;
    });
  },

  addCurrentQuestion(state) {
      state.questions.push(state.questionForm);
  },

  updateCurrentQuestion(state) {
      state.questions[state.indexQuestionForm] = structuredClone(state.questionForm);
      state.indexQuestionForm = -1;
  },

};

export const actions = {
  ...make.actions(state),

  async load({ commit }, endpoint) {
    commit("SET_LOADING", true);
    return await axios
        .get(endpoint)
        .then((r) => {
          const { data } = r.data;
          commit("SET_CATEGORYSFILTERDB", data?.categorysFilter);
          commit("SET_LANGUAGES", data?.languages);
          commit("SET_USERLOCALE", data?.userLocale);
          commit("SET_RANKING", data?.filterInRanking);
          return data?.categorysFilter;
        })
        .catch((e) => ({
          error: true,
          data: e,
        }))
        .finally(() => {
          commit("SET_LOADING", false);
        });
  },

  async deleteCategoryFilter({ commit }, { endpoint }) {
    try {
      commit("SET_LOADING", true);
      const { data, error } = await axios.delete(endpoint);
      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.log(error);
    } finally {
      commit("SET_LOADING", false);
    }
  },
  async deleteFilter({ commit }, { endpoint }) {
    try {
      const { data, error } = await axios.delete(endpoint);
      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.log(error);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async save({commit}, { endpoint, requestData }) {
    try {
      commit("SET_LOADING", true);
      const { data, error } = await axios.post(endpoint, requestData);
      commit("SET_NEW_VALUE_CATEGORYSFILTERDB", data?.data);
      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.log(error);
    }finally {
      //commit("SET_LOADING", false);
    }
  },

  async saveFilters({commit}, { endpoint, requestData }) {
    try {
      commit("SET_MENSES", false);
      const { data, error } = await axios.post(endpoint, requestData);
      //commit("SET_NEW_VALUE_CATEGORYSFILTERDB", data?.data);
      if (error) {
        throw new Error(data?.message);
      }else{
        commit("SET_MENSES", true);
      }
    } catch (error) {
      console.log(error);
    }finally {
      commit("SET_LOADING", false);
    }
  },

  async getFilterForCategoryFilterId({commit}, {endpoint}){
    //commit("SET_LOADING", true);
    return await axios
        .get(endpoint)
        .then((r) =>{
          const { data } = r.data;
          commit("SET_FILTERCATEGORYFILTERDB", data?.filter);
          return data?.filter;
        }).catch((e)=>({
          error:true,
          data:e,
        })).finally(()=>{
          commit("SET_LOADING", false);
        });
  },

  async changeSort({ commit }, { endpoint, requestData}) {
    try {
      const { data, error } = await axios.put(endpoint, requestData);

      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.log(error);
    }
  },

  async changeIsRanking({ commit }, { endpoint}) {
    try {
      const { data, error } = await axios.put(endpoint);

      if (error) {
        throw new Error(data?.message);
      }
      
    } catch (error) {
      console.log(error);
    }
  },

  async saveLocalFilters({ commit }, { requestData}) {
    try { 
      state.filter = requestData
      if (state.filter.id === 0 ) 
        state.filter.operation = 'create';
    } catch (error) {
      console.log(error);
    }    
  },
  getLocalFilters(){
    try { 
      const filter = state.filter;
      state.filter = null;
      return filter;
    } catch (error) {
      console.log(error);
    }   
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
