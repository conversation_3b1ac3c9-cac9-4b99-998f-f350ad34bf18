<template>
  <div class="home">
    <home
        title="FILTER.HOME.TITLE"
        description="FILTER.HOME.DESCRIPTION"
        src-thumbnail="/assets/imgs/filters.svg"
    >
      <template v-slot:content-main>
        <div
            class="col-12 d-flex flex-row align-items-center justify-content-center"
            v-if="loading"
        >
          <spinner />
        </div>
        <div v-else>
          <div class="col-12 d-flex align-items-center justify-content-end mt-0">
            <router-link class="btn btn-primary" :to="{ name: 'categoryFilterCreate' }">{{ $t('CATEGORY_FILTER.CREATE_TITLE') }}</router-link>
          </div>

          <table class="table table-condensed mt-5" >
            <thead>
            <tr>
              <th>{{ $t("CATEGORYFILTER.SORT") }}</th>  
              <th>  {{ $t("CATEGORYFILTER.NAME") }} </th>
              <th v-if="ranking">  {{ $t("CATEGORYFILTER.RANKING") }} </th>
              <th>  {{ $t("CATEGORYFILTER.FILTERS") }} </th>
              <th style="text-align: right;">{{ $t("ACTIONS") }}</th>
            </tr>
            </thead>
            <tbody>
            <tr
                v-for="(c, index) in categoryFiltersPages"
                :key="c.id"
                draggable
                @dragstart="startDrag($event, index)"
                @drop="onDrop(index)"
                @dragover.prevent
                @dragenter.prevent
            >
              <td><i class="fa fa-bars barsOrdes"></i></td>
              <td>
                <router-link :to="{ name: 'categoryFilterUpdate', params: {...$route.params, id: c.id} }">
                  {{ c.name }}
                </router-link >
              </td>             
              <td v-if="ranking">
                  <BaseSwitch
                    :tag="`switcher-user-filter-ranking-${c.id}`"
                    v-model="c.isRanking"
                    @change="changeStatus(c)"
                    theme="light"
                  /> 
                </td>
              <td>{{ c.totalFilters }}</td>
              <td>
                <div class="d-flex  justify-content-end ">
                  <router-link class="btn btn-sm btn-primary ml-1" :to="{ name: 'categoryFilterUpdate', params: {...$route.params, id: c.id} }">
                    <i class="fa fa-pen"></i>
                  </router-link>
                  <button @click="deleteCategoryFilter(c)" type="button" class="btn btn-sm btn-danger ms-2">
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr v-if="categorysfilterdb.length>9">
              <td colspan="6">
                <div class="d-flex align-items-center justify-content-start"  >
                  <jw-pagination
                      :items="categorysfilterdb"
                      @changePage="onChangePage"
                      :labels="customLabels"
                  ></jw-pagination>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>

      </template>
    </home>
  </div>
</template>
<script>
import Spinner from "../../admin/components/base/Spinner.vue";
import Home from "../../base/Home.vue";
import {get} from "vuex-pathify";
import LabelWithInfo from "../../common/components/ui/LabelWithInfo.vue";
import JwPagination from "jw-vue-pagination";

const PAGE_SIZE = 10;
const customLabels = {
  first: '<<',
  last: '>>',
  previous: '<',
  next: '>'
};

export default {
  name: "CategoryFilter",
  components: {Spinner, Home, LabelWithInfo, JwPagination },
  data() {
    return {
      sortBy: "name",
      sortDesc: false,
      dragItem: null,
      dropItem: null,
      customLabels,
      categoryFiltersPages: [],
      categoryFiltersSort: [],
    };
  },
  computed: {
    loading: get("categoryFilterModule/loading"),
    categorysfilterdb: get("categoryFilterModule/categorysfilterdb"),
    ranking: get("categoryFilterModule/ranking"),
  },
  async created() {
    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: this.$t('FILTER.CATEGORY.TITLE'),
        params: {}
      }
    });

    const categorysfilterdb = await this.$store.dispatch('categoryFilterModule/load', '/admin/categoryFilter/all');
    this.initializeSort(categorysfilterdb);

  },
  methods: {
    changeStatus(categoryFilter) {
        this.$store.dispatch("categoryFilterModule/changeIsRanking", {
          endpoint: `/admin/categoryFilter/${categoryFilter?.id}/isRanking`,
        });
    },
    async initializeSort(categorysfilterdb){ 
      this.categoryFiltersSort = categorysfilterdb;

      if(this.categoryFiltersSort[0].sort === 0){
        this.categoryFiltersSort.forEach((item) => {
          item.sort = item.id;
        })

        await this.$store.dispatch("categoryFilterModule/changeSort", {
          endpoint: `/admin/categoryFilter/initializeSort`,
          requestData: { categoryFilter: this.categoryFiltersSort },
        });

        await this.$store.dispatch("categoryFilterModule/changeSort", {
          endpoint: `/admin/filter/initializeSort`,
          requestData: { filters: this.categoryFiltersSort },
        });
      }
      
      this.categoryFiltersPages = this.categoryFiltersSort.slice(0, PAGE_SIZE);
    },
    onChangePage(categoryFiltersPages) {
      this.categoryFiltersPages = categoryFiltersPages;
    },
    deleteCategoryFilter(categoryFilter) {
      this.$alertify.confirmWithTitle(
          this.$t("DELETE"),
          this.$t("COMMON_AREAS.QUESTION_DELETE"),
          () => {
            this.$store
                .dispatch("categoryFilterModule/deleteCategoryFilter", {
                  endpoint: `/admin/categoryFilter/${categoryFilter?.id}/delete`,
                })
                .then((r) => {
                  const index = this.categoryFiltersPages.findIndex((item) => item.id === categoryFilter.id);
                  this.categoryFiltersPages.splice(index, 1);
                  this.$toast.success(this.$t("DELETE_SUCCESS") + "");
                })
                .catch((e) => {
                  this.$toast.error("DELETE_FAILED");
                })
                .finally(() => {

                });
          },
          () => {}
      );
    },

    startDrag(evt, index) {
      this.dragItem = index;
      evt.dataTransfer.dropEffect = "move";
      evt.dataTransfer.effectAllowed = "move";
    },

    async onDrop(index) {
      let data = this.orderNewSort(this.dragItem, index);
      
      this.dropItem = this.categoryFiltersSort.splice(this.dragItem, 1)[0];
      this.categoryFiltersSort.splice(index, 0, this.dropItem);
      this.dropItem = null;

      this.setNewSort(data);

      await this.$store.dispatch("categoryFilterModule/changeSort", {
        endpoint: `/admin/categoryFilter/changeSort`,
        requestData: { categoryFilter: data },
      });
    },
    setNewSort(data){
      data.forEach((item) => {       
        const index = this.categoryFiltersPages.findIndex((categoryFilter) => categoryFilter.id === item.id);
        this.categoryFiltersPages[index].sort = item.newSort;
      });
      this.categoryFiltersPages = this.categoryFiltersSort.slice(0, PAGE_SIZE);
    },

    //antIndexSort, newIndexSort--> posición en el vector que se muestra va de 0,9
    orderNewSort(antIndexSort, newIndexSort) {
      let newSortCategoryFilters = [];
      let sortPos = this.categoryFiltersSort[antIndexSort].sort;//sort actual en la base de datos
      let newSortPos = this.categoryFiltersSort[newIndexSort].sort;//newsort  en la base de datos

      if(sortPos > newSortPos){     
        newSortCategoryFilters = this.sortMayorMenor(sortPos, newSortPos, antIndexSort, newIndexSort);
      }

      if(sortPos < newSortPos){
        newSortCategoryFilters = this.sortMenorMayor(sortPos, newSortPos, antIndexSort, newIndexSort);
      }

      return newSortCategoryFilters;
    },
    sortMayorMenor(sortPos, newSortPos, antIndexSort, newIndexSort){
      let newSortCategoryFilters = [];

      let elmentTras = {
          id: this.categoryFiltersSort[antIndexSort].id,
          newSort: newSortPos,
      };
      newSortCategoryFilters.push(elmentTras);

      for(let index = newIndexSort; index < antIndexSort; index++){
        let element = {
          id: null,
          newSort: null,
        };  
        element.id = this.categoryFiltersSort[index].id;
        element.newSort =this.categoryFiltersSort[index].sort+1;
        
        newSortCategoryFilters.push(element);
      }

      return newSortCategoryFilters;
    },
    sortMenorMayor(sortPos, newSortPos, antIndexSort, newIndexSort){
      let newSortCategoryFilters = [];

      let elmentTras = {
          id: this.categoryFiltersSort[antIndexSort].id,
          newSort: newSortPos,
      };
      newSortCategoryFilters.push(elmentTras);

      for(let index = newIndexSort; index > antIndexSort; index--){
        let element = {
          id: null,
          newSort: null,
        };  
        element.id = this.categoryFiltersSort[index].id;
        element.newSort =this.categoryFiltersSort[index].sort-1;
      
        newSortCategoryFilters.push(element);
      }

      return newSortCategoryFilters;
    },

  }
}
</script>

<style lang="scss" scoped>
.home {
  .sort-asc::after,
  .sort-desc::after {
    content: " ▼";
    font-size: 10px;
    opacity: 0.5;
    margin-left: 5px;
  }

  .sort-desc::after {
    content: " ▲";
  }

  th {
    cursor: pointer;
  }

  th.sort-asc:hover::after,
  th.sort-desc:hover::after {
    opacity: 1;
    cursor: pointer;
  }

  .barsOrdes {
    color: var(--color-primary);
  }
}
</style>