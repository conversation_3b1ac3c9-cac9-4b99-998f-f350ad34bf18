import $ from "jquery";
import "bootstrap";

$(function() {
  $(".field-association").on("change", "#User_filter", function(e) {
    e.preventDefault();
    var userFilter = $("#User_filter");
    const url = "filters";

    const data = userFilter.val();
    const lengthObject = data.length;
    

    if (lengthObject >= 1) {
      let filters = $.ajax({
        type: "POST",
        url: url,
        data: { filtro: JSON.stringify(data) },
      });

      filters.done(function(result) {
        if (result.error) {
          console.log("ocurrio un error");
        } else {  
          $.each(result.filter, function(key, filter) {

            var buttonSave = $(".action-saveAndReturn");
            var buttonSaveContinue = $(".action-saveAndContinue");
            var div = document.createElement("DIV");

            if (filter.duplicated >= 2) {
              div.innerHTML = `Error filtro duplicado, solo se permite uno de tipo: ${filter.name}`;
              div.classList.add("error-filter");
              div.id = "error-filter";
              div.style.color = "red";
              div.style.textAlign = "center";

              document.getElementById("content-5").appendChild(div);

              buttonSave.prop("disabled", true);
              buttonSaveContinue.prop("disabled", true);
            
            } else {
              buttonSave.prop("disabled", false);
              buttonSaveContinue.prop("disabled", false); 
              var errorFilter = $(".error-filter"); 
              errorFilter.remove();   
              errorFilter.css({"color":"white"});      
              
            }
          });
        }
      });
    }
  });
});




