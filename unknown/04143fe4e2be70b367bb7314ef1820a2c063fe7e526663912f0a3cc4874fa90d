<?php

namespace App\Resources\DataFixtureBase\Announcement;

use App\Entity\TypeCourse;

class AlertTypeTutorData {

    const DEFAULT_DATA = [
        [
            'id' => 1,
            'name' => 'alert_type_tutor.1.name',
            'description' => 'alert_type_tutor.1.description',
            'active' => true,
            'typeCourses' => [TypeCourse::TYPE_TELEFORMACION,TypeCourse::TYPE_MIXTO]
        ],
        [
            'id' => 2,
            'name' => 'alert_type_tutor.2.name',
            'description' => 'alert_type_tutor.2.description',
            'active' => true,
            'typeCourses' => [TypeCourse::TYPE_TELEFORMACION,TypeCourse::TYPE_MIXTO]
        ],
        [
            'id' => 3,
            'name' => 'alert_type_tutor.3.name',
            'description' => 'alert_type_tutor.3.description',
            'active' => true,
            'typeCourses' => [TypeCourse::TYPE_TELEFORMACION,TypeCourse::TYPE_MIXTO]
        ],
        [
            'id' => 4,
            'name' => 'alert_type_tutor.4.name',
            'description' => 'alert_type_tutor.4.description',
            'active' => false,
            'typeCourses' => [TypeCourse::TYPE_TELEFORMACION,TypeCourse::TYPE_MIXTO]
        ],
        [
            'id' => 5,
            'name' => 'alert_type_tutor.5.name',
            'description' => 'alert_type_tutor.5.description',
            'active' => true,
            'typeCourses' => [TypeCourse::TYPE_TELEFORMACION,TypeCourse::TYPE_MIXTO, TypeCourse::TYPE_PRESENCIAL, TypeCourse::TYPE_AULA_VIRTUAL]
        ],
        [
            'id' => 6,
            'name' => 'alert_type_tutor.6.name',
            'description' => 'alert_type_tutor.6.description',
            'active' => true,
            'typeCourses' => [TypeCourse::TYPE_TELEFORMACION,TypeCourse::TYPE_MIXTO, TypeCourse::TYPE_PRESENCIAL, TypeCourse::TYPE_AULA_VIRTUAL]
        ],
    ];

}
