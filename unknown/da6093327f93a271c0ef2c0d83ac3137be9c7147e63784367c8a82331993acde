<?php

namespace App\Entity;

use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use App\Repository\ConfigurationClientAnnouncementRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use <PERSON>ymfony\Component\Serializer\Annotation\Groups;
use Gedmo\Mapping\Annotation as Gedmo;

/**
 * @ORM\HasLifecycleCallbacks()
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 * @ORM\Entity(repositoryClass=ConfigurationClientAnnouncementRepository::class)
 */
class ConfigurationClientAnnouncement
{
    use Blamable, Timestampable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"list"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"list"})
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $description;

    /**
     * @ORM\Column(type="boolean")
     */
    private $active;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private $extra = [];

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementConfigurationType::class, mappedBy="configurationClientAnnouncement", orphanRemoval=true)
     */
    private $announcementConfigurationTypes;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $code;

    public function __construct()
    {
        $this->announcementConfigurationTypes = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getExtra(): ?array
    {
        return $this->extra;
    }

    public function setExtra(?array $extra): self
    {
        $this->extra = $extra;

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementConfigurationType>
     */
    public function getAnnouncementConfigurationTypes(): Collection
    {
        return $this->announcementConfigurationTypes;
    }

    public function addAnnouncementConfigurationType(AnnouncementConfigurationType $announcementConfigurationType): self
    {
        if (!$this->announcementConfigurationTypes->contains($announcementConfigurationType)) {
            $this->announcementConfigurationTypes[] = $announcementConfigurationType;
            $announcementConfigurationType->setConfigurationClientAnnouncement($this);
        }

        return $this;
    }

    public function removeAnnouncementConfigurationType(AnnouncementConfigurationType $announcementConfigurationType): self
    {
        if ($this->announcementConfigurationTypes->removeElement($announcementConfigurationType)) {
            // set the owning side to null (unless already changed)
            if ($announcementConfigurationType->getConfigurationClientAnnouncement() === $this) {
                $announcementConfigurationType->setConfigurationClientAnnouncement(null);
            }
        }

        return $this;
    }

    public function __toString()
    {
        return 'configuration-client-announcement-' . $this->getId();
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): self
    {
        $this->code = $code;

        return $this;
    }
}
