<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\TypeCourseAlerts;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TypeCourseAlerts>
 *
 * @method TypeCourseAlerts|null find($id, $lockMode = null, $lockVersion = null)
 * @method TypeCourseAlerts|null findOneBy(array $criteria, array $orderBy = null)
 * @method TypeCourseAlerts[]    findAll()
 * @method TypeCourseAlerts[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TypeCourseAlertsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TypeCourseAlerts::class);
    }

    public function add(TypeCourseAlerts $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TypeCourseAlerts $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
