<?php

declare(strict_types=1);

namespace App\Service\Course\DT0;

use App\Entity\Chapter;

class ChapterQueryParams extends BaseStatsDTO
{
    public Chapter $chapter;
    public ?bool $findUsers;
    public array $users;

    public function __construct(
        Chapter $chapter,
        ?bool $findUsers = null,
        array $users = [],
        ?\DateTimeImmutable $dateFrom = null,
        ?\DateTimeImmutable $dateTo = null,
        ?int $announcementId = null,
        ?bool $courseStartedOnTime = null,
        ?bool $courseFinishedOnTime = null
    ) {
        parent::__construct($dateFrom, $dateTo, $announcementId, $courseStartedOnTime, $courseFinishedOnTime);
        $this->chapter = $chapter;
        $this->findUsers = $findUsers;
        $this->users = $users;
    }

    public static function create(array $data): self
    {
        return new self(
            $data['chapter'],
            $data['findUsers'] ?? null,
            $data['users'] ?? [],
            $data['dateFrom'] ?? null,
            $data['dateTo'] ?? null,
            isset($data['announcementId']) && is_numeric($data['announcementId']) ? (int) $data['announcementId'] : null,
            $data['courseStartedOnTime'] ?? null,
            $data['courseFinishedOnTime'] ?? null
        );
    }
}
