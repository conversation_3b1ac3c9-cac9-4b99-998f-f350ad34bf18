<?php

namespace App\Controller;

use App\Service\FilesManager\FilesManagerService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class FilesManagerController extends AbstractController
{
    /**
     * @Route("/files/{fileValue}", methods={"GET"}, name="files-manager-get-file")
     * @param Request $request
     * @param $fileValue
     * @return Response
     * @IsGranted("ROLE_USER")
     */
    public function getFile(Request $request, FilesManagerService $service, $fileValue): Response
    {
        $user = $this->getUser();
        if (!$user) return new Response('Unauthorized', Response::HTTP_UNAUTHORIZED);
        return $service->getFileResponse($fileValue);
    }
}
