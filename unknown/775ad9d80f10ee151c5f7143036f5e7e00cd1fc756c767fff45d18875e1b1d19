<?php

declare(strict_types=1);

namespace App\Modules\Survey\Repository;

use App\Entity\Survey;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Survey>
 *
 * @method Survey|null find($id, $lockMode = null, $lockVersion = null)
 * @method Survey|null findOneBy(array $criteria, array $orderBy = null)
 * @method Survey[]    findAll()
 * @method Survey[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SurveyModuleRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Survey::class);
    }

    public function add(Survey $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Survey $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getSurvey(int $pageSize, int $page)
    {
        return $this->createQueryBuilder('s')
        ->join('s.createdBy', 'u')
            ->select('s.id', 's.name', 's.active', 's.isMain')
            ->addSelect('u.firstName', 'u.lastName', 'u.avatar')
            ->addSelect('COUNT(q.id) as totalQuestions')
            ->leftJoin('s.npsQuestions', 'q')
            ->groupBy('s.id')
            ->setMaxResults($pageSize)
            ->setFirstResult(($page - 1) * $pageSize)->getQuery()->getResult();
    }

    public function getTotalSurvey(): QueryBuilder
    {
        return $this->createQueryBuilder('s')
            ->join('s.createdBy', 'u');
    }
}
