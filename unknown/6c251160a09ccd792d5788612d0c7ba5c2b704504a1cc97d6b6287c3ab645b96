<?php

namespace App\Resources\Traits\Catalog;

use App\Entity\TypeVideo;
use App\Resources\DataFixtureBase\Course\TypeVideoData;

trait TypeVideoTrait
{
    protected function getDefaultData(): array
    {
        $parameters = self::PARAMETERS_BASE;
        $parameters['fieldsToTranslate'] = ['name'];
        $parameters['setFields'] = ['name'=>'setName'];
        $parameters['fieldState'] =  null;

        return [
            'data' => TypeVideoData::DEFAULT_DATA,
            'parameters' => $parameters,
            'baseEntity' => TypeVideo::class,
            'translationEntity' => null,
        ];
    }
}