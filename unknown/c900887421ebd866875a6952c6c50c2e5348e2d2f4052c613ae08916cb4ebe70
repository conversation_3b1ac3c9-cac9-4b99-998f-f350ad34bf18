<?php

namespace App\Entity;

use App\Repository\CategorizeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;
use DateTime;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use App\Behavior\Imageable;
use Symfony\Component\Serializer\Annotation\Groups;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\HasLifecycleCallbacks()
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 * @ORM\Entity(repositoryClass=CategorizeRepository::class)
 * @Vich\Uploadable()
 */
class Categorize
{
    use Blamable;
    use Timestampable;
    use Imageable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"categorize"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"categorize"})
     */
    private $question;

    /**
     * @ORM\OneToMany(targetEntity=CategorizeAnswers::class, mappedBy="categorize", cascade={"persist", "remove"})
     * @Groups({"categorize"})
     */
    private $categorizeAnswers;

    /**
     * @Vich\UploadableField(mapping="categorize", fileNameProperty="image")
     */
    private $imageFile;

    /**
     * @ORM\ManyToOne(targetEntity=Chapter::class, inversedBy="categorizes")
     */
    private $chapter;

    /**
     * @ORM\Column(type="integer")
     * @Groups({"categorize"})
     */
    private $time;

    public function __construct()
    {
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
        $this->categorizeAnswers = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->question;
    }

    public function __clone()
    {
        $this->id = null;
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();


        // clone image
        if ($this->getUploadsFolder()) {
            $this->cloneImage();
        }

        $answers = $this->categorizeAnswers;
        $this->categorizeAnswers = new ArrayCollection();
        foreach ($answers as $answer) {
            $newAnswer = clone $answer;
            $newAnswer->setCategorize($this);
            $this->categorizeAnswers->add($newAnswer);
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getQuestion(): ?string
    {
        return $this->question;
    }

    public function setQuestion(?string $question): self
    {
        $this->question = $question;

        return $this;
    }

    /**
     * @return Collection<int, CategorizeAnswers>
     */
    public function getCategorizeAnswers(): Collection
    {
        return $this->categorizeAnswers;
    }

    public function addCategorizeAnswer(CategorizeAnswers $categorizeAnswer): self
    {
        if (!$this->categorizeAnswers->contains($categorizeAnswer)) {
            $this->categorizeAnswers[] = $categorizeAnswer;
            $categorizeAnswer->setCategorize($this);
        }

        return $this;
    }

    public function removeCategorizeAnswer(CategorizeAnswers $categorizeAnswer): self
    {
        if ($this->categorizeAnswers->removeElement($categorizeAnswer)) {
            // set the owning side to null (unless already changed)
            if ($categorizeAnswer->getCategorize() === $this) {
                $categorizeAnswer->setCategorize(null);
            }
        }

        return $this;
    }

    public function getChapter(): ?Chapter
    {
        return $this->chapter;
    }

    public function setChapter(?Chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    public function getTime(): ?int
    {
        return $this->time;
    }

    public function setTime(int $time): self
    {
        $this->time = $time;

        return $this;
    }

    /**
     * @Groups({"categorize"})
     * @return bool
     */
    public function getState(): ?bool
    {
        foreach ($this->categorizeAnswers as $answer) {
            if ($answer->isCorrect() == true) {
                return true;
            }
        }

        return false;
    }
}
