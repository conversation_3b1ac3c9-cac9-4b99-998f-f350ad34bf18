<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\Export;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Formatter\OutputFormatterStyle;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpKernel\KernelInterface;

class RemoveExpiredFilesCommand extends Command
{
    public function __construct(
        private readonly KernelInterface $appKernel,
        private readonly EntityManagerInterface $em
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('export:delete-expired')
            ->setDescription('Remove expired export files')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $outputStyleRed = new OutputFormatterStyle('red');
        $outputStyleGreen = new OutputFormatterStyle('green');

        $output->getFormatter()->setStyle('error', $outputStyleRed);
        $output->getFormatter()->setStyle('green', $outputStyleGreen);
        $output->write('Starting to delete expired exports', true);

        $exportDirectory = $this->appKernel->getProjectDir() . DIRECTORY_SEPARATOR . 'xlsx' . DIRECTORY_SEPARATOR;
        $currentDate = (new \DateTime());

        $exports = $this->em->getRepository(Export::class)->createQueryBuilder('e')
            ->where('e.available_until < :date')
            ->setParameter('date', $currentDate)
            ->orderBy('e.id', 'DESC')
            ->getQuery()
            ->getResult()
        ;
        $output->write('Total available exports: ' . \count($exports), true);

        $deletedFiles = 0;

        /** @var Export $export */
        foreach ($exports as $export) {
            $filename = "{$exportDirectory}{$export->getId()}-{$export->getCreatedAt()->format('YmdHis')}.xlsx";
            if (file_exists($filename)) {
                if (unlink($filename)) {
                    $output->write('<green>File ' . $filename . ' removed </green>', true);
                    ++$deletedFiles;
                    $export->setDeletedAt(new \DateTime());
                } else {
                    $output->write('<green>Failed to remove: ' . $filename . '</green>', true);
                }
            }
        }

        $this->em->flush();

        $output->write('<green>Deleted ' . $deletedFiles . ' files</green>', true);

        return Command::SUCCESS;
    }
}
