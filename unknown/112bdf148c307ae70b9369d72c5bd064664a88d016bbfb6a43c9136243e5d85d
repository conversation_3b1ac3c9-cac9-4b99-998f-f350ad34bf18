<?php

declare(strict_types=1);

namespace App\Service\UserCourse;

use App\Entity\ChapterTypeTranslation;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Service\StatsUser\ResultGameService;
use Doctrine\ORM\EntityManagerInterface;

class UserCourseService
{
    private EntityManagerInterface $em;
    private ResultGameService $resultGameService;

    public function __construct(EntityManagerInterface $em, ResultGameService $resultGameService)
    {
        $this->em = $em;
        $this->resultGameService = $resultGameService;
    }

    public function getUserChaptersData($chapter, $userCourse, &$started, $params): array
    {
        $userCourseChapter = $userCourse ? $this->em->getRepository(UserCourseChapter::class)->findOneBy([
            'userCourse' => $userCourse,
            'chapter' => $chapter,
        ]) : null;

        $start = $userCourseChapter ? $userCourseChapter->getStartedAt() : null;
        $finish = $userCourseChapter ? $userCourseChapter->getFinishedAt() : null;
        $chapterStatus = $userCourseChapter ? $userCourseChapter->getStatus() : UserCourseChapter::STATUS_NO_STARTED;
        if (UserCourseChapter::STATUS_FINISHED == $chapterStatus) {
            ++$started;
        }

        /** @var ChapterTypeTranslation $chapterTypeTranslation */
        $chapterType = $chapter->getType();
        $chapterTypeTranslation = $chapterType->translate($chapter->getCourse()->getLocale());
        $chapterData = [
            'id' => $chapter->getId(),
            'name' => $chapter->getTitle(),
            'type' => $chapterTypeTranslation->getName() ?: $chapterType->getNormalized(),
            'icon' => $chapterType->getIcon(),
            'start' => $start ? $start->format('c') : null,
            'end' => $finish ? $finish->format('c') : null,
            'status' => $chapterStatus,
            'image' => $chapter->getImage() ? ($params->get('app.chapter_uploads_path') . '/' . $chapter->getImage()) : null, // Require file location
            'timeSpent' => $userCourseChapter ? $userCourseChapter->getTimeSpent() : 0,
            'attempts' => $userCourseChapter ? array_map(function ($result) {
                $date = $result['date'] instanceof \DateTimeInterface ? $result['date'] : \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $result['date']);

                return [
                    'questions' => $result['questions'],
                    'state' => $result['state'],
                    'start' => $date->format('c'),
                    'end' => $date->modify("+{$result['timeTotal']} seconds")->format('c'),
                    'timeTotal' => $result['timeTotal'],
                ];
            }, $this->resultGameService->getResultGameAttempts($userCourseChapter)) : [],
        ];

        return $chapterData;
    }

    /**
     * @throws \Exception
     */
    public function updateUserCouserFinished()
    {
        try {
            return $this->em->getRepository(UserCourse::class)->getUserCourseFinished();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
    
}
