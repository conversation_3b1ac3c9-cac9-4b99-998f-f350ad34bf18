<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\LtiLineItemScore;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use OAT\Library\Lti1p3Ags\Model\Score\ScoreInterface;

/**
 * @extends ServiceEntityRepository<LtiLineItemScore>
 *
 * @method LtiLineItemScore|null find($id, $lockMode = null, $lockVersion = null)
 * @method LtiLineItemScore|null findOneBy(array $criteria, array $orderBy = null)
 * @method LtiLineItemScore[]    findAll()
 * @method LtiLineItemScore[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LtiLineItemScoreRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LtiLineItemScore::class);
    }

    public function add(LtiLineItemScore $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(LtiLineItemScore $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function cleanScore(string $resourceLinkId, string $lineItemIdentifier, string $userId)
    {
        $scores = $this->findBy(['lineItemIdentifier' => $lineItemIdentifier, 'userId' => $userId]);
        foreach ($scores as $score) {
            $this->_em->remove($score);
        }
        $this->_em->flush();
    }

    public function save(ScoreInterface $score)
    {
        $ltiScore = LtiLineItemScore::generateFromResultInterface($score);
        $this->_em->persist($ltiScore);
        $this->_em->flush();
    }

    public function findByLineItemIdentifier(string $lineItemIdentifier): array
    {
        $results = [];
        foreach ($this->findBy(['lineItemIdentifier' => $lineItemIdentifier]) as $score) {
            $results[] = $score->getScore();
        }

        return $results;
    }
}
