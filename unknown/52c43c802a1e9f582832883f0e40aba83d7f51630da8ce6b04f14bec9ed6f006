<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Classroomvirtual;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Classroomvirtual>
 *
 * @method Classroomvirtual|null find($id, $lockMode = null, $lockVersion = null)
 * @method Classroomvirtual|null findOneBy(array $criteria, array $orderBy = null)
 * @method Classroomvirtual[]    findAll()
 * @method Classroomvirtual[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ClassroomvirtualRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Classroomvirtual::class);
    }

    public function add(Classroomvirtual $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Classroomvirtual $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getClassroomToActiveRangeDate($dateInitial, $dateFinish)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.classroomvirtualType = 4')
            ->andWhere('c.state = :state')
            ->andWhere('c.startsat >= :dateInial')
            ->andWhere('c.startsat <= :dateFinish')
            ->setParameter('state', 'Created')
            ->setParameter('dateInial', $dateInitial)
            ->setParameter('dateFinish', $dateFinish)
            ->getQuery()->getResult()
        ;
    }
}
