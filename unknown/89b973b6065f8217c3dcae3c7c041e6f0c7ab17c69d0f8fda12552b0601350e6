<?php

namespace App\Service\Traits\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementTutor;
use App\Service\Annoucement\ReportPdf\User\ReportBaseService;

trait TutorTrait
{

    private function getInfomationTutor(Announcement $announcement, $idGroup, string $source = ReportBaseService::SOURCE_REQUEST)
    {
        $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->findOneBy(['announcement' => $announcement, 'id' => $idGroup]);

        if (!$announcementGroup) return [];

        $announcemementTutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(['announcementGroup' => $announcementGroup]);

        if (!$announcemementTutor) return [];

        $host = $source === ReportBaseService::SOURCE_REQUEST ? $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost() : 'qui';

        $cv = $announcemementTutor->getFilename() != null  ?  $host . '/' . $this->settings->get('app.pdf_cv_file_uploads_path') . '/' .  $announcemementTutor->getFilename() : null;
        $avatar = $announcemementTutor->getTutor()->getAvatar() ? $host . '/' .$this->settings->get('app.avatar_uploads_path') . '/' .  $announcemementTutor->getTutor()->getAvatar() : null;
		$tutor = $announcemementTutor->getTutor();

        return [
            'firstName' => $tutor->getFirstName() ?? '--',
            'lastName' => $tutor->getLastName() ?? '--',
            'fullName' => trim("{$tutor->getFirstName()} {$tutor->getLastName()}"),
            'dni' => $announcemementTutor->getDni() ?? '--',
            'email' => $announcemementTutor->getEmail() ?? '--',
            'telephone' => $announcemementTutor->getTelephone() ?? '--',
            'avatar' => $avatar,
            'cv' =>  $cv,
	        'id' => $tutor->getId() ?? 0,
            'tutoringTime' => $announcemementTutor->getTutoringTime(),
            'name' => $announcemementTutor->getName() ?? $tutor->getFullName()
        ];
    }
}
