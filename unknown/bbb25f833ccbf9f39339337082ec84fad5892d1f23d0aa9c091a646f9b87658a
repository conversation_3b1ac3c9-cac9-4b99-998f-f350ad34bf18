<?php

namespace App\Service\VirtualClass;

use App\Service\VirtualClass\ExcelReportGeneratorService;
use App\Utils\FileUtils;

use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\Classroomvirtual;
use App\Entity\ClassroomvirtualType;
use App\Entity\User;

use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Security;

use Mynaparrot\Plugnmeet\PlugNmeet;
use Mynaparrot\Plugnmeet\Parameters\AnalyticsDownloadTokenParameters;
use Mynaparrot\Plugnmeet\Parameters\RoomFeaturesParameters;
use Mynaparrot\Plugnmeet\Parameters\RoomMetadataParameters;
use Mynaparrot\Plugnmeet\Parameters\GetActiveRoomInfoParameters;
use Mynaparrot\Plugnmeet\Parameters\GenerateJoinTokenParameters;
use Mynaparrot\Plugnmeet\Parameters\FetchPastRoomsParameters;
use Mynaparrot\Plugnmeet\Parameters\CreateRoomParameters;
use Mynaparrot\Plugnmeet\Responses\FetchPastRoomsResponse;
use Mynaparrot\Plugnmeet\Responses\AnalyticsDownloadTokenResponse;
use Mynaparrot\Plugnmeet\Responses\CreateRoomResponse;
use Mynaparrot\Plugnmeet\Responses\GetActiveRoomInfoResponse;


class PlugNmeetService extends BaseVirtualClassService implements ClassroomVirtualInterface
{
    private $userRepository;
    private $security;
    protected $plugnmeet;
    private $fileUtils;
    private $excelReportGeneratorService;


    public function __construct(
        EntityManagerInterface          $em,
        SettingsService           $settings,
        UserRepository                  $userRepository,    
        Security $security,
        ExcelReportGeneratorService $excelReportGeneratorService,
        FileUtils              $fileUtils
    ) {
        parent::__construct($em, $settings);
        $this->userRepository = $userRepository;
        $this->security = $security;
        $this->excelReportGeneratorService = $excelReportGeneratorService;
        $this->fileUtils = $fileUtils;
        $this->plugnmeet = new PlugNmeet(
            $this->settings->get('app.plugNmeetServerurl'),
            $this->settings->get('app.plugNmeetApikey'),
            $this->settings->get('app.plugNmeetSecret')
        );
    }

    private function getUser(): User
    {
        return $this->userRepository->find($this->security->getUser());
    }

    public function createRoom(array $parameters): array
    {
        try {
            if (isset($parameters['idClassroomvirtual'])) {
                $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);
                if ($classroomvirtual) {
                    $announcementGroupSession = $classroomvirtual->getGroupSession();
                    $this->updateClassroomvirtual($classroomvirtual, $parameters);

                    return [
                        'tutor_url' => $announcementGroupSession->getUrl(),
                    ];
                }
            }
            srand (time());
            $parameters['start_url'] =$this->settings->get('app.plugNmeetServerurl');            
            $parameters['id'] = rand(1000,10000000);//$parameters["roomId"];//proponer cambiar roomid a string para alojar sid RM_2zd2qyNWfo8A 
            $parameters['type'] = 2;
            $classVirtualType = $this->em->getRepository(ClassroomvirtualType::class)->find(4);   
            $classroomvirtual = $this->addClassroomvirtual($parameters, $classVirtualType);

            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'tutor_url' => $parameters['start_url'],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}"
            ];
        }
    }


    public function activeRoom(array $parameters): array
    {
        try {
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);  

            $parameters["roomId"] = $classroomvirtual->getRoomid();
            $parameters["name"] = $classroomvirtual->getName();
            $parameters["type"] = $classroomvirtual->getRoomtype();
            $parameters["duration"] = $classroomvirtual->getDuration();
            $parameters["startsat"] = date_format($classroomvirtual->getStartsat(),"Y/m/d H:i:s");
            $parameters["description"]  = $classroomvirtual->getDescription();
            $parameters["max_participants"] = count($this->em->getRepository(AnnouncementUser::class)->
                    findBy(["announcementGroup" => $classroomvirtual->getAnnouncementgroup()]));
            $parameters["tutor"] = $this->em->getRepository(AnnouncementTutor::class)-> 
                            findOneBy(["announcementGroup" => $classroomvirtual->getAnnouncementgroup()]);
            $parameters["groupSession"] = $classroomvirtual->getGroupSession();   
            //******* Número de segundos para mantener la sala abierta si no entra nadie.*****/         
            $parameters["empty_timeout"] = 20*60;//

            $meetingData = $this->registerUsersInTheProvider($parameters);        

            $this->registerUsersClassroomvirtualPlugNmeet($classroomvirtual, $classroomvirtual->getAnnouncementgroup());
            $this->updateStateClassroomvirtual($classroomvirtual, 'Activated');

            return [
                'true'
            ];
        } catch (\Exception $e) {
            return [
                $e->getMessage()
            ];
        }
    }

    public function registerUsersInTheProvider(array $parameters): array
    {
        $parameters["webHookUrl"]='';
        $roomMetadata['room_features']['room_duration'] = $parameters['duration'];
        $roomMetadata['room_features']['enable_analytics'] = true;
    //    $roomMetadata['waiting_room_features']['is_active'] = true;//activar la función de sala de espera
    
        $roomCreateResponse = $this->createRoomPlugNmeet("room_".$parameters["roomId"], $parameters["name"], $parameters["description"], 
            $parameters["max_participants"], $parameters["empty_timeout"], $parameters["webHookUrl"], $roomMetadata);
        
        $roomCreate = (array) $roomCreateResponse->getRawResponse()->room_info;

        //registrar el tutor
        $tutor = $this->em->getRepository(AnnouncementTutor::class)->find($parameters["tutor"]);
        $meetingData['start_url'] = $this->registerUserPlugNmeet((string) $parameters["roomId"], 
            $parameters["tutor"]->getTutor()->getEmail(), (string) $parameters["tutor"]->getTutor()->getId(), true);

    //    $announcementGroupSession = $this->em->getRepository(AnnouncementGroupSession::class)->find($parameters["groupSession"]);
        $parameters["groupSession"]->setUrl($meetingData['start_url']);
        $this->em->persist($parameters["groupSession"]);  

        return $meetingData;

    }

    public function saveRoomAndUsersInEasylearning(array $parameters, array $answerApiProvider): void
    {
        $classVirtualType = $this->em->getRepository(ClassroomvirtualType::class)->find(4);

        $classroomvirtual = $this->addClassroomvirtual($parameters, $classVirtualType);
        $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($parameters['idAnnouncementGroup']);

        $this->registerUsersClassroomvirtualPlugNmeet($classroomvirtual, $announcementGroup);

    }

    private function registerUsersClassroomvirtualPlugNmeet(Classroomvirtual $classroomvirtual, AnnouncementGroup $announcementGroup): void
    {
        $announcementUsers = $this->em->getRepository(AnnouncementUser::class)->findBy(["announcementGroup" => $announcementGroup]);

        foreach ($announcementUsers as $user) {
            $urlregisterUserPlugNmeet = $this->registerUserPlugNmeet((string) $classroomvirtual->getRoomId(), 
                    $user->getUser()->getEmail(), (string) $user->getUser()->getId(), false);            
            $this->addClassroomvirtualUser($user, $classroomvirtual, $urlregisterUserPlugNmeet);
        }
    }

    public function deleteRoom(array $parameters): array
    {
        try {
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);
            $this->deleteClassroomVirtual($classroomvirtual);


            if ($response->getStatusCode() == 204) {
                return [
                    'message' => 'The meeting was successfully deleted',
                ];
            }
        } catch (\Exception $e) {
            return  ["message" => "An error has occurred - Error: {$e->getMessage()}"];
        }
    }


    public function getAssistanceRoomUser(array $parameters): array
    {
        try {
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);
            $announcementGroupSession = $this->em->getRepository(AnnouncementGroupSession::class)->find($parameters['idAnnouncementGroupSession']);

            $assistance = $this->getParticipantsMeetingPlugNmeet(["room_".$classroomvirtual->getRoomid()]);//utilizando la conversión array da error
            $estado = $assistance !='' ? "Realized":"Unrealized";
            $this->updateAssistanceAnnouncementGroupSession($announcementGroupSession, (array) $assistance);
            $this->updateStateClassroomvirtual($classroomvirtual, $estado);

            return [
                'status' => Response::HTTP_OK, 
                'error' => false,
                'data' => $assistance,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error hasss occurred - Error: {$e->getMessage()}"
            ];
        }
    }

    public function updateAssistanceAnnouncementGroupSession(
        AnnouncementGroupSession $announcementGroupSession,
        array $assistance
    ) {       
        $announcementGroupSession->setStudentAssistance($assistance);
        $this->em->persist($announcementGroupSession);
        $this->em->flush();
    }


    public function getParticipantsMeetingPlugNmeet(array $parameters)
    { 
        $infoRooms = $this->getPastRooms($parameters)->getRooms();
        $roomsInfo = [];
        $assistance='';

        
        foreach($infoRooms as $room){
            if($room->getAnalyticsFileId() ){
                $info = $this->getRoomsInfo($room);        
                $studentsAssistance =  $this->getStudentsAssistance(file_get_contents($info->urlFileAnalitics));      
                $assistance = $this->getUserAssistance($studentsAssistance, $info->duration);
            }
        }

        return  $assistance; 
    }

    function getUserAssistance($studentsAssistance, $duration){
        $assistance = [];
        foreach($studentsAssistance as $student){
            $PercentMinutesAttended = $student->timeConexion/$duration;
            $userId = $this->userRepository->findOneBy(["email"=>$student->name]);
            $paramParticipant = new \stdClass();
            $paramParticipant->email = $student->name;
            $paramParticipant->assistance = $PercentMinutesAttended >= Classroomvirtual::MINIMUM_ATTENDANCE_TIME ? true : false;

            array_push($assistance, $paramParticipant);
        }

        return $assistance;
    }

    function getStudentsAssistance(string $fileAnaliticsContent){
        $infoAnaliticsUsers = json_decode($fileAnaliticsContent)->users;
        $analiticsInfo = [];
        $analiticsByuser = [];
    
        foreach($infoAnaliticsUsers as $user){ 
            $info = new \stdClass();
            $info->name = $user->name;
            
            foreach($user->events as $event){
                if($event->name == 'joined') {                   
                    $info->vecesJoined = $event->total;
                    $info->joined = $event->values;
                }
                if($event->name == 'left') {
                    $info->vecesLeft = $event->total;
                    $info->left = $event->values;
                }
            }
            
            $info->timeConexion = $this->getTimeConexion($info);
            array_push($analiticsInfo, $info);
        }

        foreach($analiticsInfo as $user){         
            $info = new \stdClass();
            $info->name = $user->name;
            $info->timeConexion = $user->timeConexion;
            array_push($analiticsByuser, $info);
        }

        return $analiticsByuser;
    
    }
   
    function getTimeConexion($conexiones){
    //    date_default_timezone_set('America/Bogota');//debo cambiarlo
        $timeConexions = 0;
    
        for($i=0; $i<$conexiones->vecesLeft; $i++){
            $time1 = date("Y-m-d h:i", $conexiones->joined[$i]->time/1000);
            $time2 = date("Y-m-d h:i", $conexiones->left[$i]->time/1000);
            $diff = date_diff(date_create($time1),  date_create($time2));
            $duration = $diff->format("%h")*60+$diff->format("%i");
            $timeConexions+=$duration;
        }
    
        return $timeConexions;
    }


//*** extraidos de plugNmeetConnect */
    function registerUserPlugNmeet(string $roomId, string $name, string $userId,bool $isAdmin=false){   
        $generateJoinTokenParameters = new GenerateJoinTokenParameters();
        $generateJoinTokenParameters->setRoomId("room_".$roomId);
        $generateJoinTokenParameters->setName($name);
        $generateJoinTokenParameters->setUserId("user_".$userId);
        $generateJoinTokenParameters->setIsAdmin($isAdmin);
        $generateJoinTokenParameters->setIsHidden(false);

        $token = $this->plugnmeet->getJoinToken($generateJoinTokenParameters)->getToken();

        return$this->settings->get('app.plugNmeetServerurl')."?access_token=" . $token ;
    }

    public function getAnalyticsDownloadLink($fileId): AnalyticsDownloadTokenResponse
    {
        $analyticsDownloadTokenParameters = new AnalyticsDownloadTokenParameters();
        $analyticsDownloadTokenParameters->setFileId($fileId);

        return $this->plugnmeet->getAnalyticsDownloadToken($analyticsDownloadTokenParameters);
    }

    function getRoomsInfo($room){
        $info = new \stdClass();
        $info->room_title = $room->getRoomTitle();
        $info->starts_at = $room->getCreatedDate();      
        $info->ends_at = $room->getEndedDate();  
        $diff = date_diff(date_create($info->starts_at),  date_create($info->ends_at));
        $info->duration =  $diff->format("%h")*60+$diff->format("%i");
        $info->file_id = $room->getAnalyticsFileId();   
        $info->urlFileAnalitics =$this->settings->get('app.plugNmeetAnalyticsurl').
            $this->getAnalyticsDownloadLink($room->getAnalyticsFileId())->getToken();

        return $info;
    }

    public function createRoomPlugNmeet(string $roomId, string $roomTitle, string $welcomeMessage, int $max_participants, int $empty_timeout, string $webHookUrl, array $roomMetadata): CreateRoomResponse
    {
        $roomFeatures = $roomMetadata['room_features'];
        $features = new RoomFeaturesParameters();

        if (isset($roomFeatures['allow_webcams'])) {
            $features->setAllowWebcams($roomFeatures['allow_webcams']);
        }
        if (isset($roomFeatures['mute_on_start'])) {
            $features->setMuteOnStart($roomFeatures['mute_on_start']);
        }
        if (isset($roomFeatures['allow_screen_share'])) {
            $features->setAllowScreenShare($roomFeatures['allow_screen_share']);
        }
        if (isset($roomFeatures['allow_recording'])) {
            $features->setAllowRecording($roomFeatures['allow_recording']);
        }
        if (isset($roomFeatures['allow_rtmp'])) {
            $features->setAllowRTMP($roomFeatures['allow_rtmp']);
        }
        if (isset($roomFeatures['allow_view_other_webcams'])) {
            $features->setAllowViewOtherWebcams($roomFeatures['allow_view_other_webcams']);
        }
        if (isset($roomFeatures['allow_view_other_users_list'])) {
            $features->setAllowViewOtherParticipants($roomFeatures['allow_view_other_users_list']);
        }
        if (isset($roomFeatures['admin_only_webcams'])) {
            $features->setAdminOnlyWebcams($roomFeatures['admin_only_webcams']);
        }
        if (isset($roomFeatures['allow_polls'])) {
            $features->setAllowPolls($roomFeatures['allow_polls']);
        }
        if (isset($roomFeatures['room_duration'])) {
            if ($roomFeatures['room_duration'] > 0) {
                $features->setRoomDuration($roomFeatures['room_duration']);
            }
        }

        if (isset($roomMetadata['chat_features'])) {
            $roomChatFeatures = $roomMetadata['chat_features'];
            $chatFeatures = new ChatFeaturesParameters();
            if (isset($roomChatFeatures['allow_chat'])) {
                $chatFeatures->setAllowChat($roomChatFeatures['allow_chat']);
            }
            if (isset($roomChatFeatures['allow_file_upload'])) {
                $chatFeatures->setAllowFileUpload($roomChatFeatures['allow_file_upload']);
            }
            $features->setChatFeatures($chatFeatures);
        }

        if (isset($roomMetadata['shared_note_pad_features'])) {
            $roomSharedNotepadFeatures = $roomMetadata['shared_note_pad_features'];
            $sharedNotePadFeatures = new SharedNotePadFeaturesParameters();
            if (isset($roomSharedNotepadFeatures['allowed_shared_note_pad'])) {
                $sharedNotePadFeatures->setAllowedSharedNotePad($roomSharedNotepadFeatures['allowed_shared_note_pad']);
            }
            $features->setSharedNotePadFeatures($sharedNotePadFeatures);
        }

        if (isset($roomMetadata['whiteboard_features'])) {
            $roomWhiteboardFeatures = $roomMetadata['whiteboard_features'];
            $whiteboardFeatures = new WhiteboardFeaturesParameters();
            if (isset($roomWhiteboardFeatures['allowed_whiteboard'])) {
                $whiteboardFeatures->setAllowedWhiteboard($roomWhiteboardFeatures['allowed_whiteboard']);
            }
            $features->setWhiteboardFeatures($whiteboardFeatures);
        }

        if (isset($roomMetadata['external_media_player_features'])) {
            $roomExternalMediaPlayerFeatures = $roomMetadata['external_media_player_features'];
            $externalMediaPlayerFeatures = new ExternalMediaPlayerFeaturesParameters();
            if (isset($roomExternalMediaPlayerFeatures['allowed_external_media_player'])) {
                $externalMediaPlayerFeatures->setAllowedExternalMediaPlayer($roomExternalMediaPlayerFeatures['allowed_external_media_player']);
            }
            $features->setExternalMediaPlayerFeatures($externalMediaPlayerFeatures);
        }

        if (isset($roomMetadata['waiting_room_features'])) {
            $roomWaitingRoomFeatures = $roomMetadata['waiting_room_features'];
            $waitingRoomFeatures = new WaitingRoomFeaturesParameters();
            if (isset($roomWaitingRoomFeatures['is_active'])) {
                $waitingRoomFeatures->setIsActive($roomWaitingRoomFeatures['is_active']);
            }
            if (isset($roomWaitingRoomFeatures['waiting_room_msg'])) {
                if (!empty($roomWaitingRoomFeatures['waiting_room_msg'])) {
                    $waitingRoomFeatures->setWaitingRoomMsg($roomWaitingRoomFeatures['waiting_room_msg']);
                }
            }
            $features->setWaitingRoomFeatures($waitingRoomFeatures);
        }

        if (isset($roomMetadata['breakout_room_features'])) {
            $roomBreakoutRoomFeatures = $roomMetadata['breakout_room_features'];
            $breakoutRoomFeatures = new BreakoutRoomFeaturesParameters();
            if (isset($roomBreakoutRoomFeatures['is_allow'])) {
                $breakoutRoomFeatures->setIsAllow($roomBreakoutRoomFeatures['is_allow']);
            }
            if (isset($roomBreakoutRoomFeatures['allowed_number_rooms'])) {
                if (!empty($roomBreakoutRoomFeatures['allowed_number_rooms'])) {
                    $breakoutRoomFeatures->setAllowedNumberRooms($roomBreakoutRoomFeatures['allowed_number_rooms']);
                }
            }
            $features->setBreakoutRoomFeatures($breakoutRoomFeatures);
        }

        if (isset($roomMetadata['display_external_link_features'])) {
            $roomDisplayExternalLinkFeatures = $roomMetadata['display_external_link_features'];
            $displayExternalLinkFeatures = new DisplayExternalLinkFeaturesParameters();
            if (isset($roomDisplayExternalLinkFeatures['is_allow'])) {
                $displayExternalLinkFeatures->setIsAllow($roomDisplayExternalLinkFeatures['is_allow']);
            }
            $features->setDisplayExternalLinkFeatures($displayExternalLinkFeatures);
        }

        $metadata = new RoomMetadataParameters();
        $metadata->setRoomTitle($roomTitle);
        $metadata->setWelcomeMessage($welcomeMessage);
        $metadata->setWebhookUrl($webHookUrl);
        $metadata->setFeatures($features);

        if (isset($roomMetadata['default_lock_settings'])) {
            $defaultLocks = $roomMetadata['default_lock_settings'];
            $lockSettings = new LockSettingsParameters();

            if (isset($defaultLocks['lock_microphone'])) {
                $lockSettings->setLockMicrophone($defaultLocks['lock_microphone']);
            }
            if (isset($defaultLocks['lock_webcam'])) {
                $lockSettings->setLockWebcam($defaultLocks['lock_webcam']);
            }
            if (isset($defaultLocks['lock_screen_sharing'])) {
                $lockSettings->setLockScreenSharing($defaultLocks['lock_screen_sharing']);
            }
            if (isset($defaultLocks['lock_whiteboard'])) {
                $lockSettings->setLockWhiteboard($defaultLocks['lock_whiteboard']);
            }
            if (isset($defaultLocks['lock_shared_notepad'])) {
                $lockSettings->setLockSharedNotepad($defaultLocks['lock_shared_notepad']);
            }
            if (isset($defaultLocks['lock_chat'])) {
                $lockSettings->setLockChat($defaultLocks['lock_chat']);
            }
            if (isset($defaultLocks['lock_chat_send_message'])) {
                $lockSettings->setLockChatSendMessage($defaultLocks['lock_chat_send_message']);
            }
            if (isset($defaultLocks['lock_chat_file_share'])) {
                $lockSettings->setLockChatFileShare($defaultLocks['lock_chat_file_share']);
            }
            if (isset($defaultLocks['lock_private_chat'])) {
                $lockSettings->setLockPrivateChat($defaultLocks['lock_private_chat']);
            }

            $metadata->setDefaultLockSettings($lockSettings);
        }

        $roomCreateParams = new CreateRoomParameters();
        $roomCreateParams->setRoomId($roomId);
        if ($max_participants > 0) {
            $roomCreateParams->setMaxParticipants($max_participants);
        }
        if ($empty_timeout > 0) {
            $roomCreateParams->setEmptyTimeout($empty_timeout);
        }
        $roomCreateParams->setRoomMetadata($metadata);

        return $this->plugnmeet->createRoom($roomCreateParams);
    }   

    public function getPastRooms(array $roomIds, int $from = 0, int $limit = 20, string $orderBy = "DESC"): FetchPastRoomsResponse
    {
        $fetchPastRoomsParameters = new FetchPastRoomsParameters();
        $fetchPastRoomsParameters->setRoomIds($roomIds);
        $fetchPastRoomsParameters->setFrom($from);
        $fetchPastRoomsParameters->setLimit($limit);
        $fetchPastRoomsParameters->setOrderBy($orderBy);

        return $this->plugnmeet->fetchPastRoomsInfo($fetchPastRoomsParameters);
    }

    public function getJoinToken(string $roomId, string $name, string $userId, bool $isAdmin, bool $isHidden = false, UserMetadataParameters $userMetadata = null): GenerateJoinTokenResponse
    {
        $generateJoinTokenParameters = new GenerateJoinTokenParameters();
        $generateJoinTokenParameters->setRoomId($roomId);
        $generateJoinTokenParameters->setName($name);
        $generateJoinTokenParameters->setUserId($userId);
        $generateJoinTokenParameters->setIsAdmin($isAdmin);
        $generateJoinTokenParameters->setIsHidden($isHidden);
        if ($userMetadata !== null) {
            $generateJoinTokenParameters->setUserMetadata($userMetadata);
        }

        return $this->plugnmeet->getJoinToken($generateJoinTokenParameters);
    }

    public function getActiveRoomInfo(string $roomId): GetActiveRoomInfoResponse
    {
        $getActiveRoomInfoParameters = new GetActiveRoomInfoParameters();
        $getActiveRoomInfoParameters->setRoomId($roomId);

        return $this->plugnmeet->getActiveRoomInfo($getActiveRoomInfoParameters);
    }

    /****************** métodos para reportes/estadísticas */
    public function addResultProviders(array $parameters): array
    {
        try {           
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);  
              
            $header = $this->getRoomInfoPlugNmeetReport("room_".$classroomvirtual->getRoomId())["data"][0];
            if($header->getAnalyticsFileId() != ""){            
                $reportPlugNmeet = new \stdClass();
                $reportPlugNmeet->header = $this->getHeaderPlugNmeet($header);
                $reportPlugNmeet->body = $this->getAttendeesPlugNmeet("room_".$classroomvirtual->getRoomId());
                $reportPlugNmeet->header->participants = count($reportPlugNmeet->body);//al principio viene en cero
                $this->addClassroomvirtualResult($classroomvirtual, json_encode($reportPlugNmeet));
            }else $reportPlugNmeet = false; 

            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'reportPlugNmeet' => $reportPlugNmeet,
            ];
        } catch (\Exception $e) { 
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}"
            ];
        }

    }

    private function getHeaderPlugNmeet($header){  
        $headerPlugNmeet = new \stdClass();
        $headerPlugNmeet->participants = $header->getJoinedParticipants();
        $headerPlugNmeet->start_time = $header->getCreatedDate();
        $headerPlugNmeet->end_time = $header->getEndedDate();
        $headerPlugNmeet->duration = $this->calculateDuration($header->getCreatedDate(),$header->getEndedDate());

        return $headerPlugNmeet;
    }

    public function  getRoomInfoPlugNmeetReport(string $roomId):array
    {
        try {
            $roomInfo = $this->getPastRooms([$roomId]);

            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $roomInfo->getRooms(),
            ];
        } catch (\Exception $e) {
            return [
                'data' => false
            ];
        }
    }

    private function getAttendeesPlugNmeet(string  $roomId)
    { 
        $infoRooms = $this->getPastRooms([$roomId])->getRooms();
        $roomsInfo = [];
        $body = [];
        $assistance='';
        
        foreach($infoRooms as $room){
            if($room->getAnalyticsFileId() ){
                $info = $this->getRoomsInfo($room);        
                $studentsAssistance =  json_decode(file_get_contents($info->urlFileAnalitics))->users;  
            }
        }

        foreach($studentsAssistance as $user){ 
            $info = new \stdClass();
            
            foreach($user->events as $event){
                if($event->name == 'joined') {                   
                    $info->vecesJoined = $event->total;
                    $info->joined = $event->values;
                }
                if($event->name == 'left') {
                    $info->vecesLeft = $event->total;
                    $info->left = $event->values;
                }
            }  
            
            $partBody = new \stdClass();
            
            $partBody->join_time = date("Y-m-d h:i", $info->joined[0]->time/1000);
            $partBody->leave_time = date("Y-m-d h:i", $info->left[$info->vecesLeft-1]->time/1000);
            $partBody->duration = $this->getTimeConexion($info);
            $partBody->email = $user->name;                    
            $userRepository = $this->userRepository->findOneBy(["email" => $partBody->email]);
            $userRepository ?
                $partBody->name = $userRepository->getFirstName().' '. $userRepository->getLastName() :
                $partBody->name = $user->name;
            $partBody->conexions = $info->vecesJoined;
            
            array_push($body, $partBody);
        }

        return  $body; 
    }

    private function calculateDuration(string $timeJoin, string $timeLeft):int
    {
        $date1=date_create($timeJoin);
        $date2=date_create($timeLeft);
        $diffTime = $date1->diff($date2);
        $time = explode(":", $diffTime->format('%H:%i:%s'));
        $minutes = $time[0] * 60 + $time[1];

        return $minutes;
    }    
    
    //**generar reporte de excel */
    public function generateSessionExcelReport(array $parameters): ?array
    {
        try {        
            $plugNmeetUrlReports =$this->settings->get('app.plugNmeetUrlReports');
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']); 
            
            if (!empty($classroomvirtual->getClassroomVirtualResult())) {          
                $announcement = $classroomvirtual->getAnnouncementgroup()->getAnnouncement(); 
                $dateMeeting = date_format($classroomvirtual->getStartsat(), "Y-m-d");   
                $reportName = $plugNmeetUrlReports.'plugNmeetMeeting_'.$announcement->getId().'_'.$dateMeeting.'.xls';
                $complementaryData = $this->getComplementaryData($classroomvirtual);

                $reportData = json_decode($classroomvirtual->getClassroomVirtualResult()->getResult());
                $urlReport = $this->excelReportGeneratorService->generateExcelReportVirtualClass($reportData, $complementaryData, $reportName);

                return [
                    'data' => $urlReport
                ];
            }

            return null;
        } catch (\Exception $e) {
            return [
                'data' => $e
            ];
        }
    }

    public function generateAnnouncementExcelReport(array $parameters): ?array
    { 
        try {     
            $reportData = []; 
            $complementaryData = [];
            $plugNmeetUrlReports =$this->settings->get('app.plugNmeetUrlReports');
            $announcementgroup = $this->em->getRepository(AnnouncementGroup::class)->find($parameters['announcementGroupId']);
            $classroomvirtuals = $this->em->getRepository(Classroomvirtual::class)->findBy(["announcementgroup" => $announcementgroup]);            
            $reportName = $plugNmeetUrlReports.'AnnouncementMeetings_'.$announcementgroup->getId().'.xls';

            foreach($classroomvirtuals as $classroomvirtual){
                array_push($reportData,json_decode($classroomvirtual->getClassroomVirtualResult()->getResult()));
                array_push($complementaryData, $this->getComplementaryData($classroomvirtual));
            }

            if ($reportData) {
                $urlReport = $this->excelReportGeneratorService->generateExcelReportAnnouncement($reportData, $complementaryData, $reportName);

                return [
                    'data' => $urlReport
                ];
            }

            return null;
        } catch (\Exception $e) {
            return [
                'data' => $e
            ];
        }
    }

    private function getComplementaryData(Classroomvirtual $classroomvirtual)
    {
        $announcement = $classroomvirtual->getAnnouncementgroup()->getAnnouncement(); 
        $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(["announcement" => $announcement]);          
        
        $complementaryData = new \stdClass();
        $complementaryData->courseName = $announcement->getCourse()->getName();
        $complementaryData->tutor = $announcementTutor->getTutor()->getFirstName().' '.$announcementTutor->getTutor()->getLastName();

        return $complementaryData;
    }
}
