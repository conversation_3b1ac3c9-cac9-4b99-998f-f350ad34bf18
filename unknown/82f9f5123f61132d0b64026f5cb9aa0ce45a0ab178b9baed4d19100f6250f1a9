<?php

namespace App\Repository;

use App\Entity\Holes;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Holes>
 *
 * @method Holes|null find($id, $lockMode = null, $lockVersion = null)
 * @method Holes|null findOneBy(array $criteria, array $orderBy = null)
 * @method Holes[]    findAll()
 * @method Holes[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class HolesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Holes::class);
    }

    public function add(Holes $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Holes $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

	public function updateOrCreate($data) {
		$model = $data['id'] ? $this->findOneBy(['fillgap' => $data['fillgap'], 'hole' => $data['id']]) : null;
		if (!$model) {
			$model = new Holes();
			$model->setFillgap($data['fillgap']);
			$model->setCorrect($data['correct']);
		}
		$model->setHole($data['id']);
		$model->setAnswer($data['text']);
		$this->getEntityManager()->persist($model);
	}

	public function removeExtraHoles($fillgaps, $maxID) {
		$query = $this->getEntityManager()->createQueryBuilder();
		$holes = $query->select(['h'])
			->from('App:Holes', 'h')
			->where($query->expr()->eq('h.fillgap', $fillgaps))
			->andWhere($query->expr()->gte('h.hole', $maxID))
			->getQuery()->getResult();

		if (!$holes) return;

		foreach ($holes as $hole) $this->remove($hole);
		$this->getEntityManager()->flush();
	}
}
