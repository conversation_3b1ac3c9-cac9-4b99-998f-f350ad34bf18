<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Report\Generators;

use App\Service\Annoucement\Report\AnnouncementContainer;
use App\Service\Annoucement\Report\AnnouncementReportConstants;
use App\Service\Annoucement\Report\AnnouncementReportDataService;
use App\Utils\SpreadsheetUtil;
use Psr\Log\LoggerInterface;

class TaskReportGenerator
{
    private AnnouncementReportDataService $announcementReportDataService;
    private AnnouncementReportConstants $announcementReportConstants;
    private LoggerInterface $logger;

    public function __construct(
        AnnouncementReportDataService $announcementReportDataService,
        AnnouncementReportConstants $announcementReportConstants,
        LoggerInterface $logger
    ) {
        $this->announcementReportDataService = $announcementReportDataService;
        $this->announcementReportConstants = $announcementReportConstants;
        $this->logger = $logger;
    }

    public function generate(
        AnnouncementContainer $announcementContainer,
        string $announcementDir,
        callable $initSheetCallback,
        bool $isOnlineMode
    ): void {
        $headersInfoTask = $isOnlineMode ?
            AnnouncementReportConstants::TASKS_HEADERS['info'] :
            [];

        $headersActivitiesTask = $isOnlineMode ?
            AnnouncementReportConstants::TASKS_HEADERS['activity'] :
            [];

        $report = new SpreadsheetUtil('Reporte de tareas', 'Info');

        $initSheetCallback($report, 'Info', $headersInfoTask);
        $dataInfoTask = $this->announcementReportDataService->task_infoSheetData(
            $announcementContainer->announcement,
            $announcementContainer->announcementGroups
        );

        foreach ($dataInfoTask as &$record) {
            $startDate = $record['startDate'] instanceof \DateTimeInterface
                ? $record['startDate']->format('d-m-Y H:i:s')
                : '';

            $dateDelivery = $record['dateDelivery'] instanceof \DateTimeInterface
                ? $record['dateDelivery']->format('d-m-Y H:i:s')
                : '';

            $isVisible = $record['isVisible'] ? 'Sí' : 'No';

            $record = [
                $record['taskId'],
                $record['title'],
                $startDate,
                $dateDelivery,
                (string) $record['filesTaskCount'],
                $isVisible
            ];
        }

        $report->fromArray($dataInfoTask, '--', 'A2')->alignAllLeft();

        $initSheetCallback($report, 'Actividad de tareas', $headersActivitiesTask);
        $dataActivityTask = $this->announcementReportDataService->task_activitySheetData(
            $announcementContainer->announcement,
            $announcementContainer->announcementGroups
        );

        foreach ($dataActivityTask as &$record) {
            if (\is_array($record['meta'])) {
                $record['meta'] = json_encode($record['meta']);
            }
        }

        $report->fromArray($dataActivityTask, '--', 'A2')->alignAllLeft();

        $report->saveReport($announcementDir);
    }
}
