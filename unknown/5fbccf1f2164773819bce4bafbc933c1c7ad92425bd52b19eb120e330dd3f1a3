<?php

namespace App\Service\Catalog\ListCatalog;

use App\Resources\Traits\FixturesTrait;

abstract class BaseCatalogService
{
    const PARAMETERS_BASE =  [
        'fieldsToTranslate' => ['name', 'description'],
        'setFields' => ['name' => 'setName', 'description' => 'setDescription'],
        'fieldState' => ['active' => 'setActive'],
        'setFieldsToTranslations' => ['name' => 'setName', 'description' => 'setDescription'],
        'parametersQuery' => ['id'],
        'fileTrans' => 'fixtures',
    ];

   use FixturesTrait;
}