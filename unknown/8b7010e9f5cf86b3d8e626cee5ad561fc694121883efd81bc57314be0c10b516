<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\LibraryViews;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<LibraryViews>
 *
 * @method LibraryViews|null find($id, $lockMode = null, $lockVersion = null)
 * @method LibraryViews|null findOneBy(array $criteria, array $orderBy = null)
 * @method LibraryViews[]    findAll()
 * @method LibraryViews[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LibraryViewsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LibraryViews::class);
    }

    public function add(LibraryViews $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(LibraryViews $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
