<?php

declare(strict_types=1);

namespace App\Entity;

class ChapterTypeConstants
{
    // 0 = no se tiene en cuenta
    // 0.4 = 40% de la puntuación
    // 0.6 = 60% de la puntuación
    // 1 = 100% de la puntuación
    public const DEFAULT_QUESTION_TIME = 30;
    public const PORCENTAJE_FOR_COMPLETE_GAME = 0.75;
    public const QUIZ_PORCENTAJE_FOR_COMPLETE = 0.70;
    public const GUESSWORD_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const MEMORYMATCH_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const TRUE_OR_FALSE_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const ADIVINA_IMAGEN_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const ROULETTE_WORD_TYPE_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const LETTERSOUP_PORCENTAJE_FOR_COMPLETE = 1; // El usuario tiene que completar todas las palabras
    public const FILLGAPS_PORCENTAJE_FOR_COMPLETE = 1;
    public const ORDENARMENORMAYOR_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const CATEGORIZE_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const WORDLE_PORCENTAJE_FOR_COMPLETE = 1;

    public const SCORM_TYPE = 1;
    public const SCORM_ICON = 'scorm.svg';
    public const SCORM_DETAIL_TEMPLATE = 'admin\scorm\detail.html.twig';
    public const SCORM_PLAYER_URL = '/scorm/';

    public const CONTENT_TYPE = 2;
    public const CONTENT_ICON = 'contents.svg';
    public const CONTENT_LIST_TEMPLATE = 'admin\content\list.html.twig';
    public const CONTENT_PLAYER_URL = '/contents/';

    public const GAMES_PLAYER_URL = '/games/?id=';

    public const ROULETTE_TYPE = 3;
    public const ROULETTE_ICON = 'roulette.svg';
    public const ROULETTE_MINIMUM_QUESTIONS = 2;
    public const ROULETTE_DESCRIPTION_LABEL = 'chapter.roulette.description';
    public const ROULETTE_FINISH_LABEL = 'chapter.default.finish';

    public const X2_TYPE = 4;
    public const X2_ICON = 'x2.svg';
    public const X2_MINIMUM_QUESTIONS = 5;
    public const X2_DESCRIPTION_LABEL = 'chapter.x2.description';
    public const X2_FINISH_LABEL = 'chapter.default.finish';

    public const QUIZ_TYPE = 5;
    public const QUIZ_ICON = 'quiz.svg';
    public const QUIZ_MINIMUM_QUESTIONS = 1;
    public const QUIZ_DESCRIPTION_LABEL = 'chapter.quiz.description';
    public const QUIZ_FINISH_LABEL = 'chapter.default.finish';

    public const PUZZLE_TYPE = 6;
    public const PUZZLE_ICON = 'puzzle.svg';
    public const PUZZLE_MINIMUM_QUESTIONS = 2;
    public const PUZZLE_DESCRIPTION_LABEL = 'chapter.puzzle.description';
    public const PUZZLE_FINISH_LABEL = 'chapter.default.finish';
    public const PUZZLE_TIME_GAPS = [20, 20, 20, 20];

    public const HIDE_WORDS_TYPE = 7;
    public const HIDE_WORD_ICON = 'hide-words.svg';
    public const HIDE_WORD_MINIMUM_QUESTIONS = 1;
    public const HIDE_WORD_DESCRIPTION_LABEL = 'chapter.hide-word.description';
    public const HIDE_WORD_FINISH_LABEL = 'chapter.default.finish';
    public const HIDE_WORD_QUESTION_TIME = 30;

    public const PDF_TYPE = 8;
    public const PDF_ICON = 'pdf.svg';
    public const PDF_DETAIL_TEMPLATE = 'admin\pdf\detail.html.twig';
    public const PDF_URL = '/pdf/';

    public const VIDEO_TYPE = 9;
    public const VIDEO_ICON = 'video.svg';
    public const VIDEO_DETAIL_TEMPLATE = 'admin\video\detail.html.twig';
    public const VIDEO_URL = '/video/';

    public const SLIDER_TYPE = 10;
    public const SLIDER_ICON = 'slider.svg';
    public const SLIDER_LIST_TEMPLATE = 'admin\slider\list.html.twig';
    public const SLIDER_PLAYER_URL = '/slider/';
    public const SLIDER_MINIMUM_IMAGE = 12;

    public const QUESTIONS_LIST_TEMPLATE = 'admin\question\list.html.twig';

    public const TYPE_GAME = 'game';
    public const TYPE_CONTENT = 'content';

    public const ROULETTE_WORD_TYPE = 11;
    public const ROULETTE_WORD_ICON = 'lettersWheel.svg';
    public const ROULETTE_WORD_TEMPLATE = 'admin\rouletteWord\detail.html.twig';
    public const ROULETTE_WORD_URL = '/roulette/';

    public const TRUEORFALSE_TYPE = 12;
    public const TRUEORFALSE_ICON = 'truefalse.svg';
    public const TRUEORFALSE_TEMPLATE = 'admin\trueOrFalse\detail.html.twig';
    public const TRUEORFALSE_DESCRIPTION_LABEL = 'chapter.trueorfalse.description';
    public const TRUEORFALSE_FINISH_LABEL = 'chapter.default.finish';
    public const TRUEORFALSE_URL = '/trueorfalse/';

    public const ADIVINAIMAGEN_TYPE = 13;
    public const ADIVINAIMAGEN_ICON = 'adivina.svg';
    public const ADIVINAIMAGEN_TEMPLATE = 'admin\adivinaImagen\detail.html.twig';
    public const ADIVINAIMAGEN_DESCRIPTION_LABEL = 'chapter.adivinaImagen.description';
    public const ADIVINAIMAGEN_FINISH_LABEL = 'chapter.default.finish';
    public const ADIVINAIMAGEN_URL = '/adivinaImagen/';

    public const ORDENARMENORMAYOR_TYPE = 14;
    public const ORDENARMENORMAYOR_ICON = 'jerarquiza.svg';
    public const ORDENARMENORMAYOR_TEMPLATE = 'admin\ordenarMenorMayor\detail.html.twig';
    public const ORDENARMENORMAYOR_DESCRIPTION_LABEL = 'chapter.ordenarMenorMayor.description';
    public const ORDENARMENORMAYOR_FINISH_LABEL = 'chapter.default.finish';
    public const ORDENARMENORMAYOR_URL = '/ordenarMenorMayor/';

    public const MEMORYMATCH_TYPE = 15;
    public const MEMORYMATCH_MINIMUM_QUESTIONS = 2;
    public const MEMORYMATCH_ICON = 'parejas.svg';
    public const MEMORYMATCH_TEMPLATE = 'admin\parejas\detail.html.twig';
    public const MEMORYMATCH_DESCRIPTION_LABEL = 'chapter.Parejas.description';
    public const MEMORYMATCH_FINISH_LABEL = 'chapter.default.finish';
    public const MEMORYMATCH_URL = '/parejas/';

    public const CATEGORIZED_TYPE = 16;
    public const CATEGORIZED_ICON = 'categorized.svg';
    public const CATEGORIZED_TEMPLATE = 'admin\categorize\detail.html.twig';
    public const CATEGORIZED_DESCRIPTION_LABEL = 'chapter.categorize.description';
    public const CATEGORIZED_FINISH_LABEL = 'chapter.default.finish';
    public const CATEGORIZED_URL = '/categorized/';

    public const FILLGAPS_TYPE = 17;
    public const FILLGAPS_ICON = 'huecos.svg';
    public const FILLGAPS_TEMPLATE = 'admin\fillgaps\detail.html.twig';
    public const FILLGAPS_DESCRIPTION_LABEL = 'chapter.fillgaps.description';
    public const FILLGAPS_FINISH_LABEL = 'chapter.default.finish';
    public const FILLGAPS_URL = '/fillgaps/';

    public const GUESSWORD_TYPE = 18;
    public const GUESSWORD_ICON = 'ordena_letras.svg';
    public const GUESSWORD_TEMPLATE = 'admin\guessword\detail.html.twig';
    public const GUESSWORD_DESCRIPTION_LABEL = 'chapter.guessword.description';
    public const GUESSWORD_FINISH_LABEL = 'chapter.default.finish';
    public const GUESSWORD_URL = '/guessword/';

    public const WORDLE_TYPE = 19;
    public const WORDLE_ICON = 'palabra_secreta.svg';
    public const WORDLE_TEMPLATE = 'admin\wordle\detail.html.twig';
    public const WORDLE_DESCRIPTION_LABEL = 'chapter.wordle.description';
    public const WORDLE_FINISH_LABEL = 'chapter.default.finish';
    public const WORDLE_URL = '/wordle/';

    public const LETTERSOUP_TYPE = 20;
    public const LETTERSOUP_ICON = 'sopa_letras.svg';
    public const LETTERSOUP_TEMPLATE = 'admin\lettersoup\detail.html.twig';
    public const LETTERSOUP_DESCRIPTION_LABEL = 'chapter.lettersoup.description';
    public const LETTERSOUP_FINISH_LABEL = 'chapter.default.finish';
    public const LETTERSOUP_URL = '/lettersoup/';

    public const VIDEOQUIZ_TYPE = 21;
    public const VIDEOQUIZ_QUESTION_TIME = 20; // tiempo global por pregunta, esta en segundos
    public const VIDEOQUIZ_ICON = 'videoquiz.svg';
    public const VIDEOQUIZ_TEMPLATE = 'admin\videoquiz\detail.html.twig';
    public const VIDEOQUIZ_DESCRIPTION_LABEL = 'chapter.videoquiz.description';
    public const VIDEOQUIZ_FINISH_LABEL = 'chapter.default.finish';
    public const VIDEOQUIZ_URL = '/videoquiz/';

    public const VCMS_TYPE = 22;
    public const VCMS_ICON = 'vcms.svg';
    public const VCMS_TEMPLATE = 'admin\vcms\detail.html.twig';
    public const VCMS_URL_VIEW = '/vcms/visor/%s';
    public const VCMS_URL_EDIT = '/vcms/project-manager/%s';

    public const ROLEPLAY_TYPE = 23;
    public const ROLEPLAY_ICON = 'roleplay.svg';
    public const ROLEPLAY_TEMPLATE = 'admin\roleplay\detail.html.twig';
    public const ROLEPLAY_URL_VIEW = '/roleplay/visor/%s';
    public const ROLEPLAY_URL_EDIT = '/roleplay/project-manager/%s';

    public const PPT_TYPE = 24;
    public const PPT_ICON = 'ppt.svg';
    public const PPT_DETAIL_TEMPLATE = 'admin\ppt\detail.html.twig';
    public const PPT_URL = '/ppt/';

    public const LTI_TYPE = 25;
    public const LTI_ICON = 'lti.svg';
    public const LTI_DETAIL_TEMPLATE = 'admin\lti_chapter\detail.html.twig';
    public const LTI_PLAYER_URL = '/lti/%userId/%chapterId';
}
