<?php

namespace App\Controller\Admin;

use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\DeleteFilePathTrait;
use App\Entity\Chapter;
use App\Entity\Parejas;
use App\Entity\ParejasImagen;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ParejasCrudController extends AbstractCrudController
{
	use DeleteFilePathTrait, SerializerTrait;

	private   $em;
	private   $requestStack;
	private   $logger;
	private   $context;
	protected $translator;
	protected $adminUrlGenerator;

	public function __construct(EntityManagerInterface $em, RequestStack $requestStack, LoggerInterface $logger, AdminContextProvider $context, TranslatorInterface $translator, AdminUrlGenerator $adminUrlGenerator)
	{
		$this->em = $em;
		$this->requestStack = $requestStack;
		$this->logger = $logger;
		$this->context = $context;
		$this->translator = $translator;
		$this->adminUrlGenerator = $adminUrlGenerator;
	}

	public static function getEntityFqcn(): string
	{
		return Parejas::class;
	}


	/**
	 * @Route("/admin/new/parejas", name="admin_parejas_new", methods={"POST"})
	 */
	public function createParejas(Request $request)
	{
		try {
			$title = $request->get('title');
			$time = $request->get('time');
			$image = $request->files->get('image');
			$idChapter = $request->get('idChapter');
			$chapter = $this->em->getRepository(Chapter::class)->find($idChapter);

			$parejas = new Parejas();
			$parejas->setTiempo($time);
			$parejas->setChapter($chapter);
			$parejas->setTipo(1);
			$this->em->persist($parejas);

			$parejasImagen = new ParejasImagen();
			$parejasImagen->setParejas($parejas);
			$parejasImagen->setTexto($title);

			if ($image != null) {
				$parejasImagen->setImagen('');
				$parejasImagen->setImageFile($image);
			}

			$this->em->persist($parejasImagen);

			$this->em->flush();

			$response = [
				'status'  => 200,
				'message' => 'Parejas created successfully',
				'route'   => $this->urlChapter($idChapter)
			];
		} catch (\Exception $e) {
			$response = [
				'status' => 500,
				'error'  => true,
				'data'   => $e->getMessage(),
			];
		}

		return $this->sendResponse($response);
	}

	/**
	 * @Route("/admin/edit/parejas", name="admin_parejas_edit", methods={"POST"})
	 */
	public function editParejas(Request $request)
	{
		try {
			$id = $request->get('id');
			$title = $request->get('title');
			$time = $request->get('time');
			$image = $request->files->get('image');
			$idChapter = $request->get('idChapter');
			$chapter = $this->em->getRepository(Chapter::class)->find($idChapter);

			$parejas = $this->em->getRepository(Parejas::class)->find($id);
			$parejas->setTiempo($time);
			$parejas->setChapter($chapter);
			$parejas->setTipo(1);
			$this->em->persist($parejas);

			$parejasImagen = $this->em->getRepository(ParejasImagen::class)->findOneBy(['parejas' => $parejas]);
			$parejasImagen->setParejas($parejas);
			$parejasImagen->setTexto($title);

			if ($image != null) {
				if (!is_null($parejasImagen->getImagen())) $this->deleteFile($this->getParameter('app.gameParejasImagen_uploads_path'), $parejasImagen->getImagen());
				$parejasImagen->setImagen('');
				$parejasImagen->setImageFile($image);
			}
			$this->em->persist($parejasImagen);

			$this->em->flush();

			$response = [
				'status'  => 200,
				'message' => 'Parejas edited successfully',
				'route'   => $this->urlChapter($idChapter)
			];
		} catch (\Exception $e) {
			$response = [
				'status' => 500,
				'error'  => true,
				'data'   => $e->getMessage(),
			];
		}

		return $this->sendResponse($response);
	}

	/**
	 * @Route("/admin/delete/parejas", name="admin_parejas_delete", methods={"POST"})
	 */

	public function deleteParejas(Request  $request)
	{
		try {
			$id = $request->get('id');
			$idChapter = $request->get('idChapter');
			$parejas = $this->em->getRepository(Parejas::class)->find($id);
			$parejasImagen = $this->em->getRepository(ParejasImagen::class)->findOneBy(['parejas' => $parejas]);
			if (!is_null($parejasImagen->getImagen())) $this->deleteFile($this->getParameter('app.gameParejasImagen_uploads_path'), $parejasImagen->getImagen());
			$this->em->remove($parejasImagen);
			$this->em->remove($parejas);
			$this->em->flush();

			$response = [
				'status'  => 200,
				'message' => 'Parejas deleted successfully',
				'route'   => $this->urlChapter($idChapter)
			];
		} catch (\Exception $e) {
			$response = [
				'status' => 500,
				'error'  => true,
				'data'   => $e->getMessage(),
			];
		}

		return $this->sendResponse($response);
	}

	/**
	 * @Route("/admin/parejas/{id}", name="admin_parejas", methods={"GET"})
	 */
	public function fetchParejas(Chapter $chapter)
	{
		try {
			$parejas = $this->em->getRepository(Parejas::class)->findBy(['chapter' => $chapter, 'tipo' => 1]);
			$parejasArray = [];
			foreach ($parejas as $pareja) {
				$parejasImagen = $this->em->getRepository(ParejasImagen::class)->findOneBy(['parejas' => $pareja]);
				$parejasArray[] = [
					'id' => $pareja->getId(),
					'time' => $pareja->getTiempo(),
					'chapter' => $pareja->getChapter()->getId(),
					'text' => $parejasImagen->getTexto(),
					'image' => $parejasImagen->getImagen(),
				];
			}
			$response = [
				'status' => 200,
				'error' => false,
				'data'   => $parejasArray,
			];
		} catch (\Exception $e) {
			$response = [
				'status' => 500,
				'error'  => true,
				'data'   => $e->getMessage(),
			];
		}

		return $this->sendResponse($response);
	}

	private function urlChapter($chapterId)
	{
		return $this->adminUrlGenerator
			->unsetAll()
			->setController(ChapterCrudController::class)
			->setAction('edit')
			->setEntityId($chapterId)
			->generateUrl();
	}
}
