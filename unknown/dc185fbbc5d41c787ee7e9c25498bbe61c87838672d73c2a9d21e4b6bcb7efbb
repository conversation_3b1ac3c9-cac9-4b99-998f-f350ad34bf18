<?php

declare(strict_types=1);

namespace App\Entity;

use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use App\Repository\AnnouncementGroupRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=AnnouncementGroupRepository::class)
 */
class AnnouncementGroup
{
    use Blamable;
    use Timestampable;

    public const TYPE_ANNOUNCEMENT_GROUP = 'announcement_group';
    public const TYPE_ANNOUNCEMENT_DIRECT = Announcement::CHAT_CHANNEL_DIRECT . '_GROUP_CHAT';
    public const CHAT_CHANNEL_GROUP_CHAT = ChatChannel::TYPE_GROUP_CHAT;

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"classroomvirtual"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $companyProfile;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private $code;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $companyCif;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     *
     * @Groups({"classroomvirtual"})
     */
    private $denomination;

    /**
     * Número de expediente.
     *
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private $fileNumber;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementUser::class, mappedBy="announcementGroup", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $announcementUsers;

    /**
     * @ORM\ManyToOne(targetEntity=Announcement::class, inversedBy="announcementGroups")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $announcement;

    /**
     * @ORM\OneToOne(targetEntity=AnnouncementTutor::class, mappedBy="announcementGroup", cascade={"persist", "remove"})
     */
    private $announcementTutor;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $numSessions;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $place;

    /**
     * @ORM\OneToOne(targetEntity=SessionsAnnouncement::class, inversedBy="announcementGroup", cascade={"persist", "remove"})
     */
    private $sessionsAnnouncement;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementGroupSession::class, mappedBy="announcementGroup", orphanRemoval=true, cascade={"persist", "remove"})
     */
    private $announcementGroupSessions;

    /**
     * @ORM\OneToMany(targetEntity=Classroomvirtual::class, mappedBy="announcementgroup")
     */
    private $classroomvirtuals;

    /**
     * @ORM\OneToMany(targetEntity=TaskCourseGroup::class, mappedBy="announcementGroup")
     */
    private $taskCourseGroups;

    /**
     * @ORM\OneToMany(targetEntity=AnnouncementNotificationGroup::class, mappedBy="announcementGroup")
     */
    private $announcementNotificationGroups;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $groupNumber;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=2, nullable=true)
     */
    private $cost;

    /**
     * @ORM\ManyToOne(targetEntity=TypeMoney::class, inversedBy="announcementGroups")
     */
    private $typeMoney;

    public function __construct()
    {
        $this->createdAt = new \DateTime('now', new \DateTimeZone('UTC'));
        $this->updatedAt = new \DateTime('now', new \DateTimeZone('UTC'));
        $this->announcementUsers = new ArrayCollection();
        $this->announcementGroupSessions = new ArrayCollection();
        $this->classroomvirtuals = new ArrayCollection();
        $this->taskCourseGroups = new ArrayCollection();
        $this->announcementNotificationGroups = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCompanyProfile(): ?string
    {
        return $this->companyProfile;
    }

    public function setCompanyProfile(?string $companyProfile): self
    {
        $this->companyProfile = $companyProfile;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getCompanyCif(): ?string
    {
        return $this->companyCif;
    }

    public function setCompanyCif(?string $companyCif): self
    {
        $this->companyCif = $companyCif;

        return $this;
    }

    public function getDenomination(): ?string
    {
        return $this->denomination;
    }

    public function setDenomination(?string $denomination): self
    {
        $this->denomination = $denomination;

        return $this;
    }

    public function getFileNumber(): ?string
    {
        return $this->fileNumber;
    }

    public function setFileNumber(?string $fileNumber): self
    {
        $this->fileNumber = $fileNumber;

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementUser>
     */
    public function getAnnouncementUsers(): Collection
    {
        return $this->announcementUsers;
    }

    public function setAnnouncementUsers($announcementUsers): self
    {
        $old = $this->getAnnouncementUsers();
        foreach ($old as $o) {
            if (!\in_array($o, $announcementUsers)) {
                $this->removeAnnouncementUser($o);
            }
        }

        foreach ($announcementUsers as $u) {
            $this->addAnnouncementUser($u);
        }

        return $this;
    }

    public function addAnnouncementUser(AnnouncementUser $announcementUser): self
    {
        if (!$this->announcementUsers->contains($announcementUser)) {
            $this->announcementUsers[] = $announcementUser;
            $announcementUser->setannouncementGroup($this);
        }

        return $this;
    }

    public function removeAnnouncementUser(AnnouncementUser $announcementUser): self
    {
        if ($this->announcementUsers->removeElement($announcementUser)) {
            // set the owning side to null (unless already changed)
            if ($announcementUser->getannouncementGroup() === $this) {
                $announcementUser->setannouncementGroup(null);
            }
        }

        return $this;
    }

    public function getAnnouncement(): ?Announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement(?Announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }

    public function getAnnouncementTutor(): ?AnnouncementTutor
    {
        return $this->announcementTutor;
    }

    public function setAnnouncementTutor(?AnnouncementTutor $announcementTutor): self
    {
        // unset the owning side of the relation if necessary
        if (null === $announcementTutor && null !== $this->announcementTutor) {
            $this->announcementTutor->setAnnouncementGroup(null);
        }

        // set the owning side of the relation if necessary
        if (null !== $announcementTutor && $announcementTutor->getAnnouncementGroup() !== $this) {
            $announcementTutor->setAnnouncementGroup($this);
        }

        $this->announcementTutor = $announcementTutor;

        return $this;
    }

    public function getNumSessions(): ?int
    {
        return $this->numSessions;
    }

    public function setNumSessions(?int $numSessions): self
    {
        $this->numSessions = $numSessions;

        return $this;
    }

    public function getPlace(): ?string
    {
        return $this->place;
    }

    public function setPlace(?string $place): self
    {
        $this->place = $place;

        return $this;
    }

    public function getSessionsAnnouncement(): ?SessionsAnnouncement
    {
        return $this->sessionsAnnouncement;
    }

    public function setSessionsAnnouncement(?SessionsAnnouncement $sessionsAnnouncement): self
    {
        $this->sessionsAnnouncement = $sessionsAnnouncement;

        return $this;
    }

    public function __toString()
    {
        return $this->id . '-' . $this->code;
    }

    /**
     * @return Collection<int, AnnouncementGroupSession>
     */
    public function getAnnouncementGroupSessions(): Collection
    {
        return $this->announcementGroupSessions;
    }

    public function setAnnouncementGroupSessions($announcementGroupSessions): void
    {
        $old = $this->getAnnouncementGroupSessions();
        foreach ($old as $o) {
            if (!\in_array($o, $announcementGroupSessions)) {
                $this->removeAnnouncementGroupSession($o);
            }
        }

        foreach ($announcementGroupSessions as $session) {
            $this->addAnnouncementGroupSession($session);
        }
    }

    public function addAnnouncementGroupSession(AnnouncementGroupSession $announcementGroupSession): self
    {
        if (!$this->announcementGroupSessions->contains($announcementGroupSession)) {
            $this->announcementGroupSessions[] = $announcementGroupSession;
            $announcementGroupSession->setAnnouncementGroup($this);
        }

        return $this;
    }

    public function removeAnnouncementGroupSession(AnnouncementGroupSession $announcementGroupSession): self
    {
        if ($this->announcementGroupSessions->removeElement($announcementGroupSession)) {
            // set the owning side to null (unless already changed)
            if ($announcementGroupSession->getAnnouncementGroup() === $this) {
                $announcementGroupSession->setAnnouncementGroup(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Classroomvirtual>
     */
    public function getClassroomvirtuals(): Collection
    {
        return $this->classroomvirtuals;
    }

    public function addClassroomvirtual(Classroomvirtual $classroomvirtual): self
    {
        if (!$this->classroomvirtuals->contains($classroomvirtual)) {
            $this->classroomvirtuals[] = $classroomvirtual;
            $classroomvirtual->setAnnouncementgroup($this);
        }

        return $this;
    }

    public function removeClassroomvirtual(Classroomvirtual $classroomvirtual): self
    {
        if ($this->classroomvirtuals->removeElement($classroomvirtual)) {
            // set the owning side to null (unless already changed)
            if ($classroomvirtual->getAnnouncementgroup() === $this) {
                $classroomvirtual->setAnnouncementgroup(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, TaskCourseGroup>
     */
    public function getTaskCourseGroups(): Collection
    {
        return $this->taskCourseGroups;
    }

    public function addTaskCourseGroup(TaskCourseGroup $taskCourseGroup): self
    {
        if (!$this->taskCourseGroups->contains($taskCourseGroup)) {
            $this->taskCourseGroups[] = $taskCourseGroup;
            $taskCourseGroup->setAnnouncementGroup($this);
        }

        return $this;
    }

    public function removeTaskCourseGroup(TaskCourseGroup $taskCourseGroup): self
    {
        if ($this->taskCourseGroups->removeElement($taskCourseGroup)) {
            // set the owning side to null (unless already changed)
            if ($taskCourseGroup->getAnnouncementGroup() === $this) {
                $taskCourseGroup->setAnnouncementGroup(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, AnnouncementNotificationGroup>
     */
    public function getAnnouncementNotificationGroups(): Collection
    {
        return $this->announcementNotificationGroups;
    }

    public function addAnnouncementNotificationGroup(AnnouncementNotificationGroup $announcementNotificationGroup): self
    {
        if (!$this->announcementNotificationGroups->contains($announcementNotificationGroup)) {
            $this->announcementNotificationGroups[] = $announcementNotificationGroup;
            $announcementNotificationGroup->setAnnouncementGroup($this);
        }

        return $this;
    }

    public function removeAnnouncementNotificationGroup(AnnouncementNotificationGroup $announcementNotificationGroup): self
    {
        if ($this->announcementNotificationGroups->removeElement($announcementNotificationGroup)) {
            // set the owning side to null (unless already changed)
            if ($announcementNotificationGroup->getAnnouncementGroup() === $this) {
                $announcementNotificationGroup->setAnnouncementGroup(null);
            }
        }

        return $this;
    }

    public function getGroupNumber(): ?int
    {
        return $this->groupNumber;
    }

    public function setGroupNumber(?int $groupNumber): self
    {
        $this->groupNumber = $groupNumber;

        return $this;
    }

    public function getCost(): ?string
    {
        return $this->cost;
    }

    public function setCost(string $cost): self
    {
        $this->cost = $cost;

        return $this;
    }

    public function getTypeMoney(): ?TypeMoney
    {
        return $this->typeMoney;
    }

    public function setTypeMoney(?TypeMoney $typeMoney): self
    {
        $this->typeMoney = $typeMoney;

        return $this;
    }

    public function __clone()
    {
        $this->id = null;
    }
}
