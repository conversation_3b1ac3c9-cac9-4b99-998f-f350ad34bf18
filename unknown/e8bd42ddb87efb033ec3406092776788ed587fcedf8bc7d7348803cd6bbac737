<?php

namespace App\Controller\Admin;

use App\Admin\Field\TranslationField;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Translation\TranslatorInterface;


class FilterCategoryCrudController extends AbstractCrudController
{

    private EntityManagerInterface $em;
    private $requestStack;
    private $logger;
    protected TranslatorInterface $translator;
    private AdminContextProvider $context;
    private SettingsService $settings;


    public function __construct(EntityManagerInterface $em, RequestStack $requestStack, LoggerInterface $logger, AdminContextProvider $context, TranslatorInterface $translator, SettingsService $settings)
    {
        $this->em =             $em;
        $this->requestStack =   $requestStack;
        $this->logger =         $logger;
        $this->context = $context;
        $this->translator = $translator;
        $this->settings = $settings;
    }

    public static function getEntityFqcn(): string
    {
        return FilterCategory::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular($this->translator->trans('filter_category.label_in_singular', [], 'messages', $this->getUser()->getLocale()))
            ->setEntityLabelInPlural($this->translator->trans('filter_category.label_in_plural', [], 'messages', $this->getUser()->getLocale()))
            ->setSearchFields(['name'])
            ->addFormTheme('@A2lixTranslationForm/bootstrap_4_layout.html.twig')
             ->overrideTemplate('crud/detail', 'admin/filter/detail.html.twig');

    }

    public function configureFields(string $pageName): iterable
    {
        yield IdField::new('id', '#')->hideOnForm()
            ->setTemplatePath('bundles/EasyAdminBundle/field-linked.html.twig');
        if (Crud::PAGE_EDIT == $pageName && $this->settings->get('app.multilingual')) {
            yield FormField::addPanel($this->translator->trans('filter_category.label_in_singular', [], 'messages', $this->getUser()->getLocale()))
                ->addCssClass('col-xs-12 col-md-6');
        }

        yield TextField::new('name', $this->translator->trans('help_category.configureFields.category_name', [], 'messages', $this->getUser()->getLocale()))
            ->setColumns('col-xs-12 col-md-6')
            ->setTemplatePath('bundles/EasyAdminBundle/field-linked.html.twig');

        if (Crud::PAGE_EDIT == $pageName && $this->settings->get('app.multilingual')) {
            yield FormField::addPanel($this->translator->trans('course_category.configureFields.translations', [], 'messages', $this->getUser()->getLocale()))
                ->addCssClass('translation-form-panel form-panel-hide-legend col-xs-12 col-md-6');
            yield TranslationField::new('translations', $this->translator->trans('help_category.configureFields.translations', [], 'messages', $this->getUser()->getLocale()), [
                'name' => [
                    'label'      => $this->translator->trans('help_category.configureFields.category_name', [], 'messages', $this->getUser()->getLocale()),
                ],
            ]);
        }
    }

    public function configureActions (Actions $actions): Actions
    {
        return $actions
        ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

     public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName')) {

            $filterCategoryRepository = $this->em->getRepository(FilterCategory::class);
            $entity = $this->context->getContext()->getEntity();
            $filterCategory = $filterCategoryRepository->find($entity->getPrimaryKeyValue());

            $filterRepository = $this->em->getRepository(Filter::class);
            $filters = $filterRepository->findBy([
                'filterCategory' => $filterCategory
            ]);

            $adminUrlGenerator = $this->get(AdminUrlGenerator::class);
            $referrer = $adminUrlGenerator
                ->unsetAll()
                ->setController(FilterCategoryCrudController::class)
                ->setAction('detail')
                ->setEntityId($filterCategory->getId())
                ->generateUrl();


            $responseParameters->set('filterCategory', $filterCategory);
            $responseParameters->set('filters', $filters);
            $responseParameters->set('referrer', $referrer);

    }
        return $responseParameters;
    }


}
