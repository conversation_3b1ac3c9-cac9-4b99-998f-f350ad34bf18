<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\LtiTool;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<LtiTool>
 *
 * @method LtiTool|null find($id, $lockMode = null, $lockVersion = null)
 * @method LtiTool|null findOneBy(array $criteria, array $orderBy = null)
 * @method LtiTool[]    findAll()
 * @method LtiTool[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LtiToolRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LtiTool::class);
    }

    public function add(LtiTool $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(LtiTool $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findChapterByIndentifier($identifier)
    {
        $sql = 'SELECT lc.chapter_id
            FROM lti_tool lt
            INNER JOIN lti_chapter lc ON lc.lti_tool_id = lt.id
            WHERE lc.identifier = :IDENTIFIER
        ';

        $conn = $this->_em->getConnection();
        $query = $conn->prepare($sql);
        $results_raw = $query->executeQuery(['IDENTIFIER' => $identifier]);

        return $results_raw->fetchAssociative();
    }

    public function findConfigurationByChapterId($chapterId)
    {
        $sql = 'SELECT lt.*
            FROM lti_tool lt
            INNER JOIN lti_chapter lc ON lc.lti_tool_id = lt.id
            WHERE lc.chapter_id = :CHAPTER_ID
        ';

        $conn = $this->_em->getConnection();
        $query = $conn->prepare($sql);
        $results_raw = $query->executeQuery(['CHAPTER_ID' => $chapterId]);

        return $results_raw->fetchAssociative();
    }

    public function findConfigurationByClientId($clientId)
    {
        $sql = 'SELECT *
            FROM lti_tool
            WHERE client_id = :CLIENT_ID
        ';

        $conn = $this->_em->getConnection();
        $query = $conn->prepare($sql);
        $results_raw = $query->executeQuery(['CLIENT_ID' => $clientId]);

        return $results_raw->fetchAssociative();
    }
}
