<?php

namespace App\Service\VirtualClass;


use App\Entity\User;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;



class JitsiService extends BaseVirtualClassService implements ClassroomVirtualInterface
{
    private $userRepository;
    private $security;


    public function __construct(
        EntityManagerInterface          $em,
        SettingsService           $settings,
        UserRepository                  $userRepository,
        Security $security


    ) {
        parent::__construct($em, $settings);
        $this->userRepository = $userRepository;
        $this->security = $security;
    }



    private function getUser(): User
    {
        return $this->userRepository->find($this->security->getUser());
    }


    public function createRoom(array $parameters): array
    {
        // TODO: Implement createRoom() method.
        return [];
    }

    public function registerUsersInTheProvider(array $parametros): array
    {
        // TODO: Implement registerUsersInTheProvider() method.
        return [];
    }

    public function saveRoomAndUsersInEasylearning(array $parametros, array $answerApiProvider): void
    {
        // TODO: Implement saveRoomAndUsersInEasylearning() method.

    }

    public function deleteRoom(array $parameters): array
    {
        // TODO: Implement deleteRoom() method.
        return [];
    }


    public function getAssistanceRoomUser(array $parameters): array
    {
        // TODO: Implement getAssistanceRoomUser() method.
        return [];
    }

    public function addResultProviders(array $parameters): array
    {
        
    }

    public function generateSessionExcelReport(array $parameters): ?array
    {
    }

    
    public function generateAnnouncementExcelReport(array $parameters): ?array
    { 
    }
}
