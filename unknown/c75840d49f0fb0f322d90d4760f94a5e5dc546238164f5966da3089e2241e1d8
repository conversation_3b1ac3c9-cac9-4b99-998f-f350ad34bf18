<?php

namespace App\Controller\Admin;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use App\Entity\Course;
use App\Entity\FilterCategory;
use App\Entity\Filter;
use App\Admin\Traits\SerializerTrait;
use App\Service\SettingsService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Mailer\MailerInterface;
class CourseFilterCrudController extends AbstractController
{
    use SerializerTrait;

    public function __construct (SettingsService $settings, EntityManagerInterface $em, LoggerInterface $logger, RequestStack $requestStack, JWTManager $jwt, TranslatorInterface $translator, MailerInterface $mailer)
    {
        $this->settings       = $settings;
        $this->em           = $em;
        $this->logger       = $logger;
        $this->requestStack = $requestStack;
        $this->jwt          = $jwt;
        $this->translator   = $translator;
        $this->mailer       = $mailer;
    }

    /**
     * @Route ("/admin/courses/{course}/filters/{filter_category}", name="course_filter_list")
     * * @param Course $course
     * * @param FilterCategory $filter_category
     * @return Response
     */
    public function getCourseFilters(Course $course,FilterCategory $filter_category)
    {
        $filterRepository = $this->em->getRepository(Filter::class);
        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => ['courseFilters' => $filterRepository->getCourseFilters($course->getId(),$filter_category->getId())],
        ];
        return $this->sendResponse($response, ['groups' => 'user_area']);
    }
}
