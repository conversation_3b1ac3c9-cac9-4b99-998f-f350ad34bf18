{% block head_stylesheets %}
  {{ encore_entry_link_tags('diploma') }}
{% endblock %}
<link href="{{ asset(build_dir ~ '/diploma.css') }}" rel="stylesheet">

<div class="fondo" style="background-image: url('{{ assets_dir }}/diploma/fundae/f_fundae.svg');">
  <div class="headFundae">
    <div class="logoFundae">
      <img src="{{ assets_dir }}/diploma/fundae/logo_fundae.png" width="18rem" style="margin-bottom: 2rem" />
    </div>
    <p class="border"></p>
    <p class="textDiplomaFundae">{{ 'message_api.diploma.diploma'|trans({}, 'message_api', locale) }}</p>
    <p class="accreditation">{{ 'message_api.diploma.acreditation'|trans({}, 'message_api', locale) }}</p>
    <p class="border"></p>
  </div>

  <div class="bodyDiplomaFundae">
    <table width="100%">
      <tr>
        <td class="text" colspan="2">
          <div style="display: flex;">
            <span>{{ 'message_api.diploma.trato'|trans({}, 'message_api', locale) }}:</span>
            <span class="text-field" style="flex: 1 0 0"><strong>{{ user.firstName }} {{ user.lastName }}</strong></span>
          </div>
        </td>

        <td class="text" colspan="2">
          {{ 'message_api.diploma.nif'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ user.registerKey }}</strong></span>
        </td>
      </tr>

      <tr>
        <td class="text" colspan="2">
          {{ 'message_api.diploma.services'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ enterprise }}</strong></span>
        </td>

        <td class="text" colspan="2">
          {{ 'message_api.diploma.cif'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ enterpriseCIF }}</strong></span>
        </td>
      </tr>
    </table>

    <br />

    <table>
      <tr>
        <td class="text" colspan="4">
          {{ 'message_api.diploma.evaluation'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ course }}</strong></span>
        </td>
      </tr>

      <tr>
        <td class="text" colspan="2">
          {{ 'message_api.diploma.code'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ codeGroup }}</strong></span>
        </td>

        <td class="text" colspan="2">
          {{ 'message_api.diploma.between'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ startDate }} / {{ finishDate }}</strong></span>
        </td>
      </tr>

      <tr>
        <td class="text" colspan="2">
          {{ 'message_api.diploma.duration'|trans({}, 'message_api', locale) }}:
          <span class="text" style="text-align: center; margin-left: 3rem"><strong>{{ 'message.api.diploma.hours'|trans({ '%count%': announcement.totalHours ? announcement.totalHours : 1 }, 'message_api', locale) }}</strong></span>
        </td>

        <td class="text" colspan="2">
          {{ 'message_api.diploma.fomartionType'|trans({}, 'message_api', locale) }}:
          <span class="text-field">
            <strong>
              {% if course.typeCourse %}
                <span>{{ course.typeCourse.name }}</span>
              {% else %}
                <span>{{ 'message.api.diploma.type_course_default'|trans({}, 'message_api', locale) }}</span>
              {% endif %}
            </strong>
          </span>
        </td>
      </tr>

    </table>

    <br />

    <div style="text-align: right">
      <span>{{ 'message_api.diploma.courseContent'|trans({}, 'message_api', locale) }}</span>
    </div>
  </div>

  {{ include('diploma/templates_common/signatures.html.twig', { urlSignature: './assets/diploma/fundae/signature.png' }) }}
</div>

{% block body_javascript %}
  {{ encore_entry_script_tags('diploma') }}
{% endblock %}
