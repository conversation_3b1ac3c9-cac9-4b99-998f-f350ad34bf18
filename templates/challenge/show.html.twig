{% extends 'base.html.twig' %}

{% block title %}Challenge{% endblock %}

{% block body %}
    <h1>{{ 'challenges.challenges'|trans({}, 'translations', announcementUser.user.locale) }}</h1>

    <table class="table">
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ challenge.id }}</td>
            </tr>
            <tr>
                <th>{{ 'content.configureFields.title'|trans({}, 'messages', announcementUser.user.locale) }}</th>
                <td>{{ challenge.title }}</td>
            </tr>
            <tr>
                <th>{{ 'taskCourse.configureFields.startDate'|trans({}, 'messages', announcementUser.user.locale) }}</th>
                <td>{{ challenge.startDate ? challenge.startDate|date('Y-m-d H:i:s') : '' }}</td>
            </tr>
            <tr>
                <th>{{ 'stats.export.end_date'|trans({}, 'messages', announcementUser.user.locale) }}</th>
                <td>{{ challenge.endDate ? challenge.endDate|date('Y-m-d H:i:s') : '' }}</td>
            </tr>
            <tr>
                <th>{{ 'chapter.configureFields.image'|trans({}, 'messages', announcementUser.user.locale) }}</th>
                <td>{{ challenge.image }}</td>
            </tr>
        </tbody>
    </table>

    <a href="{{ path('challenge_index') }}">{{ 'common_areas.back_list'|trans({}, 'messages', announcementUser.user.locale) }}</a>

    <a href="{{ path('challenge_edit', {'id': challenge.id}) }}">{{ 'common_areas.edit'|trans({}, 'messages', announcementUser.user.locale) }}</a>

    {{ include('challenge/_delete_form.html.twig') }}
{% endblock %}
