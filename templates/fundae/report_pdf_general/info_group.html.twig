<div class="course-data">
  <h2>{{ 'messages.infgroup'|trans({}, 'translations', app.user.locale) }}</h2>
  <ul>
    <li>
      <strong>{{ 'report.announcement.groupCode'|trans({}, 'messages', app.user.locale) }} :</strong>
      {{ informationGroup.code }}
    </li>
    <li>
      <strong>{{ 'report.announcement.enterpriseProfile'|trans({}, 'messages', app.user.locale) }} :</strong>
      {{ informationGroup.companyProfile }}
    </li>
    <li>
      <strong>{{ 'report.announcement.enterpriseCIF'|trans({}, 'messages', app.user.locale) }} :</strong>
      {{ informationGroup.companyCif }}
    </li>
    <li>
      <strong>{{ 'report.announcement.file'|trans({}, 'messages', app.user.locale) }} :</strong>
      {{ informationGroup.numExpendient }}
    </li>
    {% if announcement.course.typeCourse.id == 2 %}
      <li>
        <strong>{{ 'announcements.configureFields.place'|trans({}, 'messages', app.user.locale) }} :</strong>
        {{ informationGroup.place }}
      </li>
    {% endif %}
    {% if informationGroup.tutor %}
      <li>
        <strong>{{ 'announcements.configureFields.tutor'|trans({}, 'messages', app.user.locale) }} :</strong>
        {{ informationGroup.tutor.fullName }}
        <ul>
          <li>
            <strong>DNI:</strong>
            {{ informationGroup.tutor.email }}
          </li>
          <li>
            <strong>{{ 'user.configureFields.email'|trans({}, 'messages', app.user.locale) }}:</strong>
            {{ informationGroup.tutor.email }}
          </li>
          <li>
            <strong>{{ 'announcements.configureFields.telephone'|trans({}, 'messages', app.user.locale) }}:</strong>
            {{ informationGroup.tutor.telephone }}
          </li>
        </ul>
      </li>
    {% endif %}
  </ul>
</div>
