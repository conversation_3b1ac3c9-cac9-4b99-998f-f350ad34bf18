<div class="content-panel pb-3 mt-5"><section id="main" class="content-body">

        <table class="table datagrid ">
            <thead>
            <tr>
                <th class="  header-for-field-association text-left" dir="ltr">
                    {{ 'Created By'|trans({}, 'messages') }}

                </th>

                <th class="  header-for-field-association text-left" dir="ltr">
                    {{ 'question.label_in_singular'|trans({}, 'messages') }}
                </th>

                <th class="  header-for-field-textarea text-left" dir="ltr">
                    {{ 'opinions.configureFields.value'|trans({}, 'messages') }}
                </th>

                <th class="  header-for-field-boolean text-left" dir="ltr">
                    {{ 'opinions.configureFields.to_post'|trans({}, 'messages') }}
                </th>

                <th dir="ltr">
                    <span class="sr-only">{{ 'common_areas.actions'|trans({}, 'messages') }}</span>
                </th>
            </tr>
            </thead>

            <tbody>
            {% for opinion in opinions %}
           <tr data-id="{{ opinion.id }}" {% if loop.index is odd %}class="color"{% endif %}>
               <td><a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\UserCrudController').setAction('detail').setEntityId(opinion.idUser).set('referrer', referrerAnnouncement) }}">{{ opinion.email }}</a></td>
               <td>{{ opinion.question }}</td>
               <td>{{ opinion.value }}</td>
                <td data-label="publicar" class=" text-left field-boolean has-switch" dir="ltr">
                {% set checked =  opinion.toPost ? 'checked' : ''  %}
                   <div class="form-check form-switch">
                       <input type="checkbox" class="form-check-input" id="01GK1YNJGERPX3HDXTEK2W6WS5" {{ checked}} data-toggle-url="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\NpsCrudController').setAction('edit').setEntityId(opinion.id).set('fieldName', 'toPost') }}" autocomplete="off">
                       <label class="form-check-label" for="01GK1YNJGERPX3HDXTEK2W6WS5"></label>
                   </div>

               </td>
               <td>
                   <a class="btn btn-secondary btn-sm" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\NpsCrudController').setAction('detail').setEntityId(opinion.id).set('referrer', referrerAnnouncement) }}">
                       <i class="fa fa-eye"></i>
                   </a>
               </td>
           </tr>
            {% endfor %}
            </tbody>

            <tfoot>
            </tfoot>
        </table>


        <div class="list-pagination">
            {{ knp_pagination_render(opinions) }}
        </div>
    </section>
</div>
