<div class="content-panel pb-3">
  <div class="page-actions pt-3 pr-3 text-right">
    <a class="action-new btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\TaskCourseCrudController').setAction('new').set('courseId', course.id).set('referrer', referrerTask) }}">{{ 'course.configureFields.add_task'|trans({}) }}</a>
  </div>
</div>
{% if taskCourse %}
<table class="table table-responsive-sm  datagrid">
  <thead>
    <tr>
      <th scope="col">{{ 'content.configureFields.title'|trans({}, 'messages') }}</th>
    {#   <th scope="col">Descripción</th> #}
      <th scope="col">{{ 'material_course.configureFields.file'|trans({}, 'messages') }}</th>
      <th scope="col" class="text-right">{{ 'Actions'|trans({}, 'messages') }}</th>
    </tr>
  </thead>
  <tbody>
    {% for task in taskCourse %}
      <tr>
        <td>{{ task.title }}</td>
      {#   <td>descripcion {{ task.description|raw }}</td> #}
        <td>{{ task.filesTasks|length }}</td>
        <td class="text-right">
          <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\TaskCourseCrudController').setAction('delete').setEntityId(task.id).set('referrer', referrerTask) }}" class="btn btn-danger action-delete btn-sm" formaction="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\TaskCourseCrudController').setAction('delete').setEntityId(task.id).set('referrer', referrerTask) }}" data-bs-toggle="modal" data-bs-target="#modal-delete">
            <i class="fa fa-trash"></i>
          </a>

          <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\TaskCourseCrudController').setAction('detail').setEntityId(task.id).set('referrer', referrerTask) }}" class="btn btn-secondary btn-sm"><i class="fa fa-eye"></i></a>
          <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\TaskCourseCrudController').setAction('edit').setEntityId(task.id).set('referrer', referrerTask) }}" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></a>
        </td>
      </tr>
    {% endfor %}
  </tbody>
</table>
  {% else %}
    <div >
      <div class="card text-center">
        <div class="card-header" style="height:20rem">
          <h3 style="padding-top:8rem"> {{ 'no_content'|trans({}, 'messages') }}</h3>
        </div>
      </div>
    </div>

{% endif %}
