

<ul class="nav nav-tabs" id="courseTab" role="tablist">
    {% if course.typeCourse == null or (course.typeCourse.id != 2 and course.typeCourse.id != 4) %}
        <li class="nav-item" role="presentation">
            <a class="nav-link  {{ tab == 'chapters' ? 'active' : '' }}" id="chapters-tab" data-toggle="tab"
               href="#course-chapters" role="tab" aria-controls="lessons" aria-selected="true">
                <th scope="col">{{ 'course.configureFields.chapters'|trans({}, 'messages', app.user.locale) }}</th>
            </a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link {{ tab == 'seasons' ? 'active' : '' }}" id="course-seasons-tab" data-toggle="tab"
               href="#course-seasons" role="tab" aria-controls="seasons"
               aria-selected="false">{{ 'course.configureFields.seasons'|trans({}, 'messages', app.user.locale) }}</a>
        </li>
    {% endif %}
    {% if tabs['announcement_enabled'] %}
    <li class="nav-item" role="presentation">
        <a class="nav-link {{ tab == 'announcement' ? 'active' : '' }}" id="course-announcement-tab" data-toggle="tab"
           href="#course-announcement" role="tab" aria-controls="announcement"
           aria-selected="false">{{ 'announcements.label_in_plural'|trans({}, 'messages', app.user.locale) }}</a>
    </li>
    {% endif %}
    {% if course.translations|length %}
        <li class="nav-item" role="presentation">
            <button class="nav-link  {{ tab == 'translate-course' ? 'active' : '' }}" id="course-translate-course-tab"
                    data-bs-toggle="tab" data-bs-target="#course-translate-course" type="button" role="tab"
                    aria-controls="course-translate-course"
                    aria-selected="false">{{ 'course.configureFields.courses_translate'|trans({}, 'messages') }}</button>
        </li>
    {% endif %}
    {% if course.typeCourse == null or (course.typeCourse.id != 2 and course.typeCourse.id != 4) %}
        {% if tabs['tabPerson']  %}
        <li class="nav-item" role="presentation">
            <button class="nav-link  {{ tab == 'users' ? 'active' : '' }}" id="course-users-tab" data-bs-toggle="tab"
                    data-bs-target="#course-users" type="button" role="tab" aria-controls="course-users"
                    aria-selected="false"                     
                    >{{ 'itinerary.tab.users'|trans({}, 'messages') }}</button>
        </li>
        {% endif %}

        {% if tabs['tabStats'] %}
        <li class="nav-item" role="presentation">
            <button class="nav-link  {{ tab == 'stats' ? 'active' : '' }}" id="course-stats-tab" data-bs-toggle="tab"
                    data-bs-target="#course-stats" type="button" role="tab" aria-controls="course-stats"
                    aria-selected="false"                   
                    >{{ 'user.configureFields.stats'|trans({}, 'messages') }}</button>
        </li>
        {% endif %}
    {% endif %}

    {% if (app_course_materials_enabled and course.typeCourse == null) or (app_course_tasks_enabled and course.typeCourse.id != 2 and course.typeCourse.id != 4) %}
        <li class="nav-item" role="presentation">
            <button class="nav-link  {{ tab == 'materials' ? 'active' : '' }}" id="course-materials-tab" data-bs-toggle="tab" data-bs-target="#course-materials" type="button" role="tab" aria-controls="course-materials" aria-selected="false">{{ 'chapter.chapter.materials'|trans({}, 'messages') }}</button>
        </li>
    {% endif %}

   
    {% if 'ROLE_ADMIN' in app.user.roles and tabs['tabOpinions'] %}
        <li class="nav-item" role="presentation">
            <button class="nav-link {{ tab == 'opinion' ? 'active' : '' }}" id="course-opinion-tab" data-bs-toggle="tab"
                    data-bs-target="#course-opinion" type="button" role="tab" aria-controls="course-opinion"
                    aria-selected="false"
                     v-on:click="changeTab('opinions')"
                    >{{ 'opinions.label_in_plural'|trans({}, 'messages') }}</button>
        </li>
    {% endif %}
</ul>

<div class="tab-content bg-white" id="course-tab-Content">
    {% if course.typeCourse == null or (course.typeCourse.id != 2 and course.typeCourse.id != 4) %}
    <div class="tab-pane fade {{ tab == 'chapters' ? 'show active' : '' }}" id="course-chapters" role="tabpanel"
         aria-labelledby="chapters-tab">
        {% block chapter %}
            {{ include('admin/course/templates/chapters/chapter.html.twig') }}
        {% endblock %}
    </div>
    <div class="tab-pane fade {{ tab == 'seasons' ? 'show active' : '' }}" id="course-seasons" role="tabpanel"
         aria-labelledby="course-seasons-tab">
        {% block seasons %}
            {{ include('admin/course/templates/seasons/season.html.twig') }}
        {% endblock %}
    </div>
    {% endif %}

    {% if tabs['announcement_enabled'] %}
    <div class="tab-pane fade {{ tab == 'announcement' ? 'show active' : '' }}" id="course-announcement" role="tabpanel"
         aria-labelledby="course-announcement-tab">
        {% block announcement %}
            {{ include('admin/course/templates/announcements/announcement.html.twig') }}
        {% endblock %}
    </div>
    {% endif %}
    <div class="tab-pane  {{ tab == 'translate-course' ? 'active' : '' }}" id="course-translate-course" role="tabpanel"
         aria-labelledby="course-translate-course-tab">
        {% if course.translations|length %}
            {% block translate_course %}
                {% include 'admin/course/templates/translate_course/translate_course_list.html.twig' %}
            {% endblock %}
        {% endif %}
    </div>
    {% if (app_course_materials_enabled and course.typeCourse == null) or (app_course_tasks_enabled and course.typeCourse.id != 2 and course.typeCourse.id != 4) %}
        <div class="tab-pane  {{ tab == 'materials' ? 'active' : '' }}" id="course-materials" role="tabpanel"
             aria-labelledby="course-materials-tab">
            {% block materials %}
                {% include 'admin/course/templates/materials/materials.html.twig' %}
            {% endblock %}
        </div>
    {% endif %}

    {% if (app_course_materials_enabled and course.typeCourse == null) or (app_course_tasks_enabled and course.typeCourse.id != 2 and course.typeCourse.id != 4) %}
        <div class="tab-pane  {{ tab == 'task' ? 'active' : '' }}" id="course-task" role="tabpanel"
             aria-labelledby="course-task-tab">
            {% block task %}
                {% include 'admin/course/templates/task/listTask.html.twig' %}
            {% endblock %}
        </div>
    {% endif %}
   {% if course.typeCourse == null or (course.typeCourse.id != 2 and course.typeCourse.id != 4) %}
        {% if tabs['tabPerson'] %}
            <div class="tab-pane  {{ tab == 'users' ? 'active' : '' }}" id="course-users" role="tabpanel"
                 aria-labelledby="course-users-tab">
                <div id="CourseStatsDetails"></div> 
            </div>
        {% endif %}

        {% if tabs['tabStats'] %}
        <div class="tab-pane  {{ tab == 'stats' ? 'active' : '' }}" id="course-stats" role="tabpanel"
             aria-labelledby="course-stats-tab">         
                  <div id="CourseStatsUsers"></div>          
        </div>
        {% endif %}
    {% endif %}
  {% if 'ROLE_ADMIN' in app.user.roles and tabs['tabOpinions'] %}
        <div class="tab-pane  {{ tab == 'opinion' ? 'active' : '' }}" id="course-opinion" role="tabpanel"
             aria-labelledby="course-opinion-tab">             
                <div id="CourseStatsOpinions"></div>       
        </div>
    {% endif %}

</div>



<script>
    const courseTranslations = {
        course_started_in_period_title: "{{ 'stats.export.filter.course_started_in_period_title'|trans({}, 'messages',  app.user.locale) }}",
        course_finished_in_period_title: "{{ 'stats.export.filter.course_finished_in_period_title'|trans({}, 'messages',  app.user.locale) }}",
        export_success: "{{ 'stats.export.export_success'|trans({}, 'messages',  app.user.locale) }}",
        export_error: "{{ 'stats.export.export_error'|trans({}, 'messages',  app.user.locale) }}",
        export_dir: "{{ 'stats.export.export_dir'|trans({}, 'messages',  app.user.locale) }}"
    }
    const courseId = {{ course.id|raw }}
    const course_data = {
        name: "{{ course.name |raw }}",
        image: "{{ course_uploads_path }}/{{ course.image }}",
        category: "{{ courseCategory|raw }}",
        type: "{{ course.typeCourse }}"
    }
    const courseStatus = {{courseStatus | json_encode | raw}}
    const assetsDir = "{{ asset('assets/chapters/') }}"
    let filters = []
    fetch('/admin/api/v1/filters/by-categories')
        .then((res) => res.json())
        .then((data) => {
            filters = (data.data || []).map(item => ({
                ...item,
                filters: (item.filters || []).filter(filter => !!filter.name),
                key: `filter-${item.id}`,
            }))
        })
        .catch(() => {
        })

</script>
