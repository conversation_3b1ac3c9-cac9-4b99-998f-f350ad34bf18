<div class="hours_information">
    <div class="row row-title">
        <div class="col-12">
            <h2 class="subtitle" @click="toggleAccordion('hours')">
                <span>
                    <i class="fas fa-clock"></i> {{ 'segmented_stats.title2'|trans({}, 'messages',  app.user.locale) }}
                </span>
                <i class="fa"
                   :class="{'fa-angle-down': !accordion.hours, 'fa-angle-up': accordion.hours}"
                ></i>
            </h2>
        </div>
    </div>
    <div class="row-panel" :class="{'hidePanel': !accordion.hours}">
        <div class="row">
            <div class="col-12">
                <h2 class="subtitle">
                    <i class="fas fa-chart-line"></i>
                    {{ 'stats.accumulative.chart'|trans({}, 'messages',  app.user.locale) }}
                </h2>
                <line-chart-double :series-data="data.hours.doubleLineChart"></line-chart-double>
            </div>
        </div>
        <div class="row mt-4">
            <div class="mt-4 col-xs-12 col-sm-6 col-md-4">
                <h2 class="subtitle"><i class="fas fa-users"></i>
                    {{ 'segmented_stats.title1'|trans({}, 'messages',  app.user.locale) }}
                    [{{ 'segmented_stats.total_hours'|trans({}, 'messages',  app.user.locale) }}]</h2>
                <pie-chart :series-data="data.hours.pieChart.totals" :colors="['#80CBC4', '#AED581', '#009688']" :inner-size="'80%'"></pie-chart>
            </div>
            <div class="mt-4 col-xs-12 col-sm-6 col-md-8">
                <h2 class="subtitle">
                    <i class="fas fa-globe-americas"></i>
                    {{ 'segmented_stats.distribution_by_country'|trans({}, 'messages',  app.user.locale) }}
                    [{{ 'segmented_stats.total_hours'|trans({}, 'messages',  app.user.locale) }}]
                </h2>
                <bar-chart :series-data="data.hours.distribution.total" :tooltip="'{point.y}'" :colors="defaultColors"></bar-chart>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-xs-12 col-sm-6 col-md-4">
                <h2 class="subtitle"><i class="fas fa-users"></i>
                    {{ 'segmented_stats.title1'|trans({}, 'messages',  app.user.locale) }}
                    [{{ 'segmented_stats.total_avg'|trans({}, 'messages',  app.user.locale) }}]</h2>
                <bar-chart :series-data="data.hours.pieChart.avg" :colors="['#80CBC4', '#AED581', '#009688']"
                           :inner-size="'80%'" type="bar" :tooltip="'{point.y}'"></bar-chart>
            </div>
            <div class="col-xs-12 col-sm-6 col-md-8">
                <h2 class="subtitle">
                    <i class="fas fa-globe-americas"></i>
                    {{ 'segmented_stats.distribution_by_country'|trans({}, 'messages',  app.user.locale) }}
                    [{{ 'segmented_stats.total_avg'|trans({}, 'messages',  app.user.locale) }}]
                </h2>
                <bar-chart :series-data="data.hours.distribution.avg" :tooltip="'{point.y}'" :colors="defaultColors"></bar-chart>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-xs-12 col-sm-6 mt-4">
                <div class="infoCard">
                    <p><i class="fas fa-clock"></i> {{ 'segmented_stats.total_hours'|trans({}, 'messages',  app.user.locale) }}</p>
                    <p class="number">${formatNumber(data.hours.department.totalStructure)}</p>
                </div>
                <div>
                    <h2 class="subtitle mb-1 mt-3">
                        <i class="fas fa-chart-bar"></i>
                        {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                        ({{ 'segmented_stats.structure_total'|trans({}, 'messages',  app.user.locale) }})
                    </h2>
                    <bar-chart :series-data="data.hours.structure.data" :tooltip="'{point.y}'"></bar-chart>
                </div>
            </div>
            <div class="col-xs-12 col-sm-6 mt-4">
                <div class="infoCard">
                    <p><i class="fas fa-clock"></i> {{ 'segmented_stats.total_avg'|trans({}, 'messages',  app.user.locale) }}</p>
                    <p class="number">${formatNumber(data.hours.department.totalStructureAVG)}</p>
                </div>
                <div>
                    <h2 class="subtitle mb-1 mt-3">
                        <i class="fas fa-chart-bar"></i>
                        {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                        ({{ 'segmented_stats.structure_avg'|trans({}, 'messages',  app.user.locale) }})
                    </h2>
                    <bar-chart :series-data="data.hours.structure.avg" :tooltip="'{point.y}'"></bar-chart>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-xs-12 col-sm-6 mt-4">
                <div class="infoCard infoCard-colective2">
                    <p><i class="fas fa-clock"></i> {{ 'segmented_stats.total_hours'|trans({}, 'messages',  app.user.locale) }}</p>
                    <p class="number">${formatNumber(data.hours.department.totalHotel)}</p>
                </div>
                <div>
                    <h2 class="subtitle mb-1 mt-3">
                        <i class="fas fa-chart-bar"></i>
                        {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                        ({{ 'segmented_stats.hotel_total'|trans({}, 'messages',  app.user.locale) }})
                    </h2>
                    <bar-chart :series-data="data.hours.hotel.data" :colors="['#E57373', '#EF9A9A']" :tooltip="'{point.y}'"></bar-chart>
                </div>
            </div>
            <div class="col-xs-12 col-sm-6 mt-4">
                <div class="infoCard infoCard-colective2">
                    <p><i class="fas fa-clock"></i> {{ 'segmented_stats.total_avg'|trans({}, 'messages',  app.user.locale) }}</p>
                    <p class="number">${formatNumber(data.hours.department.totalHotelAVG)}</p>
                </div>
                <div>
                    <h2 class="subtitle mb-1 mt-3">
                        <i class="fas fa-chart-bar"></i>
                        {{ 'segmented_stats.by_department'|trans({}, 'messages',  app.user.locale) }}
                        ({{ 'segmented_stats.hotel_avg'|trans({}, 'messages',  app.user.locale) }})
                    </h2>
                    <bar-chart :series-data="data.hours.hotel.avg" :colors="['#E57373', '#EF9A9A']" :tooltip="'{point.y}'"></bar-chart>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h2 class="subtitle">
                    <i class="fas fa-university"></i>
                    {{ 'segmented_stats.by_school'|trans({}, 'messages',  app.user.locale) }}
                </h2>
            </div>
            <div class="col-xs-12 col-sm-6">
                <h2 class="subtitle">
                    <i class="fas fa-chart-bar"></i>
                    {{ 'segmented_stats.title2'|trans({}, 'messages',  app.user.locale) }}
                </h2>
                <bar-chart :series-data="data.hours.school" :colors="defaultColors"
                           :inner-size="'80%'" type="bar" :tooltip="'{point.y}'"></bar-chart>
            </div>
            <div class="col-xs-12 col-sm-6">
                <h2 class="subtitle">
                    <i class="fas fa-chart-bar"></i>
                    {{ 'segmented_stats.avg'|trans({}, 'messages',  app.user.locale) }}
                </h2>
                <bar-chart :series-data="data.hours.schoolAVG" :colors="defaultColors"
                :inner-size="'80%'" type="bar" :tooltip="'{point.y}'"></bar-chart>
            </div>
        </div>
    </div>
</div>
