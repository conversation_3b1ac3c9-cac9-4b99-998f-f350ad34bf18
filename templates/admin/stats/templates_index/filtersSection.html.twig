<div class="row mb-3">
    <div class="col-12 d-flex justify-content-between mb-1">
        <div class="actions text-center mt-2" v-if="isLoading">
            <i class="fa fa-circle-o-notch rotationAnimation"></i> {{ 'stats.export.loading_data'|trans({}, 'messages',  app.user.locale) }}...
        </div>
        <div v-else>
            <button type="button" class="btn btn-primary" @click="generateExcelData">
                <i class="fa fa-download"></i> {{ 'stats.export.download_file_xlsx'|trans({}, 'messages',  app.user.locale) }}
            </button>
        </div>
        <div>
            <button type="button" class="btn btn-primary" @click="toggleFilterDisplay">
                <i class="fa fa-filter"></i> {{ 'stats.filters'|trans({}, 'messages',  app.user.locale) }}
            </button>
        </div>
    </div>

    {{ include("admin/stats/templates_index/commonfiltersSection.html.twig", {showOnlyActivesFilter: 0}) }}
</div>
