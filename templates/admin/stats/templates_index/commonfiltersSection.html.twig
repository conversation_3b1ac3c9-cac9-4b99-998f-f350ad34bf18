<div class="filters-section" v-show="showFilters">
    <div class="filter-tags">
        <div class="filter-tag" v-for="(data, filter) in currentBdFilters" v-if="data != ''" @click="removeBdFilter(filter)">
            <i class="fas fa-hashtag"></i>
            ${filter}: ${data}
        </div>
        <div class="filter-tag" v-for="(data, filter) in currentFilters" v-if="filter != 'category'" @click="removeFilter(filter)">
            <i class="fas fa-hashtag"></i> ${data?.name}
        </div>

        <div class="filter-tag" v-for="category in currentFilters.category" @click="removeCategory(category.id)">
            <i class="fas fa-hashtag"></i> {{ 'user.configureFields.category'|trans({}, 'messages',  app.user.locale) }}: ${category.name}
        </div>
    </div>

    <div class="filters mt-2" v-show="!isLoading">
        {% if user_use_filters %}
            {% for filter_category in filterCategories %}
                {% set filters = filter_category.filters|json_encode(constant('JSON_PRETTY_PRINT')) %}
                <my-multi-select v-model="bd_filters.category_{{filter_category.id}}" :list="{{filters}}" :empty="'Todos los {{filter_category.name}}'" :label="'{{filter_category.name}}'"></my-multi-select>
            {% endfor %}
        {% else %}
            {% if divisions|length > 0 %}
                <my-select
                        v-model="filters.division"
                        :list="divisions"
                        :empty="'{{ 'stats.all_divisions'|trans({}, 'messages',  app.user.locale) }}'"
                        :label="'{{ 'user.configureFields.division'|trans({}, 'messages',  app.user.locale) }}'"
                ></my-select>
            {% endif %}

            {% if countries|length > 0 %}
                <my-select
                        v-model="filters.country"
                        :list="filteredCountries"
                        :empty="'{{ 'stats.all_countries'|trans({}, 'messages',  app.user.locale) }}'"
                        :label="'{{ 'user.configureFields.country'|trans({}, 'messages',  app.user.locale) }}'"
                ></my-select>
            {% endif %}

            {% if centers|length > 0 %}
                <my-select
                        v-model="filters.center"
                        :list="centers"
                        :empty="'{{ 'stats.all_centers'|trans({}, 'messages',  app.user.locale) }}'"
                        :label="'{{ 'user.configureFields.center'|trans({}, 'messages',  app.user.locale) }}'"
                ></my-select>
            {% endif %}

            {% if departaments|length > 0 %}
                <my-select
                        v-model="filters.departament"
                        :list="departaments"
                        :empty="'{{ 'stats.all_departament'|trans({}, 'messages',  app.user.locale) }}'"
                        :label="'{{ 'user.configureFields.departament'|trans({}, 'messages',  app.user.locale) }}'"
                ></my-select>
            {% endif %}
            {% if professionalCategories|length > 0 %}
                <my-multi-select
                        v-model="filters.category"
                        :list="professionalCategories"
                        :empty="'{{ 'stats.all_categories'|trans({}, 'messages',  app.user.locale) }}'"
                        :label="'{{ 'user.configureFields.category'|trans({}, 'messages',  app.user.locale) }}'"
                ></my-multi-select>
            {% endif %}
        {% endif %}
        {% if genders|length > 0 %}
            <my-select
                    v-model="filters.gender"
                    :list="genders"
                    :empty="'{{ 'stats.all_gender'|trans({}, 'messages',  app.user.locale) }}'"
                    :label="'{{ 'user.configureFields.gender'|trans({}, 'messages',  app.user.locale) }}'"
            ></my-select>
        {% endif %}
        <input-date
                v-model="filters.dateFrom"
                :label="'{{ 'stats.export.start_date'|trans({}, 'messages',  app.user.locale) }}'"
        ></input-date>
        <input-date
                v-model="filters.dateTo"
                :label="'{{ 'stats.export.end_date'|trans({}, 'messages',  app.user.locale) }}'"
        ></input-date>
        {% if showOnlyActivesFilter > 0 %}
            <my-select
                    v-model="filters.active"
                    :list="onlyActivesList"
                    :empty="'Todos los usuarios'"
                    label="'{{ 'user.label_in_plural'|trans({}, 'messages',  app.user.locale) }}'"
            ></my-select>
        {% endif %}
    </div>

    <div class="actions text-center mt-2" v-show="!isLoading">
        <button type="button" class="btn btn-warning mr-1" data-dismiss="modal" @click="clearFilters">
            <i class="fa fa-brush"></i>
            {{ 'stats.clear_filters'|trans({}, 'messages',  app.user.locale) }}
        </button>
        <button type="button" class="btn btn-primary ml-1" data-dismiss="modal" @click="applyFilters">
            <i class="fa fa-check"></i>
            {{ 'stats.apply_filters'|trans({}, 'messages',  app.user.locale) }}
        </button>
    </div>
</div>
