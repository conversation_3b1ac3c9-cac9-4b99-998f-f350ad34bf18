  <div class="chapters-information">
  <h2 class="section-title">
                <i class="fas fa-book"></i>
                {{ 'stats.title_information_chapter'|trans({}, 'messages',  app.user.locale) }}
            </h2>

            <div class="active-chapters">
                <h2 class="subtitle">
                    <i class="fas fa-book"></i>
                    {{ 'user.configureFields.chapter_type'|trans({}, 'chapters',  app.user.locale) }}
                </h2>

                <div class="chapters-chart dashboard-panel">
                    <pie-chart
                        v-if="mergedChapters"
                        :series-data="mergedChapters"
                        color-to-monochrome="--color-primary-darker"
                        inner-size="80%"
                        @colors="chapterTypesSetColors($event)"
                    />
                </div>
            </div>

            <div class="chapters-realized">
                <h2 class="subtitle">
                    <i class="fas fa-book"></i>
                    {{ 'user.configureFields.finished_chapter_types'|trans({}, 'chapters',  app.user.locale) }}
                </h2>

                <div class="chapters-chart dashboard-panel">
                    <pie-chart
                        v-if="finishedChapterTypes"
                        :series-data="finishedChapterTypes"
                        color-to-monochrome="--color-dashboard-1"
                        inner-size="80%"
                    />
                </div>
            </div>

            <div class="chapters">
                <div v-for="(chapterType, i) in chapterTypesColored" :key="i" class="box-info type--2">
                    <div class="content">
                        <div class="box-icon" :style="[{'background': chapterType.color}]">
                            <img :src="'assets/chapters/' + chapterType.icon"/>
                        </div>

                        <div class="title">
                            <div class="number" :style="[{'color': chapterType.color}]">
                                <b>${chapterType.y | formatNumber}</b>
                            </div>

                            <div class="box-subtitle">
                                <b>${chapterType.done | formatNumber}</b>
                                <span>{{ 'stats.title_finish_m'|trans({}, 'messages',  app.user.locale) }}</span>
                            </div>
                        </div>

                        <div class="name">
                            ${chapterType.name}
                        </div>
                    </div>
                </div>
            </div>

            <div class="daily-chapters">
                <h2 class="subtitle">
                    <i class="fas fa-book"></i>
                    {{ 'stats.daily_chapter'|trans({}, 'messages',  app.user.locale) }}
                </h2>

                <!-- <div v-show="showFinishedChapters"> -->
                <div class="daily-chart">
                    <line-chart
                        v-if="finishedChapters"
                        :series-data="finishedChapters"
                        color="var(--color-dashboard-5)"
                        :tooltip-title="'{{ 'stats.finished_chapters'|trans({}, 'messages',  app.user.locale) }}'"
                    />
                </div>
            </div>

            <div class="times">
                <h2 class="subtitle">
                    <i class="fas fa-hourglass-half"></i>
                    {{ 'stats.total_times_spent'|trans({}, 'messages',  app.user.locale) }}
                </h2>

                <div class="times-chart">
                    <bar-chart
                        v-if="timeSpentByType"
                        :series-data="timeSpentByType"
                        :tooltip="'{point.time}'"
                        :colors=['var(--color-dashboard-4)']
                    />
                </div>

                <div class="box-info type--4">
                    <div class="content" v-if="formattedTotalHours">
                        <div class="box-icon">
                            <i class="fas fa-clock"></i>
                        </div>

                        <div class="title">
                            <div class="box-subtitle">
{#                                <b>${formattedTotalHours.days}</b>#}
{#                                <span>{{ 'stats.chapter_day'|trans({}, 'messages',  app.user.locale) }}</span>#}
                                <b>${formattedTotalHours.hours}</b>
                                <span>{{ 'stats.chaper_hours'|trans({}, 'messages',  app.user.locale) }}</span>
                                <b>${formattedTotalHours.minutes}</b>
                                <span>{{ 'stats.chapter_minutes'|trans({}, 'messages',  app.user.locale) }}</span>
                            </div>
                        </div>

                        <div class="name">
                            {{ 'stats.chapter_total'|trans({}, 'messages',  app.user.locale) }}
                        </div>
                    </div>
                </div>

                <div class="box-info type--4">
                    <div class="content" v-if="formattedTotalTimeByUser">
                        <div class="box-icon">
                            <i class="far fa-clock"></i>
                        </div>

                        <div class="title">
                            <div class="box-subtitle">
{#                                <b>${formattedTotalTimeByUser.days}</b>#}
{#                                <span>{{ 'stats.chapter_day'|trans({}, 'messages',  app.user.locale) }}</span>#}
                                <b>${formattedTotalTimeByUser.hours}</b>
                                <span>{{ 'stats.chaper_hours'|trans({}, 'messages',  app.user.locale) }}</span>
                                <b>${formattedTotalTimeByUser.minutes}</b>
                                <span> {{ 'stats.chapter_minutes'|trans({}, 'messages',  app.user.locale) }}</span>
                            </div>
                        </div>

                        <div class="name">
                             {{ 'stats.chapter_media'|trans({}, 'messages',  app.user.locale) }}
                        </div>
                    </div>
                </div>
            </div> 

			{{ include("admin/stats/templates_index/valorationCourse.html.twig") }}
			</div>