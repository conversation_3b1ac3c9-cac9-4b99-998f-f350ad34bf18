{% extends '@!EasyAdmin/page/content.html.twig' %}

{% block head_stylesheets %}
	{{ parent() }}
	{{ encore_entry_link_tags('segmentedStats') }}
{% endblock %}

{% block page_title %}
	{{ 'stats.segmented.title'|trans({}, 'messages',  app.user.locale) }}
{% endblock page_title %}

{% block main %}
	<style>
		.segmented-filters {
			display: flex;
			flex-direction: row-reverse;
			justify-content: space-between;
			align-items: center;
		}
		#segmented-stats .row-panel {
			padding-inline: 1rem;
			overflow: hidden;
			transform-origin: 0 0;
			transition: all ease-in-out 500ms;
			max-height: min-content;
			opacity: 1;
		}

		#segmented-stats .row-panel.hidePanel {
			opacity: 0;
			max-height: 0;
			transform: scaleY(0);
		}

		#segmented-stats .row-title .subtitle {
			justify-content: space-between;
		}

		#segmented-stats .infoCard {
			text-align: center;
			border-radius: 7px;
			padding: 2rem 0;
		}

		#segmented-stats .infoCard {
			background-color: #80CBC4;
		}

		#segmented-stats .infoCard-colective2 {
			background-color: #FFAB91;
		}

		#segmented-stats .infoCard p {
			margin: 0 auto;
			font-size: 1.5rem;
			color: white;
		}
		#segmented-stats .infoCard .number {
			font-size: 1.7rem;
		}
		#segmented-stats .pdf {
			width: 730px;
		}
	</style>
	<div id="segmented-stats" class="stats-panel" v-cloak v-if="data">
		{{ include("admin/stats/templates_index/filtersSection.html.twig", {'showDownloadButton': true}) }}
		<div ref="content">
			{{ include("admin/stats/templates_segmented/persons.html.twig") }}
			<br>
			{{ include("admin/stats/templates_segmented/courses.html.twig") }}
			<br>
			{{ include("admin/stats/templates_segmented/hours.html.twig") }}
			<br>
			{{ include("admin/stats/templates_segmented/access.html.twig") }}
		</div>



		<vue-html2pdf
				:show-layout="false"
				:manual-pagination="true"
				:pdf-format="htmlToPdfOptions.jsPDF.format"
				:pdf-orientation="htmlToPdfOptions.jsPDF.orientation"
				:html-to-pdf-options="htmlToPdfOptions"
				ref="html2Pdf"
				:key="showDownloadButton ? 1: 0"
		>
			<section slot="pdf-content" class="scaled">
				{{ include("admin/stats/templates_segmented/pdf/persons.html.twig") }}
				{{ include("admin/stats/templates_segmented/pdf/courses.html.twig") }}
				{{ include("admin/stats/templates_segmented/pdf/hours.html.twig") }}
				{{ include("admin/stats/templates_segmented/pdf/access.html.twig") }}
			</section>
		</vue-html2pdf>

	</div>

	<style>
		@keyframes fadeOut{from{opacity:1}to{opacity:0}}.v-toast--fade-out{animation-name:fadeOut}@keyframes fadeInDown{from{opacity:0;transform:translate3d(0, -100%, 0)}to{opacity:1;transform:none}}.v-toast--fade-in-down{animation-name:fadeInDown}@keyframes fadeInUp{from{opacity:0;transform:translate3d(0, 100%, 0)}to{opacity:1;transform:none}}.v-toast--fade-in-up{animation-name:fadeInUp}.fade-enter-active,.fade-leave-active{transition:opacity 150ms ease-out}.fade-enter,.fade-leave-to{opacity:0}.v-toast{position:fixed;display:flex;top:0;bottom:0;left:0;right:0;padding:2em;overflow:hidden;z-index:1052;pointer-events:none}.v-toast__item{display:inline-flex;align-items:center;animation-duration:150ms;margin:.5em 0;box-shadow:0 1px 4px rgba(0,0,0,.12),0 0 6px rgba(0,0,0,.04);border-radius:.25em;pointer-events:auto;opacity:.92;color:#fff;min-height:3em;cursor:pointer}.v-toast__item--success{background-color:#47d78a}.v-toast__item--info{background-color:#1c85d5}.v-toast__item--warning{background-color:#febc22}.v-toast__item--error{background-color:#f7471c}.v-toast__item--default{background-color:#343a40}.v-toast__item.v-toast__item--top,.v-toast__item.v-toast__item--bottom{align-self:center}.v-toast__item.v-toast__item--top-right,.v-toast__item.v-toast__item--bottom-right{align-self:flex-end}.v-toast__item.v-toast__item--top-left,.v-toast__item.v-toast__item--bottom-left{align-self:flex-start}.v-toast__text{margin:0;padding:.5em 1em;word-break:break-word}.v-toast__icon{display:none}.v-toast.v-toast--top{flex-direction:column}.v-toast.v-toast--bottom{flex-direction:column-reverse}.v-toast.v-toast--custom-parent{position:absolute}@media screen and (max-width: 768px){.v-toast{padding:0;position:fixed !important}}.v-toast__item{opacity:1;min-height:4em}.v-toast__item .v-toast__text{padding:1.5em 1em}.v-toast__item .v-toast__icon{display:block;width:27px;min-width:27px;height:27px;margin-left:1em;background:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 45.999 45.999'%3e %3cpath fill='%23fff' d='M39.264 6.736c-8.982-8.981-23.545-8.982-32.528 0-8.982 8.982-8.981 23.545 0 32.528 8.982 8.98 23.545 8.981 32.528 0 8.981-8.983 8.98-23.545 0-32.528zM25.999 33a3 3 0 11-6 0V21a3 3 0 116 0v12zm-3.053-17.128c-1.728 0-2.88-1.224-2.844-2.735-.036-1.584 1.116-2.771 2.879-2.771 1.764 0 2.88 1.188 2.917 2.771-.001 1.511-1.152 2.735-2.952 2.735z'/%3e %3c/svg%3e") no-repeat}.v-toast__item.v-toast__item--success .v-toast__icon{background:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 52 52'%3e %3cpath fill='%23fff' d='M26 0C11.664 0 0 11.663 0 26s11.664 26 26 26 26-11.663 26-26S40.336 0 26 0zm14.495 17.329l-16 18a1.997 1.997 0 01-2.745.233l-10-8a2 2 0 012.499-3.124l8.517 6.813L37.505 14.67a2.001 2.001 0 012.99 2.659z'/%3e %3c/svg%3e") no-repeat}.v-toast__item.v-toast__item--error .v-toast__icon{background:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 51.976 51.976'%3e %3cpath fill='%23fff' d='M44.373 7.603c-10.137-10.137-26.632-10.138-36.77 0-10.138 10.138-10.137 26.632 0 36.77s26.632 10.138 36.77 0c10.137-10.138 10.137-26.633 0-36.77zm-8.132 28.638a2 2 0 01-2.828 0l-7.425-7.425-7.778 7.778a2 2 0 11-2.828-2.828l7.778-7.778-7.425-7.425a2 2 0 112.828-2.828l7.425 7.425 7.071-7.071a2 2 0 112.828 2.828l-7.071 7.071 7.425 7.425a2 2 0 010 2.828z'/%3e %3c/svg%3e") no-repeat}.v-toast__item.v-toast__item--warning .v-toast__icon{background:url("data:image/svg+xml,%3csvg viewBox='0 0 52 52' xmlns='http://www.w3.org/2000/svg'%3e %3cpath fill='%23fff' d='M49.466 41.26L29.216 6.85c-.69-1.16-1.89-1.85-3.22-1.85-1.32 0-2.53.69-3.21 1.85L2.536 41.26c-.71 1.2-.72 2.64-.03 3.85.68 1.18 1.89 1.89 3.24 1.89h40.51c1.35 0 2.56-.71 3.23-1.89.7-1.21.69-2.65-.02-3.85zm-25.53-21.405h3.381v3.187l-.724 8.92H24.66l-.725-8.92v-3.187zm2.97 17.344a1.712 1.712 0 01-1.267.543c-.491 0-.914-.181-1.268-.543a1.788 1.788 0 01-.531-1.297c0-.502.176-.935.53-1.297a1.712 1.712 0 011.269-.544c.49 0 .914.181 1.268.544s.53.795.53 1.297c0 .503-.176.934-.53 1.297z'/%3e %3c/svg%3e") no-repeat}
	</style>
		{% endblock main %}

		{% block body_javascript %}
			{{ parent() }}
			<script type="text/javascript">
				let countries = {{ countries | json_encode | raw }};
				let courses = {{ courses | json_encode | raw }};
				let centers = {{ centers | json_encode | raw }};
				let professionalCategories = {{ professionalCategories | json_encode | raw }};
				let departaments = {{ departaments | json_encode | raw }};
				let genders = {{ genders | json_encode | raw }};
				let divisions = {{ divisions | json_encode | raw }};
				let filterCategories = {{ filterCategories | json_encode | raw }};
				let seriesNames = {
					news: '{{ 'stats.accumulative.new'|trans({}, 'messages',  app.user.locale) }}',
					accumulated: '{{ 'stats.accumulative.accumulated'|trans({}, 'messages',  app.user.locale) }}'
				};
			</script>
			{{ encore_entry_script_tags('segmentedStats') }}
		{% endblock %}
