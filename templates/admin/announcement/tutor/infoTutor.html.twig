<div class="mt-4">
    <div class="row">
        <div class="card mb-3">
            <div class="row g-0">
                <div class="col-md-2">
                    <img src="/uploads/users/avatars/{{ tutor.avatarImage }}" class="img-fluid rounded-circle"
                         alt="image" width="300px" height="300px">
                </div>
                <div class="col-md-10">
                    <div class="card-body">
                        <h5 class="card-title">{{ tutor.fullName }}</h5>
                        <hr>
                        <div>
                            {% if tutor.extra  %}
                            {% if tutor.extra.pdfCv  == '' %}
                                <form action="admin/cv-user/{{ tutor.id }}/announcement/{{ announcement.id }}"
                                      method="post" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="formFile" class="form-label">Subir cv</label>
                                        <input class="form-control" type="file" id="cv" name="cv" accept=".pdf">
                                    </div>
                                    <div>
                                        <button type="submit"
                                                class="btn btn-primary">{{ 'common_areas.save'|trans({}, 'messages') }}
                                        </button>
                                    </div>
                                </form>
                                 {% endif %}
                            {% endif %}
                        </div>

                        {% if tutor.extra  %}
                        {% if tutor.extra.pdfCv %}
                            <div class="mt-4">
                                <div class="row">
                                    <div>
                                        <button class="btn btn-primary" class="btn btn-primary" data-bs-toggle="modal"
                                                data-bs-target="#staticBackdrop"> {{ 'user.show_cv'|trans({}, 'messages') }}
                                        </button>
                                    </div>

                                    <div class="mt-3">
                                        <form action="admin/cv-user/{{ tutor.id }}/announcement/{{ announcement.id }}/delete"
                                              method="post">
                                            <button type="submit" class="btn btn-danger">{{ 'user.delete_cv'|trans({}, 'messages') }}</button>
                                        </form>
                                    </div>
                                </div>

                                <!-- Modal -->
                                <div class="modal fade" id="staticBackdrop" data-bs-backdrop="static"
                                     data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel"
                                     aria-hidden="true">
                                    <div class="modal-dialog modal-dialog-centered modal-xl">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="staticBackdropLabel">CV</h5>
                                                <button type="button" class="btn-close btn-close-white"
                                                        data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body" style="background: hsl(210deg 11% 22%)">
                                                <iframe src="/uploads/users/cv/{{ tutor.extra.pdfCv }}" width="100%"
                                                        style="height:80vh"></iframe>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                        {% else %}
                          <form action="admin/cv-user/{{ tutor.id }}/announcement/{{ announcement.id }}"
                                      method="post" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="formFile" class="form-label">Subir cv</label>
                                        <input class="form-control" type="file" id="cv" name="cv" accept=".pdf">
                                    </div>
                                    <div>
                                        <button type="submit"
                                                class="btn btn-primary">{{ 'common_areas.save'|trans({}, 'messages') }}
                                        </button>
                                    </div>
                                </form>

                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
