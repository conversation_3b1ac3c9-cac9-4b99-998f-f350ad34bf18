{% extends '@!EasyAdmin/crud/detail.html.twig' %}

{% block head_stylesheets %}
	{{ parent() }}
	{% if user_use_filters %}
		{{ encore_entry_link_tags('call_user_filter') }}
	{% else %}
		{{ encore_entry_link_tags('call') }}
	{% endif %}
	{{ encore_entry_link_tags('material-course') }}

{% endblock %}

{% block content_title %}
	<a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\CourseCrudController').setAction('detail').setEntityId(announcement.course.id) }}" class="card-link">{{ announcement.course.name }}</a>
{% endblock content_title %}

{% block page_actions %}
    {{ parent() }}
    <a class="action-report btn btn-primary" href="{{ path('report-pdf-announcement', {'id': announcement.id}) }}"><i
                class="fas fa-file-pdf"></i> {{ 'announcements.configureFields.report'|trans({}, 'messages',  app.user.locale) }}
    </a>
    <a class="action-report btn btn-success" href="{{ path('report-excel-announcement', {'id': announcement.id}) }}"><i
                class="fas fa-file-excel"></i>
        {{ 'announcements.configureFields.report'|trans({}, 'messages',  app.user.locale) }}
    </a>
{% endblock %}

{% block main %}

	<div class="row">
		<div class="col-md-3">
			<div class="content-panel p-2">
				<div class="card mb-3">
					<div class="row no-gutters">
						<div class="col-md-12">
							<div class="card">
								<a href="#" class="ea-lightbox-thumbnail" data-featherlight="#ea-lightbox-{{ announcement.course.id }}" data-featherlight-close-on-click="anywhere">
									<img src="{{ course_uploads_path }}/{{ announcement.course.image }}" class="card-img-top" alt="{{ announcement.course.name }} image">
								</a>
								<div id="ea-lightbox-{{ announcement.course.id }}" class="ea-lightbox">
									<img src="{{ course_uploads_path }}/{{ announcement.course.image }}">
								</div>
								<div class="card-body">
									<h5 class="card-title">{{ announcement.course.name }}</h5>
									{{ announcement.course.description|raw }}
								</div>
								<ul class="list-group list-group-flush">
									<li class="list-group-item"> {{'user.configureFields.started_at'|trans({}, 'messages') }}:
										{{ announcement.startAt|date('Y-m-d H:i:s') }}</li>
									<li class="list-group-item"> {{'announcements.configureFields.finish_at'|trans({}, 'messages') }}:
										{{ announcement.finishAt|date('Y-m-d H:i:s') }}</li>
								</ul>
								<div class="card-body">
									<p class="card-text">
										<small class="text-muted">{{'common_areas.created_by'|trans({}, 'messages') }}
											{{ announcement.createdBy }}
											<br>
											{{ announcement.createdAt|date('Y-m-d H:i:s') }}</small>
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>

			</div>
		</div>
		<div class="col-md-9">
			{% include 'admin/announcement/tabs.html.twig' %}
		</div>

		{% block delete_form %}
        {{ include('@EasyAdmin/crud/includes/_delete_form.html.twig', with_context = false) }}
    {% endblock delete_form %}
	</div>

{% endblock main %}

{% block body_javascript %}
	{{ parent() }}
	<script>
		let idAnnouncement = {{ announcement.id | json_encode | raw }} ;
		let idCourse = {{ announcement.course.id | json_encode | raw }};
		let entitySubsidizer = {{ announcement.subsidizerEntity | json_encode | raw }};
		let maxUser = {{ announcement.maxUsers | json_encode | raw }};
		let subsidized = {{ announcement.subsidized | json_encode | raw }};
	</script>

	{{ encore_entry_script_tags('material-course') }}

	{% if user_use_filters %}
		{{ encore_entry_script_tags('call_user_filter') }}
	{% else %}
		{{ encore_entry_script_tags('call') }}
	{% endif %}
{% endblock %}
