{% extends '@EasyAdmin/page/content.html.twig' %}

{% block content_title %}
    Convocar Usuarios - {{ challenge.title }}
{% endblock content_title %}

{% block head_stylesheets %}
    {{ parent() }}

    <style>
        .select2-container--bootstrap .select2-selection {
            max-width: none !important;
        }

        .img-fluid{
            box-shadow: 0 0 0 2px var(--white),0 0 4px 1px var(--gray-600);
            margin-bottom: 2px;
            margin-top: 2px;
            max-height: 50px;
            max-width: 100px;
        }

    </style>
{% endblock %}

{% block main %}
    <div id="challenge-detail" data-category="{{ challenge.id }}">
        <div class="row">
            <div class="col-md-3">
                <div class="content-panel p-2">

                    <div class="card mb-3">
                        <div class="row no-gutters">
                            <div class="col-md-12">
                                <div class="card">
                                    <a href="#" class="ea-lightbox-thumbnail" data-featherlight="#ea-lightbox-{{ challenge.id }}" data-featherlight-close-on-click="anywhere">
                                        <img src="{{ challenge_uploads_path }}/{{ challenge.image }}" class="card-img-top" alt="{{ challenge.title }} image">
                                    </a>
                                    <div id="ea-lightbox-{{ challenge.id }}" class="ea-lightbox">
                                        <img src="{{ challenge_uploads_path }}/{{ challenge.image }}">
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title">{{ challenge.title }}</h5>
                                        {{ challenge.title }}
                                    </div>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item">Start at: {{ challenge.startDate|date('Y-m-d H:i:s') }}</li>
                                        <li class="list-group-item">Finish at: {{ challenge.endDate|date('Y-m-d H:i:s') }}</li>
                                    </ul>
                                    <div class="card-body">
                                        <p class="card-text"><small class="text-muted">Created by {{ challenge.createdBy }} at {{ challenge.createdAt|date('Y-m-d H:i:s') }}</small></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="col-md-9">
                <div id="summon" challenge="{{ challenge.id }}">
                    <summon id="{{ challenge.id }}"></summon>
                </div>
            </div>
        </div>
    </div>


{% endblock main %}


{% block body_javascript %}

    {{ parent() }}
    {{ encore_entry_script_tags('summon') }}

{% endblock %}
