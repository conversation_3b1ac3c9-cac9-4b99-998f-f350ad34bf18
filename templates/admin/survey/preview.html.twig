{% extends 'base.html.twig' %}
{% block title %}
    Survey
{% endblock %}
{% block stylesheets %}
    {{ parent() }}
    <style>
        .Survey {
            background-color: #FFFFFF;
            width: 100%;
            margin: 1rem;
            border-radius: 10px;
            padding: 1rem;
        }
    </style>
{% endblock %}

{% block body %}
    <div class="Survey">
        <h5 class="text-center">Encuesta</h5>
        <div class="col-12 mt-3">
            {% for questionIndex, question in questions %}
                <div class="form-group col-12">
                    <label class="form-label w-100"><strong>{{ questionIndex + 1 }}.</strong> {{ question.question }}</label>
                    {% if question.type == 'text' %}
                        <textarea class="form-control"></textarea>
                    {% elseif question.type == 'nps' %}
                        <div class="pl-3">
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                        </div>
                    {% elseif question.type == 'checkbox' or question.type == 'test' %}
                        <div class="Subquestions pl-3">
                            {% for key1, subquestion in question.npsQuestionDetails %}
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="form-check-input" id="{{ question.id }}-{{ question.type }}-{{ key1 }}">
                                    <label class="form-check-label" for="{{ question.id }}-{{ question.type }}-{{ key1 }}">{{ subquestion.value }}</label>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    </div>
{% endblock %}
