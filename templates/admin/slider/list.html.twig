<div class="d-flex flex-row justify-content-between align-content-center w-100 pb-3">

<div class="col align-self-end text-right mb-2">
    <div class="page-actions">
        <a class=" action-new btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\SliderCrudController').setAction('new').set('chapterId', chapter.id).set('referrer', referrerChapter) }}">Add Slider</a>
    </div>
</div>
</div>

<div class="content-panel">
    <div class="content-panel-body with-rounded-top without-padding ">
        <table class="table datagrid with-rounded-top">
            <thead>
            <tr>
                <th><span>{{ 'content.configureFields.position'|trans({}, 'messages',  app.user.locale) }}</span></th>
                <th><span>{{ 'course.configureFields.name'|trans({}, 'messages',  app.user.locale) }}</span></th>
                <th><span>{{ 'course.configureFields.description'|trans({}, 'messages',  app.user.locale) }}</span></th>
                <th><span>{{ 'common_areas.actions'|trans({}, 'messages',  app.user.locale) }}</span></th>
            </tr>
            </thead>
            <tbody class="sortable" data-orderurl="{{ url('slider-order') }}">
            {% for slider in sliders %}
                <tr data-id="{{ slider.id }}" data-orderUrl="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\SliderCrudController').setAction('edit').setEntityId(slider.id).set('fieldName', 'position') }}">

                    <td class="order">{{ slider.position }}</td>
                    <td>{{ slider.name }}</td>
                    <td>{{ slider.description }}</td>
                    <td>
                        <a class="btn btn-sm btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\SliderCrudController').setAction('edit').setEntityId(slider.id).set('chapterId', chapter.id).set('referrer', referrerChapter) }}"><i class="fas fa-edit"></i></a>
                        <a class="action-delete btn btn-sm btn-danger" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\SliderCrudController').setAction('delete').setEntityId(slider.id).set('referrer', referrerChapter) }}" formaction="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\SliderCrudController').setAction('delete').setEntityId(slider.id).set('referrer', referrerChapter) }}" data-bs-toggle="modal" data-bs-target="#modal-delete"><i class="fas fa-trash"></i></a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
    <div class="content-panel-footer without-padding without-border">

        <div class="list-pagination">
            <div class="list-pagination-counter">
                <strong>{{ sliders|length }}</strong> {{ 'common_results'|trans({}, 'messages',  app.user.locale) }}
            </div>
        </div>

    </div>
</div>
