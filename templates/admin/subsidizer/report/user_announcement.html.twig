<div class="course-users">
    <h2>Participantes</h2>
    <table class="table datagrid with-rounded-top " style="width: 100%">
        <thead class="thead-light">
        <tr>
            <th><span> {{ 'user.label_in_singular'|trans({}, 'messages',  app.user.locale) }}</span></th>
            <th><span>{{ 'announcements.configureFields.start_at'|trans({}, 'messages',  app.user.locale) }}</span></th>
            <th><span>{{ 'announcements.configureFields.finish_at'|trans({}, 'messages',  app.user.locale) }}</span></th>
            <th class="text-center"><span>{{ 'user.configureFields.finished'|trans({}, 'messages',  app.user.locale) }}</span></th>

        </tr>
        </thead>
        <tbody>
        {% for announcementUser in announcementUsers %}
            <tr>
                <td>
                   {{ announcementUser.user.fullName }}
                </td>
                <td>
                    {% if userCourses[announcementUser.user.id].startedAt is defined and userCourses[announcementUser.user.id].startedAt is not null %}
                        {{ userCourses[announcementUser.user.id].startedAt | date('Y-m-d H:i') }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td>
                    {% if userCourses[announcementUser.user.id].finishedAt is defined and userCourses[announcementUser.user.id].finishedAt is not null %}
                        {{ userCourses[announcementUser.user.id].finishedAt | date('Y-m-d H:i') }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="text-center">
                    {% if userCourses[announcementUser.user.id].finishedAt is defined and userCourses[announcementUser.user.id].finishedAt is not null %}
                        {{ 'yes'|trans({}, 'messages',  app.user.locale) }}
                    {% else %}
                        {{ 'no'|trans({}, 'messages',  app.user.locale) }}
                    {% endif %}
                </td>

            </tr>
        {% endfor %}
        </tbody>
    </table>
</div>