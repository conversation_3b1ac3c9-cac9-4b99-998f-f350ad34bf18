<table width="100%">
    <tbody>
    <tr>
        <td>
            <strong>{{ 'announcements.configureFields.start_at'|trans({}, 'messages',  app.user.locale) }}:</strong>
        </td>
        <td>
            {% if userCourses[announcementUser.user.id].startedAt is defined and userCourses[announcementUser.user.id].startedAt is not null %}
                {{ userCourses[announcementUser.user.id].startedAt | date('Y-m-d H:i') }}
            {% else %}
                -
            {% endif %}
        </td>

    </tr>
    <tr>
        <td>
            <strong>{{ 'announcements.configureFields.finish_at'|trans({}, 'messages',  app.user.locale) }} :</strong>
        </td>
        <td>
            {% if userCourses[announcementUser.user.id].finishedAt is defined and userCourses[announcementUser.user.id].finishedAt is not null %}
                {{ userCourses[announcementUser.user.id].finishedAt | date('Y-m-d H:i') }}
            {% else %}
                -
            {% endif %}
        </td>
    </tr>

    <tr>
        <td> <strong>{{ 'user.configureFields.finished'|trans({}, 'messages',  app.user.locale) }} :</strong></td>
        <td class="text-center">
            {% if userCourses[announcementUser.user.id].finishedAt is defined and userCourses[announcementUser.user.id].finishedAt is not null %}
                {{ 'yes'|trans({}, 'messages',  app.user.locale) }}
            {% else %}
                {{ 'no'|trans({}, 'messages',  app.user.locale) }}
            {% endif %}
        </td>
        <td></td>
    </tr>

    <tr>
        <td>
            <strong>{{ 'user.configureFields.time_spent'|trans({}, 'messages',  app.user.locale) }}:</strong>
        </td>
        <td>{% if userCourses[announcementUser.user.id].id is defined %}
                {{ userChapters[announcementUser.user.id] | propertySum('timeSpent') | niceTime }}
            {% else %}
                -
            {% endif %}
        </td>

    </tr>
    <tr>
        <td>
            <strong>{{ 'user.configureFields.content_viewed'|trans({}, 'messages',  app.user.locale) }}:</strong>
        </td>
        <td>
            {% if userCourses[announcementUser.user.id].id is defined %}
                {{ (userChapters[announcementUser.user.id]  | length / announcementUser.announcement.course.chapters | length * 100) | round }}
                %
                ({{ userChapters[announcementUser.user.id]  | length }}/{{ announcementUser.announcement.course.chapters | length }})
            {% else %}
                -
            {% endif %}
        </td>
    </tr>
    <tr>
        <td>
            <strong>{{'user.configureFields.interaction_with_teacher'|trans({}, 'messages',  app.user.locale) }} :</strong>
        </td>
        <td>
            {% if userCourses[announcementUser.user.id].id is defined %}
                {{ messages | length }}
            {% else %}
                -
            {% endif %}
        </td>
        <td>
            <strong>{{'user.configureFields.interaction_in_forum'|trans({}, 'messages',  app.user.locale) }} :</strong>
        </td>
        {# <td>
            {% if userCourses[announcementUser.user.id].id is defined %}
                {{ forumPosts | length}}
            {% else %}
                -
            {% endif %}
        </td> #}
    </tr>
    <tr>
        <td><strong>{% trans %}Apt{% endtrans %}:</strong></td>
        <td>
            {% if userCourses[announcementUser.user.id].finishedAt is defined and userCourses[announcementUser.user.id].finishedAt is not null %}
                {{ 'yes'|trans({}, 'messages',  app.user.locale) }}
            {% else %}
                {{ 'no'|trans({}, 'messages',  app.user.locale) }}
            {% endif %}
        </td>
    </tr>
    </tbody>
</table>
