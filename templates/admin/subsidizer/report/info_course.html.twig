<div class="course-data">
    <h2>{{ 'course.panel.class'|trans({}, 'messages',  app.user.locale) }}</h2>
    <ul>
        <li>
            <strong>{{ 'course.label_in_singular'|trans({}, 'messages',  app.user.locale) }} :</strong>
            {{ announcement.course.name }}
        </li>
        <li>
            <strong>{{ 'taskCourse.configureFields.startDate'|trans({}, 'messages',  app.user.locale) }} :</strong>
            {{ announcement.startAt | date('d/m/Y') }}
        </li>
        <li>
            <strong>{{ 'stats.export.end_date'|trans({}, 'messages',  app.user.locale) }}:</strong>
            {{ announcement.finishAt | date('d/m/Y') }}
        </li>
         <li>
            <strong> {% trans %}Tutor{% endtrans %}:</strong>
            {% for announcementTutor in announcement.tutors %}
                {{ announcementTutor.tutor.fullName }}
            {% endfor %}
        </li>
        {% if announcement.subsidized %}
        <li>
            <strong>{{ 'announcements.configureFields.max_users'|trans({}, 'messages',  app.user.locale) }}:</strong>
            {{ announcement.maxUsers }}
        </li>
      {#   <li>
            <strong>{{ 'announcements.configureFields.formative_action_type'|trans({}, 'messages',  app.user.locale) }}:</strong>
            {{ subsidizer_formative_action_types[announcement.formativeActionType] }}
        </li>
        <li>
            <strong>{{ 'announcements.configureFields.format'|trans({}, 'messages',  app.user.locale) }}:</strong>
            {{ subsidizer_format[announcement.format] }}
        </li> #}
        <li>
            <strong>{{ 'announcements.configureFields.total_hours'|trans({}, 'messages',  app.user.locale) }}:</strong>
            {{ announcement.totalHours }}
        </li>
        <li>
            <strong>{{ 'announcements.configureFields.place'|trans({}, 'messages',  app.user.locale) }}:</strong>
            {{ announcement.place }}
        </li>
        <li>
            <strong>{{ 'announcements.configureFields.training_center'|trans({}, 'messages',  app.user.locale) }}:</strong>
            <div>
                {{ announcement.trainingCenter }}
                <div class="training-center-info">
                    <div><span>{{ 'announcements.configureFields.direction'|trans({}, 'messages',  app.user.locale) }}:</span> {{ announcement.trainingCenterAddress }}</div>
                    <div><span>{{ 'announcements.configureFields.telephone'|trans({}, 'messages',  app.user.locale) }}:</span> {{ announcement.trainingCenterPhone }}</div>
                    <div><span>{{ 'user.configureFields.email'|trans({}, 'messages',  app.user.locale) }}:</span> {{ announcement.trainingCenterEmail }}</div>
                    <div><span>{{ 'announcements.configureFields.nif'|trans({}, 'messages',  app.user.locale) }}:</span> {{ announcement.trainingCenterNif }}</div>
                </div>
            </div>
        </li>
        {% endif %}
    </ul>

    {% if announcement.generalInformation  %}
    <div>
        <h2>{{'course.configureFields.general_information'|trans({}, 'messages',  app.user.locale) }} </h2>
        {{ announcement.generalInformation |raw }}
    </div>
    {% endif %}
</div>