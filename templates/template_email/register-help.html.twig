
{% extends 'template_email/layout.html.twig' %}

{% block main %}

    <div class="containerEmail">
        <p>
            {{ 'email.description'|trans({}, 'register_help', locale) }}
        </p>
        <hr>
        <p><strong>{{ 'email.name'|trans({}, 'register_help', locale) }}</strong></p>
        <p>{{ requestData['firstName'] }} {{ requestData['lastName'] }}</p>
        <p><strong>{{ 'email.email'|trans({}, 'register_help', locale) }}</strong></p>
        <p>{{ requestData['email'] }}</p>
        <p><strong>{{ 'email.key'|trans({}, 'register_help', locale) }}</strong></p>
        <p>{{ requestData['key'] }}</p>
        {% for key, option in options %}
            <p><strong>{{ ('email.' ~ key) | trans({}, 'register_help', locale) }}</strong></p>
            <p>{{ requestData[key]['name'] }}</p>
        {% endfor %}
        <p><strong>{{ 'email.text'|trans({}, 'register_help', locale) }}</strong></p>
        <p>{{ requestData['text'] }}</p>
    </div>
{% endblock main %}


