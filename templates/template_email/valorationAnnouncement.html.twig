{% extends 'template_email/layout.html.twig' %}

{% block main %}
    <div class="containerEmail">
        {% set linkAnnouncement = '/campus/valoration-course/' ~ announcement.course.id ~ '/announcement/' ~ announcement.id %}
        <h3> {{ 'email.template_email.greet'|trans({}, 'email', locale) }}  {{ user }}</h3>
        {{ 'email.template_email.valoration_announcement_body'|trans({'%nameTutor%': tutor, '%course%': course, '%url%' : app.request.getSchemeAndHttpHost(), '%route%': linkAnnouncement, '%platform%' : appFromName }, 'email', locale)|raw }}

        {% if codigo != '' %}
            {% set path = path('recover-password-user',{'id':dataUser.id, 'hash':codigo}) %}
            {{ 'email.template_email.active_account_fundae'|trans({'%url%': app.request.getSchemeAndHttpHost(), '%path%': path }, 'email', locale)|raw }}
        {% endif %}

        <p class="content">  {{ 'email.template_email.best_regards'|trans({}, 'email', locale) }}</p>
    </div>
{% endblock main %}
