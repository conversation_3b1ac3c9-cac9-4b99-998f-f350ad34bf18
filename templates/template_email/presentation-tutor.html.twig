{% extends 'template_email/layout.html.twig' %}

{% block main %}
  <div class="containerEmail">
    <h3>{{ 'email.template_email.greet'|trans({}, 'email', locale) }} {{ user }}</h3>

    {{ 'email.template_email.content_announcement_fundae_press'|trans({ '%nameTutor%': tutor, '%announcement%': course, '%dateStart%': dateStart|date('Y/m/d'), '%dateFinish%': dateFinish|date('Y/m/d'), '%duration%': duration, '%place%': place }, 'email', locale)|raw }}

    {% if codigo != '' %}
      {% set path = path('recover-password-user', { id: dataUser.id, hash: codigo }) %}
      {{ 'email.template_email.active_account_fundae'|trans({ '%url%': app.request.getSchemeAndHttpHost(), '%path%': path }, 'email', locale)|raw }}
    {% endif %}

    <p class="content">{{ 'email.template_email.best_regards'|trans({}, 'email', locale) }}</p>
  </div>
{% endblock %}
