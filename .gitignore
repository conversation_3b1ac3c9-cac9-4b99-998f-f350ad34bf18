.idea
db
docker/db
docker/php/conf.d
docker/php/Dockerfile-dev
public/favicon.ico
public/campus
public/games
public/vcms
public/assets_announcement/didactic_guide
public/roleplay
public/examples
public/assets_announcement/assistance_group_session
/public/uploads/**
!/public/uploads/**/
!/public/uploads/**/.gitkeep
!/public/uploads/**/default.*
public/upload
public/adminer.php
public/maintenance.enable
public/_scripts
!public/_scripts/import_courses_imq.php
!public/_scripts/import_courses_imq_igurco.php
!public/_scripts/import_courses_imq_amsa.php
.php-version
simplesamlphp
.DS_Store
/assets/vue/locales
xlsx
scoreXlsx
Archivador.tar.gz

## files out of public ##
/files/**
!/files/**/
!/files/**/.gitkeep


###> symfony/framework-bundle ###
/.env
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

###> lexik/jwt-authentication-bundle ###
/config/jwt/*.pem
###< lexik/jwt-authentication-bundle ###

###> symfony/webpack-encore-bundle ###
/node_modules/
/easylearning-documentation/build/
/easylearning-documentation/node_modules/
/easylearning-documentation/.docusaurus/
/public/build/
npm-debug.log
yarn-error.log
###< symfony/webpack-encore-bundle ###

###> liip/imagine-bundle ###
/public/media/cache/
###< liip/imagine-bundle ###
/0.env.local.php
templates/admin/.DS_Store
src/.DS_Store
public/.DS_Store
docker/.DS_Store
config/.DS_Store
assets/vue/.DS_Store
assets/.DS_Store
assets/vue/locales
templates/.DS_Store
.directory


certs
# Client image files
/public/assets/login/logo.png
/public/assets/email/logo.*
/public/assets/diploma/easylearning/logo_diploma.*
/public/assets/diploma/easylearning/footer_diploma.svg
# Diploma previews - generated automatically by catalogs:update command
/public/assets_announcement/preview_type_diploma/*.pdf



cache-remove-*.php


scripts/db/*.sql
scripts/sites.json

backup.tar.gz
yarn.lock
package-lock.json

composer.phar
docker-compose-dev.yaml

/tests/http/http-client.private.env.json

###> friendsofphp/php-cs-fixer ###
/.php-cs-fixer.cache
/.php-cs-fixer.dist.php
###< friendsofphp/php-cs-fixer ###

###> squizlabs/php_codesniffer ###
/.phpcs-cache
/phpcs.xml
###< squizlabs/php_codesniffer ###

###> HTTP Client private environment files ###
tests/http/http-client.private.env.json
###< HTTP Client private environment files ###

###> Git hooks ###
cghooks.lock
###< Git hooks ###

###> PHP Unit ###
/.phpunit.result.cache
###< PHP Unit ###
###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###

###> phpstan/phpstan ###
phpstan.neon
###< phpstan/phpstan ###

### LTI Keys ###
config/secrets/lti1p3/

# Agent reference documentation
agent-ref/